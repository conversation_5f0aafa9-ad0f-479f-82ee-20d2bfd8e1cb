{% sw_extends '@Storefront/storefront/base.html.twig' %}

{% block base_body_script %}
    {{ parent() }}
    {% block profitmatrix_all_page_js_wrapper %}
        {% if config('WexoProfitmetrics.config.allPagesJavascript') %}
            {% block profitmatrix_all_page_js_content %}
                {{ config('WexoProfitmetrics.config.allPagesJavascript')|raw }}
            {% endblock %}
        {% endif %}
    {% endblock %}
    
    {% if page.order.customFields.google_ads_script %}
        <script type="text/javascript">
            {{ page.order.customFields.google_ads_script|raw }}
        </script>
    {% endif %}

    {% if config('WexoProfitmetrics.config.googleConversionId') %}
        {% set googleConversionId =  config('WexoProfitmetrics.config.googleConversionId') %}

        <script async src="https://www.googletagmanager.com/gtag/js?id={{ googleConversionId }}"></script>

    {% endif %}
{% endblock %}

{% block base_script_router %}
    {{ parent() }}
    <script>
        window.router['frontend.store.profitmetrics.tracking'] = '{{ path('frontend.store.profitmetrics.tracking') }}';
    </script>
{% endblock %}

