import './service/BsCloneApiService'
import './extension/bs-category-product-clone-view';
import './view/bs-category-product-clone';


Shopware.Module.register('bs-category-product-clone', {
    routeMiddleware(next, currentRoute) {
        if (currentRoute.name === 'sw.category.detail') {
            currentRoute.children.push({
                name: 'bs.category.product.clone',
                path: '/sw/category/index/:id/product-category',
                component: 'bs-category-product-clone',
                meta: {
                    parentPath: 'sw.category.index'
                }
            });
        }
        next(currentRoute)
    }
});
