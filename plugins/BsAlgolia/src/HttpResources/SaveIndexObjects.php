<?php

declare(strict_types=1);

namespace BsAlgolia\HttpResources;

use Algolia\AlgoliaSearch\SearchClient;
use BsAlgolia\HttpResources\SaveIndexObjectsRequest;

class SaveIndexObjects
{
    public function __construct(private readonly SearchClient $client)
    {
    }

    public function __invoke(string $indexName, SaveIndexObjectsRequest $saveIndexObjectsRequest)
    {
        $index = $this->client->initIndex($indexName);

        return $index->saveObjects($saveIndexObjectsRequest->jsonSerialize());
    }
}
