/* Containers */

.algolia-container {
  display: flex;
  margin: 0 auto;
  max-width: 1400px;
  padding: 1rem;
}

.container-filters {
  flex: 1;
  margin-right: 50px;
  width: 250px;
}

.container-header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  min-height: 40px;
}

.container-results {
  flex: 3;
}

.container-options {
  border-bottom: 1px solid #ebecf3;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 30px;
}

.container-options .container-option:not(:first-child) {
  margin-left: 48px;
}

.container-options select {
  min-width: 100px;
}

.ft-container-footer {
  margin: 3rem 0 1rem 0;
}

/* Styles the SFFV highlightings */

em {
  font-style: normal;
}

em,
mark {
  background: rgba($primary, 0.4);
}

/* Clear refinements container */

.clear-filters {
  align-items: center;
  display: flex;
}

.clear-filters svg {
  margin-right: 8px;
}

/* Panel */

.container-body .ais-Panel {
  border-top: 1px solid #ebecf3;
  padding-bottom: 2rem;
  padding-top: 2rem;
}

/* Search box */

.header .ais-SearchBox {
  height: 64px;
  width: 740px;
}

.header .ais-SearchBox .ais-SearchBox-input {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 48px 0 rgba(0, 0, 0, 0.2);
  height: 64px;
  /*
    The "Hind" font family is vertically off-balance.
    Adding 4px of padding top makes it more vertically aligned.
  */
  padding: 4px 48px 0 64px;
}

.header .ais-SearchBox-submit {
  padding: 0 1rem 0 2rem;
  width: 64px;
}

.header .ais-SearchBox .ais-SearchBox-input::placeholder {
  color: rgba(33, 36, 61, 0.24);
  opacity: 1; /* Firefox */
}

.ais-SearchBox-input:-ms-input-placeholder {
  color: rgba(33, 36, 61, 0.24);
}

.ais-SearchBox-input::-ms-input-placeholder {
  color: rgba(33, 36, 61, 0.24);
}

.ais-SearchBox-submit {
  color: $primary;
}

.ais-RefinementList .ais-SearchBox-input {
  padding-top: 2px;
}

/* Hits */

.hit {
  color: #21243d;
}

.hit-category {
  color: #21243d;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 8px;
  opacity: 0.7;
  text-transform: uppercase;
}

.hit-description {
  margin-top: 2px;
}

.hit-info-container {
  overflow-wrap: break-word;
  word-break: break-word;
}

.hit-image-container {
  align-items: center;
  display: flex;
  height: 174px;
  justify-content: center;
  margin: auto;
  width: 174px;
}

.hit-image {
  height: auto;
  max-height: 100%;
  max-width: 100%;
}

.hit-em {
  color: $primary;
  font-size: 11px;
  font-weight: 600;
}

.hit-rating {
  border: 1px solid rgba(226, 164, 0, 0.5);
  border-radius: 4px;
  margin-left: 4px;
  padding: 0 4px;
}

.hits-empty-state {
  align-items: center;
  display: flex;
  flex-direction: column;
  margin: auto;
  max-width: 300px;
}

.hits-empty-state-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0;
  text-align: center;
}

.hits-empty-state-description {
  color: rgba(35, 37, 51, 0.6);
  font-size: 0.875rem;
  text-align: center;
}

.hits-empty-state .ais-ClearRefinements {
  margin-top: 1rem;
}

.hits-empty-state .ais-ClearRefinements-button--disabled {
  display: none;
}

.hits-empty-state .ais-ClearRefinements-button {
  background: rgba(10, 8, 41, 0.04);
  border-radius: 3px;
  color: #21243d;
  min-height: 48px;
  padding: 16px 24px;
}

/* ToggleRefinement */

.ais-ToggleRefinement-label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
}

.ais-ToggleRefinement-checkbox {
  font: inherit;
  margin-left: 1rem;
  margin-right: 0;
  position: relative;
}

.ais-ToggleRefinement-checkbox:checked::before {
  color: $primary;
}

.ais-ToggleRefinement-checkbox::before {
  align-items: center;
  color: rgba(33, 36, 61, 0.32);
  content: 'No';
  display: flex;
  font-size: 0.8rem;
  height: 16px;
  position: absolute;
  right: 38px;
}

.ais-ToggleRefinement-checkbox:checked::before {
  content: 'Yes';
}

.ais-ToggleRefinement-count {
  display: none;
}

/* RatingMenu */

.ais-RatingMenu-item:not(.ais-RatingMenu-item--selected) {
  opacity: 0.5;
}

.ais-RatingMenu-starIcon {
  margin-right: 0.5rem;
}

/* Hide all mobile-specific design on desktop */

@media (min-width: 900px) {
  [data-layout='mobile'] {
    display: none;
  }
  .FsCategoryHitsList {
    grid-template-columns: 1fr !important;
  }
}

.ais-SearchBox-submit:focus {
  outline: none;
}

.ft-search {
  width: 100%;
  box-shadow: 1px 1px 5px 1px rgba($sw-color-brand-primary, .3);
}

.ft-search .ais-SearchBox-input {
  border-color: rgba($primary, 0.7);
  font-size: 1rem;
}

.ft-search .ais-SearchBox-submit {
  color: #909399;
}

.ft-search .ais-SearchBox-submit:hover {
  color: $primary;
}

.algolia-product-price {
  color: $primary;
  font-weight: 600;
  font-size: 18px !important;
}

.algolia-search .product-name {
  font-size: 15px;
  line-height: 27px;
  min-height: 54px;
  margin-bottom: 0px;
}

.ais-SearchBox-reset:hover {
  color: $primary;
}

.aa-Panel{
	top:125px !important;
	left:0 !important;
	right:0 !important;
	z-index: 1000;
	position: fixed;
  background: #ffff;
	padding-bottom: 140px;
	height: 100vh;
  overflow-y: scroll;
}

.close-search-btn {
	background-color: #f4f4f4;
	border-radius: 50%;
	width: 39px;
	height: 39px;
	margin-left: 10px;
	display: none;
	visibility: hidden;
}
.close-search-btn:focus {
	outline: none;
	box-shadow: none;
}
.close-search-btn:hover {
	border: 1px solid #ccc;
}
.ais-RefinementList-count {
  margin-right: 8px;
}
.ais-Panel-collapseButton:focus {
  outline: none;
}
.ais-RefinementList-list {
  max-height: 10rem;
  overflow: hidden;
  overflow-y: scroll;
}
.ais-SearchBox-submit,
.ais-SearchBox-reset,
.ais-ClearRefinements-button {
  background: none;
  border: none;
}
.ais-Hits-list,
.ais-RefinementList-list,
.ais-InfiniteHits-list {
  list-style: none;
  padding-left: 0;
}
.ais-InfiniteHits-loadMore {
  text-align: center;
  background: $primary;
  color: white;
  border-radius: 5px;
  padding: 4px 15px;
  border: none;
  margin-top: 20px;
}
.ais-InfiniteHits-loadMore:focus {
  outline: none;
}
.ais-InfiniteHits-loadMore--disabled {
  background: rgba($primary, 0.5);
  display: none;
}
.ais-InfiniteHits {
  text-align: center;
}
.algolia-reset-filter {
  color: #4a545b;
}
.ais-Highlight-highlighted, .ais-Snippet-highlighted {
  background: rgba($primary, 0.4) !important;
}
.FsCategoryHitsList {
  max-height: 15rem;
  // overflow: hidden;
  // overflow-y: auto;
}
.matching-categories-header{
  border-top: 1px solid #ebecf3;
  border-bottom: 1px solid #ebecf3;
  margin-bottom: 1rem;
  padding-top: 1rem;
}
[data-action='close-overlay'] {
  cursor: pointer;
}
.bs-algolia-buy,
.bs-algolia-details {
  border-radius: 6px !important;
  font-size: 14px !important;
  line-height: 25px;
  background-color: $primary;
  color: #fff;
}
.ais-InfiniteHits-item .product-action {
  margin-top: 5px;
}
.ais-InfiniteHits-item .product-price.with-list-price {
  color: $primary;
}
.ais-InfiniteHits-item .categ-in-title h3 {
  margin-top: 7px;
}