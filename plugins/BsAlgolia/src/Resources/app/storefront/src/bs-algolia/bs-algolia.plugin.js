import Plugin from 'src/plugin-system/plugin.class';
import algoliasearch from 'algoliasearch/lite';
import instantsearch from 'instantsearch.js';
import {
    searchBox as searchBoxWidget,
    panel,
    refinementList,
    stats,
    clearRefinements,
    configure as hitsPerPageWidget,
    infiniteHits,
    hits,
    index
} from 'instantsearch.js/es/widgets';
import { connectClearRefinements } from 'instantsearch.js/es/connectors';

export default class BsAlgoliaPlugin extends Plugin {
    static options = {
        applicationId: null,
        searchOnlyApiKey: null,
        productPage: null,
        hitsPerPage: null,
        currencySymbol: null,
        salesChannelId: '',
        languageId: '',
        addToCartRoute: null,
        translations: {
            searchPlaceholder: null,
            noResults: null,
            resetText: null,
            categoryText: null,
            categorySearchText: null,
            mobileResultText: null,
            mobileAllResultText: null,
            categoryNoResult: null,
            clearFiltersText: null,
            sizeText: null,
            sizeSearchText: null,
            sizeNoResult: null,
            colorText: null,
            colorSearchText: null,
            colorNoResult: null,
            showMoreResult: null,
            clearFiltersBeforeText: null,
            bundleText: null
        },
        searchInput: '.ft-search .ais-SearchBox-input',
    };

    init() {
        this._initInstantSearch();
        this._searchInput = this.el.querySelector(this.options.searchInput);
        this._searchInput.addEventListener('input', this._handleSearchInput.bind(this));
    }

    _handleSearchInput() {
        const { value } = this._searchInput;
        const closeBtn = document.querySelector('.js-search-close');
        const panelResult = document.querySelector('.aa-Panel');
    
        if (value.length > 0) {
            this._toggleResultsVisibility(true, closeBtn, panelResult);
        } else {
            this._toggleResultsVisibility(false, closeBtn, panelResult);
        }
    }

    _toggleResultsVisibility(show, closeBtn, panelResult) {
        closeBtn.style.visibility = show ? 'visible' : 'hidden';
        closeBtn.style.display = show ? 'block' : 'none';
        document.documentElement.style.overflow = show ? 'hidden' : 'unset';
        panelResult.classList.toggle('d-none', !show);
    }

    _attachEventListeners() {
        const filtersButtons = Array.prototype.slice.call(
            document.querySelectorAll('[data-action="open-overlay"]')
        );
        const closeOverlayButtons = Array.prototype.slice.call(
            document.querySelectorAll('[data-action="close-overlay"]')
        );
        const header = document.querySelector('#header');
        const resultsContainer = document.querySelector('.container-results');
          
        function openFilters() {
            document.body.classList.add('filtering');
            window.scrollTo(0, 0);
            window.addEventListener('keyup', onKeyUp);
            window.addEventListener('click', onClick);
        }
          
        function closeFilters() {
            document.body.classList.remove('filtering');
            resultsContainer.scrollIntoView();
            window.removeEventListener('keyup', onKeyUp);
            window.removeEventListener('click', onClick);
        }
          
        function onKeyUp(event) {
            if (event.key !== 'Escape') {
              return;
            }
          
            closeFilters();
        }
          
        function onClick(event) {
            if (event.target !== header) {
                return;
            }
            
            closeFilters();
        }
          
        filtersButtons.forEach((button) => {
            button.addEventListener('click', openFilters);
        });
        
        closeOverlayButtons.forEach((button) => {
            button.addEventListener('click', closeFilters);
        });
    }
    
    _initInstantSearch() {
        const searchClient = algoliasearch(
            this.options.applicationId,
            this.options.searchOnlyApiKey
        );
        searchClient.addAlgoliaAgent('bs_algolia_shopware');

        const search = instantsearch({
            searchClient,
            indexName: this._buildIndexName(),
            insights: true,
        });

        search.addWidgets([
            // this._categoryRefinementList(),
            this._sizeRefinementList(),
            this._colorRefinementList(),
            // this._clearRefinements(),
            this._clearFiltersEmptyResults(),
            this._clearFiltersMobile(),
            this._hitsPerPage(),
            this._products(this.options),
            this._resultsNumberMobile(),
            this._saveFiltersMobile(),
            this._searchBox(this.options),
            this._resetAllFilters(),
            index({ indexName: this._buildCategoryIndexName() })
            .addWidgets([
                hitsPerPageWidget({
                    hitsPerPage: 5,
                }),
                hits({
                    container: '[data-widget="matching-categories"]',
                    cssClasses: {
                        root: 'FsCategoryHits',
                        list: ['FsCategoryHitsList'],
                    },
                    templates: {
                        item(hit, { html, components }) {
                            return html`
                                <a href="/navigation/${hit.objectID}">
                                    ${components.Highlight({ attribute: 'name', hit })}
                                </a>
                            `;
                        },
                        empty(results, { html }) {
                            if(document.querySelector('.FsCategoryHitsList'))
                                document.querySelector('.FsCategoryHitsList').innerHTML = '';
                            return html`<span></span>`;
                        }
                    },
                }),
            ]),
        ]);
        search.start();
        this._attachEventListeners();
        search.on('render', () => {
            PluginManager.initializePlugins();
        });
    }

    _buildIndexName() {
        return 'BS-'+this.options.salesChannelId+'-'+this.options.languageId;
    }

    _buildCategoryIndexName() {
        return 'BS-categories-' + this.options.salesChannelId;
    }

    _searchBox(options)
    {
        const searchBox = searchBoxWidget({
            container: '[data-widget="searchbox"]',
            placeholder: this.options.translations.searchPlaceholder,
            showReset: true,
            showLoadingIndicator:true,
            templates: {
              submit: `
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 18 18">
                <g fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.67" transform="translate(1 1)">
                  <circle cx="7.11" cy="7.11" r="7.11"/>
                  <path d="M16 16l-3.87-3.87"/>
                </g>
              </svg>
              `,
              reset({ cssClasses }, { html }) {
                return html`<span class="${cssClasses.reset}">${options.translations.resetText}</span>`;
              }
            },
        });

        return searchBox;
    }

    _categoryRefinementList() {
        const collapseButtonText = ({ collapsed }) =>
        collapsed
            ? `
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13">
            <path fill="#21243d" d="M6 6V0h1v6h6v1H7v6H6V7H0V6h6z" fill-rule="evenodd"/>
        </svg>
            `
            : `
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13">
            <path fill="#21243d" d="M0 6h13v1H0z" fill-rule="evenodd"/>
        </svg>
        `;
        const categoryRefinementList = panel({
            templates: {
              header: this.options.translations.categoryText,
              collapseButtonText,
            },
            collapsed: () => true,
        })(refinementList);

        const categories = categoryRefinementList({
            container: '[data-widget="categories"]',
            attribute: 'categories',
            searchable: true,
            searchablePlaceholder: this.options.translations.categorySearchText,
            searchableShowReset: true,
            searchableIsAlwaysActive: true,
            limit: 200,
            templates: {
              searchableSubmit: `
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 14 14">
                <g fill="none" fill-rule="evenodd" stroke="#21243D" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.33" transform="translate(1 1)">
                    <circle cx="5.333" cy="5.333" r="5.333"/>
                    <path d="M12 12L9.1 9.1"/>
                </g>
              </svg>
              `,
              searchableNoResults: 
                `<span>${this.options.translations.categoryNoResult}</span>`,
            },
        });

        return categories;
    }

    _sizeRefinementList() {
        const collapseButtonText = ({ collapsed }) =>
        collapsed
            ? `
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13">
            <path fill="#21243d" d="M6 6V0h1v6h6v1H7v6H6V7H0V6h6z" fill-rule="evenodd"/>
        </svg>
            `
            : `
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13">
            <path fill="#21243d" d="M0 6h13v1H0z" fill-rule="evenodd"/>
        </svg>
        `;
        const sizeRefinementList = panel({
            templates: {
              header: this.options.translations.sizeText,
              collapseButtonText,
            },
            collapsed: () => true,
        })(refinementList);

        const sizes = sizeRefinementList({
            container: '[data-widget="sizes"]',
            attribute: 'size',
            searchable: true,
            limit: 100,
            searchablePlaceholder: this.options.translations.sizeSearchText,
            searchableShowReset: true,
            templates: {
              searchableSubmit: `
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 14 14">
                <g fill="none" fill-rule="evenodd" stroke="#21243D" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.33" transform="translate(1 1)">
                    <circle cx="5.333" cy="5.333" r="5.333"/>
                    <path d="M12 12L9.1 9.1"/>
                </g>
              </svg>
              `,
              searchableNoResults: 
                `<span>${this.options.translations.sizeNoResult}</span>`,
            },
        });

        return sizes;
    }

    _colorRefinementList() {
        const collapseButtonText = ({ collapsed }) =>
        collapsed
            ? `
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13">
            <path fill="#21243d" d="M6 6V0h1v6h6v1H7v6H6V7H0V6h6z" fill-rule="evenodd"/>
        </svg>
            `
            : `
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13">
            <path fill="#21243d" d="M0 6h13v1H0z" fill-rule="evenodd"/>
        </svg>
        `;
        const colorRefinementList = panel({
            templates: {
              header: this.options.translations.colorText,
              collapseButtonText,
            },
            collapsed: () => true,
        })(refinementList);

        const color = colorRefinementList({
            container: '[data-widget="color"]',
            attribute: 'color',
            searchable: true,
            limit: 100,
            searchablePlaceholder: this.options.translations.colorSearchText,
            searchableShowReset: true,
            templates: {
              searchableSubmit: `
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 14 14">
                <g fill="none" fill-rule="evenodd" stroke="#21243D" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.33" transform="translate(1 1)">
                    <circle cx="5.333" cy="5.333" r="5.333"/>
                    <path d="M12 12L9.1 9.1"/>
                </g>
              </svg>
              `,
              searchableNoResults: 
                `<span>${this.options.translations.colorNoResult}</span>`,
            },
        });

        return color;
    }

    _saveFiltersMobile() {
        const saveFiltersMobile = stats({
            container: '[data-widget="save-filters-mobile"]',
            templates: {
              text: `
              <button class="button button-primary">
                ${this.options.translations.mobileAllResutText}
              </button>
          `,
            },
        });

        return saveFiltersMobile;
    }

    _resultsNumberMobile() {
        const resultsNumberMobile = stats({
            container: '[data-widget="results-number-mobile"]',
            templates: {
              text: `<strong>{{#helpers.formatNumber}}{{nbHits}}{{/helpers.formatNumber}}</strong> 
              ${this.options.translations.mobileResutText}`,
            },
        });

        return resultsNumberMobile;
    }

    _clearRefinements() {
        let clerLabels = this.options.translations;
        const clearFilters = clearRefinements({
            container: '[data-widget="clear-filters"]',
            templates: {
                resetLabel({ hasRefinements }, { html }) {
                    if(hasRefinements) {
                        return html`<div class="clear-filters"><svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11">
                        <g fill="none" fill-rule="evenodd" opacity=".4">
                            <path d="M0 0h11v11H0z"/>
                            <path fill="#000" fill-rule="nonzero" d="M8.26 2.75a3.896 3.896 0 1 0 1.102 3.262l.007-.056a.49.49 0 0 1 .485-.456c.253 0 .451.206.437.457 0 0 .012-.109-.006.061a4.813 4.813 0 1 1-1.348-3.887v-.987a.458.458 0 1 1 .917.002v2.062a.459.459 0 0 1-.459.459H7.334a.458.458 0 1 1-.002-.917h.928z"/>
                        </g>
                        </svg> ${clerLabels.clearFiltersText} </div>`;
                    } else {
                        return html`<div class="clear-filters algolia-reset-filter">${clerLabels.clearFiltersBeforeText}</div>`;
                    }
                },
            },   
        });

        return clearFilters;
    }

    _clearFiltersEmptyResults() {
        const clearFilters = panel({
            hidden(options) {
              return options.results.nbHits > 0;
            },
        })(clearRefinements);

        const clearFiltersEmptyResults = clearFilters({
            container: '[data-widget="clear-filters-empty-state"]',
            templates: {
              resetLabel: `
                <div class="clear-filters">
                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11">
                    <g fill="none" fill-rule="evenodd">
                        <path d="M0 0h11v11H0z"/>
                        <path fill="#000" fill-rule="nonzero" d="M8.26 2.75a3.896 3.896 0 1 0 1.102 3.262l.007-.056a.49.49 0 0 1 .485-.456c.253 0 .451.206.437.457 0 0 .012-.109-.006.061a4.813 4.813 0 1 1-1.348-3.887v-.987a.458.458 0 1 1 .917.002v2.062a.459.459 0 0 1-.459.459H7.334a.458.458 0 1 1-.002-.917h.928z"/>
                    </g>
                    </svg>
                    ${this.options.translations.clearFiltersText}
                </div>
              `,
            },
        });

        return clearFiltersEmptyResults;
    }

    _clearFiltersMobile() {
        const resultsContainer = document.querySelector('.container-results');

        const clearRefinements = connectClearRefinements(
            ({ refine, widgetParams }, isFirstRender) => {
              const { container } = widgetParams;
              const containerNode = document.querySelector(container);
          
                if (isFirstRender) {
                    const wrapper = document.createElement('div');
                    wrapper.classList.add('ais-ClearRefinements');
                    const button = document.createElement('button');
                    button.classList.add('ais-ClearRefinements-button');
                    button.textContent = this.options.translations.clearFiltersText;
            
                    button.addEventListener('click', () => {
                        refine();
                        // document.querySelector('.fs-missing-categories').innerHTML = '';
                        document.body.classList.remove('filtering');
                        resultsContainer.scrollIntoView();
                    })
            
                    wrapper.appendChild(button)
                    containerNode.appendChild(wrapper)
                }
            }
        )
          
        const clearFiltersMobile = clearRefinements({
            container: '[data-widget="clear-filters-mobile"]'
        })

        return clearFiltersMobile;
    }

    _hitsPerPage() {
        let numberOfHits = (this.options.hitsPerPage > 0)? this.options.hitsPerPage: 50;

        const hitsPerPage = hitsPerPageWidget({
            hitsPerPage: numberOfHits,
        });

        return hitsPerPage;
    }

    _displayFromVariants(hit) {
        return Object.keys(hit.cheapestPrice).length > 1;
    }
    
    _products(options) {
        const that = this;
        let products = infiniteHits({
            container: '[data-widget="hits"]',
            templates: {
                item(hit, { html, components, sendEvent }) {
                    const listPrice = hit.listPrice.gross !== null;
                    let cheapestPrice;
                    if (hit.cheapestPrice) {
                        cheapestPrice = that._resolveCheapestPrice(hit.cheapestPrice);
                    } 
                    if(!cheapestPrice) {
                        cheapestPrice = {gross: hit.price.gross};
                    }
                    const displayVariants = Object.keys(hit.cheapestPrice).length > 1;
                    let mainUrl = options.productPage;
                    let isBundle = false;
                    if(hit.isBundle == true) {
                        isBundle = true;
                    }

                    return html`
                        <div class="card product-box box-minimal algolia-search" onClick="${() => sendEvent('click', hit, 'Product Clicked')}">
                            <div class="box-fes-bg">
                                <div class="hovereffect">
                                    <a href="${decodeURI(mainUrl).replace('%productId%', hit.objectID)}" title=${hit.name}>
                                        <img src=${hit.image} class="product-image is-" alt=${hit.name} title=${hit.name} />
                                    </a>
                                </div>
                            </div>
                            <div class="categ-in-title text-center">
                                <h3>
                                    <a href="${decodeURI(mainUrl).replace('%productId%', hit.objectID)}" class="product-name" title=${hit.name}>${hit.name}</a>
                                </h3>
                                <div class="aa-ItemContentDescription d-none">
                                    ${hit.productNumber && html `${hit.productNumber}`}
                                </div>
                                ${cheapestPrice && html`
                                    <div class="product-price-info">
                                        <div class="product-price-wrapper">
                                            ${!listPrice && displayVariants && html `${options.translations.listingTextFrom} `}
                                            <span class="algolia-product-price product-price ${listPrice && (hit.listPrice.gross > cheapestPrice.gross) && html `with-list-price`}">
                                                ${options.currencySymbol} ${cheapestPrice.gross}
                                                ${listPrice && (hit.listPrice.gross > cheapestPrice.gross) && html`
                                                    <span class="list-price">
                                                        <span class="list-price-price">
                                                            ${options.currencySymbol} ${hit.listPrice.gross}
                                                        </span>
                                                    </span>
                                                `}
                                            </span>
                                        </div>
                                    </div>
                                `}
                                <div class="product-action">
                                    ${hit.isBuyable && !isBundle && html`
                                        <div class="d-grid">
                                            <button class="btn btn-buy bs-algolia-buy clerk-add-to-cart"
                                                    title="${options.translations.boxAddProduct}"
                                                    data-clerk-product-id="${hit.objectID}"
                                            >
                                                ${options.translations.boxAddProduct}
                                            </button>
                                        </div>
                                    `}

                                    ${ isBundle && html`
                                        <div class="d-grid">
                                            <a href="${decodeURI(mainUrl).replace('%productId%', hit.objectID)}"
                                            class="btn btn-light bs-algolia-details"
                                            title="${options.translations.bundleText}">
                                            ${options.translations.bundleText}
                                            </a>
                                        </div>
                                    `}

                                    ${ !hit.isBuyable && !isBundle && html`
                                        <div class="d-grid">
                                            <a href="${decodeURI(mainUrl).replace('%productId%', hit.objectID)}"
                                            class="btn btn-light bs-algolia-details"
                                            title="${options.translations.boxProductDetails}">
                                            ${options.translations.boxProductDetails}
                                            </a>
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                    `;
                },
                empty(results, { html }) {
                    return html`${options.translations.noResults}`;
                },
                showMoreText(data, { html }) {
                    return html`<span>${options.translations.showMoreResult}</span>`;
                },
            },
        });

        return products;
    }

    _resolveCheapestPrice(cheapestPriceContainer) {
        const that = this;
        
        for (const ruleId of that.options.ruleIds) {
            const cheapestPrice = cheapestPriceContainer.find(item => item.ruleId === ruleId);

            if (cheapestPrice !== undefined) {
                return cheapestPrice;
            }
        }

        return cheapestPriceContainer.find(item => item.ruleId === 'default');
    }

    _resetAllFilters() {
        const clearRefinements = connectClearRefinements(
            function render({ refine, widgetParams }, isFirstRendering) {
                if (!isFirstRendering) {
                    return;
                }

                let button = document.querySelector('.ais-SearchBox-reset');
                button.addEventListener('click', () => {
                    document.querySelector('.FsCategoryHitsList').innerHTML = '';
                    refine();
                });
                let queryInput = document.querySelector('.ft-search .ais-SearchBox-input'); 
                queryInput.addEventListener('keyup', () => {
                    if(queryInput.value.length < 1) {
                        document.querySelector('.FsCategoryHitsList').innerHTML = '';
                        refine();
                    }
                });
            }
        );

        const clearALL = clearRefinements({container: '.ais-SearchBox-reset',});

        return clearALL;
    }
}
