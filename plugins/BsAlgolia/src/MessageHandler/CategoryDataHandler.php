<?php

declare(strict_types=1);

namespace BsAlgolia\MessageHandler;

use BsAlgolia\Algolia\Action;
use BsAlgolia\Algolia\BsSalesChannel;
use BsAlgolia\HttpResources\DeleteIndexObjects;
use BsAlgolia\HttpResources\SaveIndexObjects;
use BsAlgolia\HttpResources\SaveIndexObjectsRequest;
use BsAlgolia\Message\CategoryDataMessage;
use BsAlgolia\Service\IndexNameGenerator;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\Util\Random;
use Shopware\Core\System\SalesChannel\Context\SalesChannelContextServiceInterface;
use Shopware\Core\System\SalesChannel\Context\SalesChannelContextServiceParameters;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SalesChannel\SalesChannelEntity;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository AS SalesChannelCategoryRepository;

#[AsMessageHandler]
class CategoryDataHandler
{
    public function __construct(
        private readonly SaveIndexObjects $saveIndexObjects,
        private readonly DeleteIndexObjects $deleteIndexObjects,
        private readonly BsSalesChannel $bsSalesChannel,
        private readonly SalesChannelContextServiceInterface $salesChannelContextService,
        private readonly SalesChannelCategoryRepository $salesChannelCategoryRepository,
        private readonly IndexNameGenerator $indexNameGenerator,
    ) {
    }

    public function __invoke(CategoryDataMessage $message): void
    {
        $operation = $message->getOperation();

        switch ($operation) {
            case Action::ACTION_UPDATE:
                foreach ($message->getCategoryIds() as $categoryId) {
                    /** @var SalesChannelEntity $salesChannel */
                    foreach ($this->bsSalesChannel->getActiveSalesChannels() as $salesChannel) {
                        $salesChannelContext = $this->salesChannelContextService->get(
                            $this->createSalesChannelContextParameters($salesChannel)
                        );

                        $categoryIndex = $this->indexNameGenerator->categoryIndex($salesChannel);

                        $mainNavigationId = $salesChannelContext->getSalesChannel()->getNavigationCategoryId();

                        if(empty($mainNavigationId)) {
                            continue;
                        }

                        $category = $this->fetchById(
                            $categoryId,
                            $salesChannelContext,
                            $mainNavigationId
                        );

                        if(empty($category)) {
                            continue;
                        }

                        $categoryId = $category->getId();

                        if ($category->getProductAssignmentType() !== 'product' ||
                            $category->getType() !== 'page' ||
                            $category->getVisible() === false ||
                            $category->getActive() === false ||
                            isset($category->getCustomFields()['festtema_is_separator']) && $category->getCustomFields()['festtema_is_separator'] === true
                        ) {
                            ($this->deleteIndexObjects)($this->indexNameGenerator->categoryIndex($salesChannel), [$categoryId]);
                            continue;
                        }

                        $categoryName = ($category->getName() == null)? $category->getTranslated()['name']: $category->getName();
                        
                        $payload = [
                            'objectID' => $categoryId,
                            'name' => $categoryName
                        ];

                        ($this->saveIndexObjects)($categoryIndex, SaveIndexObjectsRequest::withData([$payload]));
                    }
                }

                break;
            case Action::ACTION_DELETE:
                foreach ($message->getCategoryIds() as $categoryId) {
                    /** @var SalesChannelEntity $salesChannel */
                    foreach ($this->bsSalesChannel->getActiveSalesChannels() as $salesChannel) {
                        $categoryIndex = $this->indexNameGenerator->categoryIndex($salesChannel);
                        ($this->deleteIndexObjects)($categoryIndex, [$categoryId]);
                    }
                }
        }
    }

    public function fetchById(string $categoryId, SalesChannelContext $salesChannelContext, $mainNavigationId)
    {
        $criteria = (new Criteria([$categoryId]))
                        ->addFilter(new ContainsFilter('path', $mainNavigationId));

        $category = $this->salesChannelCategoryRepository->search($criteria, $salesChannelContext)->first();

        return $category;
    }

    private function createSalesChannelContextParameters(SalesChannelEntity $salesChannel)
    {
        return new SalesChannelContextServiceParameters(
            $salesChannel->getId(),
            Random::getAlphanumericString(32)
        );
    }
}
