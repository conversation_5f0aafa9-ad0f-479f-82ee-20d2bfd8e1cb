<?php

declare(strict_types=1);

namespace BsAlgolia\Service;

use BsAlgolia\Algolia\Action;
use BsAlgolia\Algolia\BsSalesChannel;
use BsAlgolia\Message\CategoryDataMessage;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\RepositoryIterator;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Util\Random;
use Shopware\Core\System\SalesChannel\Context\SalesChannelContextServiceInterface;
use Shopware\Core\System\SalesChannel\Context\SalesChannelContextServiceParameters;
use Symfony\Component\Messenger\MessageBusInterface;

class SyncCategoriesService
{
    private const OFFSET = 0;
    private const LIMIT = 20;

    public function __construct(
        private readonly EntityRepository $categoryRepository,
        private readonly BsSalesChannel $bsSalesChannel,
        private readonly SalesChannelContextServiceInterface $salesChannelContextService,
        private readonly MessageBusInterface $messageBus,
    ) {
    }

    public function __invoke(): void
    {
        foreach ($this->bsSalesChannel->getActiveSalesChannels() as $salesChannel) {
            $salesChannelContext = $this->salesChannelContextService->get(
                new SalesChannelContextServiceParameters(
                    $salesChannel->getId(),
                    Random::getAlphanumericString(32),
                ),
            );

            $mainNavigationId = $salesChannelContext->getSalesChannel()->getNavigationCategoryId();

            if(empty($mainNavigationId)) {
                continue;
            }

            $criteria = (new Criteria())
            ->setOffset(self::OFFSET)
            ->setLimit(self::LIMIT)
            ->addFilter(new EqualsFilter('active', true))
            ->addFilter(new EqualsFilter('visible', true))
            ->addFilter(new EqualsFilter('type', 'page'))
            ->addFilter(new ContainsFilter('path', $mainNavigationId));

            $iterator = new RepositoryIterator($this->categoryRepository, new Context(new SystemSource()), $criteria);

            /** @var EntitySearchResult $searchResult */
            while ($searchResult = $iterator->fetch()) {
                $this->messageBus->dispatch(
                    CategoryDataMessage::categoryData(
                        $searchResult->getIds(),
                        Action::ACTION_UPDATE,
                    ),
                );
            }

        }
    }

}