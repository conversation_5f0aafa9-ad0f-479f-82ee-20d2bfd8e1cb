<?php

declare(strict_types=1);

namespace BsAlgolia\Algolia;

use Shopware\Core\Defaults;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;

class BsSalesChannel
{
    public function __construct(private readonly EntityRepository $salesChannelRepository)
    {
    }

    public function getActiveSalesChannels(): EntityCollection
    {
        $criteria = new Criteria();
        $criteria
            ->addAssociation('languages')
            ->addAssociation('currency')
            ->addFilter(new EqualsFilter('typeId', Defaults::SALES_CHANNEL_TYPE_STOREFRONT))
            ->addFilter(new EqualsFilter('active', true));

        return $this->salesChannelRepository->search($criteria, new Context(new SystemSource()))->getEntities();
    }
}