<?php

declare(strict_types=1);

namespace BsAlgolia\Subscriber;

use BsAlgolia\Algolia\Action;
use BsAlgolia\Message\ProductDataMessage;
use Shopware\Core\Content\Product\ProductEvents;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityDeletedEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class ProductUpdateSubscriber implements EventSubscriberInterface
{
    public function __construct(private readonly MessageBusInterface $messageBus)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ProductEvents::PRODUCT_WRITTEN_EVENT => 'onProductUpdate',
            ProductEvents::PRODUCT_DELETED_EVENT => 'onProductDelete',
        ];
    }

    public function onProductUpdate(EntityWrittenEvent $event): void
    {
        $productParentId = $this->productParentId($event);

        if ([] === $productParentId) {
            return;
        }

        foreach ($event->getWriteResults() as $writeResult) {
            $payload = $writeResult->getPayload();

            if (!array_key_exists('active', $payload)) {
                continue;
            }

            if (false === $payload['active']) {
                $this->dispatchProductDataMessage($productParentId, Action::ACTION_DELETE);

                return;
            }
        }

        $this->dispatchProductDataMessage($productParentId, Action::ACTION_UPDATE);
    }

    public function onProductDelete(EntityDeletedEvent $event): void
    {
        $productParentId = $this->productParentId($event);

        if ([] === $productParentId) {
            return;
        }

        $this->dispatchProductDataMessage($productParentId, Action::ACTION_DELETE);
    }

    private function productParentId(EntityWrittenEvent $event): array
    {
        if (array_key_exists(1, $event->getWriteResults())) {
            return [$event->getWriteResults()[1]->getPrimaryKey()];
        }

        return [$event->getWriteResults()[0]->getPrimaryKey()];
    }

    private function dispatchProductDataMessage(array $productIds, string $action): void
    {
        $this->messageBus->dispatch(
            ProductDataMessage::productData(
                $productIds,
                $action,
            ),
        );
    }
}
