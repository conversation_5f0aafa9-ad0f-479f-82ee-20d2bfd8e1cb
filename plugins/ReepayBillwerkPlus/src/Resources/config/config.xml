<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/trunk/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>Billwerk+ API Configuration</title>
        <title lang="de-DE">Billwerk+ API instellungen</title>
        <title lang="da-DK">Billwerk+ API Konfiguration</title>
        <input-field>
            <name>privateApiKey</name>
            <label>Private API Key</label>
            <label lang="de-DE">Privater API-Schlüssel</label>
            <label lang="da-DK">Privat API nøgle</label>
            <type>text</type>
            <defaultValue></defaultValue>
        </input-field>
        <component name="test-api-component">
            <name>testapikey</name>
        </component>
        <input-field>
            <name>webhookSecret</name>
            <label>Webhook Secret</label>
            <label lang="de-DE">Webhook-Geheimnis</label>
            <label lang="da-DK">Webhook Secret</label>
            <type>password</type>
            <defaultValue></defaultValue>
        </input-field>
        <component name="install-webhook-component">
            <name>installwebhook</name>
        </component>
    </card>
    <card>
        <title>Billwerk+ Payment Configuration</title>
        <title lang="de-DE">Billwerk Zahlungskonfiguration</title>
        <title lang="da-DK">Billwerk Betalingskonfiguration</title>
        <input-field>
            <name>sendTransactions</name>
            <label>Send transactions</label>
            <label lang="de-DE">Transaktionen senden</label>
            <label lang="da-DK">Send transaktioner</label>
            <type>checkbox</type>
            <defaultValue>true</defaultValue>
        </input-field>
        <input-field>
            <name>addShippingCosts</name>
            <label>Add shipping costs</label>
            <label lang="de-DE">Versandkosten hinzufügen</label>
            <label lang="da-DK">Tilføj forsendelsesomkostninger</label>
            <type>checkbox</type>
            <defaultValue>true</defaultValue>
        </input-field>
        <input-field>
            <name>interactOnPaymentStatusChange</name>
            <label>Capture/Refund/Cancel on payment status change</label>
            <label lang="de-DE">Erfassung/Rückerstattung/Stornierung bei Änderung des Zahlungsstatus</label>
            <label lang="da-DK">Hæv/refunder/annuller på betalingsstatus ændring</label>
            <type>checkbox</type>
            <defaultValue>false</defaultValue>
        </input-field>
    </card>
</config>