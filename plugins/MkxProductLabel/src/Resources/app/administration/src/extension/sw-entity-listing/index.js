import template from './sw-entity-listing.html.twig';

const { Component, Mixin } = Shopware;
const { Criteria } = Shopware.Data;

Component.override('sw-entity-listing', {
    template,

    computed: {
        isProductListing() {
            return this.repository.entityName === 'product';
        },
    },

    methods: {
        createdComponent() {
            // Copied from sw-entity-listing
            this.$super('createdComponent');

            if (this.items) {
                this.applyResult(this.items);
            }
        },

        onOpenPrintFormBulk() {
            let productNumbers = Object.values(this.selection).map(el => el.productNumber);
            this.$router.push({
                name: 'mkx.productlabel.print.form',
                query: {
                    productNumbers: productNumbers.join()
                },
            });
        },
    },
});
