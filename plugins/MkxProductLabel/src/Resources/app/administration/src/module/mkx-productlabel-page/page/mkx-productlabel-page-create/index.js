const {Component} = Shopware;

Component.extend('mkx-productlabel-page-create', 'mkx-productlabel-page-detail', {
    methods: {
        getPage() {
            this.page = this.repository.create(Shopware.Context.api);
            this.page.marginLeftInMm = 0;
            this.page.marginRightInMm = 0;
            this.page.marginBottomInMm = 0;
            this.page.marginTopInMm = 0;
            this.page.spaceBetweenLabelsHorizontalInMm = 0;
            this.page.spaceBetweenLabelsVerticalInMm = 0;
            this.page.isLabelBorderVisible = false;
        },

        onClickSave() {
            if (!this.validatedInput()) {
                return;
            }
            this.isLoading = true;

            this.repository
                .save(this.page, Shopware.Context.api)
                .then(() => {
                    this.isLoading = false;
                    this.$router.push({name: 'mkx.productlabel.page.detail', params: {id: this.page.id}});
                })
                .catch((exception) => {
                    this.isLoading = false;

                    this.createNotificationError({
                        title: this.$t('mkx.productLabel.error.missingDataTitle'),
                        message: this.$t('mkx.productLabel.error.missingDataMessage'),
                    });
                })
            ;
        }
    }
});
