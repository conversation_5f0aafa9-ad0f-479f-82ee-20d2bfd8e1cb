const {Component} = Shopware;

Component.extend('mkx-productlabel-label-create', 'mkx-productlabel-label-detail', {
    methods: {
        getLabel() {
            this.label = this.labelRepository.create(Shopware.Context.api);
            this.label.marginLeftInMm = 0;
            this.label.marginRightInMm = 0;
            this.label.marginBottomInMm = 0;
            this.label.marginTopInMm = 0;
            this.label.template = this.$t('mkx.productLabel.template.defaultTemplate');
        },

        onClickSave() {
            if (!this.validatedInput()) {
                return;
            }
            this.isLoading = true;

            this.labelRepository
                .save(this.label, Shopware.Context.api)
                .then(() => {
                    this.isLoading = false;
                    this.$router.push({name: 'mkx.productlabel.label.detail', params: {id: this.label.id}});
                })
                .catch((exception) => {
                    this.isLoading = false;

                    this.createNotificationError({
                        title: this.$t('mkx.productLabel.error.missingDataTitle'),
                        message: this.$t('mkx.productLabel.error.missingDataMessage'),
                    });
                })
            ;
        }
    }
});
