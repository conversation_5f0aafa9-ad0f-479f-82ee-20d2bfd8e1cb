<?xml version="1.0"?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service
            id="StudioSolid\ProductVideos\Core\Content\Product\Service\ProductEmbeddedVideoMediaService">
            <argument type="service" id="solid_product_videos.filesystem.public" />
            <argument type="service"
                id="StudioSolid\ProductVideos\Core\Content\Product\Service\ProductEmbeddedMediaThumbnailUrlGenerator" />
        </service>

        <service
            id="StudioSolid\ProductVideos\Core\Content\Product\Service\ProductEmbeddedMediaThumbnailUrlGenerator">
            <argument type="service" id="solid_product_videos.filesystem.public" />
            <argument type="service" id="request_stack" />
            <tag name="kernel.reset" method="reset" />
        </service>
    </services>
</container>
