{% sw_extends '@Storefront/storefront/element/cms-element-image-gallery.html.twig' %}

{% block element_image_gallery_inner %}
    {% set isMappedToProductMedia = element.config.sliderItems.source == 'mapped' and element.config.sliderItems.value == 'product.media' %}
    {% set combinedProductMedia = page.product.extensions.solidPvCombinedMedia.get('combinedMedia') %}
    {% set mediaItems = combinedProductMedia ? (isProduct or isMappedToProductMedia ? combinedProductMedia : mediaItems) : mediaItems %}
    {% set imageCount = mediaItems|length %}

    {{ parent() }}
{% endblock %}

{% block element_image_gallery_inner_items %}
    {% block solid_pv_element_image_gallery_inner_items %}
        {% set productVideosConfig = config('SolidProductVideos.config') %}
        {% set isMappedToProductMedia = element.config.sliderItems.source == 'mapped' and element.config.sliderItems.value == 'product.media' %}
        {% set combinedProductMedia = page.product.extensions.solidPvCombinedMedia.get('combinedMedia') %}
        {% set mediaItems = combinedProductMedia ? (isProduct or isMappedToProductMedia ? combinedProductMedia : mediaItems) : mediaItems %}

        {% for mediaItem in mediaItems %}
            {% block element_image_gallery_inner_item %}
                <div class="gallery-slider-item-container">
                    {% if (isProduct or isMappedToProductMedia) and ((mediaItem.source and mediaItem.videoId) or mediaItem.media.mimeType | split('/')[0] == 'video') %}
                        {% set isVideo = true %}
                    {% endif %}

                    <div class="gallery-slider-item is-{{ displayMode }} {% if isVideo %}is-solid-pv-video{% else %}js-magnifier-container{% endif %}" {% if minHeight and (displayMode == "cover" or displayMode == "contain" ) %} style="min-height: {{ minHeight }}" {% endif %}>
                        {% set attributes = {
                            'class': 'img-fluid gallery-slider-image' ~ (isVideo ?: ' magnifier-image js-magnifier-image'),
                            'alt': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.alt ?: fallbackImageTitle) : (mediaItem.translated.alt ?: fallbackImageTitle)),
                            'title': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.title ?: fallbackImageTitle) : (mediaItem.translated.title ?: fallbackImageTitle)),
                            'data-full-image': (isVideo ?: (isProduct or isMappedToProductMedia ? mediaItem.media.url : mediaItem.url))
                        } %}

                        {% if displayMode == 'cover' or displayMode == 'contain' %}
                            {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
                        {% endif %}

                        {% if (isProduct or isMappedToProductMedia) and not isVideo %}
                            {% set attributes = attributes|merge({ 'itemprop': 'image' }) %}
                        {% endif %}

                        {% block solid_pv_gallery_slider_image_conditions %}
                            {% if (isProduct or isMappedToProductMedia) and (mediaItem.source and mediaItem.videoId) %}
                                {% if productVideosConfig.forceUserConsent %}
                                    {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-content-blocker.html.twig' with {
                                        source: mediaItem.source,
                                        videoId: mediaItem.videoId,
                                        poster: mediaItem.thumbnailMedia,
                                    } %}
                                {% else %}
                                    {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-' ~ mediaItem.source ~ '.html.twig' with {
                                        videoId: mediaItem.videoId,
                                        poster: mediaItem.thumbnailMedia,
                                    } %}
                                {% endif %}
                            {% elseif (isProduct or isMappedToProductMedia) and mediaItem.media.mimeType | split('/')[0] == 'video' %}
                                {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-video.html.twig' %}
                            {% else %}
                                {% block solid_pv_gallery_slider_image_image %}
                                    {% sw_thumbnails 'gallery-slider-image-thumbnails' with {
                                        media: (isProduct or isMappedToProductMedia ? mediaItem.media : mediaItem)
                                    } %}
                                {% endblock %}
                            {% endif %}
                        {% endblock %}
                    </div>
                </div>
            {% endblock %}
        {% endfor %}
    {% endblock %}
{% endblock %}

{% block element_image_gallery_inner_single %}
    {% set productVideosConfig = config('SolidProductVideos.config') %}
    {% set isMappedToProductMedia = element.config.sliderItems.source == 'mapped' and element.config.sliderItems.value == 'product.media' %}
    {% set combinedProductMedia = page.product.extensions.solidPvCombinedMedia.get('combinedMedia') %}
    {% set mediaItems = combinedProductMedia ? (isProduct or isMappedToProductMedia ? combinedProductMedia : mediaItems) : mediaItems %}
    {% set mediaItem = mediaItems|first %}
    {% set imageCount = mediaItems|length %}

    {% block solid_pv_element_image_gallery_inner_single %}
        {% if (isProduct or isMappedToProductMedia) and ((mediaItem.source and mediaItem.videoId) or mediaItem.media.mimeType | split('/')[0] == 'video') %}
            {% set isVideo = true %}
        {% endif %}

        <div class="gallery-slider-single-image is-{{ displayMode }} {% if isVideo %}is-solid-pv-video{% else %}js-magnifier-container{% endif %}" {% if minHeight %} style="min-height: {{ minHeight }}" {% endif %}>
            {% if imageCount > 0 %}
                {% set attributes = {
                    'class': 'img-fluid gallery-slider-image' ~ (isVideo ?: ' magnifier-image js-magnifier-image'),
                    'alt': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.alt ?: fallbackImageTitle) : (mediaItem.translated.alt ?: fallbackImageTitle)),
                    'title': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.title ?: fallbackImageTitle) : (mediaItem.translated.title ?: fallbackImageTitle)),
                    'data-full-image': (isVideo ?: (isProduct or isMappedToProductMedia ? mediaItem.media.url : mediaItem.url))
                } %}

                {% if displayMode == 'cover' or displayMode == 'contain' %}
                    {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
                {% endif %}

                {% if (isProduct or isMappedToProductMedia) and not isVideo %}
                    {% set attributes = attributes|merge({ 'itemprop': 'image' }) %}
                {% endif %}

                {% block solid_pv_gallery_slider_image_single_conditions %}
                    {% if (isProduct or isMappedToProductMedia) and (mediaItem.source and mediaItem.videoId) %}
                        {% if productVideosConfig.forceUserConsent %}
                            {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-content-blocker.html.twig' with {
                                source: mediaItem.source,
                                videoId: mediaItem.videoId,
                                poster: mediaItem.thumbnailMedia,
                            } %}
                        {% else %}
                            {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-' ~ mediaItem.source ~ '.html.twig' with {
                                videoId: mediaItem.videoId,
                                poster: mediaItem.thumbnailMedia,
                            } %}
                        {% endif %}
                    {% elseif (isProduct or isMappedToProductMedia) and mediaItem.media.mimeType | split('/')[0] == 'video' %}
                        {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-video.html.twig' %}
                    {% else %}
                        {% block solid_pv_gallery_slider_image_single_image %}
                            {% sw_thumbnails 'gallery-slider-image-thumbnails' with {
                                media: (isProduct or isMappedToProductMedia ? mediaItem.media : mediaItem)
                            } %}
                        {% endblock %}
                    {% endif %}
                {% endblock %}
            {% else %}
                {% sw_icon 'placeholder' style {
                    'size': 'fluid'
                } %}
            {% endif %}
        </div>
    {% endblock %}
{% endblock %}

{% block element_image_gallery_inner_thumbnails_items %}
    {% block solid_pv_element_image_gallery_inner_thumbnails_items %}
        {% set productVideosConfig = config('SolidProductVideos.config') %}
        {% set isMappedToProductMedia = element.config.sliderItems.source == 'mapped' and element.config.sliderItems.value == 'product.media' %}
        {% set combinedProductMedia = page.product.extensions.solidPvCombinedMedia.get('combinedMedia') %}
        {% set mediaItems = combinedProductMedia ? (isProduct or isMappedToProductMedia ? combinedProductMedia : mediaItems) : mediaItems %}

        {% for mediaItem in mediaItems %}
            {% block element_image_gallery_inner_thumbnails_item %}
                <div class="gallery-slider-thumbnails-item">
                    {% block element_image_gallery_inner_thumbnails_item_inner %}
                        <div class="gallery-slider-thumbnails-item-inner">
                            {% set attributes = {
                                'class': 'gallery-slider-thumbnails-image',
                                'alt': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.alt ?: fallbackImageTitle) : (mediaItem.translated.alt ?: fallbackImageTitle)),
                                'title': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.title ?: fallbackImageTitle) : (mediaItem.translated.title ?: fallbackImageTitle))
                            } %}

                            {% if (isProduct or isMappedToProductMedia) and not isVideo %}
                                {% set attributes = attributes|merge({ 'itemprop': 'image' }) %}
                            {% endif %}

                            {% block solid_pv_gallery_slider_thumbnail_conditions %}
                                {% if (isProduct or isMappedToProductMedia) and (mediaItem.media.mimeType | split('/')[0] == 'video' and not mediaItem.extensions.solidPvVideoThumbnail) %}
                                    {% block solid_pv_gallery_slider_thumbnail_video %}
                                        <div class="solid-pv-item">
                                            <video preload="none">
                                                <source src="{{ mediaItem.media.url }}" type="{{ mediaItem.media.mimeType }}">
                                            </video>

                                            <div class="solid-pv-icon">
                                                {% sw_icon 'multicolor-action-play' style {
                                                    'namespace': 'SolidProductVideos'
                                                } %}
                                            </div>
                                        </div>
                                    {% endblock %}
                                {% else %}
                                    {% block solid_pv_gallery_slider_thumbnail_image %}
                                        {% if mediaItem.source and mediaItem.videoId %}
                                            {% if mediaItem.thumbnailMedia %}
                                                {% sw_thumbnails 'gallery-slider-thumbnails-image-thumbnails' with {
                                                    media: mediaItem.thumbnailMedia,
                                                    sizes: {
                                                        'default': '200px'
                                                    }
                                                } %}
                                            {% else %}
                                                <img
                                                    class="gallery-slider-thumbnails-image"
                                                    src="{{ solidPvGetProductEmbeddedVideoMediaThumbnailUrl(mediaItem) }}"
                                                    alt="{{ page.product.translated.name }}"
                                                    title="{{ page.product.translated.name }}"
                                                >
                                            {% endif %}
                                        {% elseif mediaItem.extensions.solidPvVideoThumbnail %}
                                            {% sw_thumbnails 'gallery-slider-thumbnails-image-thumbnails' with {
                                                media: mediaItem.extensions.solidPvVideoThumbnail.media,
                                                sizes: {
                                                    'default': '200px'
                                                }
                                            } %}
                                        {% else %}
                                            {% sw_thumbnails 'gallery-slider-thumbnails-image-thumbnails' with {
                                                media: (isProduct or isMappedToProductMedia ? mediaItem.media : mediaItem),
                                                sizes: {
                                                    'default': '200px'
                                                }
                                            } %}
                                        {% endif %}

                                        {% block solid_pv_gallery_slider_thumbnail_icon %}
                                            {% if (isProduct or isMappedToProductMedia) and ((mediaItem.source and mediaItem.videoId) || mediaItem.media.mimeType | split('/')[0] == 'video') %}
                                                <div class="solid-pv-icon">
                                                    {% sw_icon 'multicolor-action-play' style {
                                                        'namespace': 'SolidProductVideos'
                                                    } %}
                                                </div>
                                            {% endif %}
                                        {% endblock %}
                                    {% endblock %}
                                {% endif %}
                            {% endblock %}
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}
        {% endfor %}
    {% endblock %}
{% endblock %}

{% block element_image_gallery_inner_zoom_modal_slider_items %}
    {% block solid_pv_element_image_gallery_inner_zoom_modal_slider_items %}
        {% set productVideosConfig = config('SolidProductVideos.config') %}
        {% set isMappedToProductMedia = element.config.sliderItems.source == 'mapped' and element.config.sliderItems.value == 'product.media' %}
        {% set combinedProductMedia = page.product.extensions.solidPvCombinedMedia.get('combinedMedia') %}
        {% set mediaItems = combinedProductMedia ? (isProduct or isMappedToProductMedia ? combinedProductMedia : mediaItems) : mediaItems %}

        {% for mediaItem in mediaItems %}
            {% block element_image_gallery_inner_zoom_modal_slider_item %}
                {% if (isProduct or isMappedToProductMedia) and ((mediaItem.source and mediaItem.videoId) or mediaItem.media.mimeType | split('/')[0] == 'video') %}
                    {% set isVideo = true %}
                {% endif %}

                <div class="gallery-slider-item {% if isVideo %}is-solid-pv-video{% endif %}">
                    {% block element_image_gallery_inner_zoom_modal_slider_item_zoom_container %}
                        {% block element_image_gallery_inner_zoom_modal_slider_item_image %}
                            {% block solid_pv_gallery_slider_zoom_modal_image_conditions %}
                                {% if (isProduct or isMappedToProductMedia) and (mediaItem.source and mediaItem.videoId) %}
                                    {% if productVideosConfig.forceUserConsent %}
                                        {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-content-blocker.html.twig' with {
                                            source: mediaItem.source,
                                            videoId: mediaItem.videoId,
                                            poster: mediaItem.thumbnailMedia,
                                        } %}
                                    {% else %}
                                        {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-' ~ mediaItem.source ~ '.html.twig' with {
                                            videoId: mediaItem.videoId,
                                            poster: mediaItem.thumbnailMedia,
                                        } %}
                                    {% endif %}
                                {% elseif (isProduct or isMappedToProductMedia) and mediaItem.media.mimeType | split('/')[0] == 'video' %}
                                    {% sw_include '@SolidProductVideos/storefront/element/cms-element-image-gallery/image-gallery-components/cms-element-image-gallery-video.html.twig' %}
                                {% else %}
                                    <div class="image-zoom-container" data-image-zoom="true">
                                        {% block solid_pv_gallery_slider_zoom_modal_image_image %}
                                            {% sw_thumbnails 'gallery-slider-image-thumbnails' with {
                                                media: (isProduct or isMappedToProductMedia ? mediaItem.media : mediaItem),
                                                attributes: {
                                                    'class': 'gallery-slider-image js-image-zoom-element js-load-img',
                                                    'alt': (image.translated.alt ?: fallbackImageTitle),
                                                    'title': (image.translated.title ?: fallbackImageTitle)
                                                },
                                                load: false,
                                                loadOriginalImage: true,
                                                autoColumnSizes: false
                                            } %}
                                        {% endblock %}
                                    </div>
                                {% endif %}
                            {% endblock %}
                        {% endblock %}
                    {% endblock %}
                </div>
            {% endblock %}
        {% endfor %}
    {% endblock %}
{% endblock %}

{% block element_image_gallery_inner_zoom_modal_thumbnails_items %}
    {% block solid_pv_element_image_gallery_inner_zoom_modal_thumbnails_items %}
        {% set productVideosConfig = config('SolidProductVideos.config') %}
        {% set isMappedToProductMedia = element.config.sliderItems.source == 'mapped' and element.config.sliderItems.value == 'product.media' %}
        {% set combinedProductMedia = page.product.extensions.solidPvCombinedMedia.get('combinedMedia') %}
        {% set mediaItems = combinedProductMedia ? (isProduct or isMappedToProductMedia ? combinedProductMedia : mediaItems) : mediaItems %}

        {% for mediaItem in mediaItems %}
            {% block element_image_gallery_inner_zoom_modal_thumbnails_item %}
                {% if (isProduct or isMappedToProductMedia) and ((mediaItem.source and mediaItem.videoId) or mediaItem.media.mimeType | split('/')[0] == 'video') %}
                    {% set isVideo = true %}
                {% endif %}

                <div class="gallery-slider-thumbnails-item {% if isVideo %}is-solid-pv-video{% endif %}">
                    {% block element_image_gallery_inner_zoom_modal_thumbnails_item_inner %}
                        <div class="gallery-slider-thumbnails-item-inner">
                            {% block solid_pv_gallery_slider_zoom_modal_thumbnail_conditions %}
                                {% if (isProduct or isMappedToProductMedia) and (mediaItem.media.mimeType | split('/')[0] == 'video' and not mediaItem.extensions.solidPvVideoThumbnail) %}
                                    {% block solid_pv_gallery_slider_zoom_modal_thumbnail_video %}
                                        <div class="solid-pv-item">
                                            <video preload="none">
                                                <source src="{{ mediaItem.media.url }}" type="{{ mediaItem.media.mimeType }}">
                                            </video>

                                            <div class="solid-pv-icon">
                                                {% sw_icon 'multicolor-action-play' style {
                                                    'namespace': 'SolidProductVideos'
                                                } %}
                                            </div>
                                        </div>
                                    {% endblock %}
                                {% else %}
                                    {% block solid_pv_gallery_slider_zoom_modal_thumbnail_image %}
                                        {% if mediaItem.source and mediaItem.videoId %}
                                            {% if mediaItem.thumbnailMedia %}
                                                {% sw_thumbnails 'gallery-slider-thumbnails-image-thumbnails' with {
                                                    media: mediaItem.thumbnailMedia,
                                                    sizes: {
                                                        'default': '200px'
                                                    },
                                                    attributes: {
                                                        'class': 'gallery-slider-thumbnails-image',
                                                        'alt': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.alt ?: fallbackImageTitle) : (mediaItem.translated.alt ?: fallbackImageTitle)),
                                                        'title': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.title ?: fallbackImageTitle) : (mediaItem.translated.title ?: fallbackImageTitle))
                                                    }
                                                } %}
                                            {% else %}
                                                <img
                                                    class="gallery-slider-thumbnails-image"
                                                    src="{{ solidPvGetProductEmbeddedVideoMediaThumbnailUrl(mediaItem) }}"
                                                    alt="{{ page.product.translated.name }}"
                                                    title="{{ page.product.translated.name }}"
                                                >
                                            {% endif %}
                                        {% elseif mediaItem.extensions.solidPvVideoThumbnail %}
                                            {% sw_thumbnails 'gallery-slider-thumbnails-image-thumbnails' with {
                                                media: mediaItem.extensions.solidPvVideoThumbnail.media,
                                                sizes: {
                                                    'default': '200px'
                                                },
                                                attributes: {
                                                    'class': 'gallery-slider-thumbnails-image',
                                                    'alt': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.alt ?: fallbackImageTitle) : (mediaItem.translated.alt ?: fallbackImageTitle)),
                                                    'title': (isProduct or isMappedToProductMedia ? (mediaItem.media.translated.title ?: fallbackImageTitle) : (mediaItem.translated.title ?: fallbackImageTitle))
                                                }
                                            } %}
                                        {% else %}
                                            {% sw_thumbnails 'gallery-slider-thumbnails-image-thumbnails' with {
                                                media: (isProduct or isMappedToProductMedia ? mediaItem.media : mediaItem),
                                                sizes: {
                                                    'default': '200px'
                                                },
                                                attributes: {
                                                    'class': 'gallery-slider-thumbnails-image js-load-img',
                                                    'alt': (image.translated.alt ?: fallbackImageTitle),
                                                    'title': (image.translated.title ?: fallbackImageTitle)
                                                },
                                                load: false
                                            } %}
                                        {% endif %}

                                        {% block solid_pv_gallery_slider_zoom_modal_thumbnail_icon %}
                                            {% if (isProduct or isMappedToProductMedia) and ((mediaItem.source and mediaItem.videoId) || mediaItem.media.mimeType | split('/')[0] == 'video') %}
                                                <div class="solid-pv-icon">
                                                    {% sw_icon 'multicolor-action-play' style {
                                                        'namespace': 'SolidProductVideos'
                                                    } %}
                                                </div>
                                            {% endif %}
                                        {% endblock %}
                                    {% endblock %}
                                {% endif %}
                            {% endblock %}
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}
        {% endfor %}
    {% endblock %}
{% endblock %}
