{"name": "huebert/huebertaccessoriesdirectly-plugin", "description": "Show accessories directly in the article, select quantity and with a click on the shopping cart button, put everything together in the shopping cart.", "type": "shopware-platform-plugin", "license": "proprietary", "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://huebert-webentwicklung.de"}], "version": "2.1.2", "autoload": {"psr-4": {"Huebert\\AccessoriesDirectly\\": "src/"}}, "extra": {"shopware-plugin-class": "Huebert\\AccessoriesDirectly\\HuebertAccessoriesDirectly", "label": {"de-DE": "Zubehör im Artikel direkt in den Warenkorb", "en-GB": "Accessories in the article directly into the shopping cart"}, "description": {"de-DE": "Zubehör direkt im Artikel anzeigen. Menge auswählen und mit einem Klick auf den Warenkorb-<PERSON><PERSON> alles in Warenkorb legen.", "en-GB": "Show accessories directly in the article, select quantity and with a click on the shopping cart button, put everything together in the shopping cart."}, "manufacturerLink": {"de-DE": "https://www.huebert-webentwicklung.de", "en-GB": "https://www.huebert-webentwicklung.de"}, "supportLink": {"de-DE": "https://www.huebert-webentwicklung.de/kontakt", "en-GB": "https://www.huebert-webentwicklung.de/kontakt"}}, "require": {"php": ">= 8.1", "shopware/core": "6.5.*"}, "require-dev": {"shopware/storefront": "*"}}