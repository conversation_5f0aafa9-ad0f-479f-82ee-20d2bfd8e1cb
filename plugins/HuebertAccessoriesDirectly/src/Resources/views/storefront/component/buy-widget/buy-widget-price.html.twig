{% sw_extends "@Storefront/storefront/component/buy-widget/buy-widget-price.html.twig" %}

{% block buy_widget_price_content %}
    <input type="hidden" data-hueb-price="{{ price.unitPrice }}">
    <input type="hidden" data-hueb-price-currency="{{ context.currency.translated.shortName }}"/>
    {% set listPrice = price.listPrice %}
    {% set isRegulationPrice = price.regulationPrice != null %}

    {% if page.HuebertAccessoriesDirectlyBundling %}
        {% sw_include '@HuebertAccessoriesDirectlyBundling/storefront/page/product-detail/hueb-bundle-main-price.html.twig' ignore missing %}
    {% else %}
        <div class="d-flex" style="align-items: flex-end;">
        <p class="product-detail-price{% if listPrice.percentage > 0 %} with-list-price{% endif %}">
            {{ price.unitPrice|currency }}{{ "general.star"|trans|sw_sanitize }}
        </p>


            {% if isRegulationPrice %}
                <div class="product-detail-list-price-wrapper">
                    <span class="regulation-price">&nbsp; {{ "general.listPricePreviously"|trans({'%price%': price.regulationPrice.price|currency }) }}{{ "general.star"|trans|sw_sanitize }}</span>
                </div>
            {% endif %}
        </div>
    {% endif %}

    {% if listPrice.percentage > 0 %}
        {% block buy_widget_was_price %}
            {{ parent() }}
        {% endblock %}
    {% endif %}

    {% set huebCrossSelling, emptyCrossSelling = null, true %}
    {% for sections in cmsPage.sections %}
        {% for blocks in sections.blocks %}
            {% if blocks.type == "cross-selling" and blocks.slots.elements|first.data.crossSellings == not null %}
                {% do page.setCrossSellings(blocks.slots.elements|first.data.crossSellings) %}
                {% set emptyCrossSelling = false %}
            {% endif %}
        {% endfor %}
    {% endfor %}

    {% block hueb_accessories_total_price_widget %}
        {% if not emptyCrossSelling %}
            {% if page.crossSellings.elements|length > 0 and page.HuebertAccessoriesDirectly.config.accessoryTotalPrice == "belowPrice" %}
                {% for crossSelling in page.crossSellings.elements %}{% if not break %}
                    {% if crossSelling.crossSelling.display == "everywhere" or crossSelling.crossSelling.display == "pluginOnly" or not crossSelling.crossSelling.display %}
                        <div class="hueb-accessories_total-wrapper">
                            <p class="hueb-accessories_total">
                                {{ "huebert-acessories-directly.product-detail.accessory-total-price"|trans|sw_sanitize }}
                                <span class="hueb-accessories_price"><span
                                            id="hueb-accessories_total-price">{{ price.unitPrice|currency }}</span>{{ "general.star"|trans|sw_sanitize }}
                                </span>
                            </p>
                        </div>
                        {% set break = true %}
                    {% endif %}
                {% endif %}{% endfor %}
            {% endif %}
        {% endif %}
        {% sw_include '@HuebertAccessoriesDirectlyBundling/storefront/page/product-detail/hueb-bundle-extra-price.html.twig' ignore missing %}
    {% endblock %}

{% endblock %}

{% block buy_widget_price_block_table_body_cell_quantity %}
    {{ parent() }}
    <td style="display: none;">
        <input type="hidden" data-hueb-price="{{ price.unitPrice }}"/>
        <input type="hidden" data-hueb-price-currency="{{ context.currency.translated.shortName }}"/>
    </td>
{% endblock %}
