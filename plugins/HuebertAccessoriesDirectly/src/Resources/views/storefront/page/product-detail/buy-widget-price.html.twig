{% sw_extends "@Storefront/storefront/page/product-detail/buy-widget-price.html.twig" %}

{% block page_product_detail_price_content %}
    {# added from other plugin #}
    {% if page.product.translated.customFields.deposittype %}
        <div class="product-detail-deposittype">
            {{ page.product.translated.customFields.deposittype }}
        </div>
    {% endif %}

    <input type="hidden" data-hueb-price="{{ price.unitPrice }}">
    <input type="hidden" data-hueb-price-currency="{{ context.currency.translated.shortName }}"/>
    {% set isRegulationPrice = price.regulationPrice != null %}
    <div>

        {% set listPrice = price.listPrice %}
        {% if page.crossSellings|length > 0 and page.HuebertAccessoriesDirectlyBundling %}
            {% sw_include '@HuebertAccessoriesDirectlyBundling/storefront/page/product-detail/hueb-bundle-main-price.html.twig' ignore missing %}
        {% else %}
            <p class="product-detail-price{% if listPrice.percentage > 0 %} with-list-price{% endif %}">
                {{ price.unitPrice|currency }}{{ "general.star"|trans|sw_sanitize }}
            </p>


        {% endif %}

        {% if listPrice.percentage > 0 %}
            {% block page_product_detail_was_price %}
                {{ parent() }}
            {% endblock %}
        {% endif %}

        {% block hueb_accessories_total_price %}
            {% if page.crossSellings.elements|length > 0 and page.HuebertAccessoriesDirectly.config.accessoryTotalPrice == "belowPrice" %}
                {% for crossSelling in page.crossSellings.elements %}{% if not break %}
                    {% if crossSelling.crossSelling.display == "everywhere" or crossSelling.crossSelling.display == "pluginOnly" or not crossSelling.crossSelling.display %}
                        <div class="hueb-accessories_total-wrapper">
                            <p class="hueb-accessories_total">
                                {{ "huebert-acessories-directly.product-detail.accessory-total-price"|trans|sw_sanitize }}
                                <span class="hueb-accessories_price"><span
                                            id="hueb-accessories_total-price">{{ price.unitPrice|currency }}</span>{{ "general.star"|trans|sw_sanitize }}
                                </span>
                            </p>
                        </div>
                        {% set break = true %}
                    {% endif %}
                {% endif %}{% endfor %}
            {% endif %}
            {% if page.crossSellings|length > 0 and page.HuebertAccessoriesDirectlyBundling %}
                {% sw_include '@HuebertAccessoriesDirectlyBundling/storefront/page/product-detail/hueb-bundle-extra-price.html.twig' ignore missing %}
            {% endif %}
        {% endblock %}

        {% if isRegulationPrice %}
            <div class="product-detail-list-price-wrapper">
                <span class="regulation-price"> &nbsp;{{ "general.listPricePreviously"|trans({'%price%': price.regulationPrice.price|currency }) }}{{ "general.star"|trans|sw_sanitize }}</span>
            </div>
        {% endif %}

        {# added from other plugin #}
        {% if page.product.translated.customFields.deposit %}
            {% set deposit = page.product.translated.customFields.deposit|currency %}

{#            <div class="product-detail-deposit">#}
{#                {{ "FlowsiteDepositSystemBasic.plusDeposit"|trans({#}
{#                    '%deposit%': deposit#}
{#                })|sw_sanitize }}#}
{#            </div>#}
        {% endif %}
    </div>
{% endblock %}



{% block page_product_detail_price_block_table_body_cell_quantity %}
    {{ parent() }}
    <td style="display: none;">
        <input type="hidden" data-hueb-price="{{ price.unitPrice }}"/>
        <input type="hidden" data-hueb-price-currency="{{ context.currency.translated.shortName }}"/>
    </td>
{% endblock %}
