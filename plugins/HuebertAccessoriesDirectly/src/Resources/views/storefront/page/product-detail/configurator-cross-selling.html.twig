{% block page_product_detail_configurator %}
    <div  class="product-detail-configurato varinats-container" data-variant="true">
        {% block page_product_detail_configurator_form %}
            <form class="huebert-variant-configurator">
                <input type="hidden" value="{{page.header.activeLanguage.name }}" class="current-language"/>
                <input type="hidden" value="{{product.availableStock }}" class="current-availableStock"/>
                <input type="hidden" value="{{product.minPurchase }}" class="current-minPurchase"/>
                <input type="hidden" value="{{product.deliveryTime.id }}" class="current-deliveryTime"/>
                <input type="hidden" value="{{product.isCloseout }}" class="current-isCloseout"/>
                <input type="hidden" value="{{product.restockTime }}" class="current-restockTime"/>

                {%if page.HuebertAccessoriesDirectly.config.accessoryViewStatic === false%}
                                {%set class = 'without-image'%}
                {%endif%}

                {% if selectableAmount == 0 %}
                    {% set viewClass = 30 %}
                    {%set class = 'without-image'%}
                {% endif %}


                        <div class="variant-price {{class}}" style="width: {{ viewClass }}%">
                <div  class="product-price huebert-accessory_pricing variant-price-pricing">
                    {{ "huebert-acessories-directly.product-detail.accessory-price"|trans(
                        {'%price%': pricing}
                    )|sw_sanitize }}
                </div>
                            {# added from other plugin #}
                            {% if product.translated.customFields.deposit %}
                                {% set deposit = product.translated.customFields.deposit|currency %}

                                <div class="product-detail-deposit">
                                    {{ "FlowsiteDepositSystemBasic.plusDeposit"|trans({
                                        '%deposit%': deposit
                                    })|sw_sanitize }}
                                </div>
                            {% endif %}
                            {% set referencePricing = null %}

                            {% if product.calculatedPrices|length > 1 %}
                                {% set priceFrom = product.calculatedPrices|last %}
                                {% if priceFrom.referencePrice.price  %}
                                    {% set referencePricing %}
                                        ({{ priceFrom.referencePrice.price|currency }}
                                        -
                                        {{ priceTo.referencePrice.price|currency }}{{ "general.star"|trans|sw_sanitize }} / {{ priceFrom.referencePrice.referenceUnit }} {{ priceFrom.referencePrice.unitName }})
                                    {% endset %}
                                {% endif %}
                            {% elseif product.calculatedPrices|length == 1 %}
                                {% if product.calculatedPrices.first.referencePrice.price %}
                                    {% set referencePricing %}
                                        ({{ product.calculatedPrices.first.referencePrice.price|currency }}{{ "general.star"|trans|sw_sanitize }} / {{ product.calculatedPrices.first.referencePrice.referenceUnit }} {{ product.calculatedPrices.first.referencePrice.unitName }})
                                    {% endset %}
                                {% endif %}
                            {% else %}
                                {% if product.calculatedPrice.referencePrice.price %}
                                    {% set referencePricing %}
                                        ({{ product.calculatedPrice.referencePrice.price|currency }}{{ "general.star"|trans|sw_sanitize }} / {{ product.calculatedPrice.referencePrice.referenceUnit }} {{ product.calculatedPrice.referencePrice.unitName }})
                                    {% endset %}
                                {% endif %}
                            {% endif %}

                            {% if page.acessoryOptions.showUnits%}
                                <div class="product-detail-price-unit">
                            <span style="display: none" class="price-unit-label  huebert-unit-label">
                                        {{ "detail.priceUnitName"|trans|sw_sanitize }}
                                    </span>

                                    <span style="display: none" class="price-unit-content huebert-unit">
                                    {{ product.purchaseUnit }} {% if product.unit.name %}{{ product.unit.name }}{% endif%} {% if referencePricing %}/ {{ referencePricing }}{% endif %}
                                    </span>
                                </div>

                                {% if product.purchaseUnit %}
                                    {% block buy_widget_price_unit %}
                                        <div class="product-detail-price-unit huebert-unit-label">
                                            {% block buy_widget_price_unit_label %}
                                                <span class="price-unit-label">
                                        {{ "detail.priceUnitName"|trans|sw_sanitize }}
                                    </span>
                                            {% endblock %}

                                            {% block buy_widget_price_unit_content %}
                                                <span class="price-unit-content huebert-unit">
                                    {{ product.purchaseUnit }} {% if product.unit.name %}{{ product.unit.name }}{% endif%} {% if referencePricing %}{{ referencePricing }}{% endif %}
                                    </span>
                                            {% endblock %}
                                        </div>
                                    {% endblock %}
                                {% endif %}

                            {% endif %}
                        </div>





                {% block page_product_detail_configurator_groups %}
                    {% for group in product.productConfigurator %}
                        {% set counter = loop.index %}
                        {% block page_product_detail_configurator_group %}
                            <div class="product-detail-configurator-group">
                                {% if group.displayType == 'select' %}
                                    {% sw_include '@Storefront/storefront/page/product-detail/configurator/select.html.twig' %}
                                {% else %}
                                    {% block page_product_detail_configurator_group_title %}
                                        <div class="product-detail-configurator-group-title">
                                            {% block page_product_detail_configurator_group_title_text %}
                                                {{ group.translated.name }}
                                            {% endblock %}
                                        </div>
                                    {% endblock %}

                                    {% block page_product_detail_configurator_options %}
                                        <div class="product-detail-configurator-options">
                                            {% for option in group.options %}

                                                {% set optionIdentifier = [group.id, option.id]|join('-') %}
                                                {% set isActive = false %}
                                                {% set isCombinableCls = 'is-combinable' %}

                                                {% if option.id in product.optionIds %}
                                                    {% set isActive = true %}
                                                {% endif %}

                                                {% if not option.combinable %}
                                                    {% set isCombinableCls = false %}
                                                {% endif %}

                                                {% if option.configuratorSetting.media %}
                                                    {% set displayType = 'media' %}
                                                    {% set media = option.configuratorSetting.media %}
                                                {% else %}
                                                    {% set displayType = group.displayType %}
                                                    {% if option.media %}
                                                        {% set media = option.media %}
                                                    {% else %}
                                                        {% set media = false %}
                                                    {% endif %}
                                                {% endif %}

                                                {% block page_product_detail_configurator_option %}
                                                    <div class="product-detail-configurator-option">
                                                        {% block page_product_detail_configurator_option_radio %}
                                                            <input type="radio"
                                                                   name="{{ group.id }}--{{ product.parentId }}"
                                                                   value="{{ option.id }}"
                                                                   class="product-detail-configurator-option-input {% if isCombinableCls %} {{ isCombinableCls }}{% endif %} huebert-variant-option"
                                                                   title="{{ optionIdentifier }}"
                                                                   id="huebert-{{ counter }}-{{ optionIdentifier }}__{{ loop.index }}--{{ product.id }}"
                                                                   {% if isActive %}checked="checked"{% endif %}
                                                            >


                                                            {% block page_product_detail_configurator_option_radio_label %}
                                                                <label class="product-detail-configurator-option-label{% if isCombinableCls %} {{ isCombinableCls }}{% endif %} is-display-{{ displayType }}"
                                                                    {% if displayType == 'color' and option.colorHexCode %}
                                                                        style="background-color: {{ option.colorHexCode }}"
                                                                    {% endif %}
                                                                       title="{{ option.translated.name }}"
                                                                       for="huebert-{{ counter }}-{{ optionIdentifier }}__{{ loop.index }}--{{ product.id }}">

                                                                    {% if displayType == 'media' and media %}
                                                                        {% block page_product_detail_configurator_option_radio_label_media %}
                                                                            {% sw_thumbnails 'configurator-option-img-thumbnails' with {
                                                                                media: media,
                                                                                sizes: {
                                                                                    'default': '52px'
                                                                                },
                                                                                attributes: {
                                                                                    'class': 'product-detail-configurator-option-image',
                                                                                    'alt': option.translated.name,
                                                                                    'title': option.translated.name
                                                                                }
                                                                            } %}
                                                                        {% endblock %}
                                                                    {% elseif displayType == 'text' or
                                                                        (displayType == 'media' and not media) or
                                                                        (displayType == 'color' and not option.colorHexCode) %}
                                                                        {% block page_product_detail_configurator_option_radio_label_text %}
                                                                            {{ option.translated.name }}
                                                                        {% endblock %}
                                                                    {% endif %}
                                                                </label>
                                                            {% endblock %}
                                                        {% endblock %}
                                                    </div>
                                                {% endblock %}
                                            {% endfor %}
                                        </div>
                                    {% endblock %}
                                {% endif %}
                            </div>
                        {% endblock %}
                    {% endfor %}
                {% endblock %}
                <input type="hidden" class="generated-variant"value="">
               {%if product.availableStock > product.minPurchase%}
                {%set stock = "false"%}
                   {%else%}
                   {%set stock = "true"%}
                {%endif%}
                <input type="hidden" class="stock-huebert" value="{{ stock }}">
                <input type="hidden" class="display-delivery" value="{{ page.HuebertAccessoriesDirectly.config.showDelivery }}">
                {% if page.HuebertAccessoriesDirectly.config.showDelivery%}
                    <div  class="huebert-acessory_deliveryTime">
                        <p style="display: none" class="delivery-information  first-d">
                            <span class="delivery-status-indicator bg-success"></span>
                            {{ "detail.deliveryTimeAvailable"|trans({
                                '%name%': product.deliveryTime.translation('name')
                            })|sw_sanitize }}
                        </p>
                        <p style="display: none" class="delivery-information second-d">
                            <span class="delivery-status-indicator bg-danger"></span>
                            {{ "detail.soldOut"|trans|sw_sanitize }}
                        </p>
                        <p style="display: none" class="delivery-information third-d">
                            <span class="delivery-status-indicator bg-warning"></span>
                            {{ "detail.deliveryTimeRestock"|trans({
                                '%count%': product.restockTime,
                                '%restockTime%': product.restockTime,
                                '%name%': product.deliveryTime.translation('name')
                            })|sw_sanitize }}
                        </p>
                    </div>

                    {% else %}

                        <p class="delivery-information  first-d"></p>

                        <p class=" second-d"></p>
                        <p class="third-d"></p>
                {%endif%}





            </form>
        {% endblock %}
    </div>
{% endblock %}
