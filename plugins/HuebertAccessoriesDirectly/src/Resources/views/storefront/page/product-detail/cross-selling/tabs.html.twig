{% sw_extends "@Storefront/storefront/page/product-detail/cross-selling/tabs.html.twig" %}

{% block page_product_detail_cross_selling_tabs_inner %}
    {% set counter = 0 %}
    {% for item in crossSellings %}
        {% if item.crossSelling.active and item.getProducts().elements and (item.crossSelling.display == "everywhere" or item.crossSelling.display == "sliderOnly" or not item.crossSelling.display) %}
            {% set counter = counter + 1 %}
        {% endif %}
    {% endfor %}

    {% if counter > 0 %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{% block page_product_detail_cross_selling_tabs_navigation_container %}
    <ul class="nav nav-tabs product-detail-tab-navigation-list"
        id="product-detail-cross-selling-tabs"
        role="tablist">
        {% for item in crossSellings %}
            {% set id = item.crossSelling.id %}
            {% if item.crossSelling.active and item.getProducts().elements and (item.crossSelling.display == "everywhere" or item.crossSelling.display == "sliderOnly" or not item.crossSelling.display) %}
                <li class="nav-item">
                    <a class="nav-link product-detail-tab-navigation-link{% if not isActiveTab and (item.crossSelling.display == "everywhere" or item.crossSelling.display == "sliderOnly" or not item.crossSelling.display) %} active{% set isActiveTab = true %}{% endif %}"
                       id="cs-{{ id }}-tab"
                       data-bs-toggle="tab"
                       href="#cs-{{ id }}-tab-pane"
                       role="tab"
                       aria-controls="cs-{{ id }}-tab-pane"
                       aria-selected="true">
                        {{ item.crossSelling.translated.name }}
                        <span class="product-detail-tab-navigation-icon">
                            {% sw_icon 'arrow-medium-right' style {'pack':'solid'} %}
                        </span>
                    </a>
                </li>
            {% endif %}
        {% endfor %}
    </ul>
{% endblock %}

{% block page_product_detail_cross_selling_tabs_content_container %}
    <div class="tab-content">
        {% for item in crossSellings %}
            {% set id = item.crossSelling.id %}
            {% if item.crossSelling.active and item.getProducts().elements and (item.crossSelling.display == "everywhere" or item.crossSelling.display == "sliderOnly" or not item.crossSelling.display) %}
                <div class="tab-pane fade show{% if not isActiveContent and (item.crossSelling.display == "everywhere" or item.crossSelling.display == "sliderOnly" or not item.crossSelling.display) %} active{% set isActiveContent = true %}{% endif %}"
                     id="cs-{{ id }}-tab-pane"
                     role="tabpanel"
                     aria-labelledby="cs-{{ id }}-tab">
                    {% set config = {
                        'title': {
                            'value': item.crossSelling.name
                        },
                        'border': {
                            'value': false
                        },
                        'rotate': {
                            'value': false
                        },
                        'products': {
                            'value': item.getProducts()
                        },
                        'boxLayout': {
                            'value': 'standard'
                        },
                        'elMinWidth': {
                            'value': '300px'
                        },
                        'navigation': {
                            'value': true
                        },
                        'displayMode': {
                            'value': 'minimal'
                        },
                        'verticalAlign': {
                            'value': 'top'
                        },
                    } %}

                    {% sw_include "@Storefront/storefront/element/cms-element-product-slider.html.twig" with {
                        sliderConfig: config,
                        element: {
                            'data': {
                                'products': {
                                    elements: item.getProducts()
                                }
                            },
                            type: 'product-slider'
                        }
                    } only %}
                </div>
            {% endif %}
        {% endfor %}
    </div>
{% endblock %}
