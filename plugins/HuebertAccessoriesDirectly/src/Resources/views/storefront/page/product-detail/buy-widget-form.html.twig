{% sw_extends "@Storefront/storefront/page/product-detail/buy-widget-form.html.twig" %}

{% block page_product_detail_buy_form_inner %}
    {% if page.acessoryOptions.accessoryPosition == "below" %}
        {% block hueb_accessories_total_price_above_buy_button %}
            {% if page.crossSellings.elements|length > 0 and page.HuebertAccessoriesDirectly.config.accessoryTotalPrice == "aboveBuyButton" and (page.product.calculatedListingPrice.from.unitPrice == page.product.calculatedListingPrice.to.unitPrice)%}
                {% for crossSelling in page.crossSellings.elements %}{% if not break %}
                    {% if crossSelling.crossSelling.display == "everywhere" or crossSelling.crossSelling.display == "pluginOnly" or not crossSelling.crossSelling.display %}
                        {% if page.product.calculatedPrices|length == 1 %}
                            {% set initPrice = page.product.calculatedPrices.first.unitPrice|currency %}
                        {% else %}
                            {% set initPrice = page.product.calculatedPrice.unitPrice|currency %}
                        {% endif %}
                        <div class="hueb-accessories_total-wrapper">
                            <div class="hueb-accessories_total">
                                {{ "huebert-acessories-directly.product-detail.accessory-total-price"|trans|sw_sanitize }}
                            </div>
                            <p class="hueb-accessories_price">
                                <span id="hueb-accessories_total-price">{{ initPrice }} </span>{{ "general.star"|trans|sw_sanitize }}
                            </p>
                        </div>
                        {% set break = true %}
                    {% endif %}
                {% endif %}{% endfor %}
            {% endif %}
        {% endblock %}
        {{ parent() }}
    {% endif %}

    {% block hueb_acessories_directly %}
        {% sw_include '@HuebertAccessoriesDirectly/storefront/page/product-detail/hueb-accessories-accordion.html.twig' %}
    {% endblock %}

    {% if page.acessoryOptions.accessoryPosition == "above" or not page.acessoryOptions.accessoryPosition %}
        {{ block('hueb_accessories_total_price_above_buy_button') }}
        {{ parent() }}
    {% endif %}
{% endblock %}
