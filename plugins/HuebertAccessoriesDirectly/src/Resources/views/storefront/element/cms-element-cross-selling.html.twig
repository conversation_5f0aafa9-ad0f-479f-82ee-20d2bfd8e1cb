{% sw_extends "@Storefront/storefront/element/cms-element-cross-selling.html.twig" %}

{% block cms_element_cross_selling_tabs_inner %}
    {% set counter = 0 %}
    {% for item in element.data.crossSellings.elements %}
        {% if item.crossSelling.active and item.getProducts().elements and (item.crossSelling.display == "everywhere" or item.crossSelling.display == "sliderOnly" or not item.crossSelling.display) %}
            {% set counter = counter + 1 %}
        {% endif %}
    {% endfor %}

    {% if counter > 0 %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{% block cms_element_cross_selling_tabs_navigation_container %}
    <ul class="nav nav-tabs product-detail-tab-navigation-list"
        id="product-detail-cross-selling-tabs"
        role="tablist">
        {% for item in element.data.crossSellings.elements %}
            {% set crossSelling = item.crossSelling %}
            {% set products = item.products %}
            {% set id = crossSelling.id %}
            {% if crossSelling.active and products and (crossSelling.display == "everywhere" or crossSelling.display == "sliderOnly" or not crossSelling.display) %}
                <li class="nav-item">
                    <a class="nav-link product-detail-tab-navigation-link{% if not isActiveTab and (crossSelling.display == "everywhere" or crossSelling.display == "sliderOnly" or not crossSelling.display) %} active{% set isActiveTab = true %}{% endif %}"
                       id="cross-selling-{{ id }}tab"
                       data-toggle="tab"
                       href="#cross-selling-{{ id }}tab-pane"
                       role="tab"
                       aria-controls="cross-selling-{{ id }}tab-pane"
                       aria-selected="true">
                        {{ crossSelling.translated.name }}
                        <span class="product-detail-tab-navigation-icon">
                            {% sw_icon 'arrow-medium-right' style {'pack':'solid'} %}
                        </span>
                    </a>
                </li>
            {% endif %}
        {% endfor %}
    </ul>
{% endblock %}

{% block cms_element_cross_selling_tabs_content_container %}
    <div class="tab-content">
        {% for item in element.data.crossSellings.elements %}
            {% set crossSelling = item.crossSelling %}
            {% set products = item.products %}
            {% set id = crossSelling.id %}
            {% if crossSelling.active and products and (crossSelling.display == "everywhere" or crossSelling.display == "sliderOnly" or not crossSelling.display) %}
                <div class="tab-pane fade show{% if not isActiveContent and (crossSelling.display == "everywhere" or crossSelling.display == "sliderOnly" or not crossSelling.display) %} active{% set isActiveContent = true %}{% endif %}"
                     id="cross-selling-{{ id }}tab-pane"
                     role="tabpanel"
                     aria-labelledby="cross-selling-{{ id }}tab">
                    {% set config = {
                        'title': {
                            'value': null
                        },
                        'border': {
                            'value': false
                        },
                        'rotate': {
                            'value': false
                        },
                        'products': {
                            'value': products
                        },
                        'boxLayout': {
                            'value': sliderConfig.boxLayout.value
                        },
                        'elMinWidth': {
                            'value': sliderConfig.elMinWidth.value
                        },
                        'navigation': {
                            'value': true
                        },
                        'displayMode': {
                            'value': sliderConfig.displayMode.value
                        },
                        'verticalAlign': {
                            'value': center
                        }
                    } %}

                    {% sw_include "@Storefront/storefront/element/cms-element-product-slider.html.twig" with {
                        sliderConfig: config,
                        element: {
                            'data': {
                                'products': products
                            },
                            type: 'product-slider'
                        }
                    } only %}
                </div>
            {% endif %}
        {% endfor %}
    </div>
{% endblock %}
