!function(e){var n={};function t(s){if(n[s])return n[s].exports;var o=n[s]={i:s,l:!1,exports:{}};return e[s].call(o.exports,o,o.exports,t),o.l=!0,o.exports}t.m=e,t.c=n,t.d=function(e,n,s){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:s})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var s=Object.create(null);if(t.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)t.d(s,o,function(n){return e[n]}.bind(null,o));return s},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p=(window.__sw__.assetPath + '/bundles/huebertaccessoriesdirectly/'),t(t.s="83Dy")}({"83Dy":function(e,n,t){"use strict";t.r(n);t("r7qe"),Shopware.Data.Criteria;var s=Shopware,o=s.Component,i=(s.Context,o.getComponentHelper());i.mapPropertyErrors,i.mapGetters,i.mapState;o.override("sw-product-cross-selling-form",{template:'{% block sw_prduct_cross_selling_form_product_stream_field %}\n    <sw-container\n            columns="2fr 1fr"\n            gap="0px 30px"\n            align="start"\n    >\n        <sw-select-field\n                v-if="!crossSelling.huebBundle"\n                class="hueb-accessories_show"\n                :label="$tc(\'hueb-acc.product.card.show\')"\n                :value="crossSelling.display ? crossSelling.display : \'sliderOnly\'"\n                @change="crossSelling.display = $event">\n            <option v-for="option in optionsShow"\n                    :value="option.value"\n                    :key="option.value">\n                {{ option.label }}\n            </option>\n        </sw-select-field>\n        <sw-field\n                v-if="!crossSelling.huebBundle"\n                v-model="crossSelling.selectableAmount"\n                type="switch"\n                :label="$tc(\'hueb-acc.product.card.selectableAmount\')"\n                :disabled="!allowEdit"\n        />\n    </sw-container>\n    <sw-entity-single-select\n            :helpText="$tc(\'hueb-acc.product.card.help\')"\n            entity="product_stream"\n            class="sw-select-cross-selling__select-product-stream"\n            id="sw-field--crossSelling-product-group"\n            :label="$tc(\'sw-product.crossselling.inputCrossSellingProductStream\')"\n            :placeholder="$tc(\'sw-product.crossselling.inputCrossSellingProductStreamPlaceholder\')"\n            v-model="crossSelling.productStreamId"\n            :disabled="!allowEdit">\n    </sw-entity-single-select>\n{% endblock %}\n\n{% block sw_product_detail_cross_selling_assignment %}\n    <sw-container\n            columns="2fr 1fr"\n            gap="0px 30px"\n            align="start"\n    >\n        <sw-select-field\n                v-if="useManualAssignment"\n                class="hueb-accessories_show"\n                :label="$tc(\'hueb-acc.product.card.show\')"\n                :value="crossSelling.display ? crossSelling.display : \'sliderOnly\'"\n                @change="crossSelling.display = $event">\n            <option v-for="option in optionsShow"\n                    :value="option.value"\n                    :key="option.value">\n                {{ option.label }}\n            </option>\n        </sw-select-field>\n        <sw-field\n                v-if="useManualAssignment"\n                v-model="crossSelling.selectableAmount"\n                type="switch"\n                :label="$tc(\'hueb-acc.product.card.selectableAmount\')"\n                :disabled="!allowEdit"\n        />\n        <sw-switch-field\n            :label="$tc(\'hueb-acc.product.card.sameQuantityAsMainProduct\')"\n            v-model="crossSelling.sameQuantityAsMainProduct"\n            :helpText="$tc(\'hueb-acc.product.card.sameQuantityHelpText\')">\n        </sw-switch-field>\n    </sw-container>\n    <sw-product-cross-selling-assignment\n            v-if="useManualAssignment"\n            :assignedProducts="crossSelling.assignedProducts"\n            :crossSellingId="crossSelling.id"\n            :searchableFields="[\'name\', \'productNumber\']">\n    </sw-product-cross-selling-assignment>\n{% endblock %}\n',data:function(){return{optionsShow:[{value:"everywhere",label:this.$tc("hueb-acc.product.options.everywhere")},{value:"pluginOnly",label:this.$tc("hueb-acc.product.options.pluginOnly")},{value:"sliderOnly",label:this.$tc("hueb-acc.product.options.sliderOnly")}]}}});var r=Shopware,a=r.Component,l=r.Mixin;a.override("sw-product-cross-selling-assignment",{template:'{% block sw_product_cross_selling_assignment_select %}\n    <sw-entity-single-select\n        :helpText="$tc(\'hueb-acc.product.card.help\')"\n        :label="$tc(\'sw-product.crossselling.inputCrossSellingProductList\')"\n        entity="product"\n        value=""\n        :criteria="searchCriteria"\n        :context="searchContext"\n        disableAutoClose\n        :disabled="!allowEdit"\n        @change="onToggleProduct">\n\n        <template #result-item="{ item, index }">\n            <slot name="result-item" v-bind="{ item, index, isSelected }">\n                <li is="sw-select-result" :selected="isSelected(item)" v-bind="{ item, index }">\n                    {% block sw_entity_single_select_base_results_list_result_label %}\n                        <span class="sw-select-result__result-item-text">\n                                        <sw-product-variant-info :variations="item.variation">\n                                            {{ item.translated.name || item.name }}\n                                        </sw-product-variant-info>\n                                    </span>\n                    {% endblock %}\n                </li>\n            </slot>\n        </template>\n\n    </sw-entity-single-select>\n{% endblock %}\n\n\n{% block sw_product_cross_selling_assignment_option_grid %}\n    <sw-data-grid\n            v-if="total"\n            :key="isLoadingGrid"\n            :data-source="assignedProducts"\n            :is-loading="isLoadingGrid"\n            :columns="assignedProductColumns"\n            :show-settings="true"\n            :show-selection="false"\n    >\n\n        <template #actions="{ item }">\n            <sw-context-menu-item\n                    :disabled="!allowEdit"\n                    variant="danger"\n                    @click="removeItem(item)"\n            >\n                {{ $tc(\'global.default.delete\') }}\n            </sw-context-menu-item>\n            <sw-context-menu-item\n                    :disabled="!allowEdit"\n                    @click="setAccessoryQuantity(item)"\n            >\n                {{ $tc(\'hueb-acc.product.setAccessoryQuantity\') }}\n            </sw-context-menu-item>\n        </template>\n\n        <template #column-product.translated.name="{ item }">\n            \n            {% block sw_data_grid_columns_render_value %}\n                <span>\n                    <sw-product-variant-info :variations="item.product.variation">\n                        {{ item.product.translated.name || item.product.name || variantNames[item.product.id] }}\n                    </sw-product-variant-info>\n                </span>\n            {% endblock %}\n        </template>\n\n        \n        {% block sw_product_cross_selling_assignment_option_grid_column_position %}\n            <template #column-position="{ item }">\n                <sw-data-grid-column-position\n                        ref="columnPosition"\n                        v-model="assignedProducts"\n                        :show-value="true"\n                        :item="item"\n                />\n            </template>\n        {% endblock %}\n    </sw-data-grid>\n\n    <sw-modal\n            class="accessory-qty-modal"\n            v-if="showModal"\n            variant="small"\n            :title="$tc(\'hueb-acc.product.accessoryQuantityTitle\')"\n            @modal-close="closeModal">\n\n        <sw-field\n                type="number"\n                v-model="selectedAccessoryQuantity"\n                @input-change="onQuantityChange"\n                :label="$tc(\'hueb-acc.product.accessoryQuantityLabel\')">\n        </sw-field>\n\n        <sw-button\n                v-if="selectedAssignedProduct"\n                variant="primary"\n                size="small"\n                @click="updateQuantity">\n            {{ $tc(\'hueb-acc.product.saveBtn\') }}\n        </sw-button>\n\n    </sw-modal>\n{% endblock %}',mixins:[l.getByName("notification")],data:function(){return{showModal:!1,selectedAssignedProduct:null}},computed:{assignedProductRepository:function(){return this.repositoryFactory.create("product_cross_selling_assigned_products")},assignedProductColumns:function(){var e=this.$super("assignedProductColumns");return e.push({property:"accessoryQuantity",label:this.$tc("hueb-acc.product.accessoryQuantityLabel"),inlineEdit:"number",allowResize:!0,sortable:!1}),e}},methods:{setAccessoryQuantity:function(e){this.showModal=!0,this.selectedAccessoryQuantity=e.accessoryQuantity,this.selectedAssignedProduct=e},openModal:function(e){e&&(this.showModal=!0)},closeModal:function(){this.showModal=!1},onQuantityChange:function(e){this.selectedAccessoryQuantity=e},updateQuantity:function(){var e=this;console.log("this.selectedAccessoryQuantity",this.selectedAccessoryQuantity),this.isLoadingData=!0,this.selectedAssignedProduct.accessoryQuantity=this.selectedAccessoryQuantity,this.assignedProductRepository.save(this.selectedAssignedProduct).then((function(){e.showModal=!1,e.createNotificationSuccess({title:e.$t("hueb-acc.notification.successTitle"),message:e.$t("hueb-acc.notification.successMessage")}),e.isLoadingData=!1})).catch((function(n){e.showModal=!1,e.isLoadingData=!1}))}}});t("DGHW"),t("Orhe")},DGHW:function(e){e.exports=JSON.parse("{}")},"JR+k":function(e,n,t){},Orhe:function(e){e.exports=JSON.parse("{}")},P8hj:function(e,n,t){"use strict";function s(e,n){for(var t=[],s={},o=0;o<n.length;o++){var i=n[o],r=i[0],a={id:e+":"+o,css:i[1],media:i[2],sourceMap:i[3]};s[r]?s[r].parts.push(a):t.push(s[r]={id:r,parts:[a]})}return t}t.r(n),t.d(n,"default",(function(){return f}));var o="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},r=o&&(document.head||document.getElementsByTagName("head")[0]),a=null,l=0,c=!1,d=function(){},u=null,p="data-vue-ssr-id",m="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function f(e,n,t,o){c=t,u=o||{};var r=s(e,n);return h(r),function(n){for(var t=[],o=0;o<r.length;o++){var a=r[o];(l=i[a.id]).refs--,t.push(l)}n?h(r=s(e,n)):r=[];for(o=0;o<t.length;o++){var l;if(0===(l=t[o]).refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete i[l.id]}}}}function h(e){for(var n=0;n<e.length;n++){var t=e[n],s=i[t.id];if(s){s.refs++;for(var o=0;o<s.parts.length;o++)s.parts[o](t.parts[o]);for(;o<t.parts.length;o++)s.parts.push(y(t.parts[o]));s.parts.length>t.parts.length&&(s.parts.length=t.parts.length)}else{var r=[];for(o=0;o<t.parts.length;o++)r.push(y(t.parts[o]));i[t.id]={id:t.id,refs:1,parts:r}}}}function g(){var e=document.createElement("style");return e.type="text/css",r.appendChild(e),e}function y(e){var n,t,s=document.querySelector("style["+p+'~="'+e.id+'"]');if(s){if(c)return d;s.parentNode.removeChild(s)}if(m){var o=l++;s=a||(a=g()),n=w.bind(null,s,o,!1),t=w.bind(null,s,o,!0)}else s=g(),n=_.bind(null,s),t=function(){s.parentNode.removeChild(s)};return n(e),function(s){if(s){if(s.css===e.css&&s.media===e.media&&s.sourceMap===e.sourceMap)return;n(e=s)}else t()}}var b,v=(b=[],function(e,n){return b[e]=n,b.filter(Boolean).join("\n")});function w(e,n,t,s){var o=t?"":s.css;if(e.styleSheet)e.styleSheet.cssText=v(n,o);else{var i=document.createTextNode(o),r=e.childNodes;r[n]&&e.removeChild(r[n]),r.length?e.insertBefore(i,r[n]):e.appendChild(i)}}function _(e,n){var t=n.css,s=n.media,o=n.sourceMap;if(s&&e.setAttribute("media",s),u.ssrId&&e.setAttribute(p,n.id),o&&(t+="\n/*# sourceURL="+o.sources[0]+" */",t+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},r7qe:function(e,n,t){var s=t("JR+k");s.__esModule&&(s=s.default),"string"==typeof s&&(s=[[e.i,s,""]]),s.locals&&(e.exports=s.locals);(0,t("P8hj").default)("63a88718",s,!0,{})}});
//# sourceMappingURL=huebert-accessories-directly.js.map