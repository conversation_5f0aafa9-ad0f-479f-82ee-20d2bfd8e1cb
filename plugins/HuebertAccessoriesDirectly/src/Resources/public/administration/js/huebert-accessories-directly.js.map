{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:////Users/<USER>/Workspace/shopware2/custom/plugins/HuebertAccessoriesDirectly/src/Resources/app/administration/src/module/sw-product/component/sw-product-cross-selling-form/index.js", "webpack:////Users/<USER>/Workspace/shopware2/custom/plugins/<PERSON>ebertAccessoriesDirectly/src/Resources/app/administration/src/module/sw-product/component/sw-product-cross-selling-form/sw-product-cross-selling-form.html.twig", "webpack:////Users/<USER>/Workspace/shopware2/custom/plugins/<PERSON>ebertAccessoriesDirectly/src/Resources/app/administration/src/module/sw-product/component/sw-product-cross-selling-assignment/sw-product-cross-selling-assignment.html.twig", "webpack:////Users/<USER>/Workspace/shopware2/custom/plugins/HuebertAccessoriesDirectly/src/Resources/app/administration/src/module/sw-product/component/sw-product-cross-selling-assignment/index.js", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////Users/<USER>/Workspace/shopware2/custom/plugins/HuebertAccessoriesDirectly/src/Resources/app/administration/src/module/sw-product/component/sw-product-cross-selling-form/sw-product-cross-selling-form.scss"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Shopware", "Data", "Criteria", "_Shopware", "Component", "_Component$getCompone", "Context", "getComponentHelper", "mapPropertyErrors", "mapGetters", "mapState", "override", "template", "data", "optionsShow", "label", "this", "$tc", "Mixin", "mixins", "getByName", "showModal", "selectedAssignedProduct", "computed", "assignedProductRepository", "repositoryFactory", "assignedProductColumns", "productColumns", "$super", "push", "inlineEdit", "allowResize", "sortable", "methods", "setAccessoryQuantity", "item", "selectedAccessoryQuantity", "accessoryQuantity", "openModal", "emitted", "closeModal", "onQuantityChange", "quantity", "updateQuantity", "_this", "console", "log", "isLoadingData", "save", "then", "createNotificationSuccess", "title", "$t", "message", "catch", "exception", "listToStyles", "parentId", "list", "styles", "newStyles", "length", "id", "part", "css", "media", "sourceMap", "parts", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "type", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "content", "default", "locals", "add"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,uCAIjBlC,EAAoBA,EAAoBmC,EAAI,Q,uDC9EhCC,SAASC,KAAtBC,SCJO,IDKfC,EAA+BH,SAAvBI,EAASD,EAATC,UACRC,GAD0BF,EAAPG,QACiCF,EAAUG,sBAArCF,EAAjBG,kBAA6BH,EAAVI,WAAoBJ,EAARK,SAEvCN,EAAUO,SAAS,gCAAiC,CAChDC,SCTW,8oGDWXC,KAAI,WACA,MAAO,CACHC,YAAa,CAAC,CAAC7B,MAAO,aAAc8B,MAAOC,KAAKC,IAAI,wCACtC,CAAChC,MAAO,aAAc8B,MAAOC,KAAKC,IAAI,wCACtC,CAAChC,MAAO,aAAc8B,MAAOC,KAAKC,IAAI,6CEfjD,ICEfd,EAA6BH,SAArBI,EAASD,EAATC,UAAWc,EAAKf,EAALe,MAEnBd,EAAUO,SAAS,sCAAuC,CACtDC,SDLW,y1HCOXO,OAAQ,CACJD,EAAME,UAAU,iBAGpBP,KAAI,WACA,MAAO,CACHQ,WAAW,EACXC,wBAAyB,OAIjCC,SAAU,CACNC,0BAAyB,WACrB,OAAOR,KAAKS,kBAAkBnC,OAAO,4CAGzCoC,uBAAsB,WAClB,IAAMC,EAAiBX,KAAKY,OAAO,0BAWnC,OAVAD,EAAeE,KACX,CACIlC,SAAU,oBACVoB,MAAOC,KAAKC,IAAI,2CAChBa,WAAY,SACZC,aAAa,EACbC,UAAU,IAIXL,IAIfM,QAAS,CACLC,qBAAoB,SAACC,GACjBnB,KAAKK,WAAY,EACjBL,KAAKoB,0BAA4BD,EAAKE,kBACtCrB,KAAKM,wBAA0Ba,GAGnCG,UAAS,SAACC,GACHA,IACCvB,KAAKK,WAAY,IAIzBmB,WAAU,WACNxB,KAAKK,WAAY,GAGrBoB,iBAAgB,SAACC,GACb1B,KAAKoB,0BAA4BM,GAGrCC,eAAc,WAAI,IAADC,EAAA,KACbC,QAAQC,IAAI,iCAAkC9B,KAAKoB,2BACnDpB,KAAK+B,eAAgB,EACrB/B,KAAKM,wBAAwBe,kBAAoBrB,KAAKoB,0BACtDpB,KAAKQ,0BAA0BwB,KAAKhC,KAAKM,yBAAyB2B,MAAK,WACnEL,EAAKvB,WAAY,EAEjBuB,EAAKM,0BAA0B,CAC3BC,MAAOP,EAAKQ,GAAG,sCACfC,QAAST,EAAKQ,GAAG,0CAErBR,EAAKG,eAAgB,KACtBO,OAAM,SAACC,GACNX,EAAKvB,WAAY,EACjBuB,EAAKG,eAAgB,S,0KCtEtB,SAASS,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACP5F,EAAI,EAAGA,EAAI0F,EAAKG,OAAQ7F,IAAK,CACpC,IAAImE,EAAOuB,EAAK1F,GACZ8F,EAAK3B,EAAK,GAIV4B,EAAO,CACTD,GAAIL,EAAW,IAAMzF,EACrBgG,IALQ7B,EAAK,GAMb8B,MALU9B,EAAK,GAMf+B,UALc/B,EAAK,IAOhByB,EAAUE,GAGbF,EAAUE,GAAIK,MAAMtC,KAAKkC,GAFzBJ,EAAO9B,KAAK+B,EAAUE,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,KAKlD,OAAOJ,E,+CCjBT,IAAIS,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiB7B,EAAUC,EAAM6B,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAI7B,EAASH,EAAaC,EAAUC,GAGpC,OAFA+B,EAAe9B,GAER,SAAiB+B,GAEtB,IADA,IAAIC,EAAY,GACP3H,EAAI,EAAGA,EAAI2F,EAAOE,OAAQ7F,IAAK,CACtC,IAAImE,EAAOwB,EAAO3F,IACd4H,EAAWpB,EAAYrC,EAAK2B,KACvB+B,OACTF,EAAU9D,KAAK+D,GAEbF,EAEFD,EADA9B,EAASH,EAAaC,EAAUiC,IAGhC/B,EAAS,GAEX,IAAS3F,EAAI,EAAGA,EAAI2H,EAAU9B,OAAQ7F,IAAK,CACzC,IAAI4H,EACJ,GAAsB,KADlBA,EAAWD,EAAU3H,IACZ6H,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASzB,MAAMN,OAAQiC,IACzCF,EAASzB,MAAM2B,YAEVtB,EAAYoB,EAAS9B,OAMpC,SAAS2B,EAAgB9B,GACvB,IAAK,IAAI3F,EAAI,EAAGA,EAAI2F,EAAOE,OAAQ7F,IAAK,CACtC,IAAImE,EAAOwB,EAAO3F,GACd4H,EAAWpB,EAAYrC,EAAK2B,IAChC,GAAI8B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASzB,MAAMN,OAAQiC,IACzCF,EAASzB,MAAM2B,GAAG3D,EAAKgC,MAAM2B,IAE/B,KAAOA,EAAI3D,EAAKgC,MAAMN,OAAQiC,IAC5BF,EAASzB,MAAMtC,KAAKkE,EAAS5D,EAAKgC,MAAM2B,KAEtCF,EAASzB,MAAMN,OAAS1B,EAAKgC,MAAMN,SACrC+B,EAASzB,MAAMN,OAAS1B,EAAKgC,MAAMN,YAEhC,CACL,IAAIM,EAAQ,GACZ,IAAS2B,EAAI,EAAGA,EAAI3D,EAAKgC,MAAMN,OAAQiC,IACrC3B,EAAMtC,KAAKkE,EAAS5D,EAAKgC,MAAM2B,KAEjCtB,EAAYrC,EAAK2B,IAAM,CAAEA,GAAI3B,EAAK2B,GAAI+B,KAAM,EAAG1B,MAAOA,KAK5D,SAAS6B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAaE,KAAO,WACpB1B,EAAK2B,YAAYH,GACVA,EAGT,SAASF,EAAUM,GACjB,IAAIC,EAAQC,EACRN,EAAe5B,SAASmC,cAAc,SAAWxB,EAAW,MAAQqB,EAAIvC,GAAK,MAEjF,GAAImC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaQ,WAAWC,YAAYT,GAIxC,GAAIhB,EAAS,CAEX,IAAI0B,EAAa/B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDM,EAASM,EAAoBpH,KAAK,KAAMyG,EAAcU,GAAY,GAClEJ,EAASK,EAAoBpH,KAAK,KAAMyG,EAAcU,GAAY,QAGlEV,EAAeD,IACfM,EAASO,EAAWrH,KAAK,KAAMyG,GAC/BM,EAAS,WACPN,EAAaQ,WAAWC,YAAYT,IAMxC,OAFAK,EAAOD,GAEA,SAAsBS,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO9C,MAAQqC,EAAIrC,KACnB8C,EAAO7C,QAAUoC,EAAIpC,OACrB6C,EAAO5C,YAAcmC,EAAInC,UAC3B,OAEFoC,EAAOD,EAAMS,QAEbP,KAKN,IACMQ,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,QAI1C,SAAST,EAAqBX,EAAcgB,EAAOV,EAAQF,GACzD,IAAIrC,EAAMuC,EAAS,GAAKF,EAAIrC,IAE5B,GAAIiC,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUP,EAAYC,EAAOjD,OAChD,CACL,IAAIwD,EAAUnD,SAASoD,eAAezD,GAClC0D,EAAazB,EAAayB,WAC1BA,EAAWT,IAAQhB,EAAaS,YAAYgB,EAAWT,IACvDS,EAAW7D,OACboC,EAAa0B,aAAaH,EAASE,EAAWT,IAE9ChB,EAAaG,YAAYoB,IAK/B,SAASX,EAAYZ,EAAcI,GACjC,IAAIrC,EAAMqC,EAAIrC,IACVC,EAAQoC,EAAIpC,MACZC,EAAYmC,EAAInC,UAiBpB,GAfID,GACFgC,EAAa2B,aAAa,QAAS3D,GAEjCc,EAAQ8C,OACV5B,EAAa2B,aAAa5C,EAAUqB,EAAIvC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU4D,QAAQ,GAAK,MAEnD9D,GAAO,uDAAyD+D,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUjE,MAAgB,OAG9H+B,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUvD,MAC7B,CACL,KAAOiC,EAAamC,YAClBnC,EAAaS,YAAYT,EAAamC,YAExCnC,EAAaG,YAAY/B,SAASoD,eAAezD,O,qBCxNrD,IAAIqE,EAAU,EAAQ,QACnBA,EAAQjJ,aAAYiJ,EAAUA,EAAQC,SACnB,iBAAZD,IAAsBA,EAAU,CAAC,CAACtK,EAAOC,EAAIqK,EAAS,MAC7DA,EAAQE,SAAQxK,EAAOD,QAAUuK,EAAQE,SAG/BC,EADH,EAAQ,QAAyJF,SAC1J,WAAYD,GAAS,EAAM", "file": "static/js/huebert-accessories-directly.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/bundles/huebertaccessoriesdirectly/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"83Dy\");\n", "import template from './sw-product-cross-selling-form.html.twig';\nimport './sw-product-cross-selling-form.scss';\n\n\nconst { Criteria } = Shopware.Data;\nconst { Component, Context } = Shopware;\nconst { mapPropertyErrors, mapGetters, mapState } = Component.getComponentHelper();\n\nComponent.override('sw-product-cross-selling-form', {\n    template,\n\n    data() {\n        return {\n            optionsShow: [{value: 'everywhere', label: this.$tc('hueb-acc.product.options.everywhere')},\n                          {value: 'pluginOnly', label: this.$tc('hueb-acc.product.options.pluginOnly')},\n                          {value: 'sliderOnly', label: this.$tc('hueb-acc.product.options.sliderOnly')}],\n        }\n    }\n});\n", "export default \"{% block sw_prduct_cross_selling_form_product_stream_field %}\\n    <sw-container\\n            columns=\\\"2fr 1fr\\\"\\n            gap=\\\"0px 30px\\\"\\n            align=\\\"start\\\"\\n    >\\n        <sw-select-field\\n                v-if=\\\"!crossSelling.huebBundle\\\"\\n                class=\\\"hueb-accessories_show\\\"\\n                :label=\\\"$tc('hueb-acc.product.card.show')\\\"\\n                :value=\\\"crossSelling.display ? crossSelling.display : 'sliderOnly'\\\"\\n                @change=\\\"crossSelling.display = $event\\\">\\n            <option v-for=\\\"option in optionsShow\\\"\\n                    :value=\\\"option.value\\\"\\n                    :key=\\\"option.value\\\">\\n                {{ option.label }}\\n            </option>\\n        </sw-select-field>\\n        <sw-field\\n                v-if=\\\"!crossSelling.huebBundle\\\"\\n                v-model=\\\"crossSelling.selectableAmount\\\"\\n                type=\\\"switch\\\"\\n                :label=\\\"$tc('hueb-acc.product.card.selectableAmount')\\\"\\n                :disabled=\\\"!allowEdit\\\"\\n        />\\n    </sw-container>\\n    <sw-entity-single-select\\n            :helpText=\\\"$tc('hueb-acc.product.card.help')\\\"\\n            entity=\\\"product_stream\\\"\\n            class=\\\"sw-select-cross-selling__select-product-stream\\\"\\n            id=\\\"sw-field--crossSelling-product-group\\\"\\n            :label=\\\"$tc('sw-product.crossselling.inputCrossSellingProductStream')\\\"\\n            :placeholder=\\\"$tc('sw-product.crossselling.inputCrossSellingProductStreamPlaceholder')\\\"\\n            v-model=\\\"crossSelling.productStreamId\\\"\\n            :disabled=\\\"!allowEdit\\\">\\n    </sw-entity-single-select>\\n{% endblock %}\\n\\n{% block sw_product_detail_cross_selling_assignment %}\\n    <sw-container\\n            columns=\\\"2fr 1fr\\\"\\n            gap=\\\"0px 30px\\\"\\n            align=\\\"start\\\"\\n    >\\n        <sw-select-field\\n                v-if=\\\"useManualAssignment\\\"\\n                class=\\\"hueb-accessories_show\\\"\\n                :label=\\\"$tc('hueb-acc.product.card.show')\\\"\\n                :value=\\\"crossSelling.display ? crossSelling.display : 'sliderOnly'\\\"\\n                @change=\\\"crossSelling.display = $event\\\">\\n            <option v-for=\\\"option in optionsShow\\\"\\n                    :value=\\\"option.value\\\"\\n                    :key=\\\"option.value\\\">\\n                {{ option.label }}\\n            </option>\\n        </sw-select-field>\\n        <sw-field\\n                v-if=\\\"useManualAssignment\\\"\\n                v-model=\\\"crossSelling.selectableAmount\\\"\\n                type=\\\"switch\\\"\\n                :label=\\\"$tc('hueb-acc.product.card.selectableAmount')\\\"\\n                :disabled=\\\"!allowEdit\\\"\\n        />\\n        <sw-switch-field\\n            :label=\\\"$tc('hueb-acc.product.card.sameQuantityAsMainProduct')\\\"\\n            v-model=\\\"crossSelling.sameQuantityAsMainProduct\\\"\\n            :helpText=\\\"$tc('hueb-acc.product.card.sameQuantityHelpText')\\\">\\n        </sw-switch-field>\\n    </sw-container>\\n    <sw-product-cross-selling-assignment\\n            v-if=\\\"useManualAssignment\\\"\\n            :assignedProducts=\\\"crossSelling.assignedProducts\\\"\\n            :crossSellingId=\\\"crossSelling.id\\\"\\n            :searchableFields=\\\"['name', 'productNumber']\\\">\\n    </sw-product-cross-selling-assignment>\\n{% endblock %}\\n\";", "export default \"{% block sw_product_cross_selling_assignment_select %}\\n    <sw-entity-single-select\\n        :helpText=\\\"$tc('hueb-acc.product.card.help')\\\"\\n        :label=\\\"$tc('sw-product.crossselling.inputCrossSellingProductList')\\\"\\n        entity=\\\"product\\\"\\n        value=\\\"\\\"\\n        :criteria=\\\"searchCriteria\\\"\\n        :context=\\\"searchContext\\\"\\n        disableAutoClose\\n        :disabled=\\\"!allowEdit\\\"\\n        @change=\\\"onToggleProduct\\\">\\n\\n        <template #result-item=\\\"{ item, index }\\\">\\n            <slot name=\\\"result-item\\\" v-bind=\\\"{ item, index, isSelected }\\\">\\n                <li is=\\\"sw-select-result\\\" :selected=\\\"isSelected(item)\\\" v-bind=\\\"{ item, index }\\\">\\n                    {% block sw_entity_single_select_base_results_list_result_label %}\\n                        <span class=\\\"sw-select-result__result-item-text\\\">\\n                                        <sw-product-variant-info :variations=\\\"item.variation\\\">\\n                                            {{ item.translated.name || item.name }}\\n                                        </sw-product-variant-info>\\n                                    </span>\\n                    {% endblock %}\\n                </li>\\n            </slot>\\n        </template>\\n\\n    </sw-entity-single-select>\\n{% endblock %}\\n\\n\\n{% block sw_product_cross_selling_assignment_option_grid %}\\n    <sw-data-grid\\n            v-if=\\\"total\\\"\\n            :key=\\\"isLoadingGrid\\\"\\n            :data-source=\\\"assignedProducts\\\"\\n            :is-loading=\\\"isLoadingGrid\\\"\\n            :columns=\\\"assignedProductColumns\\\"\\n            :show-settings=\\\"true\\\"\\n            :show-selection=\\\"false\\\"\\n    >\\n\\n        <template #actions=\\\"{ item }\\\">\\n            <sw-context-menu-item\\n                    :disabled=\\\"!allowEdit\\\"\\n                    variant=\\\"danger\\\"\\n                    @click=\\\"removeItem(item)\\\"\\n            >\\n                {{ $tc('global.default.delete') }}\\n            </sw-context-menu-item>\\n            <sw-context-menu-item\\n                    :disabled=\\\"!allowEdit\\\"\\n                    @click=\\\"setAccessoryQuantity(item)\\\"\\n            >\\n                {{ $tc('hueb-acc.product.setAccessoryQuantity') }}\\n            </sw-context-menu-item>\\n        </template>\\n\\n        <template #column-product.translated.name=\\\"{ item }\\\">\\n            \\n            {% block sw_data_grid_columns_render_value %}\\n                <span>\\n                    <sw-product-variant-info :variations=\\\"item.product.variation\\\">\\n                        {{ item.product.translated.name || item.product.name || variantNames[item.product.id] }}\\n                    </sw-product-variant-info>\\n                </span>\\n            {% endblock %}\\n        </template>\\n\\n        \\n        {% block sw_product_cross_selling_assignment_option_grid_column_position %}\\n            <template #column-position=\\\"{ item }\\\">\\n                <sw-data-grid-column-position\\n                        ref=\\\"columnPosition\\\"\\n                        v-model=\\\"assignedProducts\\\"\\n                        :show-value=\\\"true\\\"\\n                        :item=\\\"item\\\"\\n                />\\n            </template>\\n        {% endblock %}\\n    </sw-data-grid>\\n\\n    <sw-modal\\n            class=\\\"accessory-qty-modal\\\"\\n            v-if=\\\"showModal\\\"\\n            variant=\\\"small\\\"\\n            :title=\\\"$tc('hueb-acc.product.accessoryQuantityTitle')\\\"\\n            @modal-close=\\\"closeModal\\\">\\n\\n        <sw-field\\n                type=\\\"number\\\"\\n                v-model=\\\"selectedAccessoryQuantity\\\"\\n                @input-change=\\\"onQuantityChange\\\"\\n                :label=\\\"$tc('hueb-acc.product.accessoryQuantityLabel')\\\">\\n        </sw-field>\\n\\n        <sw-button\\n                v-if=\\\"selectedAssignedProduct\\\"\\n                variant=\\\"primary\\\"\\n                size=\\\"small\\\"\\n                @click=\\\"updateQuantity\\\">\\n            {{ $tc('hueb-acc.product.saveBtn') }}\\n        </sw-button>\\n\\n    </sw-modal>\\n{% endblock %}\";", "import template from './sw-product-cross-selling-assignment.html.twig';\n\nconst { Component, Mixin } = Shopware;\n\nComponent.override('sw-product-cross-selling-assignment', {\n    template,\n\n    mixins: [\n        Mixin.getByName('notification')\n    ],\n\n    data() {\n        return {\n            showModal: false,\n            selectedAssignedProduct: null\n        };\n    },\n\n    computed: {\n        assignedProductRepository() {\n            return this.repositoryFactory.create('product_cross_selling_assigned_products');\n        },\n\n        assignedProductColumns() {\n            const productColumns = this.$super('assignedProductColumns');\n            productColumns.push(\n                {\n                    property: 'accessoryQuantity',\n                    label: this.$tc('hueb-acc.product.accessoryQuantityLabel'),\n                    inlineEdit: 'number',\n                    allowResize: true,\n                    sortable: false\n                }\n            );\n\n            return productColumns;\n        }\n    },\n\n    methods: {\n        setAccessoryQuantity(item) {\n            this.showModal = true;\n            this.selectedAccessoryQuantity = item.accessoryQuantity\n            this.selectedAssignedProduct = item\n        },\n\n        openModal(emitted) {\n            if(emitted) {\n                this.showModal = true;\n            }\n        },\n\n        closeModal() {\n            this.showModal = false;\n        },\n\n        onQuantityChange(quantity) {\n            this.selectedAccessoryQuantity = quantity\n        },\n\n        updateQuantity() {\n            console.log('this.selectedAccessoryQuantity', this.selectedAccessoryQuantity)\n            this.isLoadingData = true;\n            this.selectedAssignedProduct.accessoryQuantity = this.selectedAccessoryQuantity\n            this.assignedProductRepository.save(this.selectedAssignedProduct).then(() => {\n                this.showModal = false;\n\n                this.createNotificationSuccess({\n                    title: this.$t('hueb-acc.notification.successTitle'),\n                    message: this.$t('hueb-acc.notification.successMessage')\n                });\n                this.isLoadingData = false\n            }).catch((exception) => {\n                this.showModal = false;\n                this.isLoadingData = false\n            })\n        }\n    }\n});\n", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-product-cross-selling-form.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"63a88718\", content, true, {});"], "sourceRoot": ""}