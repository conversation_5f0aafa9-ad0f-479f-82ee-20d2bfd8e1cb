<?php


namespace <PERSON><PERSON>ert\AccessoriesDirectly\Resources\snippet\en_GB;

use Shopware\Core\System\Snippet\Files\SnippetFileInterface;

class SnippetFile_en_GB implements SnippetFileInterface
{
    public function getAuthor(): string
    {
        return '<PERSON>';
    }
    public function getIso(): string
    {
        return 'en-GB';
    }
    public function getName(): string
    {
        return 'storefront.en-GB';
    }
    public function getPath(): string
    {
        return __DIR__ . '/storefront.en-GB.json';
    }
    public function isBase(): bool
    {
        return false;
    }
}
