{% block sw_prduct_cross_selling_form_product_stream_field %}
    <sw-container
            columns="2fr 1fr"
            gap="0px 30px"
            align="start"
    >
        <sw-select-field
                v-if="!crossSelling.huebBundle"
                class="hueb-accessories_show"
                :label="$tc('hueb-acc.product.card.show')"
                :value="crossSelling.display ? crossSelling.display : 'sliderOnly'"
                @change="crossSelling.display = $event">
            <option v-for="option in optionsShow"
                    :value="option.value"
                    :key="option.value">
                {{ option.label }}
            </option>
        </sw-select-field>
        <sw-field
                v-if="!crossSelling.huebBundle"
                v-model="crossSelling.selectableAmount"
                type="switch"
                :label="$tc('hueb-acc.product.card.selectableAmount')"
                :disabled="!allowEdit"
        />
    </sw-container>
    <sw-entity-single-select
            :helpText="$tc('hueb-acc.product.card.help')"
            entity="product_stream"
            class="sw-select-cross-selling__select-product-stream"
            id="sw-field--crossSelling-product-group"
            :label="$tc('sw-product.crossselling.inputCrossSellingProductStream')"
            :placeholder="$tc('sw-product.crossselling.inputCrossSellingProductStreamPlaceholder')"
            v-model="crossSelling.productStreamId"
            :disabled="!allowEdit">
    </sw-entity-single-select>
{% endblock %}

{% block sw_product_detail_cross_selling_assignment %}
    <sw-container
            columns="2fr 1fr"
            gap="0px 30px"
            align="start"
    >
        <sw-select-field
                v-if="useManualAssignment"
                class="hueb-accessories_show"
                :label="$tc('hueb-acc.product.card.show')"
                :value="crossSelling.display ? crossSelling.display : 'sliderOnly'"
                @change="crossSelling.display = $event">
            <option v-for="option in optionsShow"
                    :value="option.value"
                    :key="option.value">
                {{ option.label }}
            </option>
        </sw-select-field>
        <sw-field
                v-if="useManualAssignment"
                v-model="crossSelling.selectableAmount"
                type="switch"
                :label="$tc('hueb-acc.product.card.selectableAmount')"
                :disabled="!allowEdit"
        />
        <sw-switch-field
            :label="$tc('hueb-acc.product.card.sameQuantityAsMainProduct')"
            v-model="crossSelling.sameQuantityAsMainProduct"
            :helpText="$tc('hueb-acc.product.card.sameQuantityHelpText')">
        </sw-switch-field>
    </sw-container>
    <sw-product-cross-selling-assignment
            v-if="useManualAssignment"
            :assignedProducts="crossSelling.assignedProducts"
            :crossSellingId="crossSelling.id"
            :searchableFields="['name', 'productNumber']">
    </sw-product-cross-selling-assignment>
{% endblock %}
