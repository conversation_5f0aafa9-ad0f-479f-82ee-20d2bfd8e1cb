{% block sw_product_cross_selling_assignment_select %}
    <sw-entity-single-select
        :helpText="$tc('hueb-acc.product.card.help')"
        :label="$tc('sw-product.crossselling.inputCrossSellingProductList')"
        entity="product"
        value=""
        :criteria="searchCriteria"
        :context="searchContext"
        disableAutoClose
        :disabled="!allowEdit"
        @change="onToggleProduct">

        <template #result-item="{ item, index }">
            <slot name="result-item" v-bind="{ item, index, isSelected }">
                <li is="sw-select-result" :selected="isSelected(item)" v-bind="{ item, index }">
                    {% block sw_entity_single_select_base_results_list_result_label %}
                        <span class="sw-select-result__result-item-text">
                                        <sw-product-variant-info :variations="item.variation">
                                            {{ item.translated.name || item.name }}
                                        </sw-product-variant-info>
                                    </span>
                    {% endblock %}
                </li>
            </slot>
        </template>

    </sw-entity-single-select>
{% endblock %}

<!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
{% block sw_product_cross_selling_assignment_option_grid %}
    <sw-data-grid
            v-if="total"
            :key="isLoadingGrid"
            :data-source="assignedProducts"
            :is-loading="isLoadingGrid"
            :columns="assignedProductColumns"
            :show-settings="true"
            :show-selection="false"
    >

        <template #actions="{ item }">
            <sw-context-menu-item
                    :disabled="!allowEdit"
                    variant="danger"
                    @click="removeItem(item)"
            >
                {{ $tc('global.default.delete') }}
            </sw-context-menu-item>
            <sw-context-menu-item
                    :disabled="!allowEdit"
                    @click="setAccessoryQuantity(item)"
            >
                {{ $tc('hueb-acc.product.setAccessoryQuantity') }}
            </sw-context-menu-item>
        </template>

        <template #column-product.translated.name="{ item }">
            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_data_grid_columns_render_value %}
                <span>
                    <sw-product-variant-info :variations="item.product.variation">
                        {{ item.product.translated.name || item.product.name || variantNames[item.product.id] }}
                    </sw-product-variant-info>
                </span>
            {% endblock %}
        </template>

        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_product_cross_selling_assignment_option_grid_column_position %}
            <template #column-position="{ item }">
                <sw-data-grid-column-position
                        ref="columnPosition"
                        v-model="assignedProducts"
                        :show-value="true"
                        :item="item"
                />
            </template>
        {% endblock %}
    </sw-data-grid>

    <sw-modal
            class="accessory-qty-modal"
            v-if="showModal"
            variant="small"
            :title="$tc('hueb-acc.product.accessoryQuantityTitle')"
            @modal-close="closeModal">

        <sw-field
                type="number"
                v-model="selectedAccessoryQuantity"
                @input-change="onQuantityChange"
                :label="$tc('hueb-acc.product.accessoryQuantityLabel')">
        </sw-field>

        <sw-button
                v-if="selectedAssignedProduct"
                variant="primary"
                size="small"
                @click="updateQuantity">
            {{ $tc('hueb-acc.product.saveBtn') }}
        </sw-button>

    </sw-modal>
{% endblock %}