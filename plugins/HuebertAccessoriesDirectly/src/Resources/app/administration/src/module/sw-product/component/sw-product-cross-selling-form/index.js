import template from './sw-product-cross-selling-form.html.twig';
import './sw-product-cross-selling-form.scss';


const { Criteria } = Shopware.Data;
const { Component, Context } = Shopware;
const { mapPropertyErrors, mapGetters, mapState } = Component.getComponentHelper();

Component.override('sw-product-cross-selling-form', {
    template,

    data() {
        return {
            optionsShow: [{value: 'everywhere', label: this.$tc('hueb-acc.product.options.everywhere')},
                          {value: 'pluginOnly', label: this.$tc('hueb-acc.product.options.pluginOnly')},
                          {value: 'sliderOnly', label: this.$tc('hueb-acc.product.options.sliderOnly')}],
        }
    }
});
