import template from './sw-product-cross-selling-assignment.html.twig';

const { Component, Mixin } = Shopware;

Component.override('sw-product-cross-selling-assignment', {
    template,

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            showModal: false,
            selectedAssignedProduct: null
        };
    },

    computed: {
        assignedProductRepository() {
            return this.repositoryFactory.create('product_cross_selling_assigned_products');
        },

        assignedProductColumns() {
            const productColumns = this.$super('assignedProductColumns');
            productColumns.push(
                {
                    property: 'accessoryQuantity',
                    label: this.$tc('hueb-acc.product.accessoryQuantityLabel'),
                    inlineEdit: 'number',
                    allowResize: true,
                    sortable: false
                }
            );

            return productColumns;
        }
    },

    methods: {
        setAccessoryQuantity(item) {
            this.showModal = true;
            this.selectedAccessoryQuantity = item.accessoryQuantity
            this.selectedAssignedProduct = item
        },

        openModal(emitted) {
            if(emitted) {
                this.showModal = true;
            }
        },

        closeModal() {
            this.showModal = false;
        },

        onQuantityChange(quantity) {
            this.selectedAccessoryQuantity = quantity
        },

        updateQuantity() {
            console.log('this.selectedAccessoryQuantity', this.selectedAccessoryQuantity)
            this.isLoadingData = true;
            this.selectedAssignedProduct.accessoryQuantity = this.selectedAccessoryQuantity
            this.assignedProductRepository.save(this.selectedAssignedProduct).then(() => {
                this.showModal = false;

                this.createNotificationSuccess({
                    title: this.$t('hueb-acc.notification.successTitle'),
                    message: this.$t('hueb-acc.notification.successMessage')
                });
                this.isLoadingData = false
            }).catch((exception) => {
                this.showModal = false;
                this.isLoadingData = false
            })
        }
    }
});
