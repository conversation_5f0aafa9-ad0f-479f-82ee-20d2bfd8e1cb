"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["huebert-accessories-directly"],{4081:(e,t,r)=>{r.d(t,{Z:()=>y});var a,n,l,i=r(6285),s=r(3206),u=r(9658),c=r(2005),o=r(8254);class y extends i.Z{init(){this._client=new o.Z,this.isNeonConfiguratorInstalled=document.querySelector(this.options.isNeonConfiguratorInstalled).value,this.mainProductId=document.querySelector(this.options.mainProductId).value,this._registerEvents()}_registerEvents(){const e=this,t=document.getElementById("productDetailPageBuyProductForm");new c.Z(t).subscribe("beforeFormSubmit",(e=>{this._formSubmit(e)}));let r=document.querySelector(".product-detail-quantity-input");r&&r.addEventListener("change",e._quantityChange.bind(e),!1);const a=this._getAccessories(this.el);if(!this.isNeonConfiguratorInstalled){const t=document.querySelector(".js-btn-plus"),r=document.querySelector(".js-btn-minus");t.addEventListener("click",e._mainQuantityUp.bind(e),!1),r.addEventListener("click",e._mainQuantityDown.bind(e),!1)}Array.from(a).forEach((function(t){const r=t.querySelector(".quantity-up"),a=t.querySelector(".quantity-dn"),n=t.querySelector(".accessory-checkbox"),l=t.querySelector(".accessory-quantity"),i=u.Z.isTouchDevice()?"touchstart":"click";if(r&&r.addEventListener(i,e._quantityUp.bind(e),!1),a&&a.addEventListener(i,e._quantityDown.bind(e),!1),n){let t=!1;n&&n.dataset&&n.dataset.preselectedCheckboxes&&(t=n.dataset.preselectedCheckboxes),n.checked=t,l.value=0,n.addEventListener("change",e._toggleAccessory.bind(e))}if(!s.Z.querySelector(t,".accessory-quantity").hasAttribute(".unforce")){s.Z.querySelector(t,".accessory-quantity").addEventListener("change",e._quantityChange.bind(e),!1)}})),e._quantityChange(null)}_mainQuantityUp(e){this._quantityChange(null)}_mainQuantityDown(e){this._quantityChange(null)}_quantityUp(e){const t=e.target.closest("[data-huebert-add-accessory-config]"),r=t.querySelector(".accessory-quantity"),a=JSON.parse(t.getAttribute("data-huebert-add-accessory-config"));let n=null;r.hasAttribute("max")&&(n=parseInt(r.getAttribute("max"),10));let l=0;r.dataset.quantity&&(l=parseInt(r.dataset.quantity)),0===parseInt(r.value,10)?r.value=this._calcQuantity(r.value,!0,a,n):isNaN(parseInt(r.value,10))?r.value=parseInt(a.accessory.minPurchase,10):r.value=this._calcQuantity(r.value,!0,a,n);let i=null;if(t.querySelector(".accessory-quantity-as-main")&&(i=t.querySelector(".accessory-quantity-as-main").value),i){let e=1;t.querySelector(".product-detail-quantity-input")&&(e=t.querySelector(".product-detail-quantity-input").value),r.value=e}this.getTotalPrice()}_quantityDown(e){const t=e.target.closest("[data-huebert-add-accessory-config]"),r=t.querySelector(".accessory-quantity"),a=JSON.parse(t.getAttribute("data-huebert-add-accessory-config"));0===parseInt(r.value,10)?r.value=this._calcQuantity(r.value,!1,a):isNaN(parseInt(r.value,10))?r.value="0":r.value=this._calcQuantity(r.value,!1,a),this.getTotalPrice()}_toggleAccessory(e){e.target.checked?this._quantityUp(e):this._quantityDown(e)}async _quantityChange(e){const t=document.getElementsByClassName("hueb-accessories_total-wrapper"),r=document.getElementById("hueb-accessories_total-price");let a=document.querySelectorAll(".huebert-direct-accessories-block");if(Array.from(a).forEach((function(e){let t=null;e.querySelector(".same-quantity-input")&&(t=e.querySelector(".same-quantity-input").value);const r=e.querySelectorAll(".hueb-bundle-quantity .accessory-quantity");let a=document.querySelector(".product-detail-quantity-input");a=a&&parseInt(a.value,10)||1,Array.from(r).every((e=>{if(e){let t=e.value*a,r=e.getAttribute("max"),n=document.querySelector(".btn-buy");return r&&t>r?(n.disabled=!0,!1):(n.disabled=!1,!0)}})),t&&setTimeout((()=>{let t=document.querySelector("input.js-quantity-selector").value;if(e.querySelectorAll(".same-quantity-as-main")){e.querySelectorAll(".same-quantity-as-main").forEach((e=>{e.textContent=t}))}if(e.querySelectorAll(".same-quantity-input")){e.querySelectorAll(".same-quantity-input").forEach((e=>{e.value=t}))}}),"300");let n=null;if(e.querySelector(".accessory-quantity-as-main")&&(n=e.querySelector(".accessory-quantity-as-main").value),n&&e.querySelectorAll(".accessory-quantity")){let t=0;document.querySelector(".product-detail-quantity-input")&&(t=document.querySelector(".product-detail-quantity-input").value),e.querySelectorAll(".accessory-quantity").forEach((e=>{let r=e.nextElementSibling;r&&r.checked?e.value=t:e.value=0}))}})),r){let e=parseFloat(document.querySelector("input[data-hueb-price]").getAttribute("data-hueb-price")),a=document.querySelector(".product-detail-quantity-input");a=a&&parseInt(a.value,10)||1,null!==document.querySelector(".product-block-prices")&&(e=this._getPriceBrackets(a));const n=Math.round(e*a*100)/100;let l=n;const i=this._getAccessories(this.el);Array.from(i).forEach((function(e){const t=parseFloat(s.Z.querySelector(e,"[data-huebert-pricing]").getAttribute("data-huebert-pricing")),r=s.Z.querySelector(e,".accessory-quantity");let a=parseInt(r.value)||0;a>0&&r.dataset.quantity&&(a=parseInt(r.dataset.quantity)),l+=Math.round(t*a*100)/100}));const u=document.documentElement.getAttribute("lang"),c=document.querySelector("input[data-hueb-price-currency]").getAttribute("data-hueb-price-currency"),o=new Intl.NumberFormat(u,{style:"currency",currency:c});r.innerHTML=o.format(l),n===l&&t||isNaN(l)?t[0].style="display:none":t&&(t[0].style="display:inherit")}}getTotalPrice(){const e=document.getElementsByClassName("hueb-accessories_total-wrapper"),t=document.getElementById("hueb-accessories_total-price");if(t){let r=parseFloat(document.querySelector("input[data-hueb-price]").getAttribute("data-hueb-price")),a=document.querySelector(".product-detail-quantity-input");a=a&&parseInt(a.value,10)||1,null!==document.querySelector(".product-block-prices")&&(r=this._getPriceBrackets(a));const n=Math.round(r*a*100)/100;let l=n;const i=this._getAccessories(this.el);Array.from(i).forEach((function(e){const t=parseFloat(s.Z.querySelector(e,"[data-huebert-pricing]").getAttribute("data-huebert-pricing")),r=s.Z.querySelector(e,".accessory-quantity");let a=parseInt(r.value)||0;a>0&&r.dataset.quantity&&(a=parseInt(r.dataset.quantity)),l+=Math.round(t*a*100)/100}));const u=document.documentElement.getAttribute("lang"),c=document.querySelector("input[data-hueb-price-currency]").getAttribute("data-hueb-price-currency"),o=new Intl.NumberFormat(u,{style:"currency",currency:c});t.innerHTML=o.format(l),n===l&&e||isNaN(l)?e[0].style="display:none":e&&(e[0].style="display:inherit")}}async _formSubmit(e){e.preventDefault();const t=e.detail,r=this._getAccessories(this.el);let a=!1,n=null;if(this.isNeonConfiguratorInstalled)n=document.querySelector(this.options.mainProductId).value;else{var l;let e=null===(l=document.querySelector(".product-detail-form-container .buy-widget-container select"))||void 0===l?void 0:l.getAttribute("name");e||(e=document.querySelector(".product-detail-form-container .buy-widget-container input").getAttribute("name"));const t=e.match(/lineItems\[(.+?)\]/);n=t[1]}let i=document.querySelector(".product-detail-quantity-input");i=i&&parseInt(i.value,10)||1;let u=!1;Array.from(r).every((function(e){const t=s.Z.querySelector(e,".accessory-quantity");let r=t.value;r>0&&t.dataset.quantity&&(r=t.dataset.quantity);let a=null;return t.hasAttribute("max")&&(a=parseInt(t.getAttribute("max"),10)),!(a&&r*i>a)||(u=!0,!1)})),u?this._client.delete("/checkout/line-item/delete/"+n):Array.from(r).forEach((function(e){let r;const l=s.Z.querySelector(e,".accessory-quantity");r=l.value,r>0&&l.dataset.quantity&&(r=l.dataset.quantity);const u=e.querySelector(".same-and-selectable");u&&u.value&&(r=i);let c=e.getAttribute("data-product-id"),o=s.Z.querySelector(e,".generated-variant");const y=s.Z.querySelector(e,".accessory-quantity").hasAttribute("readonly"),d=e.getAttribute("data-cross-selling-id"),p=e.classList.contains("huebert-bundling"),m=s.Z.querySelector(e,".accessory-quantity").classList.contains("unforce");p&&m?r=s.Z.querySelector(e,".accessory-quantity").checked?"1;":"0":p&&!m&&(r="1"),parseInt(r,10)>0&&(p?(a=!0,t.append("lineItems["+d+"][id]",d),t.append("lineItems["+d+"][referencedId]",n),t.append("lineItems["+d+"][type]","huebbundling"),t.append("lineItems["+d+"][quantity]","1")):y||(null!==o&&"0"!==o.value&&""!==o.value&&(c=o.value),a=!0,t.append("lineItems["+c+"][id]",c),t.append("lineItems["+c+"][referencedId]",c),t.append("lineItems["+c+"][type]","product"),t.append("lineItems["+c+"][quantity]",r),t.append("lineItems["+c+"][stackable]","1"),t.append("lineItems["+c+"][removable]","1")))}))}_calcQuantity(e,t,r,a=null){const n=parseInt(r.accessory.purchaseSteps,10);let l=parseInt(r.accessory.maxPurchase,10);const i=parseInt(r.accessory.minPurchase,10);a&&(l=parseInt(a,10));const s=parseInt(e,10);return t?isNaN(s)||s<=0?i:s<l?s+1*n:s>=l?l:void 0:s>0&&s>=n?1*(s-n):0}_getAccessories(e){return e.getElementsByClassName("huebert-accessory")}_logFormData(e){for(var t of e.entries())console.log(t[0]+" - "+t[1])}_getPriceBrackets(e){let t,r=document.getElementsByClassName("product-block-prices-grid")[0].getElementsByTagName("tr");for(let a=1;a<r.length;a++){let n=r[a].getElementsByTagName("th");if(n=parseInt(n[0].textContent.match(/\d+/)[0]),e<=n||a===r.length-1){t=parseFloat(r[a].querySelector("input[data-hueb-price]").getAttribute("data-hueb-price"));break}}return t}_sleep(e){return new Promise((t=>setTimeout(t,e)))}_dataLength(e){var t=0;for(var r of e.entries())t++;return t}}a=y,l={isNeonConfiguratorInstalled:"#isNeonConfiguratorInstalled",mainProductId:"#mainProductId"},(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n="options"))in a?Object.defineProperty(a,n,{value:l,enumerable:!0,configurable:!0,writable:!0}):a[n]=l},2634:(e,t,r)=>{var a=r(4081),n=r(6285),l=r(8254),i=r(3206);class s extends n.Z{init(){this._client=new l.Z,this.switch=document.querySelectorAll(".huebert-variant-option"),this._registerEvents()}_registerEvents(){const e=this._getForms(this.el);this.getVariantId(e)}getVariantId(e){Array.from(e).forEach((function(e,t){const r=i.Z.querySelectorAll(e,".huebert-variant-option"),a=i.Z.querySelector(e,".current-language").value;let n=i.Z.querySelector(e,".current-availableStock").value,s=i.Z.querySelector(e,".current-minPurchase").value,u=i.Z.querySelector(e,".current-deliveryTime").value,c=i.Z.querySelector(e,".current-isCloseout").value,o=i.Z.querySelector(e,".current-restockTime").value,y=i.Z.querySelector(e,".display-delivery").value,d=i.Z.querySelector(e,".first-d"),p=i.Z.querySelector(e,".second-d"),m=i.Z.querySelector(e,".third-d");n>=s&&u?(d.style.display="block",p.style.display="none",m.style.display="none",i.Z.querySelector(e,".stock-huebert").value=""):c&&n<s?(d.style.display="none",p.style.display="block",m.style.display="none",i.Z.querySelector(e,".stock-huebert").value="true"):n<s&&u&&o&&(d.style.display="none",p.style.display="none",m.style.display="block",i.Z.querySelector(e,".stock-huebert").value="");let h={},q=null,b=a;b&&(b=a.toLowerCase()),r.forEach((t=>{if(t.checked){const e=t.value,r=t.getAttribute("name").split("--");h[r[0]]=e,q=r[1]}t.addEventListener("click",(function(){const r=t.value;for(let e in h){const a=t.getAttribute("name").split("--");q=a[1],e===a[0]&&(h[a[0]]=r)}const a={productId:q,options:h,locale:b};this._client=new l.Z,this._client.post("/huebert/options",JSON.stringify(a),(t=>{let r=JSON.parse(t),a=i.Z.querySelector(e,".generated-variant");const n=i.Z.querySelector(e,".variant-price-pricing");if(r.deposit){const t=i.Z.querySelector(e,".product-detail-deposit");t&&(t.innerHTML=r.deposit)}y?r.product.availableStock>=r.product.minPurchase&&r.product.deliveryTime?(d.innerHTML='<span class="delivery-status-indicator bg-success"></span> Sofort verfügbar, Lieferzeit '+r.product.deliveryTime.name,d.style.display="block",p.style.display="none",m.style.display="none",a.value=r.productId):r.product.isCloseout&&r.product.availableStock<r.product.minPurchase?(d.style.display="none",p.style.display="block",m.style.display="none",a.value="0",i.Z.querySelector(e,".stock-huebert").value="true"):r.product.availableStock<r.product.minPurchase&&r.product.deliveryTime&&r.product.restockTime?(d.style.display="none",p.style.display="none",m.innerHTML='<span class="delivery-status-indicator bg-warning"></span>\n Versandfertig in '+r.product.restockTime+" Tagen, Lieferzeit "+r.product.deliveryTime.name,m.style.display="block",a.value=r.productId,i.Z.querySelector(e,".stock-huebert").value=""):(d.style.display="none",p.style.display="none",m.style.display="none",a.value=r.productId,i.Z.querySelector(e,".stock-huebert").value=""):(a.value=r.productId,i.Z.querySelector(e,".stock-huebert").value="");const l=e.closest(".huebert-accessory");if(i.Z.querySelector(l,".huebert-accessory_input-wrapper").setAttribute("data-huebert-pricing",r.product.calculatedPrice.totalPrice),n.innerHTML=r.calculatedPrice,document.querySelector(".huebert-unit")){const t=i.Z.querySelectorAll(e,".huebert-unit"),a=i.Z.querySelectorAll(e,".huebert-unit-label");if(r.unitInfo){let e="";r.unitInfo&&(e=r.unitInfo);let n="";null!=r.referencePricing&&(n=r.referencePricing),t[0].innerHTML=e+" "+n,t[0].style.display="inline",a[0].style.display="inline",t.length>1&&(t[1].style.display="none",a[1].style.display="none")}else t[0].style.display="none",a[0].style.display="none",t.length>1&&(t[1].style.display="none",a[1].style.display="none")}}))}))}))}))}_getForms(e){return e.getElementsByClassName("huebert-variant-configurator")}checkDeliveryInfo(e,t,r,a,n,l,s,u){return e>=t&&r?(l.style.display="block",s.style.display="none",u.style.display="none",i.Z.querySelector(elemt,".stock-huebert").value="true"):a&&e<t?(l.style.display="none",s.style.display="block",u.style.display="none",i.Z.querySelector(elemt,".stock-huebert").value="true"):e<t&&r&&n?(l.style.display="none",s.style.display="none",u.style.display="block",i.Z.querySelector(elemt,".stock-huebert").value="true"):(l.style.display="none",s.style.display="none",u.style.display="none",i.Z.querySelector(elemt,".stock-huebert").value=""),!0}}const u=window.PluginManager;u.register("AddAcessory",a.Z,"[data-add-accessory]"),u.register("HuebertVariant",s,"[data-variant]")}},e=>{e.O(0,["vendor-node","vendor-shared"],(()=>{return t=2634,e(e.s=t);var t}));e.O()}]);