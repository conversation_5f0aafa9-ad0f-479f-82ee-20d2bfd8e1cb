import HuebertAddToCart from './huebert-accessories-directly/huebert-accessories-directly';
import <PERSON>ebertVariant from './variant-switch';


const PluginManager = window.PluginManager;

PluginManager.register('AddAcessory', HuebertAddToCart, '[data-add-accessory]');
PluginManager.register('<PERSON><PERSON><PERSON><PERSON>arian<PERSON>', <PERSON><PERSON>ertVariant, '[data-variant]');



if (module.hot) {
    module.hot.accept();
}
