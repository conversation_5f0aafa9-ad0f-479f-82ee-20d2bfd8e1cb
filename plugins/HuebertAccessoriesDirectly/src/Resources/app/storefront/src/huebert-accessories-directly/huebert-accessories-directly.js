/* eslint no-unused-vars: "off" */
/* eslint no-undef: "off" */
/* eslint no-empty: "off" */


import Plugin from 'src/plugin-system/plugin.class';
import DomAccess from 'src/helper/dom-access.helper';
import DeviceDetection from 'src/helper/device-detection.helper';
import NativeEventEmitter from 'src/helper/emitter.helper';
import HttpClient from 'src/service/http-client.service';

export default class HuebertAddToCart extends Plugin {

    static options = {
        isNeonConfiguratorInstalled: '#isNeonConfiguratorInstalled',
        mainProductId: '#mainProductId'
    };

    init() {
        this._client = new HttpClient();
        this.isNeonConfiguratorInstalled = document.querySelector(this.options.isNeonConfiguratorInstalled).value;
        this.mainProductId = document.querySelector(this.options.mainProductId).value;
        this._registerEvents();
    }

    _registerEvents() {
        const me = this;

        const buyForm = document.getElementById('productDetailPageBuyProductForm');
        const emitter = new NativeEventEmitter(buyForm);

        emitter.subscribe('beforeFormSubmit', (event) => {
            this._formSubmit(event);
        });

        let mainQuantity = document.querySelector('.product-detail-quantity-input');
        if (mainQuantity) {
            mainQuantity.addEventListener('change', me._quantityChange.bind(me), false);
        }

        const accs = this._getAccessories(this.el);

        if (!this.isNeonConfiguratorInstalled) {
            const mainQuantityUp = document.querySelector('.js-btn-plus');
            const mainQuantityDown = document.querySelector('.js-btn-minus');
            mainQuantityUp.addEventListener('click', me._mainQuantityUp.bind(me), false);
            mainQuantityDown.addEventListener('click', me._mainQuantityDown.bind(me), false);
        }

        Array.from(accs).forEach(function (acc) {
            const upBtn = acc.querySelector('.quantity-up');
            const dnBtn = acc.querySelector('.quantity-dn');
            const checkbox = acc.querySelector('.accessory-checkbox');
            const input = acc.querySelector('.accessory-quantity');

            const event = (DeviceDetection.isTouchDevice()) ? 'touchstart' : 'click';

            if (upBtn) {
                upBtn.addEventListener(event, me._quantityUp.bind(me), false);
            }
            if (dnBtn) {
                dnBtn.addEventListener(event, me._quantityDown.bind(me), false);
            }
            if (checkbox) {
                let preselectedCheckboxes = false;
                if (checkbox && checkbox.dataset && checkbox.dataset.preselectedCheckboxes) {
                    preselectedCheckboxes = checkbox.dataset.preselectedCheckboxes;
                }
                checkbox.checked = preselectedCheckboxes;
                input.value = 0;
                checkbox.addEventListener('change', me._toggleAccessory.bind(me));
            }

            const eventChange = 'change';
            //only register input form if it is forced
            if (!DomAccess.querySelector(acc, '.accessory-quantity').hasAttribute('.unforce')) {
                const accQuantity = DomAccess.querySelector(acc, '.accessory-quantity');
                accQuantity.addEventListener(eventChange, me._quantityChange.bind(me), false);
            }
        });

        me._quantityChange(null);
    }

    _mainQuantityUp(me) {
        this._quantityChange(null)
    }

    _mainQuantityDown(me) {
        this._quantityChange(null)
    }

    _quantityUp(event) {
        const elemParent = event.target.closest('[data-huebert-add-accessory-config]');
        const quantityInput = elemParent.querySelector('.accessory-quantity');
        const options = JSON.parse(elemParent.getAttribute('data-huebert-add-accessory-config'));

        let maxClearanceSale = null;
        if (quantityInput.hasAttribute('max')) {
            maxClearanceSale = parseInt(quantityInput.getAttribute('max'), 10);
        }

        let accQuantity = 0;
        if (quantityInput.dataset.quantity) {
            accQuantity = parseInt(quantityInput.dataset.quantity)
        }

        if (parseInt(quantityInput.value, 10) === 0) {
            quantityInput.value = this._calcQuantity(quantityInput.value, true, options, maxClearanceSale);
        } else if (!isNaN(parseInt(quantityInput.value, 10))) {
            quantityInput.value = this._calcQuantity(quantityInput.value, true, options, maxClearanceSale);
        } else {
            quantityInput.value = parseInt(options.accessory.minPurchase, 10);
        }

        let sameQuantity = null;
        if (elemParent.querySelector('.accessory-quantity-as-main')) {
            sameQuantity = elemParent.querySelector('.accessory-quantity-as-main').value;
        }

        // ON CHECKBOX CHECKED SAME QUANTIY AS MAIN PRODUCT
        if (sameQuantity) {
            let mainProductQuantity = 1;
            if (elemParent.querySelector('.product-detail-quantity-input')) {
                mainProductQuantity = elemParent.querySelector('.product-detail-quantity-input').value;
            }
            quantityInput.value = mainProductQuantity;
        }
        this.getTotalPrice()
        // const ev = document.createEvent('Event');
        // ev.initEvent('change', true, false);
        // quantityInput.dispatchEvent(ev);
    }

    _quantityDown(event) {
        const elemParent = event.target.closest('[data-huebert-add-accessory-config]');
        const quantityInput = elemParent.querySelector('.accessory-quantity');
        const options = JSON.parse(elemParent.getAttribute('data-huebert-add-accessory-config'));

        if (parseInt(quantityInput.value, 10) === 0) {
            quantityInput.value = this._calcQuantity(quantityInput.value, false, options);
        } else if (!isNaN(parseInt(quantityInput.value, 10))) {
            quantityInput.value = this._calcQuantity(quantityInput.value, false, options);
        } else {
            quantityInput.value = '0';
        }
        this.getTotalPrice()
        // const ev = document.createEvent('Event');
        // ev.initEvent('change', true, false);
        // quantityInput.dispatchEvent(ev);
    }

    _toggleAccessory(event) {
        if (event.target.checked) {
            this._quantityUp(event);
        } else {
            this._quantityDown(event);
        }
    }

    async _quantityChange(event) {
        const pPrice = document.getElementsByClassName('hueb-accessories_total-wrapper');
        const totalPrice = document.getElementById('hueb-accessories_total-price');

        let accordions = document.querySelectorAll('.huebert-direct-accessories-block');

        Array.from(accordions).forEach(function (accordion) {

            let sameQuantity = null;
            if (accordion.querySelector('.same-quantity-input')) {
                sameQuantity = accordion.querySelector('.same-quantity-input').value;
            }


            const maxQuantityBundle = accordion.querySelectorAll('.hueb-bundle-quantity .accessory-quantity');
            let mainQuantity = document.querySelector('.product-detail-quantity-input');
            if (mainQuantity) {
                mainQuantity = parseInt(mainQuantity.value, 10) || 1;
            } else {
                mainQuantity = 1;
            }
            Array.from(maxQuantityBundle).every(item => {
                if (item) {
                    let currentBundleQuantity = item.value;
                    let totalBundleQuantity = currentBundleQuantity * mainQuantity;
                    let maxBundleQuantity = item.getAttribute('max');
                    let addToCartButton = document.querySelector('.btn-buy');

                    if (maxBundleQuantity && totalBundleQuantity > maxBundleQuantity) {
                        addToCartButton.disabled = true;
                        return false;
                    } else {
                        addToCartButton.disabled = false;
                    }

                    return true;
                }

            });


            if (sameQuantity) {
                // Timeout because from 6.5 version quantity is not initialized yet
                setTimeout(() => {
                    let selectedQuantity = document.querySelector('input.js-quantity-selector').value;
                    if (accordion.querySelectorAll('.same-quantity-as-main')) {
                        let sameQuantityForAll = accordion.querySelectorAll('.same-quantity-as-main')
                        sameQuantityForAll.forEach(item => {
                            item.textContent = selectedQuantity
                        })
                    }
                    if (accordion.querySelectorAll('.same-quantity-input')) {
                        let sameQuantityForAll = accordion.querySelectorAll('.same-quantity-input')
                        sameQuantityForAll.forEach(item => {
                            item.value = selectedQuantity
                        })
                    }
                }, "300");
            }

            //ON SELECT WHEN CHECKBOX IS ACTIVATED
            let sameQuantityDropdown = null;
            if (accordion.querySelector('.accessory-quantity-as-main')) {
                sameQuantityDropdown = accordion.querySelector('.accessory-quantity-as-main').value;
            }
            if (sameQuantityDropdown) {
                if (accordion.querySelectorAll('.accessory-quantity')) {
                    let mainProductQuantity = 0;
                    if (document.querySelector('.product-detail-quantity-input')) {
                        mainProductQuantity = document.querySelector('.product-detail-quantity-input').value;
                    }
                    let accessoryQuantity = accordion.querySelectorAll('.accessory-quantity')
                    accessoryQuantity.forEach(item => {
                        let next = item.nextElementSibling;
                        if (next && next.checked) {
                            item.value = mainProductQuantity
                        } else {
                            item.value = 0
                        }
                    })
                }
            }
        });

        if (totalPrice) {
            let mainPrice = parseFloat(document.querySelector('input[data-hueb-price]').getAttribute('data-hueb-price'));
            let mainQuantity = document.querySelector('.product-detail-quantity-input');
            if (mainQuantity) {
                mainQuantity = parseInt(mainQuantity.value, 10) || 1;
            } else {
                mainQuantity = 1;
            }

            //check for step prices
            if (document.querySelector('.product-block-prices') !== null) {
                mainPrice = this._getPriceBrackets(mainQuantity);
            }
            const initPrice = Math.round(mainPrice * mainQuantity * 100) / 100;
            let valuePrice = initPrice;
            const accs = this._getAccessories(this.el);
            Array.from(accs).forEach(function (acc) {
                const accessoryPrice = parseFloat(DomAccess.querySelector(acc, '[data-huebert-pricing]').getAttribute('data-huebert-pricing'));
                const quantityField = DomAccess.querySelector(acc, '.accessory-quantity');

                let accessoryQuantity = parseInt(quantityField.value) || 0;
                if (accessoryQuantity > 0) {
                    if (quantityField.dataset.quantity) {
                        accessoryQuantity = parseInt(quantityField.dataset.quantity)
                    }
                }

                valuePrice += Math.round(accessoryPrice * accessoryQuantity * 100) / 100;
            });
            const locale = document.documentElement.getAttribute('lang');
            const currency = document.querySelector('input[data-hueb-price-currency]').getAttribute('data-hueb-price-currency');
            const formatter = new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: currency
            });
            totalPrice.innerHTML = formatter.format(valuePrice);

            if (initPrice === valuePrice && pPrice || isNaN(valuePrice)) {
                pPrice[0].style = "display:none";
            } else if (pPrice) {
                pPrice[0].style = "display:inherit";
            }
        }
    }

    getTotalPrice() {
        const pPrice = document.getElementsByClassName('hueb-accessories_total-wrapper');
        const totalPrice = document.getElementById('hueb-accessories_total-price');
        if (totalPrice) {
            let mainPrice = parseFloat(document.querySelector('input[data-hueb-price]').getAttribute('data-hueb-price'));
            let mainQuantity = document.querySelector('.product-detail-quantity-input');
            if (mainQuantity) {
                mainQuantity = parseInt(mainQuantity.value, 10) || 1;
            } else {
                mainQuantity = 1;
            }

            //check for step prices
            if (document.querySelector('.product-block-prices') !== null) {
                mainPrice = this._getPriceBrackets(mainQuantity);
            }
            const initPrice = Math.round(mainPrice * mainQuantity * 100) / 100;
            let valuePrice = initPrice;
            const accs = this._getAccessories(this.el);
            Array.from(accs).forEach(function (acc) {
                const accessoryPrice = parseFloat(DomAccess.querySelector(acc, '[data-huebert-pricing]').getAttribute('data-huebert-pricing'));
                const quantityField = DomAccess.querySelector(acc, '.accessory-quantity');
                let accessoryQuantity = parseInt(quantityField.value) || 0;
                if (accessoryQuantity > 0) {
                    if (quantityField.dataset.quantity) {
                        accessoryQuantity = parseInt(quantityField.dataset.quantity)
                    }
                }
                valuePrice += Math.round(accessoryPrice * accessoryQuantity * 100) / 100;
            });
            const locale = document.documentElement.getAttribute('lang');
            const currency = document.querySelector('input[data-hueb-price-currency]').getAttribute('data-hueb-price-currency');
            const formatter = new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: currency
            });
            totalPrice.innerHTML = formatter.format(valuePrice);

            if (initPrice === valuePrice && pPrice || isNaN(valuePrice)) {
                pPrice[0].style = "display:none";
            } else if (pPrice) {
                pPrice[0].style = "display:inherit";
            }
        }
    }

    async _formSubmit(event) {
        event.preventDefault();

        const formData = event.detail;
        const elements = this._getAccessories(this.el);
        let validData = false;

        let mainProductId = null;

        if (this.isNeonConfiguratorInstalled) {
            mainProductId = document.querySelector(this.options.mainProductId).value;
            // const mainProductId = this.mainProductId;
        } else {
            let mainProductIdString = document.querySelector('.product-detail-form-container .buy-widget-container select')?.getAttribute("name");
            if (!mainProductIdString) {
                mainProductIdString = document.querySelector('.product-detail-form-container .buy-widget-container input').getAttribute("name");
            }
            const mainProductIdMatch = mainProductIdString.match(/lineItems\[(.+?)\]/);
            mainProductId = mainProductIdMatch[1];
        }

        let mainQuantity = document.querySelector('.product-detail-quantity-input');
        if (mainQuantity) {
            mainQuantity = parseInt(mainQuantity.value, 10) || 1;
        } else {
            mainQuantity = 1;
        }

        let quantityOverMaxClearanceSale = false;
        Array.from(elements).every(function (elem) {
            const quantityField = DomAccess.querySelector(elem, '.accessory-quantity');
            let quantity = quantityField.value;

            if (quantity > 0) {
                if (quantityField.dataset.quantity) {
                    quantity = quantityField.dataset.quantity
                }
            }

            let maxClearanceSale = null;
            if (quantityField.hasAttribute('max')) {
                maxClearanceSale = parseInt(quantityField.getAttribute('max'), 10);
            }

            let accessoryQuantity = quantity * mainQuantity;
            if (maxClearanceSale && accessoryQuantity > maxClearanceSale) {

                quantityOverMaxClearanceSale = true;
                return false;
            }
            return true;
        });

        if (!quantityOverMaxClearanceSale) {
            Array.from(elements).forEach(function (elem) {

                let quantity;
                const quantityField = DomAccess.querySelector(elem, '.accessory-quantity');
                quantity = quantityField.value;

                if (quantity > 0) {
                    if (quantityField.dataset.quantity) {
                        quantity = quantityField.dataset.quantity
                    }
                }

                const sameAndSelectableEl = elem.querySelector('.same-and-selectable')
                if (sameAndSelectableEl && sameAndSelectableEl.value) {
                    quantity = mainQuantity
                }

                let id = elem.getAttribute('data-product-id');
                let generatedVariant = DomAccess.querySelector(elem, '.generated-variant');
                const readonly = DomAccess.querySelector(elem, '.accessory-quantity').hasAttribute('readonly');
                const crossId = elem.getAttribute('data-cross-selling-id');
                const bundle = elem.classList.contains('huebert-bundling');
                const unforce = DomAccess.querySelector(elem, '.accessory-quantity').classList.contains('unforce');
                //temporary solution for fixed bundles
                if (bundle && unforce) {
                    if (DomAccess.querySelector(elem, '.accessory-quantity').checked) {
                        quantity = '1;'
                    } else {
                        quantity = '0';
                    }
                } else if (bundle && !unforce) {
                    quantity = '1';
                }

                if (parseInt(quantity, 10) > 0) {
                    if (bundle) {
                        validData = true;
                        formData.append('lineItems[' + crossId + '][id]', crossId);
                        formData.append('lineItems[' + crossId + '][referencedId]', mainProductId);
                        formData.append('lineItems[' + crossId + '][type]', 'huebbundling');
                        //formData.append('lineItems['+crossId+'][quantity]', quantity);  -- implement quantity changer + options
                        formData.append('lineItems[' + crossId + '][quantity]', '1');
                    } else if (!readonly) {
                        if (generatedVariant !== null) {
                            if (generatedVariant.value !== "0" && generatedVariant.value !== "") {
                                id = generatedVariant.value;
                            }
                        }
                        validData = true;
                        //This was a check that was not adding variant product with stock 0 from cross selling
                        // if(stock.value !== 'true') {
                        formData.append('lineItems[' + id + '][id]', id);
                        formData.append('lineItems[' + id + '][referencedId]', id);
                        formData.append('lineItems[' + id + '][type]', 'product');
                        formData.append('lineItems[' + id + '][quantity]', quantity);
                        formData.append('lineItems[' + id + '][stackable]', '1');
                        formData.append('lineItems[' + id + '][removable]', '1');
                        // }
                    }
                }
            });
        } else {
            //remove main product from cart and add cart notice
            this._client.delete('/checkout/line-item/delete/' + mainProductId);

        }
        // const me = this;
        // me._quantityChange(null);
    }

    _calcQuantity(value, sum, options, maxClearanceSale = null) {
        const step = parseInt(options.accessory.purchaseSteps, 10);
        let max = parseInt(options.accessory.maxPurchase, 10);
        const min = parseInt(options.accessory.minPurchase, 10);

        if (maxClearanceSale) {
            max = parseInt(maxClearanceSale, 10);
        }

        const current = parseInt(value, 10);

        if (sum) {
            if (isNaN(current) || current <= 0) {
                return min;
            }
            if (current < max) {
                return current + (step * 1);
            }
            if (current >= max) {
                return max;
            }
        } else {
            if (current > 0 && current >= step) {
                return (current - step) * 1;
            } else {
                return 0;
            }

        }
    }

    _getAccessories(parentEl) {
        return parentEl.getElementsByClassName('huebert-accessory');
    }

    _logFormData(data) {
        for (var pair of data.entries()) {
            console.log(pair[0] + ' - ' + pair[1]);
        }
    }

    _getPriceBrackets(mainQuantity) {
        let priceBracket;
        let table = document.getElementsByClassName('product-block-prices-grid')[0];
        let rows = table.getElementsByTagName('tr');
        for (let i = 1; i < rows.length; i++) {
            let quantity = rows[i].getElementsByTagName("th");
            quantity = parseInt(quantity[0].textContent.match(/\d+/)[0]);

            if (mainQuantity <= quantity || i === rows.length - 1) {
                priceBracket = parseFloat(rows[i].querySelector('input[data-hueb-price]').getAttribute('data-hueb-price'));
                break;
            }
        }
        return priceBracket;
    }

    _sleep(duration) {
        return new Promise(resolve => setTimeout(resolve, duration));
    }

    _dataLength(data) {
        var i = 0;
        for (var value of data.entries()) {
            i++;
        }
        return i;
    }
}
