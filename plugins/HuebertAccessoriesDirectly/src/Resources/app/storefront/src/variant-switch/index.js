import Plugin from 'src/plugin-system/plugin.class';
import HttpClient from 'src/service/http-client.service';
import DomAccess from 'src/helper/dom-access.helper';

export default class HuebertVariant extends Plugin {



    init() {
        this._client = new HttpClient();
        this.switch = document.querySelectorAll('.huebert-variant-option');
        // this.generatedVariant = document.querySelector('.generated-variant');
        this._registerEvents();
    }

    _registerEvents() {
        const elements = this._getForms(this.el);
        this.getVariantId(elements);

    }

    getVariantId(elements) {
        Array.from(elements).forEach(function(elemt, index) {
            const switchVariant = DomAccess.querySelectorAll(elemt, '.huebert-variant-option')
            const locale = DomAccess.querySelector(elemt, '.current-language').value;
            let   availableStock = DomAccess.querySelector(elemt, '.current-availableStock').value
            let   minPurchase = DomAccess.querySelector(elemt, '.current-minPurchase').value
            let   deliveryTime = DomAccess.querySelector(elemt, '.current-deliveryTime').value
            let   isCloseout = DomAccess.querySelector(elemt, '.current-isCloseout').value
            let   restockTime = DomAccess.querySelector(elemt, '.current-restockTime').value
            let displayDelivery = DomAccess.querySelector(elemt, '.display-delivery').value

            let f = DomAccess.querySelector(elemt, '.first-d')
            let s = DomAccess.querySelector(elemt, '.second-d')
            let t = DomAccess.querySelector(elemt, '.third-d')
            if(availableStock >= minPurchase && deliveryTime){
                f.style.display = 'block'
                s.style.display = 'none'
                t.style.display = 'none'
                DomAccess.querySelector(elemt, '.stock-huebert').value = '';
            }else if (isCloseout && availableStock < minPurchase){
                f.style.display = 'none'
                s.style.display = 'block'
                t.style.display = 'none'
                DomAccess.querySelector(elemt, '.stock-huebert').value = 'true';
            }else if(availableStock < minPurchase && deliveryTime && restockTime){
                f.style.display = 'none'
                s.style.display = 'none'
                t.style.display = 'block'
                DomAccess.querySelector(elemt, '.stock-huebert').value = '';
            }
            let options = {};
            let productId = null;
            let locale1 = locale
            if(locale1){
                locale1 = locale.toLowerCase()
            }
            switchVariant.forEach((elem) => {

                if(elem.checked){
                    const optionId = elem.value;
                    const tempArr = elem.getAttribute("name").split("--");
                    options[tempArr[0]] = optionId;
                    productId = tempArr[1];

                }
                elem.addEventListener('click', function(){
                    const optionId = elem.value;
                    for(let key in options){
                        const tempArr = elem.getAttribute("name").split("--");
                        productId = tempArr[1];
                        if(key === tempArr[0] ){
                            options[tempArr[0]] = optionId;
                        }
                    }

                    const data = {
                        productId: productId,
                        options: options,
                        locale: locale1
                    }
                    this._client = new HttpClient();
                    this._client.post('/huebert/options', JSON.stringify(data), (response) => {
                        let data = JSON.parse(response);
                        let generatedVariant = DomAccess.querySelector(elemt, '.generated-variant');
                        const variantPrice = DomAccess.querySelector(elemt,'.variant-price-pricing');
                        if (data.deposit) {
                            const depositField = DomAccess.querySelector(elemt, '.product-detail-deposit');
                            if (depositField) {
                                depositField.innerHTML = data.deposit;
                            }
                        }

                        if(displayDelivery) {
                            if (data.product.availableStock >= data.product.minPurchase && data.product.deliveryTime) {
                                f.innerHTML = '<span class="delivery-status-indicator bg-success"></span> Sofort verfügbar, Lieferzeit ' + data.product.deliveryTime.name
                                f.style.display = 'block'
                                s.style.display = 'none'
                                t.style.display = 'none'
                                generatedVariant.value = data.productId;

                            } else if (data.product.isCloseout && data.product.availableStock < data.product.minPurchase) {
                                f.style.display = 'none'
                                s.style.display = 'block'
                                t.style.display = 'none'
                                generatedVariant.value = '0';
                                DomAccess.querySelector(elemt, '.stock-huebert').value = 'true';
                            } else if (data.product.availableStock < data.product.minPurchase && data.product.deliveryTime && data.product.restockTime) {
                                f.style.display = 'none'
                                s.style.display = 'none'
                                t.innerHTML = '<span class="delivery-status-indicator bg-warning"></span>\n Versandfertig in ' + data.product.restockTime + " Tagen, Lieferzeit " + data.product.deliveryTime.name
                                t.style.display = 'block'
                                generatedVariant.value = data.productId;
                                DomAccess.querySelector(elemt, '.stock-huebert').value = '';
                            } else {
                                f.style.display = 'none'
                                s.style.display = 'none'
                                t.style.display = 'none'
                                generatedVariant.value = data.productId;
                                DomAccess.querySelector(elemt, '.stock-huebert').value = '';
                            }
                        }else{
                            generatedVariant.value = data.productId;
                            DomAccess.querySelector(elemt, '.stock-huebert').value = '';
                        }
                        const accesoryParent = elemt.closest('.huebert-accessory');
                        const accessoryItemQty = DomAccess.querySelector(accesoryParent,'.huebert-accessory_input-wrapper');
                        accessoryItemQty.setAttribute("data-huebert-pricing",   data.product.calculatedPrice.totalPrice);

                        variantPrice.innerHTML = data.calculatedPrice
                        if(document.querySelector('.huebert-unit')) {
                            const unit = DomAccess.querySelectorAll(elemt, '.huebert-unit')
                            const unitLabel = DomAccess.querySelectorAll(elemt, '.huebert-unit-label')
                            if (data.unitInfo) {
                                let unitInfo = '';
                                if (data.unitInfo) {
                                    unitInfo = data.unitInfo;
                                }
                                let referencePricing = '';
                                if (data.referencePricing != null) {
                                    referencePricing= data.referencePricing
                                }

                                unit[0].innerHTML = unitInfo + ' ' + referencePricing
                                unit[0].style.display = "inline"
                                unitLabel[0].style.display = "inline"

                                if (unit.length > 1) {
                                    unit[1].style.display = "none"
                                    unitLabel[1].style.display = "none"
                                }
                            } else {
                                unit[0].style.display = "none"
                                unitLabel[0].style.display = "none"
                                if (unit.length > 1) {
                                    unit[1].style.display = "none"
                                    unitLabel[1].style.display = "none"
                                }
                            }
                        }
                    });

                });
            });
        })
    }


    _getForms(parentEl){
        return parentEl.getElementsByClassName('huebert-variant-configurator');
    }

    checkDeliveryInfo(availableStock, minPurchase, deliveryTime, isCloseout, restockTime, f, s, t){
            if(availableStock >= minPurchase && deliveryTime){
                f.style.display = 'block'
                s.style.display = 'none'
                t.style.display = 'none'
                DomAccess.querySelector(elemt, '.stock-huebert').value = 'true';
            }else if (isCloseout &&  availableStock < minPurchase){
                f.style.display = 'none'
                s.style.display = 'block'
                t.style.display = 'none'
                DomAccess.querySelector(elemt, '.stock-huebert').value = 'true';
            }else if(availableStock < minPurchase && deliveryTime && restockTime){
                f.style.display = 'none'
                s.style.display = 'none'
                t.style.display = 'block'
                DomAccess.querySelector(elemt, '.stock-huebert').value = 'true';
            }else{
                f.style.display = 'none'
                s.style.display = 'none'
                t.style.display = 'none'
                DomAccess.querySelector(elemt, '.stock-huebert').value = '';
            }

            return true;
    }


}
