.huebert-direct-accessories-block{
    .accessoryBtn{
        float: left;
        padding: 0 10px;
        line-height: 33px;
    }
    .icon{
        margin-top: -8px;
    }
    .input-group-text{
        border: none;
        background: transparent;
    }
    .form-control{
        text-align: center;
    }

    .huebert-accessory{
        button > *{
            pointer-events: none;
        }

        .huebert-accessory-hover {
            display: none;
            position: absolute;
            z-index: 9999;
            padding: 5px;
            margin-top: -10%;
            left: -215px;
            width: 200px;
            text-align: center;
            background-color: $sw-background-color;
        }

        @include hover {
            .huebert-accessory-hover {
                display: block;
            }
        }
    }

    .huebert-accessory_pricing{
        font-size: inherit;
        margin-top: unset;
    }

    .hueb-accessories_price{
        font-weight: bold;
        font-size: 1.5rem;
    }

    .hueb-accessories_total-price{
        font-weight: bold;
    }

    .btn-accordion{
        font-weight: $font-weight-bold;
        color: $link-color;
        text-decoration: none;

        @include hover {
            color: $link-hover-color;
            text-decoration: none;
        }

        &:focus,
        &.focus {
            text-decoration: none;
            box-shadow: none;
        }

        &:disabled,
        &.disabled {
            color: $btn-link-disabled-color;
            pointer-events: none;
            .icon-arrow-head-up {
                display: block;
            }
        }
    }

    .collapsed .icon-arrow-head-up{
        display: none;
    }
    .icon-arrow-head-up {
        display: inline-flex;
    }
    .collapsed .icon-arrow-head-down{
        display: inline-flex;
    }
    .icon-arrow-head-down {
        display: none;
    }

    .card {
        overflow: inherit;
    }
}

.product-detail-price-container{
    .hueb-accesories_price{
        font-weight: $font-weight-bold;
    }
}

.product-detail-form-container{
    .hueb-accesories_price{
        color: $price-color;
        font-weight: $font-weight-bold;
        @include font-size(28);
    }
    .huebert-accessory_variants{
        min-height: inherit;
    }
    .huebert-acessory_deliveryTime{
        margin-top: 0.5rem;
        margin-bottom: 0;
    }
    .delivery-information{
        margin-bottom: inherit;
    }
}

.hueb-accessories_total-wrapper{
    display: none;
}
