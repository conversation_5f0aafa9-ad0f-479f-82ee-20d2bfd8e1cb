@media only screen and (max-width : 400px) {
    .quickview-minimal-product {
        .quickview-minimal-product-manufacturer-logo {
            width: auto;
            max-width: 12rem;
            max-height: 6rem;
        }
    }
}

@media only screen and (max-width: 700px) {
    .huebert-accessory .product-configurator-variants .varinats-container .variant-price,
    .huebert-accessory-tags .product-configurator-variants .varinats-container .variant-price {
        position:absolute;
        top: 13px;
        right: 0;
        width: 140px;
    }

    .huebert-accessory .product-configurator-variants .varinats-container .without-image,
    .huebert-accessory-tags .product-configurator-variants .varinats-container .without-image {
        right: 0;
    }

    .huebert-accessory  .huebert-unit-label, .huebert-accessory  .product-detail-price-unit,
    .huebert-accessory-tags .huebert-unit-label, .huebert-accessory-tags .product-detail-price-unit {
        text-align: right;
        padding-right: 10px;
    }
}

.huebert-direct-accessories-block {
    position: relative;

    @media only screen and (min-width: 992px) {

        .thumbnails-view{
            position: absolute;
            right: 0;
        }
    }


    .accessoryBtn{
        float: left;
        padding: 0 10px;
        line-height: 33px;
    }
    .icon{
        margin-top: -8px;
    }
    .input-group-text{
        border: none;
        background: transparent;
    }
    .form-control{
        text-align: center;
    }

    .huebert-accessory_input-wrapper {
        min-width: 9.5em;
        max-width: 11em;
        z-index: 10;
        margin-left: 10px;

        @include media-breakpoint-up(lg) {
            min-width: unset;
            max-width: unset;
            width: 100%;
            flex-wrap: nowrap;
        }

        @media only screen and (max-width : 400px) {
            margin-top: 0.75rem;
            margin-bottom: 1.25rem;
            &.huebert-checkbox{
                height: 100%;
                margin-top: 0;
                margin-bottom: 0;
            }
        }
    }

    .checkbox-mobile-wrapper {
        .huebert-accessory_input-wrapper.huebert-checkbox > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            @include media-breakpoint-up(md) {
                margin-left: 35px;
                margin-top: 14px;
            }
        }
    }

    .themeware .huebert-accessory_input-wrapper.input-group {
        font-size: 0.875rem !important;
    }

    .huebert-accessory, .huebert-accessory-tags {
        position: relative;

        button > *{
            pointer-events: none;
        }

        .hue-title-price{
            margin-top: 0.5rem;
            font-size: 1rem;
            .product-price.huebert-accessory_pricing{
                margin-top: 1rem;
            }
        }

        .huebert-accessory-hover {
            display: none;
            position: absolute;
            z-index: 9999;
            padding: 5px;
            margin-top: -10%;
            left: -215px;
            width: 200px;
            text-align: center;
            background-color: $sw-background-color;
        }

        .viewport-small .product-image {
            padding-left: 20px;
            margin-bottom: 20px;
        }

        .product-variant-characteristics .product-variant-characteristics-text{
            height: auto;
        }

        //@include hover {
        //    .huebert-accessory-hover {
        //        display: block;
        //    }
        //}
    }

    .huebert-accessory_pricing {
        font-size: inherit;
        margin-top: unset;
        font-size: 0.9rem;
        @media only screen and (max-width : 500px) {
            text-align: right;
            margin-top: 0.2rem;
            //font-size: 0.8rem;
            padding-right: 10px;
        }
    }

    .hueb-accessories_price{
        font-weight: bold;
        font-size: 1.5rem;
    }

    .btn-accordion {
        font-weight: $font-weight-bold;
        color: $link-color;
        text-decoration: none;

        //@include hover {
        //    color: $link-hover-color;
        //    text-decoration: none;
        //}

        &:focus,
        &.focus {
            text-decoration: none;
            box-shadow: none;
        }

        &:disabled,
        &.disabled {
            color: $btn-link-disabled-color;
            pointer-events: none;
            .icon-arrow-head-up {
                display: block;
            }
        }
    }

    .collapsed .icon-arrow-head-up {
        display: none;
    }
    .icon-arrow-head-up {
        display: inline-flex;
    }
    .collapsed .icon-arrow-head-down {
        display: inline-flex;
    }
    .icon-arrow-head-down {
        display: none;
    }

    .card, .accordion-item {
        overflow: inherit;
    }


    .viewport-default {
        display:none;
    }
    .no-pad{
        padding: 0;
    }

    @media only screen
    and (min-width : 992px) {
        .viewport-default{
            display:block;
        }
        .viewport-small{
            display:none;
        }
    }

    @media only screen
    and (min-width : 992px) and (max-width : 1024px) {
        .viewport-default.hue-title-price{
            padding-left: 14%;
        }
    }

    .accessory-checkbox {
        margin: auto;
        transform: scale(1.5);
        @media only screen and (min-width: 992px) {
            transform: scale(2);
            margin-top: 0.75em;
        }
    }
}

.product-detail-price-container {
    .hueb-accesories_price {
        font-weight: $font-weight-bold;
    }
}



.product-detail-form-container {
    .hueb-accesories_price {
        color: $price-color;
        font-weight: $font-weight-bold;
        @include font-size(28);
    }

    .huebert-accessory_variants {
        min-height: inherit;
    }

    .huebert-acessory_deliveryTime {
        margin-top: 0.5rem;
        margin-bottom: 0;
        font-size: 0.8rem;
    }

    .delivery-information{
        margin-bottom: inherit;
    }
}

.hueb-accessories_total-wrapper {
    display: none;
}

hueb-accessories-bundle-quantity {
    min-width: 3em;
}

.huebert-accessory, .huebert-accessory-tags {
    padding: 0.5rem;
    margin: 0.2rem;
    border-bottom: 1px solid #f8f8f8;
}

.hue-checkbox {
    .hue-price-wrapper {
        margin-left: 2.5rem;
        margin-top: 1rem;
        .huebert-accessory_pricing, .product-detail-price-unit{
            text-align: left;
        }
    }
}

@media only screen and (max-width : 992px) {
    .huebert-accessory .product-configurator-variants, .huebert-accessory-tags .product-configurator-variants {
        padding: 5%;
    }

    .huebert-accessory .row.my-2, .huebert-accessory-tags .row.my-2{
        position: relative;
    }
}

.no-pad{
    @media only screen and (max-width: 700px) {
        padding: 0;
        /* align-items: center; */
        justify-content: center;
        display: flex;
        flex-direction: column;
    }
}
@media only screen and (max-width: 991px) {
    .hue-checkbox {
        .checkbox-mobile-wrapper {
            padding: 0 0 0 20px;
            max-width: 92px;
            margin-top: -10px;
            margin-left: -40px;
        }

        .hue-product-title{
            //margin-left: 1rem;
            padding: 0;
            width: calc(100% - 92px);
            margin-bottom: 20px;
        }

        .product-configurator-variants {
            margin-top: -2.25rem;

            .variant-price.without-image {
                margin-top: -1rem;
                width: 20%;
                position: unset;
                margin-bottom: 1rem;

                .huebert-unit-label {
                    text-align: left;
                }
            }
        }

        .huebert-direct-accessories-block .huebert-accessory_pricing {
            margin-top: 10px;
        }

        .input-group.huebert-accessory_input-wrapper {
            .input-group {
                margin-top: 20px;
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .checkbox-mobile-wrapper {
        .huebert-accessory_input-wrapper.huebert-checkbox > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            margin-top: 14px;
            margin-left: 25px;
        }
    }

    .input-group.huebert-accessory_input-wrapper {
        .input-group {
            margin-top: 20px;
        }
    }
}

@media only screen and (max-width: 575px) {
    .hue-checkbox {
        .checkbox-mobile-wrapper {
            margin-top: -10px;
            margin-left: -13px;
            max-width: 63px;
        }

        .hue-product-title {
            margin-bottom: 30px;
        }

        .col-12 {
            width: 83.33333333%;
        }
    }

    .checkbox-mobile-wrapper {
        .huebert-accessory_input-wrapper.huebert-checkbox > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            margin-top: 14px;
            margin-left: 10px;
        }
    }

    .huebert-direct-accessories-block .huebert-accessory_input-wrapper {
        margin-left: 2px;
    }
}

.hue-accordion-btn {
    text-indent: 30px;

    &::after {
        position: absolute;
        left: 20px;
    }
}

@include media-breakpoint-down(md) {
    .left-space {
        margin-left: 100px;
    }

    .img-100 {
        width: 100px;
        max-width: 100px;
        margin-bottom: -78px;
    }

    .p-abs {
        position: absolute;
        right: 20px;
    }
}

.accessory-quantity::-webkit-outer-spin-button,
.accessory-quantity::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.accessory-quantity[type=number] {
    -moz-appearance: textfield;
}
.huebert-accessory, .huebert-accessory-tags {
    &:hover {
        .huebert-accessory-hover{
            display: block;
          } 
    }
}

.input-group.hueb-accessories-bundle-quantity {
    @include media-breakpoint-up(md) {
        margin-left: 30px;
    }
}