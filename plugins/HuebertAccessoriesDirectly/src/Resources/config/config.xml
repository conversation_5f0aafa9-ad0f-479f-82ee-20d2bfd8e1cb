<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>Basic Settings</title>
        <title lang="de-DE">Grundeinstellungen</title>
        <input-field type="bool">
            <name>alwaysShowAccessories</name>
            <label>Always show accessories</label>
            <label lang="de-DE">Zubehör immer ausgeklappt</label>
        </input-field>
        <input-field type="bool">
            <name>showDelivery</name>
            <label>Show Shopware delivery time</label>
            <label lang="de-DE">Shopware Lieferzeit anzeigen</label>
        </input-field>
        <input-field type="bool">
            <name>showHeadline</name>
            <label>Disable placeholder headline</label>
            <label lang="de-DE">Platzhalter Überschrift ausblenden</label>
        </input-field>
        <input-field type="bool">
            <name>showDescription</name>
            <label>Disable placeholder description</label>
            <label lang="de-DE">Platzhalter Beschreibung ausblenden</label>
        </input-field>
        <input-field type="single-select">
            <name>accessoryPosition</name>
            <options>
                <option>
                    <id>above</id>
                    <name>Above Buy Button</name>
                    <name lang="de-DE">Oberhalb Warenkorbbutton</name>
                </option>
                <option>
                    <id>below</id>
                    <name>Below Buy Button</name>
                    <name lang="de-DE">Unterhalb Warenkorbbutton</name>
                </option>
            </options>
            <defaultValue>above</defaultValue>
            <label>Position</label>
            <label lang="de-DE">Position</label>
        </input-field>
        <input-field type="single-select">
            <name>accessoryTotalPrice</name>
            <options>
                <option>
                    <id>deactivate</id>
                    <name>Deactivate</name>
                    <name lang="de-DE">Deaktivieren</name>
                </option>
                <option>
                    <id>aboveBuyButton</id>
                    <name>Above Buy Button</name>
                    <name lang="de-DE">Oberhalb Warenkorbbutton</name>
                </option>
                <option>
                    <id>belowPrice</id>
                    <name>Below Main Price</name>
                    <name lang="de-DE">Unterhalb Hauptpreis</name>
                </option>
            </options>
            <defaultValue>deactivate</defaultValue>
            <label>Show total price</label>
            <label lang="de-DE">Gesamtpreis anzeigen</label>
        </input-field>
    </card>
    <card>
        <title>Accessory View</title>
        <title lang="de-DE">Zubehöransicht</title>
        <input-field type="bool">
            <name>accessoryViewStatic</name>
            <label>Show thumbnail for accessories</label>
            <label lang="de-DE">Thumbnail für Zubehör statisch anzeigen</label>
            <helpText>Product picture is always visible on the right side of the accessory.</helpText>
            <helpText lang="de-DE">Produktbild ist immer rechts vom Zubehör sichtbar.</helpText>
        </input-field>
        <input-field type="bool">
            <name>accessoryViewHover</name>
            <label>Show thumbnail for accessories while hovering</label>
            <label lang="de-DE">Thumbnail für Zubehör beim Hovern anzeigen</label>
            <helpText>Product picture is visible while hovering above the accessory.</helpText>
            <helpText lang="de-DE">Produktbild ist beim Hovern über dem Zubehör sichtbar.</helpText>
        </input-field>
        <input-field type="single-select">
            <name>globalSelectableAmount</name>
            <options>
                <option>
                    <id>custom</id>
                    <name>As configured in cross selling</name>
                    <name lang="de-DE">Wie im Cross-Selling definiert</name>
                </option>
                <option>
                    <id>1</id>
                    <name>All products with selectable amount</name>
                    <name lang="de-DE">Alle Artikel mit Mengen-Auswahl</name>
                </option>
                <option>
                    <id>0</id>
                    <name>All products with checkboxes</name>
                    <name lang="de-DE">Alle Artikel mit Checkboxen</name>
                </option>
            </options>
            <defaultValue>custom</defaultValue>
            <label>Accessories selectable amount</label>
            <label lang="de-DE">Zubehör Mengen-Auswahl</label>
        </input-field>

        <input-field type="bool">
            <name>activateVariants</name>
            <label>Variants Selection via main article in accessories</label>
            <label lang="de-DE">Varianten Auswahl über Hauptartikel im Zubehör</label>
            <helpText>If activated, the varieties are not listed individually but are available as a selection in the main article.</helpText>
            <helpText lang="de-DE">Wenn aktiviert werden die Varainten nicht einzelnen aufgelistet sondern stehen beim Hauptartikel als Auswahl zur Verfügung.</helpText>
            <defaultValue>false</defaultValue>
        </input-field>


        <input-field type="bool">
            <name>showUnits</name>
            <label>Show product units</label>
            <label lang="de-DE">Maße und Verpackungs-Einheiten anzeigen</label>
            <helpText>Purchase units and units of measurement are displayed if they are stored for the product.</helpText>
            <helpText lang="de-DE">Verkaufseinheiten und Maßeinheiten werden angezeigt wenn diese beim Produkt hinterlegt sind.</helpText>
            <defaultValue>false</defaultValue>
        </input-field>

        <input-field type="bool">
            <name>preselectedSelectableAmount</name>
            <label>Checkboxes preselected</label>
            <label lang="de-DE">Checkboxen vorausgewählt</label>
            <helpText>"Amount selectable" must be disabled for the checkboxes to be enabled. Through this option the checkboxes are preselected.</helpText>
            <helpText lang="de-DE">"Anzahl auswählbar" muss deaktiviert sein, damit die Checkboxen aktiviert sind. Durch diese Option werden die Checkboxen vorausgewählt.</helpText>
            <defaultValue>false</defaultValue>
        </input-field>
    </card>

    <card>
        <title>Product tags to use articles only as links</title>
        <title lang="de-DE">Produkt-Tags um Artikel nur als Verlinkung zu verwenden</title>
        <component name="sw-entity-multi-id-select">
            <name>accessoryTagIds</name>
            <entity>tag</entity>
            <label>Product tag for articles that are linked to the article and cannot be added directly to the shopping cart.</label>
            <label lang="de-DE">Produkt-Tag für Produkte die nur zur Seite des Produkts verlinkt werden und nicht mit in den Warenkorb gelegt werden können.</label>
            <helpText>Items with one of these tags cannot be added directly to the shopping cart and are only used as a preview. The article can be clicked on and changes to the article detail page.</helpText>
            <helpText lang="de-DE">Artikel die einen dieser Tags hinterlegt haben, können nicht direkt in den Warenkorb gelegt werden und dienen nur als Vorschau. Der Artikel ist anklickbar und wechselt zur Artikel-Detailseite.</helpText>
        </component>
    </card>
</config>
