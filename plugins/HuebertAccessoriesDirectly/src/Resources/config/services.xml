<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Huebert\AccessoriesDirectly\Resources\snippet\de_DE\SnippetFile_de_DE" public="true">
            <tag name="shopware.snippet.file"/>
        </service>
        <service id="Hu<PERSON><PERSON>\AccessoriesDirectly\Resources\snippet\en_GB\SnippetFile_en_GB" public="true">
            <tag name="shopware.snippet.file"/>
        </service>
        <service id="Huebert\AccessoriesDirectly\Subscriber\ProductDetailSubscriber">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="plugin.repository"/>
            <argument type="service" id="sales_channel.product.repository" />
            <tag name="kernel.event_subscriber"/>
        </service>
        <service id="Huebert\AccessoriesDirectly\Subscriber\CrossSellingSubscriber">
            <argument type="service" id="property_group_option.repository"/>
            <argument type="service" id="Shopware\Storefront\Page\Product\Configurator\ProductPageConfiguratorLoader"/>

            <tag name="kernel.event_subscriber"/>
        </service>
        <service id="Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingExtension"
                 decorates="Shopware\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingDefinition" public="true">
            <call method="compile">
                <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\DefinitionInstanceRegistry"/>
            </call>
        </service>
        <service id="Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingExtensionHydrator"
                 decorates="Shopware\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingHydrator"
                 decoration-on-invalid="ignore"
                 public="true">
            <argument type="service" id="service_container"/>
        </service>
        <service id="Huebert\AccessoriesDirectly\Twig\LoadParentLanguageNameFunction">
            <argument type="service" id="product_cross_selling.repository"/>
            <tag name="twig.extension"/>
        </service>


        <service id="Huebert\AccessoriesDirectly\Controller\SelectOptionController" public="true">
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\FindVariant\FindProductVariantRoute"/>
            <argument type="service" id="sales_channel.product.repository" />
            <argument type="service" id="translator" />
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsExtension"
                 decorates="Shopware\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsDefinition" public="true">
            <call method="compile">
                <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\DefinitionInstanceRegistry"/>
            </call>
        </service>
        <service id="Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsExtensionHydrator"
                 decorates="Shopware\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsHydrator"
                 decoration-on-invalid="ignore"
                 public="true">
            <argument type="service" id="service_container"/>
        </service>
    </services>
</container>
