<?php declare(strict_types=1);

namespace <PERSON>ebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts;

use Shopware\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsHydrator;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;

/**
 * Class ProductCrossSellingAssignedProductsHydrator
 * @package Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts
 */
class ProductCrossSellingAssignedProductsExtensionHydrator extends ProductCrossSellingAssignedProductsHydrator
{
    protected function assign(EntityDefinition $definition, Entity $entity, string $root, array $row, Context $context): Entity
    {
        $entity = parent::assign( $definition, $entity,  $root, $row,  $context);
        if (isset($row[$root . '.accessoryQuantity'])) {
            $entity->accessoryQuantity = (int) $row[$root . '.accessoryQuantity'];
        }
        return $entity;
    }
}
