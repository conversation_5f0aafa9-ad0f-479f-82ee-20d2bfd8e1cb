<?php declare(strict_types=1);

namespace <PERSON>ebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts;

use Shopware\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IntField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

/**
 * Class ProductCrossSellingExtension
 * @package Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts
 */
class ProductCrossSellingAssignedProductsExtension extends ProductCrossSellingAssignedProductsDefinition
{
    protected function defineFields(): FieldCollection
    {
        $parentFields = parent::defineFields();
        $parentFields->add(
            (new IntField('accessory_quantity', 'accessoryQuantity'))
        );
        return $parentFields;
    }
}
