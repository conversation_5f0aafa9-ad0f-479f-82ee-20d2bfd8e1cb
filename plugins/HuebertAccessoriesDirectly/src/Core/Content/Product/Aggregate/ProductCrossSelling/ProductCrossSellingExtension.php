<?php declare(strict_types=1);

namespace <PERSON>ebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSelling;

use Shopware\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\BoolField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

/**
 * Class ProductCrossSellingExtension
 * @package Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSelling
 */
class ProductCrossSellingExtension extends ProductCrossSellingDefinition
{
    protected function defineFields(): FieldCollection
    {
        $parentFields = parent::defineFields();
        $parentFields->add(
            (new StringField('display', 'display'))
        );
        $parentFields->add(
            (new BoolField('selectable_amount', 'selectableAmount'))
        );
        $parentFields->add(
            new BoolField('same_quantity_as_main_product', 'sameQuantityAsMainProduct')
        );
        return $parentFields;
    }
}
