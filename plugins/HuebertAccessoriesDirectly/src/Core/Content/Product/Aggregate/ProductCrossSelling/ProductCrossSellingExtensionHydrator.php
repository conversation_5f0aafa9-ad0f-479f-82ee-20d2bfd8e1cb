<?php declare(strict_types=1);

namespace <PERSON>eb<PERSON>\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSelling;

use Shopware\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingHydrator;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;

/**
 * Class ProductCrossSellingExtensionHydrator
 * @package Huebert\AccessoriesDirectly\Core\Content\Product\Aggregate\ProductCrossSelling
 */
class ProductCrossSellingExtensionHydrator extends ProductCrossSellingHydrator
{
    protected function assign(EntityDefinition $definition, Entity $entity, string $root, array $row, Context $context): Entity
    {
        $entity = parent::assign( $definition, $entity,  $root, $row,  $context);
        if (isset($row[$root . '.display'])) {
            $entity->display = $row[$root . '.display'];
        }
        if (isset($row[$root . '.selectableAmount'])) {
            $entity->selectableAmount = (bool) $row[$root . '.selectableAmount'];
        }
        if (isset($row[$root . '.sameQuantityAsMainProduct'])) {
            $entity->sameQuantityAsMainProduct = (bool)$row[$root . '.sameQuantityAsMainProduct'];
        }
        return $entity;
    }
}
