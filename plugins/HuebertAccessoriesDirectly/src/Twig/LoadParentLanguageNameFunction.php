<?php declare(strict_types=1);

namespace <PERSON>ebert\AccessoriesDirectly\Twig;

use Shopware\Core\Defaults;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Shopware\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;

class LoadParentLanguageNameFunction extends AbstractExtension
{

    public function __construct(
        private readonly EntityRepository $crossSellingRepo
    ) {}

    public function getFunctions(): array
    {
        return [
            new TwigFunction('huebert_load_parent_language_name', [$this, 'loadParentLanguageName'])
        ];
    }

    public function loadParentLanguageName(ProductCrossSellingEntity $crossSelling, Context $context){
        $languageIdChain = $context->getLanguageIdChain();
        $rootLanguage = array_pop($languageIdChain);
        $rootLanguageContext = new Context($context->getSource(), [], Defaults::CURRENCY, [$rootLanguage]);
        $rootLanguageCrossSelling = $this->crossSellingRepo->search(new Criteria([$crossSelling->getId()]), $rootLanguageContext)->first();
        if(!$rootLanguageCrossSelling){
            return '';
        }
        return $rootLanguageCrossSelling->getName();
    }
}