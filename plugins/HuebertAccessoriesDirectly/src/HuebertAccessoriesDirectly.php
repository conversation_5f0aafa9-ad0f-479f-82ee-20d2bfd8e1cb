<?php declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AccessoriesDirectly;

use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Shopware\Core\Framework\Plugin\Context\UpdateContext;
use Shopware\Core\System\CustomField\CustomFieldTypes;
use Doctrine\DBAL\Connection;

class HuebertAccessoriesDirectly extends Plugin
{
    public function install(InstallContext $installContext): void
    {
        $this->createCustomFields($installContext->getContext());
        $this->createCustomFieldsHeadline($installContext->getContext());
        parent::install($installContext); // TODO: Change the autogenerated stub
    }
    public function uninstall(UninstallContext $uninstallContext): void
    {
        if ($uninstallContext->keepUserData()) {
            parent::uninstall($uninstallContext);
            return;
        }
        $this->removeCustomFields($uninstallContext->getContext());

        $connection = $this->container->get(Connection::class);
        if ($connection->executeQuery("SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'product_cross_selling' AND column_name = 'display';")->fetchOne()) {
            $connection->executeUpdate('ALTER TABLE `product_cross_selling` DROP COLUMN `display`');
        }
        if ($connection->executeQuery("SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'product_cross_selling' AND column_name = 'selectable_amount';")->fetchOne()) {
            $connection->executeUpdate('ALTER TABLE `product_cross_selling` DROP COLUMN `selectable_amount`');

        }
        if ($connection->executeQuery("SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'product_cross_selling' AND column_name = 'same_quantity_as_main_product';")->fetchOne()) {
            $connection->executeUpdate('ALTER TABLE `product_cross_selling` DROP COLUMN `same_quantity_as_main_product`');
        }
        if ($connection->executeQuery("SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = 'product_cross_selling_assigned_products' AND column_name = 'accessory_quantity';")->fetchOne()) {
            $connection->executeUpdate('ALTER TABLE `product_cross_selling_assigned_products` DROP COLUMN `accessory_quantity`');
        }

        parent::uninstall($uninstallContext);
    }

    public function update(UpdateContext $updateContext): void
    {
        $this->createCustomFieldsHeadline($updateContext->getContext());
        parent::update($updateContext); // TODO: Change the autogenerated stub
    }

    private function createCustomFields($context): void
    {
        $customFields = [
            [
                'name' => 'huebert_accessory_directly_custom_fields',
                'active' => true,
                'config' => [
                    'label' => [
                        'en-GB' => 'Acessory',
                        'de-DE' => 'Zubehör'
                    ],
                ],
                'customFields' => [
                    [
                        'name' => 'huebert_accessory_directly_customtext',
                        'type' => CustomFieldTypes::TEXT,
                        'config' => [
                            'label' => [
                                'en-GB' => 'Additional Text',
                                'de-DE' => 'Zusatztext',
                            ]
                        ]
                    ],
                ],
                'relations' => [
                    ['entityName' => 'product']
                ]
            ],
        ];
        $repo = $this->container->get('custom_field_set.repository');

        //check if entries exist to prevent re-installation error
        $customFieldSetEntry = false;
        $customFieldSetEntry = $repo->search((new Criteria())
            ->addFilter(
                new EqualsFilter('name', 'huebert_accessory_directly_custom_fields')
            ), $context)->first();

        if(!$customFieldSetEntry) {
            foreach ($customFields as $customFieldSet) {
                $repo->upsert([$customFieldSet], $context);
            }
        }

    }

    private function removeCustomFields($context): void
    {

        $cfsRepo = $this->container->get('custom_field_set.repository');
        $cfRepo = $this->container->get('custom_field.repository');
        $sRepo = $this->container->get('snippet.repository');
        $sysRepo = $this->container->get('system_config.repository');

        //delete custom_field_set entry
        $cfsId = $cfsRepo->search((new Criteria())
               ->addFilter(new EqualsFilter('name', 'huebert_accessory_directly_custom_fields')), $context
        )->first();

        $cfsRepo->delete([
            ['id' => $cfsId->getId()]
        ], $context);

        //delete custom_field entries
        $cfIds = $cfRepo->search((new Criteria())
             ->addFilter(
                 new EqualsFilter('customFieldSetId', $cfsId->getId())
             ), $context)->getIds();
        $ids = [];
        foreach($cfIds as $id){
            $ids[] = ['id' => $id];
        }
        $cfRepo->delete($ids, $context);

        //delete system_config entries
        $sysIds = $sysRepo->search((new Criteria())
            ->addFilter(
                new ContainsFilter('configurationKey', 'HuebertAccessoriesDirectly.config')
            ), $context)->getIds();
        $configIds = [];
        foreach($sysIds as $id){
            $configIds[] = ['id' => $id];
        }
        $sysRepo->delete($configIds, $context);

        // delete snippet entries
        $sIds = $sRepo->search((new Criteria())
            ->addFilter(
                new MultiFilter(
                    MultiFilter::CONNECTION_OR,
                    [
                        new ContainsFilter('translationKey', 'huebert_accessory_directly_customtext'),
                        new ContainsFilter('translationKey', 'huebert-acessories-directly')
                    ]
                )
            ), $context)->getIds();
        $snippetIds = [];
        foreach($sIds as $id){
            $snippetIds[] = ['id' => $id];
        }
        $sRepo->delete($snippetIds, $context);
    }

    private function createCustomFieldsHeadline($context): void
    {
        $repo = $this->container->get('custom_field_set.repository');
        $repoField = $this->container->get('custom_field.repository');

        //get current setId to add new custom fields to existing field Set
        $customFieldSetEntry = $repo->search((new Criteria())
            ->addFilter(
                new EqualsFilter('name', 'huebert_accessory_directly_custom_fields')
            ), $context)->first();

        //get current custom field ids to avoid upsert error for existing custom fields
        $customFieldTitle = $repoField->search((new Criteria())
            ->addFilter(
                new EqualsFilter('name', 'huebert_accessory_directly_title')
            ), $context)->first();
        $titleId = null;
        if($customFieldTitle) {
            $titleId = $customFieldTitle->get('id');
        }
        $customFieldSubtitle = $repoField->search((new Criteria())
            ->addFilter(
                new EqualsFilter('name', 'huebert_accessory_directly_subtitle')
            ), $context)->first();
        $subtitleId = null;
        if($customFieldSubtitle) {
            $subtitleId = $customFieldSubtitle->get('id');
        }
        $customFieldCustom = $repoField->search((new Criteria())
            ->addFilter(
                new EqualsFilter('name', 'huebert_accessory_directly_customtext')
            ), $context)->first();
        $customId = null;
        if($customFieldCustom) {
            $customId = $customFieldCustom->get('id');
        }
        $tagHeadingFieldCustom = $repoField->search((new Criteria())
            ->addFilter(
                new EqualsFilter('name', 'huebert_accessory_directly_tag_heading')
            ), $context)->first();
        $tagHeadingId = null;
        if($tagHeadingFieldCustom) {
            $tagHeadingId = $tagHeadingFieldCustom->get('id');
        }


        $customFields = [
            [
                'id' => $customFieldSetEntry->get('id'),
                'config' => [
                    'label' => [
                        'en-GB' => 'Accessory',
                        'de-DE' => 'Zubehör'
                    ],
                ],
                'customFields' => [
                    [
                        'id' => $titleId,
                        'name' => 'huebert_accessory_directly_title',
                        'type' => CustomFieldTypes::TEXT,
                        'config' => [
                            'label' => [
                                'en-GB' => 'Title Accessories',
                                'de-DE' => 'Überschrift Zubehör (Gilt für den Artikel, für welchen das Zubehör zugeordnet wird)',
                            ],
                            'customFieldPosition' => 1,
                            'helpText' => [
                                'en-GB' => 'Overrides the default title above the accessory selection',
                                'de-DE' => 'Überschreibt den Standart Titel der oberhalb der Zubehör-Auswahl angezeigt wird',
                            ]
                        ]
                    ],
                    [
                        'id' => $subtitleId,
                        'name' => 'huebert_accessory_directly_subtitle',
                        'type' => CustomFieldTypes::TEXT,
                        'config' => [
                            'label' => [
                                'en-GB' => 'Description Accessories',
                                'de-DE' => 'Beschreibung Zubehör (Gilt für den Artikel, für welchen das Zubehör zugeordnet wird)',
                            ],
                            'customFieldPosition' => 2,
                            'helpText' => [
                                'en-GB' => 'Overrides the default description above the accessory selection',
                                'de-DE' => 'Überschreibt die Standart Beschreibung die oberhalb der Zubehör-Auswahl angezeigt wird',
                            ]
                        ]
                    ],
                    [
                        'id' => $customId,
                        'type' => CustomFieldTypes::TEXT,
                        'name' => 'huebert_accessory_directly_customtext',
                        'config' => [
                            'label' => [
                                'en-GB' => 'Display Name Accessories',
                                'de-DE' => 'Anzeigename Zubehör (Gilt für den eigentlichen Zubehörartikel)',
                            ],
                            'customFieldPosition' => 3,
                            'helpText' => [
                                'en-GB' => 'Overrides the actual product title',
                                'de-DE' => 'Überschreibt den eigentlichen Produkttitel mit dem hier hinterlegten',
                            ]
                        ]
                    ],
                    [
                        'id' => $tagHeadingId,
                        'type' => CustomFieldTypes::TEXT,
                        'name' => 'huebert_accessory_directly_tag_heading',
                        'config' => [
                            'label' => [
                                'en-GB' => 'Link text for articles with tag (defined in plugin configuration)',
                                'de-DE' => 'Link-Text für Artikel mit Tag (Definiert in Plugin-Konfiguration)',
                            ],
                            'customFieldPosition' => 4,
                        ]
                    ]
                ],
            ],
        ];
        $repo->upsert($customFields,$context);
    }
}
