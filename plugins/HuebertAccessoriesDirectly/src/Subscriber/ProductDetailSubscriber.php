<?php declare(strict_types=1);

namespace <PERSON>ebert\AccessoriesDirectly\Subscriber;

use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Page\Product\ProductPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ProductDetailSubscriber implements EventSubscriberInterface
{

    public function __construct(
        private readonly SystemConfigService $systemConfigService,
        private readonly EntityRepository    $pluginRepo,
        protected SalesChannelRepository $productRepository,
    ) {}

    public static function getSubscribedEvents(): array
    {
        return [
            ProductPageLoadedEvent::class => "onDetailPageLoaded",

        ];
    }

    public function onDetailPageLoaded(ProductPageLoadedEvent $pageLoadedEvent): void
    {
        /** IF neon/configurator PLUGIN IS INSTALLED **/
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('composerName', 'neon/configurator'));
        $criteria->addFilter(new EqualsFilter('active', true));
        $isNeonConfiguratorPluginInstalled = $this->pluginRepo->search($criteria, $pageLoadedEvent->getContext())->first();
        $pageLoadedEvent->getPage()->assign(['isNeonConfiguratorPluginInstalled' => $isNeonConfiguratorPluginInstalled]);

        $accessoryTagIds = $this->systemConfigService->get('HuebertAccessoriesDirectly.config.accessoryTagIds');
        $pageLoadedEvent->getPage()->assign(['accessoryTagIds' => $accessoryTagIds]);

        if(array_key_exists('HuebertAccessoriesDirectly', $this->systemConfigService->all($pageLoadedEvent->getSalesChannelContext()->getSalesChannel()->getId()))){

            $config = $this->systemConfigService->all($pageLoadedEvent->getSalesChannelContext()->getSalesChannel()->getId())['HuebertAccessoriesDirectly']['config'];

            //legacy code
            $pageLoadedEvent->getPage()->assign(['acessoryOptions' => [
                'alwaysShow' => !empty($config['alwaysShowAccessories']),
                'accessoryPosition' => $config['accessoryPosition'],
                'activateVariants' => $config['activateVariants'],
                'showUnits' => $config['showUnits']
                ]
            ]);

            //assign HuebertAccessoriesDirectly config
            $pageLoadedEvent->getPage()->assign(['HuebertAccessoriesDirectly' => ['config' => $config]]);
        }
    }

}
