<?php declare(strict_types=1);

namespace <PERSON>ebert\AccessoriesDirectly\Subscriber;

use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Storefront\Page\Product\Configurator\ProductPageConfiguratorLoader;
use Shopware\Core\Content\Product\Events\ProductCrossSellingsLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class CrossSellingSubscriber implements EventSubscriberInterface
{

    public function __construct(
        protected EntityRepository $optionRepository,
        private readonly ProductPageConfiguratorLoader $configuratorLoader
    ) {}


    public static function getSubscribedEvents(): array
    {
        return [
            ProductCrossSellingsLoadedEvent::class => "onProductCrossSellingLoaded"
        ];
    }

    public function onProductCrossSellingLoaded(ProductCrossSellingsLoadedEvent $crossSellingLoadedEvent): void
    {
        $elements = $crossSellingLoadedEvent->getCrossSellings();
        foreach ($elements as $element) {

            $products = $element->getProducts();
            foreach ($products as $product) {
                $_productConfigurator = $this->configuratorLoader->load($product, $crossSellingLoadedEvent->getSalesChannelContext());

                if ($product->getOptionIds() && count($product->getVariation()) === 0) {

                    $parts = [];
                    foreach ($product->getOptionIds() as $optionId) {
                        $criteria = New Criteria();
                        $criteria
                            ->addFilter(new EqualsFilter('id', $optionId))
                            ->addAssociation('group');
                        $option = $this->optionRepository->search($criteria, $crossSellingLoadedEvent->getContext())->first();
                        if ($option) {
                            $groupName = null;
                            if ($option->getGroup()) {
                                if ($option->getGroup()->getTranslation('name')) {
                                    $groupName = $option->getGroup()->getTranslation('name');
                                }
                            }
                            $optionName = null;
                            if ($option->getTranslation('name')) {
                                $optionName = $option->getTranslation('name');
                            }
                            $parts[] = [
                                'group' => $groupName,
                                'option' => $optionName
                            ];
                        }
                    }
                    if (count($parts) > 0) {
                        $product->setVariation(
                            $parts
                        );
                    }

                }

                $product->assign(
                  [  'productConfigurator' => $_productConfigurator,]
                );
            }
        }
    }
}
