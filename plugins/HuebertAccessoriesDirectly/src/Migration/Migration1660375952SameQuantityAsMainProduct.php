<?php declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AccessoriesDirectly\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1660375952SameQuantityAsMainProduct extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1660375952;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling` ADD COLUMN `same_quantity_as_main_product` tinyint(1) default 0;");
    }

    /**
     * @throws Exception
     */
    public function updateDestructive(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling` DROP COLUMN IF EXISTS `same_quantity_as_main_product`;");
    }
}
