<?php declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AccessoriesDirectly\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1633212029AddSelectableAmountField extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1633212029;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling` ADD COLUMN `selectable_amount` TINYINT(1) NULL DEFAULT 1;");
    }

    /**
     * @throws Exception
     */
    public function updateDestructive(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling` DROP COLUMN IF EXISTS `selectable_amount`;");
    }
}
