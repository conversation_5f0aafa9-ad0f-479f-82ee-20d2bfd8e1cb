<?php declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AccessoriesDirectly\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1610295027HuebertAccessoriesDirectlyCrossExtension extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1610295027;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling` ADD COLUMN `display` varchar (20);");
    }

    /**
     * @throws Exception
     */
    public function updateDestructive(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling` DROP COLUMN IF EXISTS `display`;");
    }
}
