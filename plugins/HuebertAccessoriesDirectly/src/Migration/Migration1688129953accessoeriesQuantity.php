<?php declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AccessoriesDirectly\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1688129953accessoeriesQuantity extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1688129953;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling_assigned_products` ADD COLUMN `accessory_quantity` int default 0;");
    }

    /**
     * @throws Exception
     */
    public function updateDestructive(Connection $connection): void
    {
        $connection->executeStatement("ALTER TABLE `product_cross_selling_assigned_products` DROP COLUMN IF EXISTS `accessory_quantity`;");
    }
}
