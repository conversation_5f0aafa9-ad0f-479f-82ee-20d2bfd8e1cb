<?php declare(strict_types=1);

namespace <PERSON>ebert\AccessoriesDirectly\Controller;

use Shopware\Core\Content\Product\Exception\ProductNotFoundException;
use Shopware\Core\Content\Product\SalesChannel\FindVariant\FindProductVariantRoute;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class SelectOptionController extends StorefrontController
{

    public function __construct(
        private readonly FindProductVariantRoute $combinationFinder,
        private readonly SalesChannelRepository  $productRepository,
        private readonly TranslatorInterface     $translator
    ) {}

    #[Route(path: '/huebert/options', name: 'frontend.huebert.options', defaults: ['XmlHttpRequest' => true], methods: ['POST'])]
    public function switchOptions(Request $request, SalesChannelContext $salesChannelContext): JsonResponse
    {

        try {
            $switchedOption = $request->query->has('switched') ? (string) $request->query->get('switched') : null;
            $payload = (array)  json_decode($request->getContent());
            $options = (array) $payload['options'];
            $productId = $payload['productId'];
            $pricing = null;
            $languageName =  $payload['locale'];
            $request = new Request();
            $request->request->set('switchedGroup', $switchedOption);
            $request->request->set('options', $options);
            $product = null;
            $stock = null;
            $unitInfo = null;
            $referencePricing = null;
            $deposit = null;

            try {
                $redirect = $this->combinationFinder->load($productId, $request, $salesChannelContext);
                $productId = $redirect->getFoundCombination()->getVariantId();
                $criteria = new Criteria();
                $criteria->addFilter(new EqualsFilter('id', $productId));
                $product = $this->productRepository->search($criteria, $salesChannelContext)->first();
                $price = $product->getCalculatedPrices();
                $stock = $product->getStock();

                if(count((array)$price->getElements()) > 1) {

                    $priceFrom = $price->last();
                    $priceTo = $price->first();

                    if (property_exists($priceTo, 'unitPrice')) {
                        $loopPrice = $priceTo->getUnitPrice();

                        foreach ($price as $element) {
                            if ($element->getUnitPrice() <= $loopPrice) {
                                $priceFrom = $element;
                            } elseif ($element->getUnitPrice() > $loopPrice) {
                                $priceTo = $element;
                            }

                            $loopPrice = $element->getUnitPrice();
                        }

                        $languageName = strtolower($languageName);
                        if($languageName == 'deutsch' || $languageName == 'german') {
                            $pricing =  number_format($priceFrom->getUnitPrice(),2,',','.').' '.$salesChannelContext->getCurrency()->getSymbol(). " - " .number_format($priceTo->getUnitPrice(),2, ',','.').' '.$salesChannelContext->getCurrency()->getSymbol()."*";
                        }else {
                            $pricing = $salesChannelContext->getCurrency()->getSymbol() . number_format($priceFrom->getUnitPrice(), 2) ." - " . $salesChannelContext->getCurrency()->getSymbol() . number_format($priceTo->getUnitPrice(), 2, ) ."*";
                        }
                    }
                } elseif(count((array)$price->getElements()) == 0) {
                    $price = $product->getCalculatedPrice();
                    if($languageName == 'deutsch' || $languageName == 'german') {
                        $pricing = number_format($price->getUnitPrice(), 2, ',', '.') . ' ' . $salesChannelContext->getCurrency()->getSymbol() . "*";
                    }else{
                        $pricing = $salesChannelContext->getCurrency()->getSymbol() .number_format($price->getUnitPrice(), 2) . "*";

                    }
                }
                $purchaseUnit = $product->getPurchaseUnit();
                if($product->getUnit() != null && $purchaseUnit != null) {
                    $purchaseName = $product->getUnit()->getName();
                    $unitInfo = $purchaseUnit." ".$purchaseName;
                } else if ($purchaseUnit) {
                    $unitInfo = $purchaseUnit;
                }

                error_log(print_r(array('$purchaseUnit: ', $purchaseUnit), true) . "\n", 3, '../error.log');

                if (count($product->getCalculatedPrices()) > 1) {
                    $priceTo = $product->getCalculatedPrices()->first();
                    $priceFrom = $product->getCalculatedPrices()->last();
                    if ($priceFrom->getReferencePrice()->getPrice()) {
                        $referencePricing = '('.$priceFrom->getReferencePrice()->getPrice() . $salesChannelContext->getCurrency()->getSymbol() . ' - ' .
                        $priceTo->getReferencePrice()->getPrice() . $salesChannelContext->getCurrency()->getSymbol() . ' / ' .
                        $priceFrom->getReferencePrice()->getReferenceUnit() . ' ' . $priceFrom->getReferencePrice()->getUnitName() .')';
                    }
                } else if (count($product->getCalculatedPrices()) == 1) {
                    if ($product->getCalculatedPrices()->first()->getReferencePrice() && $product->getCalculatedPrices()->first()->getReferencePrice()->getPrice()) {
                        $referencePricing = '('.$product->getCalculatedPrices()->first()->getReferencePrice()->getPrice() . $salesChannelContext->getCurrency()->getSymbol() . ' / ' .
                        $product->getCalculatedPrices()->first()->getReferencePrice()->getReferenceUnit() . ' ' . $product->getCalculatedPrices()->first()->getReferencePrice()->getUnitName() .')';
                    } else {
                        $referencePricing = '('.$product->getCalculatedPrices()->first()->getUnitPrice() . $salesChannelContext->getCurrency()->getSymbol()  .')';
                        if($languageName == 'deutsch' || $languageName == 'german') {
                            $pricing = number_format($product->getCalculatedPrices()->first()->getUnitPrice(), 2, ',', '.') . ' ' . $salesChannelContext->getCurrency()->getSymbol() . "*";
                        }else{
                            $pricing = $salesChannelContext->getCurrency()->getSymbol() .number_format($product->getCalculatedPrices()->first()->getUnitPrice(), 2) . "*";
                        }
                    }
                } else {
                    if ($product->getCalculatedPrice()->getReferencePrice() && $product->getCalculatedPrice()->getReferencePrice()->getPrice()) {
                        $referencePricing = '('.$product->getCalculatedPrice()->getReferencePrice()->getPrice() . $salesChannelContext->getCurrency()->getSymbol() . ' / ' .
                        $product->getCalculatedPrice()->getReferencePrice()->getReferenceUnit() . ' ' . $product->getCalculatedPrice()->getReferencePrice()->getUnitName() .')';
                    }
                }

                if(isset($product->getCustomFields()['deposit']) && $product->getCustomFields()['deposit']) {
                    $deposit = $salesChannelContext->getCurrency()->getSymbol() . number_format((float)$product->getCustomFields()['deposit'], 2, '.', '');
                    $deposit = $this->translator->trans('FlowsiteDepositSystemBasic.plusDeposit', ['%deposit%' => $deposit]);
                }

            } catch (ProductNotFoundException $productNotFoundException) {
                //nth
            }

            return new JsonResponse(['product' => $product, 'productId' => $productId, 'calculatedPrice'=> $pricing, 'stock' => $stock, 'unitInfo' => $unitInfo, 'success' => true, 'referencePricing' => $referencePricing, 'deposit' => $deposit]);

        } catch (\Exception $exception) {
            return new JsonResponse(['success' => false]);
        }


    }

}
