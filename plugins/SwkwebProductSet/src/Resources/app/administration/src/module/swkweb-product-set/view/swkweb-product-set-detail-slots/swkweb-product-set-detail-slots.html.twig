{% block swkweb_product_set_detail_slots %}
    <div class="swkweb-product-set-detail-slots">
        <swkweb-product-set-slot-form
            v-for="productSetSlotAssignment in productSet.slotAssignments"
            :key="productSetSlotAssignment.id"
            :disallow-edit="!acl.can('swkweb_product_set.editor')"
            :product-set-slot="productSetSlotAssignment.slot"
            :product-set-slots-total="productSet.slotAssignments.length"
            :position="productSetSlotAssignment.position"
            :is-loading="loading.productSet"
            :product-set-slot-custom-field-sets="productSetSlotCustomFieldSets"
            :product-set-option-custom-field-sets="productSetOptionCustomFieldSets"
            @position-up="onProductSetSlotPositionUp(productSetSlotAssignment)"
            @position-down="onProductSetSlotPositionDown(productSetSlotAssignment)"
            @delete="onDeleteProductSetSlot(productSetSlotAssignment)"
        />

        <sw-card
            v-if="acl.can('swkweb_product_set.editor')"
            hero
            position-identifier="swkweb-product-set-detail-slots-add-slot"
        >
            <span v-tooltip="{ message: $t('swkweb-product-set.detail.tooltipAddSlotDisabledLanguage'), width: 'auto', disabled: isSystemLanguage }">
                <sw-button
                    variant="ghost"
                    :disabled="!isSystemLanguage"
                    @click="onAddProductSetSlot"
                >
                    {{ $t('swkweb-product-set.detail.buttonAddSlot') }}
                </sw-button>

                <div class="swkweb-product-set-detail-slots__separator">
                    {{ $t('swkweb-product-set.detail.separatorText') }}
                </div>

                <sw-entity-single-select
                    :value="null"
                    entity="swkweb_product_set_slot"
                    :criteria="productSetSlotPresetCriteria"
                    :placeholder="$t('swkweb-product-set.detail.selectAddSlotPresetPlaceholder')"
                    @change="onSelectProductSetSlotPreset"
                />
            </span>
        </sw-card>
    </div>
{% endblock %}
