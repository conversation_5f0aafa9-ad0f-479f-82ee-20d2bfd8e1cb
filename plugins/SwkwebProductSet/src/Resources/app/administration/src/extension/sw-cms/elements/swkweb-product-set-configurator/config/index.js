import template from './sw-cms-el-config-swkweb-product-set-configurator.html.twig';

const { Component, Mixin } = Shopware;

Component.register('sw-cms-el-config-swkweb-product-set-configurator', {
    template,

    mixins: [Mixin.getByName('cms-element')],

    computed: {
        pageType() {
            return this.cmsPageState?.currentPage?.type ?? null;
        },

        isProductPage() {
            return this.pageType === 'product_detail';
        },
    },

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            this.initElementConfig('swkweb-product-set-configurator');
        },

        onProductChange() {
            this.$emit('element-update', this.element);
        },
    },
});
