import DomAccess from 'src/helper/dom-access.helper';
import SwkwebProductSetSlot from '../product-set-slot.class';

export default class SwkwebProductSetSlotBaseClickToggle extends SwkwebProductSetSlot {
    /** @type {string} */
    _noTriggerSelector = '.option-quantity-select, .option-quantity-select option, .swkweb-product-set-option-variant-select, .swkweb-product-set-option-variant-select option';
    /** @type {string} */
    _nestedSetSelector = '.swkweb-product-set-option-nested-set';
    /** @type {NodeList} */
    _clickOptions;

    init() {
        this._clickOptions = DomAccess.querySelectorAll(this._el, '.swkweb-product-set-option');

        for (const optionNode of this._clickOptions) {
            if (!optionNode.attributes['value'] || !optionNode.attributes['value'].nodeValue) {
                console.warn('Option ID is missing');

                continue;
            }

            const optionId = optionNode.attributes['value'].nodeValue;
            const option = this._options.get(optionId);

            // TODO: Remove Option from node list, if configuration missing?
            if (option) {
                option._el.addEventListener('click', this._onClickOption.bind(this, optionId));

                if (option.quantity > 0) {
                    this._selectOption(option._id);
                }
            }
        }
    }

    /**
     * @access protected
     */
    _onClickOption(optionId, event) {
        if (!this.__isValidClickTarget(event.target)) {
            return;
        }

        if (this._isSelected(optionId)) {
            this._unselectOption(optionId);
        } else {
            this._selectOption(optionId);
        }

        this.getConfigurator().$emitter.publish('swkwebProductSet/onToggleSlotOptionClicked', {slot: this, optionId, event});
    }

    _selectOption(id) {
        super._selectOption(id);

        const option = this._options.get(id);
        if (option && option.isSelected) {
            option._el.classList.add('is-selected');
        }
    }

    _update() {
        super._update();

        const selectedOptionsCount = this.selectedOptionsCount;
        if (selectedOptionsCount >= this._maximumSelectedOptions) {
            this._el.classList.add('is-maximum-options-selected');
        } else {
            this._el.classList.remove('is-maximum-options-selected');
        }

        if (this._limitTotalOptions) {
            const selectableOptions = this._maximumSelectedOptions - selectedOptionsCount;
            for (const option of this._options.values()) {
                if (!option.isSelected && option.minimumQuantity > selectableOptions) {
                    option._el.classList.add('not-selectable');
                } else {
                    option._el.classList.remove('not-selectable');
                }

                if (option.quantitySelect) {
                    let selectableQuantities;
                    if (option.isSelected) {
                        selectableQuantities = selectableOptions + option.quantity;
                    } else {
                        selectableQuantities = selectableOptions;
                    }

                    for (const quantityOption of option.quantitySelect.options) {
                        if (quantityOption.value > selectableQuantities) {
                            quantityOption.disabled = true;
                        } else {
                            quantityOption.disabled = false;
                        }
                    }
                }
            }
        }

        this.getConfigurator().$emitter.publish('swkwebProductSet/onToggleSlotUpdated', {slot: this});
    }

    _unselectOption(id) {
        const option = this._options.get(id);

        super._unselectOption(id);

        if (option && !option.isSelected) {
            option._el.classList.remove('is-selected');
        }
    }

    /**
     * @access private
     * @param {HTMLElement} target
     * @returns {boolean}
     */
    __isValidClickTarget(target) {
        if (target.matches(this._noTriggerSelector)) {
            return false;
        }

        let parent = target;
        do {
            if (parent.matches(this._nestedSetSelector)) {
                return false;
            }
        } while (parent !== this._el && (parent = parent.parentNode));

        return true;
    }
}
