import SwkwebProductSetPreselection from './product-set-preselection.class';

export default class SwkwebProductSetPreselectionV1 extends SwkwebProductSetPreselection
{
    /**
     * @param {string} string
     * @returns {SwkwebProductSetPreselectionV1}
     */
    static createFromString(string) {
        return new this(JSON.parse(decodeURIComponent(string)));
    }

    /**
     * @returns {string}
     */
    toString() {
        return encodeURIComponent(JSON.stringify(this._configuration));
    }

    /**
     * @param {string} uuid
     * @returns {string}
     */
    static _transformUuid(uuid) {
        if (uuid.length !== 32) {
            throw new Error(`Invalid UUID: ${uuid}`);
        }

        return uuid.slice(0, 8);
    }
}
