{"extends": "stylelint-config-recommended-scss", "customSyntax": "postcss-scss", "plugins": ["stylelint-scss", "stylelint-prettier"], "rules": {"prettier/prettier": [true, {"printWidth": 100, "tabWidth": 4}], "property-no-unknown": [true, {"severity": "warning", "ignoreProperties": ["user-focus", "user-input", "user-modify"]}], "no-descending-specificity": null, "max-nesting-depth": [3, {"ignore": ["blockless-at-rules", "pseudo-classes"], "severity": "warning"}]}}