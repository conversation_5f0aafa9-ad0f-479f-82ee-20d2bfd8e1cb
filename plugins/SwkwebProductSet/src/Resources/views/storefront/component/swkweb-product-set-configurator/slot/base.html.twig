{% set modalDisplaySettings = productSetSlot.displaySettings.modal ?? 'none' %}
{% set showStatusBadge = not productSetSlot.displaySettings.hideStatusBadge %}
{% set slotOptional = productSetSlot.config.minimumSelectedOptions <= 0 %}
{% set slotValidByDefault = slotOptional or slotTemplate == 'dropdown' or slotTemplate == 'static' or slotTemplate == 'invisible' %}
{% set slotClasses = ['swkweb-product-set-slot', 'swkweb-product-set-slot-' ~ slotTemplate] %}
{% if slotValidByDefault %}
    {% set slotClasses = slotClasses|merge(['is-valid']) %}
{% else %}
    {% set slotClasses = slotClasses|merge(['is-invalid']) %}
{% endif %}

{% block swkweb_product_set_slot %}
    <div class="{{ slotClasses|join(' ') }}"
      data-swkweb-product-set-slot="true"
      data-swkweb-product-set-slot-id="{{ productSetSlotId }}"
      data-swkweb-product-set-slot-template="{{ slotTemplate }}"
      data-swkweb-product-set-slot-config="{{ productSetSlot.config|json_encode }}"
    >
        {% block swkweb_product_set_slot_inner %}
            {% block swkweb_product_set_slot_header_container %}
                {% sw_include '@Storefront/storefront/component/swkweb-product-set-configurator/slot/components/header.html.twig' %}
            {% endblock %}

            {% block swkweb_product_set_slot_content %}
                <div class="swkweb-product-set-slot-content{% if productSetSlot.displaySettings.accordion != 'none' %} collapse{% if productSetSlot.displaySettings.accordion == 'open' %} show{% endif %}{% endif %}"
                  {% if productSetSlot.displaySettings.accordion != 'none' %}
                    id="swkweb-product-set-slot-collapse-{{ slotCount }}"
                    aria-labelledby="swkweb-product-set-slot-collapse-{{ slotCount }}"
                  {% endif %}
                >
                    {% block swkweb_product_set_slot_options_container %}
                        <div class="swkweb-product-set-option-container">
                            {% block swkweb_product_set_slot_options %}
                                {% for productSetOption in productSetSlot.options %}
                                    {% block swkweb_product_set_slot_options_include %}
                                        {% sw_include '@SwkwebProductSet/storefront/component/swkweb-product-set-configurator/option/' ~ slotTemplate ~ '.html.twig' %}
                                    {% endblock %}
                                {% endfor %}
                            {% endblock %}
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}
        {% endblock %}
    </div>
{% endblock %}
