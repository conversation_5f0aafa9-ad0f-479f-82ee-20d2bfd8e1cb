<?php declare(strict_types=1);

namespace Swkweb\ProductSet\Core\Content\ProductSet\Cart\Error;

use Swkweb\ProductSet\Core\Content\ProductSet\Aggregate\ProductSetOption\SalesChannel\SalesChannelProductSetOptionEntity;

abstract class ProductSetOptionError extends ProductSetError
{
    public function __construct(
        string $lineItemId,
        string $path,
        protected SalesChannelProductSetOptionEntity $option,
    ) {
        parent::__construct($lineItemId, $path);
    }

    public function getParameters(): array
    {
        $parameters = parent::getParameters();

        $parameters['optionName'] = $this->option->getTranslation('name') ??
            (($this->option->getOptionProduct() !== null) ? $this->option->getOptionProduct()->getTranslation('name') : null);

        return $parameters;
    }
}
