<?php declare(strict_types=1);

namespace Swkweb\ProductSet\Core\Content\ProductSet\Cart\Error;

use Swkweb\ProductSet\Core\Content\ProductSet\Aggregate\ProductSetSlot\SalesChannel\SalesChannelProductSetSlotEntity;

abstract class ProductSetSlotError extends ProductSetError
{
    public function __construct(
        string $lineItemId,
        string $path,
        protected SalesChannelProductSetSlotEntity $slot,
    ) {
        parent::__construct($lineItemId, $path);
    }

    public function getParameters(): array
    {
        $parameters = parent::getParameters();

        $parameters['slotName'] = $this->slot->getTranslation('name');

        return $parameters;
    }
}
