<?php declare(strict_types=1);

namespace Swkweb\ProductSet\Core\Util\ExpressionCalculator\Operator;

use Swkweb\ProductSet\Core\Util\ExpressionCalculator\Struct\ExpressionContext;

class MinOperator extends FunctionOperator
{
    public function supports(string $string): bool
    {
        return mb_strtolower($string) === 'min';
    }

    protected function calculateValue(ExpressionContext $expressionContext, float ...$values): float
    {
        // TODO: Is the fallback value 0 reasonable?
        return min(...$values) ?: 0;
    }
}
