<?php declare(strict_types=1);

namespace Swkweb\ProductSet\Core\Checkout\Promotion\Cart\Discount\ScopePackager;

use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\LineItem\Group\LineItemQuantity;
use Shopware\Core\Checkout\Cart\LineItem\Group\LineItemQuantityCollection;
use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Cart\LineItem\LineItemCollection;
use Shopware\Core\Checkout\Promotion\Cart\Discount\DiscountLineItem;
use Shopware\Core\Checkout\Promotion\Cart\Discount\DiscountPackage;
use Shopware\Core\Checkout\Promotion\Cart\Discount\DiscountPackageCollection;
use Shopware\Core\Checkout\Promotion\Cart\Discount\DiscountPackager;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Swkweb\ProductSet\Core\Content\ProductSet\Cart\ProductSetCartProcessor;

class CartScopeDiscountPackager extends DiscountPackager
{
    public function __construct(private readonly DiscountPackager $coreDiscountPackager) {}

    public function getDecorated(): DiscountPackager
    {
        return $this->coreDiscountPackager;
    }

    public function getMatchingItems(DiscountLineItem $discount, Cart $cart, SalesChannelContext $context): DiscountPackageCollection
    {
        $discountPackages = $this->getDecorated()->getMatchingItems($discount, $cart, $context);
        $setDiscountPackage = $this->getSetDiscountPackage($cart->getLineItems()->filter(
            fn (LineItem $lineItem) => $lineItem->getType() === ProductSetCartProcessor::TYPE,
        ));

        if ($setDiscountPackage !== null) {
            $discountPackages->add($setDiscountPackage);
        }

        return $discountPackages;
    }

    private function getSetDiscountPackage(LineItemCollection $setItems): ?DiscountPackage
    {
        $discountItems = [];
        foreach ($setItems as $setItem) {
            for ($i = 1; $i <= $setItem->getQuantity(); $i++) {
                $discountItems[] = new LineItemQuantity($setItem->getId(), 1);
            }
        }

        if (count($discountItems) === 0) {
            return null;
        }

        return new DiscountPackage(
            new LineItemQuantityCollection($discountItems),
        );
    }
}
