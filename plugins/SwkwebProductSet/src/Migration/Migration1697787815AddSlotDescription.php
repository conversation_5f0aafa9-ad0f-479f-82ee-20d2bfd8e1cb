<?php declare(strict_types=1);

namespace Swkweb\ProductSet\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1697787815AddSlotDescription extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1697787815;
    }

    public function update(Connection $connection): void
    {
        $this->createSlotDescriptionColumn($connection);
    }

    public function updateDestructive(Connection $connection): void {}

    private function createSlotDescriptionColumn(Connection $connection): void
    {
        $sql = <<<'SQL'
            ALTER TABLE `swkweb_product_set_slot_translation` ADD `description` TEXT NULL;
            SQL;

        $connection->executeStatement($sql);
    }
}
