const ApiService = Shopware.Classes.ApiService;
const { Application } = Shopware;

class ApiClient extends ApiService {

    constructor(httpClient, loginService, apiEndpoint = 'pagespeed-media-scan') {
        super(httpClient, loginService, apiEndpoint);
    }

    check() {
        const headers = this.getBasicHeaders({});

        return this.httpClient
            .post(`_action/${this.getApiBasePath()}/run`, true, {
                headers
            })
            .then((response) => {
                return ApiService.handleResponse(response);
            });
    }

}

Application.addServiceProvider('weedesignPageSpeedMediaScan', (container) => {
    const initContainer = Application.getContainer('init');
    return new ApiClient(initContainer.httpClient, container.loginService);
});
