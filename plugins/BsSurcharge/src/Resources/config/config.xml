<?xml version="1.0" encoding="UTF-8"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/shopware/trunk/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>General Configuration</title>

        <input-field type="text">
            <name>excludeCustomerGroup</name>
            <label>Don't apply on customer group Id</label>
            <helpText>The surcharge will not apply on added customer group ID.
            </helpText>
        </input-field>
    </card>

    <card>
        <title>Minimum Order Value Surcharge Configuration</title>

        <input-field type="int">
            <name>surchargeApplyAmount</name>
            <label>Minimum Order Value (MOV)</label>
            <helpText>The surcharge will be added if the order value is less than this value. if leave it blank or add 0
                then the surcharge will not apply.
            </helpText>
            <defaultValue>0</defaultValue>
        </input-field>

        <input-field type="int">
            <name>surchargeAmount</name>
            <label>Surcharge Fees</label>
            <helpText>The surcharge will be this value for matched orders. if left blank then the surcharge value will
                be default 0.
            </helpText>
            <defaultValue>0</defaultValue>
        </input-field>

        <input-field type="text">
            <name>surchargeLabel</name>
            <label>Surcharge Label For Minimum Order Value</label>
            <helpText>The surcharge label added here will be shown in the name of the line item.</helpText>
            <defaultValue>Minimum ordreværdi tillæg</defaultValue>
        </input-field>

        <input-field type="bool">
            <name>surchargeShowVat</name>
            <label>Show VAT</label>
            <defaultValue>false</defaultValue>
        </input-field>
    </card>

    <card>
        <title>Payment Surcharge Configuration</title>

        <input-field type="int">
            <name>paymentSurchargeAmount</name>
            <label>Surcharge Fees For (EAN/Faktura)</label>
            <helpText>The surcharge will be for mentioned payment methods. if left blank then the surcharge value will
                be default 0.
            </helpText>
            <defaultValue>0</defaultValue>
        </input-field>

        <input-field type="text">
            <name>paymentSurchargeLabel</name>
            <label>Surcharge Label For Payment</label>
            <helpText>The surcharge label added here will be shown in the name of the line item.</helpText>
            <defaultValue>Betalingstillæg</defaultValue>
        </input-field>

        <input-field type="bool">
            <name>paymentSurchargeShowVat</name>
            <label>Show VAT</label>
            <defaultValue>false</defaultValue>
        </input-field>
    </card>

</config>
