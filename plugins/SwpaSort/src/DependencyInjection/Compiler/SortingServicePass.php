<?php declare(strict_types=1);

namespace Swpa\SwpaSort\DependencyInjection\Compiler;

use Swpa\SwpaSort\Service\CategoryProductService;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

class SortingServicePass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        if (!$container->has(CategoryProductService::class)) {
            return;
        }
        $definition = $container->findDefinition(CategoryProductService::class);
        $taggedServices = $container->findTaggedServiceIds('swpa.sort.sorting');
        foreach ($taggedServices as $id => $tags) {
            $definition->addMethodCall('addSortingService', [new Reference($id)]);
        }
    }
}
