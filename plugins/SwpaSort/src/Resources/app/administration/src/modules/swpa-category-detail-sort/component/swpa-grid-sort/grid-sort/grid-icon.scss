.swpa-grid-item {
    position: relative;
    margin: 2px;
    width: auto;
    border: 1px solid #D1D9E0;
    border-radius: 4px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.07);
    color: #777;
    background: #FFF;
    font-weight: 100;
    font-size: 12px;
    line-height: 1.5;
    text-align: center;
    transition: all 0.3s;
    display: flex;
    flex-wrap: wrap;
    min-height: 294px;
    cursor: move;
    &.is-group {
        box-shadow:0px 0px 0px 2px #D1D9E0 inset;
        padding: 2px;
    }
    &:hover {
        box-shadow: 0 1px 16px rgba(63,127,180,0.3), 0 2px 4px rgba(140,159,174,0.3);
        border-color: #afd3f1;
    }

    &.not-active {
        opacity: 0.7;
    }

    .item-delete-btn {
        display: block;
        position: absolute;
        width: 14px;
        height: 14px;
        right: 4px;
        top: 2px;
        z-index: 1000;
        cursor: pointer;
        .sw-icon {
            width: 14px;
            height: 14px;
            color:#52667A;
        }
        &:hover {
            .sw-icon{
                color:darkred;
            }
        }
    }

    .item-image {
        display: flex;
        flex-basis: 100%;
        width: 100%;
        min-height: 140px;
        margin-top: 20px;
        cursor: move;
        position: relative;
        img {
            user-select: none;
            -moz-user-select: none;
            -webkit-user-drag: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            border: 1px solid #ccc;
            cursor: move;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        .badge {
            position: absolute;
            left: 0;
            top: 0;
            padding: 1px 5px;
            border-radius: 0 4px 4px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.38);
            opacity: 0.75;
            z-index: 10;
            &.out-of-stock {
                background: red;
                color: white;
            }
            &.not-active {
                background: darkgrey;
                color: black;
            }
        }
    }
    .item-info {
        display: flex;
        flex-direction: column;
        margin: 10px 5px 10px 10px;
        justify-content: left;
        text-align: left;
        cursor: move;
        overflow: hidden;
        .item-name {
            font-weight: bold;
            cursor: move;
            height: 37px;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .item-number {
            font-weight: bold;
            font-style: italic;
            overflow-x: hidden;
            width: 90%;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .item-price {
            cursor: move;
        }
    }
    .item-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-basis: 100%;
        margin: auto 0 0;
        padding: 5px;
        background: #e2f2ff;
        width: 100%;
        cursor: default;
        a {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            width: 25px;
            height: 25px;
            border: 1px solid transparent;
            border-radius: 3px;
            background-color: #fff;
            background-repeat: no-repeat;
            box-shadow: 1px 2px 6px rgba(34,103,157,0.3), 0 1px 1px rgba(38,79,111,0.1);
            cursor: pointer;
            .sw-icon {
                width: 16px;
                height: 16px;
                color:#52667A;
                &.loading {
                    position: absolute;
                    top: 0;
                    right: 0;
                    color: red;
                    animation-name: sw-media-loader-rotation;
                    animation-duration: 1s;
                    animation-iteration-count: infinite;
                }
                @keyframes sw-media-loader-rotation {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(180deg); }
                }
            }
            &:hover{
                background: #189EFF ;
                .sw-icon {
                    color:#FFF;
                }
            }
        }
    }
    a[target=_blank]:not(.sw-external-link,.sw-internal-link,.sw-button):after {
        display: none !important;
    }
}
.v-grid {
    position: relative;
}
.v-grid-item-dragging .icon {
    animation-name: shake;
    animation-duration: 0.07s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
}

.v-grid-container {
    position: relative;
    width: 100%;
    height: 100%;
}


.v-grid-item-wrapper {
    display: block;
    position: absolute;
    box-sizing: border-box;
    left: 0;
    top: 0;
    user-select: none;
    transform: translate3d(0px, 0px, 0px);
    z-index: 1;
}

.v-grid-item-wrapper.v-grid-item-animate {
    transition: transform 800ms ease;
}
