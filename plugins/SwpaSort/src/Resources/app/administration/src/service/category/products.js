const ApiService = Shopware.Classes.ApiService;

class SwpaSortCategoryAssignService extends ApiService {

    constructor(httpClient, loginService, apiEndpoint = 'swpa-sort') {
        super(httpClient, loginService, apiEndpoint);
    }

    assign(productId,productVersionId,categoryId,categoryVersionId) {
        const params = {};
        const headers = this.getBasicHeaders();
        return this.httpClient
            .post(`${this.getApiBasePath()}/assign-product`,
                {productId,productVersionId,categoryId,categoryVersionId},
                {params, headers})
            .then((response) => {
                return ApiService.handleResponse(response);
            });
    }

    remove(productId,productVersionId,categoryId,categoryVersionId) {
        const params = {};
        const headers = this.getBasicHeaders();
        return this.httpClient
            .post(`${this.getApiBasePath()}/remove-product`,
                {productId,productVersionId,categoryId,categoryVersionId},
                {params, headers})
            .then((response) => {
                return ApiService.handleResponse(response);
            });
    }

    removeAll(categoryId,categoryVersionId) {
        const params = {};
        const headers = this.getBasicHeaders();
        return this.httpClient
            .post(`${this.getApiBasePath()}/remove-all-products`,
                {categoryId,categoryVersionId},
                {params, headers})
            .then((response) => {
                return ApiService.handleResponse(response);
            });
    }
}

export default SwpaSortCategoryAssignService;
