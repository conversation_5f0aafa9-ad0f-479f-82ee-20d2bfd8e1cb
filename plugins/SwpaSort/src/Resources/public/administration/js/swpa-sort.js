!function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p=(window.__sw__.assetPath + '/bundles/swpasort/'),n(n.s="XJJV")}({A96t:function(t,e,n){var i=n("x3Tp");i.__esModule&&(i=i.default),"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n("P8hj").default)("7861531e",i,!0,{})},P8hj:function(t,e,n){"use strict";function i(t,e){for(var n=[],i={},r=0;r<e.length;r++){var o=e[r],s=o[0],a={id:t+":"+r,css:o[1],media:o[2],sourceMap:o[3]};i[s]?i[s].parts.push(a):n.push(i[s]={id:s,parts:[a]})}return n}n.r(e),n.d(e,"default",(function(){return m}));var r="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!r)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},s=r&&(document.head||document.getElementsByTagName("head")[0]),a=null,c=0,l=!1,u=function(){},d=null,p="data-vue-ssr-id",h="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(t,e,n,r){l=n,d=r||{};var s=i(t,e);return f(s),function(e){for(var n=[],r=0;r<s.length;r++){var a=s[r];(c=o[a.id]).refs--,n.push(c)}e?f(s=i(t,e)):s=[];for(r=0;r<n.length;r++){var c;if(0===(c=n[r]).refs){for(var l=0;l<c.parts.length;l++)c.parts[l]();delete o[c.id]}}}}function f(t){for(var e=0;e<t.length;e++){var n=t[e],i=o[n.id];if(i){i.refs++;for(var r=0;r<i.parts.length;r++)i.parts[r](n.parts[r]);for(;r<n.parts.length;r++)i.parts.push(v(n.parts[r]));i.parts.length>n.parts.length&&(i.parts.length=n.parts.length)}else{var s=[];for(r=0;r<n.parts.length;r++)s.push(v(n.parts[r]));o[n.id]={id:n.id,refs:1,parts:s}}}}function g(){var t=document.createElement("style");return t.type="text/css",s.appendChild(t),t}function v(t){var e,n,i=document.querySelector("style["+p+'~="'+t.id+'"]');if(i){if(l)return u;i.parentNode.removeChild(i)}if(h){var r=c++;i=a||(a=g()),e=b.bind(null,i,r,!1),n=b.bind(null,i,r,!0)}else i=g(),e=S.bind(null,i),n=function(){i.parentNode.removeChild(i)};return e(t),function(i){if(i){if(i.css===t.css&&i.media===t.media&&i.sourceMap===t.sourceMap)return;e(t=i)}else n()}}var y,w=(y=[],function(t,e){return y[t]=e,y.filter(Boolean).join("\n")});function b(t,e,n,i){var r=n?"":i.css;if(t.styleSheet)t.styleSheet.cssText=w(e,r);else{var o=document.createTextNode(r),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(o,s[e]):t.appendChild(o)}}function S(t,e){var n=e.css,i=e.media,r=e.sourceMap;if(i&&t.setAttribute("media",i),d.ssrId&&t.setAttribute(p,e.id),r&&(n+="\n/*# sourceURL="+r.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}},XJJV:function(t,e,n){"use strict";n.r(e);var i={SWPA_SORT_PERMISSION_KEY:"swpa_sort"};function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(o=i.key,s=void 0,s=function(t,e){if("object"!==r(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!==r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o,"string"),"symbol"===r(s)?s:String(s)),i)}var o,s}function a(t,e){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function c(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=u(t);if(e){var r=u(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return l(this,n)}}function l(t,e){if(e&&("object"===r(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}void 0!==Shopware.Service("privileges")&&Shopware.Service("privileges").addPrivilegeMappingEntry({category:"permissions",parent:null,key:i.SWPA_SORT_PERMISSION_KEY,roles:{viewer:{privileges:[i.SWPA_SORT_PERMISSION_KEY+":viewer","category:read","currency:read","product:read","product_translation:read","product_category_tree:read","product_category:read"],dependencies:["category.viewer","product.viewer"]},editor:{privileges:[i.SWPA_SORT_PERMISSION_KEY+":editor"],dependencies:[i.SWPA_SORT_PERMISSION_KEY+".viewer","category.editor"]},creator:{privileges:[i.SWPA_SORT_PERMISSION_KEY+":creator"],dependencies:[i.SWPA_SORT_PERMISSION_KEY+".editor","category.creator"]},deleter:{privileges:[i.SWPA_SORT_PERMISSION_KEY+":deleter"],dependencies:[i.SWPA_SORT_PERMISSION_KEY+".creator","category.deleter"]}}});var d=Shopware.Classes.ApiService,p=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&a(t,e)}(l,t);var e,n,i,r=c(l);function l(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"swpa-sort";return o(this,l),r.call(this,t,e,n)}return e=l,(n=[{key:"apply",value:function(t,e,n,i){var r=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/apply"),{items:t,categoryId:n,categoryVersionId:i},{params:{},headers:r}).then((function(t){return d.handleResponse(t)}))}},{key:"fetch",value:function(t,e){var n=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/fetch"),{categoryId:t,categoryVersionId:e},{params:{},headers:n}).then((function(t){return d.handleResponse(t)}))}},{key:"init",value:function(t,e){var n=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/init"),{categoryId:t,categoryVersionId:e},{params:{},headers:n}).then((function(t){return d.handleResponse(t)}))}},{key:"sort",value:function(t,e,n){var i=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/sort"),{type:t,categoryId:e,categoryVersionId:n},{params:{},headers:i}).then((function(t){return d.handleResponse(t)}))}},{key:"moveToTop",value:function(t,e,n){var i=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/move-to-top"),{productId:t,categoryId:e,categoryVersionId:n},{params:{},headers:i}).then((function(t){return d.handleResponse(t)}))}},{key:"moveToEnd",value:function(t,e,n){var i=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/move-to-end"),{productId:t,categoryId:e,categoryVersionId:n},{params:{},headers:i}).then((function(t){return d.handleResponse(t)}))}},{key:"moveToPage",value:function(t,e,n,i,r){var o=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/move-to-page"),{productId:t,pageNumber:e,limit:n,categoryId:i,categoryVersionId:r},{params:{},headers:o}).then((function(t){return d.handleResponse(t)}))}}])&&s(e.prototype,n),i&&s(e,i),Object.defineProperty(e,"prototype",{writable:!1}),l}(d);function h(t){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function f(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(r=i.key,o=void 0,o=function(t,e){if("object"!==h(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!==h(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r,"string"),"symbol"===h(o)?o:String(o)),i)}var r,o}function g(t,e){return(g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function v(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=w(t);if(e){var r=w(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return y(this,n)}}function y(t,e){if(e&&("object"===h(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function w(t){return(w=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var b=Shopware.Classes.ApiService,S=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&g(t,e)}(o,t);var e,n,i,r=v(o);function o(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"swpa-sort";return m(this,o),r.call(this,t,e,n)}return e=o,(n=[{key:"assign",value:function(t,e,n,i){var r=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/assign-product"),{productId:t,productVersionId:e,categoryId:n,categoryVersionId:i},{params:{},headers:r}).then((function(t){return b.handleResponse(t)}))}},{key:"remove",value:function(t,e,n,i){var r=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/remove-product"),{productId:t,productVersionId:e,categoryId:n,categoryVersionId:i},{params:{},headers:r}).then((function(t){return b.handleResponse(t)}))}},{key:"removeAll",value:function(t,e){var n=this.getBasicHeaders();return this.httpClient.post("".concat(this.getApiBasePath(),"/remove-all-products"),{categoryId:t,categoryVersionId:e},{params:{},headers:n}).then((function(t){return b.handleResponse(t)}))}}])&&f(e.prototype,n),i&&f(e,i),Object.defineProperty(e,"prototype",{writable:!1}),o}(b),_=Shopware.Application;_.addServiceProvider("SwpaSortSortingService",(function(t){var e=_.getContainer("init");return new p(e.httpClient,t.loginService)})),_.addServiceProvider("SwpaSortCategoryAssignService",(function(t){var e=_.getContainer("init");return new S(e.httpClient,t.loginService)}));function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function C(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function A(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==P(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!==P(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===P(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var M=Shopware,E=M.Mixin,I=M.Component,O=Shopware.Data.Criteria;I.extend("swpa-entity-listing","sw-entity-listing",{template:'{% block sw_data_grid_select_item_checkbox %}\n    <sw-checkbox-field\n        :disabled="!hasAccess(\'swpa_sort:editor\') || !isRecordSelectable(item)"\n        :value="isSelected(item[itemIdentifierProperty])"\n        @change="selectItem($event, item)">\n    </sw-checkbox-field>\n{% endblock %}\n\n{% block sw_data_grid_select_all_checkbox %}\n\n{% endblock %}\n\n{% block sw_data_grid_bulk_selected_actions %}{% endblock %}\n{% block sw_data_grid_bulk_selected_count %}{% endblock %}\n\n\n{% block sw_data_grid_body_cell_actions_content %}{% endblock %}\n',mixins:[E.getByName("notification")],inject:["SwpaSortCategoryAssignService","acl"],props:{preSelectedItems:{type:Array,required:!1},repositorySelectedProducts:{type:Object,required:!0},category:{type:Object,required:!0}},data:function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?C(Object(n),!0).forEach((function(e){A(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({initSelection:!1,selected:null},i)},computed:{assignedElements:function(){return null===this.selected&&(this.selected=this.preSelectedItems),this.selected}},methods:{hasAccess:function(t){return void 0===this.acl||this.acl.can(this.SWPA_SORT_PERMISSION_KEY+"."+t)},isSelected:function(t){return void 0!==this.assignedElements[t]},onDbClickCell:function(t){},selectItem:function(t,e){if(!t&&!this.hasAccess("editor"))return this.noAccessMessage();if(this.isRecordSelectable(e)){var n=this.selection;t?this.assignProduct(e):this.removeProduct(e),t?this.$set(this.selection,e[this.itemIdentifierProperty],e):!t&&n[e[this.itemIdentifierProperty]]&&this.$delete(this.selection,e[this.itemIdentifierProperty]),this.$emit("select-item",this.selection,e,t)}},selectAll:function(t){var e=this;this.$delete(this.selection),this.records.forEach((function(n){e.isSelected(n[e.itemIdentifierProperty])!==t&&e.selectItem(t,n)})),this.$emit("select-all-items",this.selection)},assignProduct:function(t){var e=this;if(!this.hasAccess("editor"))return this.noAccessMessage();this.SwpaSortCategoryAssignService.assign(t.id,t.versionId,this.category.id,this.category.versionId).then((function(t){t.result?e.createSystemNotificationSuccess({title:e.$tc("swpa-sort.message.assign_product.success.title"),message:e.$tc("swpa-sort.message.assign_product.success.text")}):e.createSystemNotificationError({title:e.$tc("swpa-sort.message.assign_product.error.title"),message:e.$tc("swpa-sort.message.assign_product.error.text")+" "+t.message})})).catch((function(t){e.createSystemNotificationError({title:e.$tc("swpa-sort.message.assign_product.error.title"),message:e.$tc("swpa-sort.message.assign_product.error.text")})}))},removeProduct:function(t){var e=this;if(!this.hasAccess("deletor"))return this.noAccessMessage();this.SwpaSortCategoryAssignService.remove(t.id,t.versionId,this.category.id,this.category.versionId).then((function(t){t.result?e.createSystemNotificationSuccess({title:e.$tc("swpa-sort.message.remove_product.success.title"),message:e.$tc("swpa-sort.message.remove_product.success.text")}):e.createSystemNotificationError({title:e.$tc("swpa-sort.message.assign_product.error.title"),message:e.$tc("swpa-sort.message.assign_product.error.text")+" "+t.message})})).catch((function(t){e.createSystemNotificationSuccess({title:e.$tc("swpa-sort.message.assign_product.error.title"),message:e.$tc("swpa-sort.message.assign_product.error.text")})}))},doSearch:function(){var t=this;return this.loading=!0,this.repository.search(this.items.criteria,this.items.context).then((function(e){var n=new O(1,500),i=[];e.forEach((function(t){i.push(t.id)})),t.selected=[],n.addFilter(O.equalsAny("id",i)),t.repositorySelectedProducts.search(n,t.items.context).then((function(n){n.forEach((function(e){t.selected[e.id]=e})),t.applyResult(e)}))}))},noAccessMessage:function(){this.createSystemNotificationWarning({title:this.$tc("swpa-sort.message.noAccess.title"),message:this.$tc("swpa-sort.message.noAccess.text")})}}});Shopware.Component.override("sw-category-view",{template:"{% block sw_category_view_tabs_cms %}\n    {% parent %}\n    <sw-tabs-item\n        class=\"sw-category-detail__tab-custom\"\n        :route=\"{ name: 'swpa.category.detail.sort' }\"\n        :title=\"$tc('swpa-sort.tab.title')\">\n        {{ $tc('swpa-sort.tab.name') }}\n    </sw-tabs-item>\n{% endblock %}\n"});n("Y6C0");var $={data:function(){return{windowHeight:0,windowWidth:0}},created:function(){window.addEventListener("resize",this.getWindowSize),this.getWindowSize()},mounted:function(){this.getWindowSize()},beforeDestroy:function(){window.removeEventListener("resize",this.getWindowSize)},methods:{getWindowSize:function(){this.$el&&(this.windowHeight=this.$el.clientHeight,this.windowWidth=this.$el.clientWidth)}}},T=Shopware.Component;T.register("swpa-grid-item",{template:'<div ref="self"\n     :class="className"\n     :style="style"\n     @mousedown="mousedown"\n     @touchstart.stop="mousedown">\n    <slot/>\n</div>\n',name:"swpa-grid-item",props:{index:{type:Number},sort:{type:Number},cellWidth:{type:Number},cellHeight:{type:Number},rowCount:{type:Number},rowShift:{type:Number,default:0},draggable:{type:Boolean},dragDelay:{type:Number,default:0}},data:function(){return{animate:!0,dragging:!1,shiftStartX:0,shiftStartY:0,mouseMoveStartX:0,mouseMoveStartY:0,shiftX:0,shiftY:0,timer:null,zIndex:1}},mounted:function(){var t=this;this.$refs.self.addEventListener("transitionend",(function(e){t.dragging||(t.zIndex=1)}),!1)},computed:{className:function(){return["v-grid-item-wrapper",{"v-grid-item-animate":this.animate,"v-grid-item-dragging":this.dragging}]},style:function(){var t=this.zIndex,e=this.cellWidth,n=this.cellHeight,i=this.top,r=this.left;return{zIndex:t,width:e+"px",height:n+"px",transform:"translate3d(".concat(r,"px, ").concat(i,"px, 0)")}},left:function(){return this.dragging?this.shiftX:this.rowShift+this.sort%this.rowCount*this.cellWidth},top:function(){return this.dragging?this.shiftY:Math.floor(this.sort/this.rowCount)*this.cellHeight}},methods:{wrapEvent:function(t){return{event:t,index:this.index,sort:this.sort}},dragStart:function(t){var e=t.touches?t.touches[0]:t;this.zIndex=50,this.shiftX=this.shiftStartX=this.left,this.shiftY=this.shiftStartY=this.top,this.mouseMoveStartX=e.pageX,this.mouseMoveStartY=e.pageY,this.animate=!1,this.dragging=!0,document.addEventListener("mousemove",this.documentMouseMove),document.addEventListener("touchmove",this.documentMouseMove),this.$emit("dragstart",this.wrapEvent(t))},drag:function(t){var e=t.touches?t.touches[0]:t,n=e.pageX-this.mouseMoveStartX,i=e.pageY-this.mouseMoveStartY;this.shiftX=n+this.shiftStartX,this.shiftY=i+this.shiftStartY;var r=Math.round(this.shiftX/this.cellWidth),o=Math.round(this.shiftY/this.cellHeight),s=(r=Math.min(r,this.rowCount-1))+(o=Math.max(o,0))*this.rowCount,a={event:t,distanceX:n,distanceY:i,positionX:this.shiftX,positionY:this.shiftY,index:this.index,gridX:r,gridY:o,gridPosition:s};this.$emit("drag",a)},mousedown:function(t){var e=this;this.draggable&&(this.timer=setTimeout((function(){e.dragStart(t)}),this.dragDelay),document.addEventListener("mouseup",this.documentMouseUp),document.addEventListener("touchend",this.documentMouseUp))},documentMouseMove:function(t){this.draggable&&this.dragging&&this.drag(t)},documentMouseUp:function(t){this.timer&&(clearTimeout(this.timer),this.timer=null);var e=this.shiftStartX-this.shiftX,n=this.shiftStartY-this.shiftY,i=Math.sqrt(e*e+n*n);this.animate=!0,this.dragging=!1,this.mouseMoveStartX=0,this.mouseMoveStartY=0,this.shiftStartX=0,this.shiftStartY=0,document.removeEventListener("mousemove",this.documentMouseMove),document.removeEventListener("touchmove",this.documentMouseMove),document.removeEventListener("mouseup",this.documentMouseUp),document.removeEventListener("touchend",this.documentMouseUp);var r=this.wrapEvent(t);i<4&&this.$emit("click",r),this.$emit("dragend",r)}}});n("A96t");var x=Shopware.Component,k=Shopware.Data.Criteria;function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function L(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach((function(e){N(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function N(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==R(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!==R(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===R(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function D(t){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function W(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?B(Object(n),!0).forEach((function(e){V(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function V(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==D(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!==D(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===D(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}x.register("swpa-grid-element",{template:'<div :class="elementClass" :style="style">\n    <div v-if="withButton"\n         class="item-delete-btn"\n         @mousedown="remove"><sw-icon name="regular-times-circle" ></sw-icon></div>\n    <slot name="image">\n        <div class="item-image">\n            <div class="out-of-stock badge" v-if="!isInStock(item) && isActive">{{ $tc(\'swpa-sort.badge.out_of_stock\') }}</div>\n            <div class="not-active badge" v-if="!isActive">{{ $tc(\'swpa-sort.badge.not-active\') }}</div>\n            <img\n                v-if="previewUrl"\n                :src="previewUrl"\n                :srcset="sourceSet"\n                :sizes="`${width}px`" >\n        </div>\n    </slot>\n    <slot name="info">\n        <div class="item-info">\n            <span class="item-name">{{ item.translated.name }}</span>\n            <span class="item-number">{{ productNumber }}</span>\n            <template v-for="currency in currencies">\n                <span v-if="getCurrencyPriceByCurrencyId(currency.id, item.price).gross!==null" class="item-price">\n                    {{ getCurrencyPriceByCurrencyId(currency.id, item.price).gross | currency(currency.isoCode) }}\n                </span>\n            </template>\n        </div>\n    </slot>\n    <slot name="actions">\n        <div class="item-actions">\n            <a href="#" class="move-top" :title="$tc(\'swpa-sort.element_actions.move_to_top\')" @mousedown="moveToTop(item)"> <sw-icon name="regular-chevron-circle-up"></sw-icon></a>\n            <router-link target="_blank" :title="$tc(\'swpa-sort.element_actions.edit_product\')" :to="{ name: \'sw.product.detail\', params: { id: item.id } }"><sw-icon name="regular-external-link"></sw-icon></router-link>\n            <a href="#" class="open-link" @mousedown="copyToClipboard(item.productNumber)" :title="$tc(\'swpa-sort.element_actions.copy\')"> <sw-icon name="regular-copy"></sw-icon></a>\n            <a href="#" class="open-link" @mousedown="moveToPage(item)" :title="$tc(\'swpa-sort.element_actions.move_to_page\')"> <sw-icon name="regular-chevron-circle-right"></sw-icon></a>\n            <a href="#" class="move-bottom"  :title="$tc(\'swpa-sort.element_actions.move_to_end\')"  @mousedown="moveToEnd(item)"> <sw-icon name="regular-chevron-circle-down"></sw-icon></a>\n        </div>\n    </slot>\n</div>\n',name:"swpa-grid-element",props:{index:{type:Number},withButton:{type:Boolean,default:!1},total:{type:Number},item:{type:Object,default:function(){return null}},currencies:{type:Array,default:function(){return null}}},inject:["repositoryFactory"],data:function(){return{width:130}},computed:{style:function(){return null},product:function(){return this.item},elementClass:function(){return"swpa-grid-item "+(!1===this.item.active?"not-active":"active")+(this.item.variantListingConfig&&this.item.variantListingConfig.configuratorGroupConfig&&this.item.variantListingConfig.configuratorGroupConfig.length>0&&null===this.item.variantListingConfig.displayParent?" is-group":"")},previewUrl:function(){return this.item.cover?this.item.cover.media.url:null},sourceSet:function(){if(!this.item.cover)return null;var t=this.item.cover.media;if(0===t.thumbnails.length)return"";var e=[];return t.thumbnails.forEach((function(t){var n=encodeURI(t.url);e.push("".concat(n," ").concat(t.width,"w"))})),e.join(", ")},productNumber:function(){return this.item.productNumber},productRepository:function(){return this.repositoryFactory.create("product")},productCriteria:function(){var t=new k(1,25);return t.getAssociation("media"),t.addAssociation("cover"),t},isActive:function(){return this.item.parent&&this.item.parent.active&&null===this.item.active||this.item.active}},methods:{remove:function(){this.$emit("remove",{index:this.index})},moveToTop:function(t){this.$emit("movetotop",{element:t,model:this})},moveToEnd:function(t){this.$emit("movetoend",{element:t,model:this})},copyToClipboard:function(t){console.log(this.total),this.$emit("copy",{element:t,model:this})},getCurrencyPriceByCurrencyId:function(t,e){var n=e.find((function(e){return e.currencyId===t}));return n||{currencyId:null,gross:null,linked:!0,net:null}},moveToPage:function(t){this.$emit("movetopage",{element:t,model:this})},isInStock:function(t){return t.children.length>0?t.children.filter((function(t){return!0===t.available})).length>0:!0===t.available}}}),Shopware.Component.register("swpa-grid",{template:'<div class="v-grid-wrapper">\n    <div class="v-grid-pagination">\n        <sw-pagination\n            v-if="total > 0"\n            v-bind="{ limit, page, total }"\n            :steps="steps"\n            :autoHide="false"\n            @page-change="paginateGrid">\n        </sw-pagination>\n    </div>\n    <div class="v-grid" :style="style">\n        <swpa-grid-item v-for="v in list"\n                        :key="v.index"\n                        :index="v.index"\n                        :sort="v.sort"\n                        :draggable="draggable"\n                        :drag-delay="dragDelay"\n                        :row-count="rowCount"\n                        :cell-width="cellWidth"\n                        :cell-height="cellHeight"\n                        :window-width="windowWidth"\n                        :row-shift="rowShift"\n                        @dragstart="onDragStart"\n                        @dragend="onDragEnd"\n                        @drag="onDrag"\n                        @click="click">\n            <slot name="cell"\n                  :item="v.item"\n                  :index="v.index"\n                  :sort="v.sort"\n                  :total="total"\n                  :remove="() => { removeItem(v) }"\n                  :movetopage="() => { moveToPage(v) }"\n                  :movetotop="() => { moveToTop(v) }"\n                  :movetoend="() => { moveToEnd(v) }"\n                  :copy="() => { copyToClipboard(v) }"\n            >\n            </slot>\n        </swpa-grid-item>\n    </div>\n</div>\n',name:"swpa-grid",mixins:[$],props:{items:{type:Array,default:function(){return[]}},gridWidth:{type:Number,default:-1},cellWidth:{type:Number,default:190},cellHeight:{type:Number,default:190},draggable:{type:Boolean,default:!1},dragDelay:{type:Number,default:0},sortable:{type:Boolean,default:!1},center:{type:Boolean,default:!1},total:{type:Number,default:0}},data:function(){return{list:[],limit:12,page:1,steps:[12,24,32,64,128,256]}},watch:{items:{handler:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.list=t.map((function(t,e){return{item:t,index:e,sort:e}}))},immediate:!0}},computed:{gridResponsiveWidth:function(){return this.gridWidth<0?this.windowWidth:Math.min(this.windowWidth,this.gridWidth)},height:function(){return Math.ceil(this.list.length/this.rowCount)*this.cellHeight},style:function(){return{height:this.height+"px"}},rowCount:function(){return Math.floor(this.gridResponsiveWidth/this.cellWidth)},rowShift:function(){if(this.center){var t=this.list.length*this.cellWidth,e=t<this.gridResponsiveWidth?(this.gridResponsiveWidth-t)/2:this.gridResponsiveWidth%this.cellWidth/2;return Math.floor(e)}return 0}},methods:{paginateGrid:function(t){this.$emit("pagination",this.wrapEvent(t))},wrapEvent:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return L({datetime:Date.now(),items:this.getListClone()},t)},getListClone:function(){return this.list.slice(0).sort((function(t,e){return t.sort-e.sort}))},removeItem:function(t){var e=t.index,n=this.list.find((function(t){return t.index===e})),i=n.sort;this.list=this.list.filter((function(t){return t.index!==e})).map((function(t){var e=t.sort>i?t.sort-1:t.sort;return L(L({},t),{},{sort:e})})),this.$emit("remove",this.wrapEvent({index:e,removeItem:n}))},moveToTop:function(t){this.$emit("movetotop",this.wrapEvent({event:t}))},moveToPage:function(t){this.$emit("movetopage",this.wrapEvent({event:t}))},moveToEnd:function(t){this.$emit("movetoend",this.wrapEvent({event:t}))},copyToClipboard:function(t){this.$emit("copy",this.wrapEvent({event:t}))},onDragStart:function(t){this.$emit("dragstart",this.wrapEvent(t))},onDragEnd:function(t){this.$emit("dragend",this.wrapEvent(t))},click:function(t){this.$emit("click",this.wrapEvent(t))},onDrag:function(t){this.sortable&&this.sortList(t.index,t.gridPosition),this.$emit("drag",this.wrapEvent({event:t}))},sortList:function(t,e){var n=this.list.find((function(e){return e.index===t})),i=n.sort;e=Math.max(e,0),e=Math.min(e,this.list.length-1),i!==e&&(this.list=this.list.map((function(t){if(t.index===n.index)return L(L({},t),{},{sort:e});var r=t.sort;return i>e&&r<=i&&r>=e?L(L({},t),{},{sort:r+1}):i<e&&r>=i&&r<=e?L(L({},t),{},{sort:r-1}):t})),this.$emit("sort",this.wrapEvent()))}}});var Y=Shopware,z=Y.Component,H=Y.Mixin,X=Y.Context,F=Shopware.Data.Criteria,U=Shopware.Utils.dom;z.register("swpa-grid-sort",{template:'{% block sw_category_detail_sort %}\n    <div class="sw-category-detail__cms">\n        <sw-card positionIdentifier="sort-grid" :title="$tc(\'swpa-sort.title\')"  :disabled="isApplySortActionDisabled" :isLoading="isLoadingProducts">\n            <sw-container class="sorting-actions-container" columns="2fr 0.5fr 1fr 1fr 1fr" gap="0px 5px">\n                <sw-single-select name="sort_type"\n                                  :placeholder="$tc(\'swpa-sort.actions.sort_type.placeholder\')"\n                                  labelProperty="name"\n                                  valueProperty="id"\n                                  :options="availableSorting"\n                                  :value="sortType"\n                                  @change="updateSortType">\n                </sw-single-select>\n                <sw-button @click="applySortAction" :disabled="isApplySortActionDisabled" size="small"\n                           :isLoading="isSortingLoading">\n                    {{ $tc(\'swpa-sort.actions.sort_type.button\') }}\n                </sw-button>\n                <div></div>\n                <sw-button @click="addAction" :disabled="isAddActionDisabled" size="small" :isLoading="isAddingLoading">\n                    {{ $tc(\'swpa-sort.actions.adding.button\') }}\n                </sw-button>\n                <div></div>\n                <sw-button @click="onDeleteElements" :disabled="isRemoveActionDisabled" size="small" :isLoading="isRemovingLoading">\n                    {{ $tc(\'swpa-sort.actions.removing.button\') }}\n                </sw-button>\n            </sw-container>\n            <div class="v-grid-container">\n                <swpa-grid\n                    :center="false"\n                    :draggable="true"\n                    :sortable="true"\n                    :items="products"\n                    :cellHeight="300"\n                    :cellWidth="180"\n                    :total="total"\n                    @remove="remove"\n                    @movetotop="moveToTop"\n                    @movetopage="moveToPage"\n                    @movetoend="moveToEnd"\n                    @copy="copyToClipboard"\n                    @pagination="pagination"\n                    @sort="sort"\n                    @dragstart="dragstart"\n                    @dragend="dragend">\n                    <template slot="cell" slot-scope="props">\n                        <swpa-grid-element :item="props.item"\n                                           :currencies="currencies"\n                                           :index="props.index"\n                                           :with-button="true"\n                                           @remove="props.remove()"\n                                           @movetopage="props.movetopage()"\n                                           @movetotop="props.movetotop()"\n                                           @movetoend="props.movetoend()"\n                                           @copy="props.copy()"\n                        />\n                    </template>\n                </swpa-grid>\n            </div>\n        </sw-card>\n        <swpa-product-select\n            ref="swpaProductSearchRef"\n            @close="productSelectionClose">\n\n        </swpa-product-select>\n        <sw-modal v-if="showDeleteModal"\n                              @modal-close="onCloseDeleteModal"\n                              :title="$tc(\'global.default.warning\')"\n                              variant="small">\n            <div>\n                <p class="sw_tree__confirm-delete-text">\n                   {{ $tc(\'swpa-sort.actions.removing.confirmation_text\') }}\n               </p>\n            </div>\n\n            <template slot="modal-footer">\n                {% block sw_tree_delete_modal_cancel %}\n                    <sw-button @click="onCloseDeleteModal"\n                               size="small">\n                        {{ $tc(\'global.default.cancel\') }}\n                    </sw-button>\n                {% endblock %}\n\n                {% block sw_tree_delete_modal_confirm %}\n                    <sw-button @click="onConfirmDelete()"\n                               variant="danger"\n                               size="small">\n                        {{ $tc(\'global.default.delete\') }}\n                    </sw-button>\n                {% endblock %}\n            </template>\n\n        </sw-modal>\n\n        <sw-modal v-if="showPageModal"\n                              @modal-close="onClosePageModal"\n                              :title="$tc(\'swpa-sort.modal.move_to_page.title\')"\n                              variant="small">\n            <div>\n                <p class="sw_tree__confirm-delete-text">\n                    <sw-number-field\n                        @input-change="updatePageValue"\n                        @change="updatePageValue"\n                        :max="maxPage"\n                        :min="minPage"\n                        :value="pageNumberToMove"\n                        :placeholder="$tc(\'swpa-sort.modal.move_to_page.placeholder\')"\n                        >\n                    </sw-number-field>\n               </p>\n            </div>\n\n            <template slot="modal-footer">\n                {% block swpa_move_to_page_modal_cancel %}\n                    <sw-button @click="onClosePageModal"\n                               size="small">\n                        {{ $tc(\'global.default.cancel\') }}\n                    </sw-button>\n                {% endblock %}\n\n                {% block swpa_move_to_page_modal_confirm %}\n                    <sw-button @click="onConfirmMoveToPage()"\n                               variant="primary"\n                               :disabled="!isMoveToPageConfirmAvailable"\n                               size="small">\n                        {{ $tc(\'swpa-sort.modal.move_to_page.button.confirm\') }}\n                    </sw-button>\n                {% endblock %}\n            </template>\n\n        </sw-modal>\n    </div>\n{% endblock %}\n\n',inject:["repositoryFactory","acl","SwpaSortSortingService","SwpaSortCategoryAssignService"],mixins:[H.getByName("listing"),H.getByName("notification"),H.getByName("placeholder")],data:function(){return W({products:[],currencies:[],isLoadingProducts:!1,reversedVisibility:null,selected:null,total:0,page:1,productsLimit:12,sortType:null,searchTerm:null,isBulkLoading:!1,isSortingLoading:!1,isAddingLoading:!1,isRemovingLoading:!1,showProductListModal:!1,showDeleteModal:!1,isSorting:!1,showPageModal:!1,pageNumberToMove:null,moveToPageEvent:null},i)},props:{isLoading:{type:Boolean,required:!0}},created:function(){this.hasAccess("editor")&&(this.repository=this.repositoryFactory.create("product"))},computed:{availableSorting:function(){return[{id:"default",name:this.$tc("swpa-sort.sorting.default")},{id:"out_of_stock",name:this.$tc("swpa-sort.sorting.out_of_stock")},{id:"inactive",name:this.$tc("swpa-sort.sorting.inactive")},{id:"newest_first",name:this.$tc("swpa-sort.sorting.newest_first")},{id:"name_asc",name:this.$tc("swpa-sort.sorting.name_asc")},{id:"name_desc",name:this.$tc("swpa-sort.sorting.name_desc")},{id:"best_sellers",name:this.$tc("swpa-sort.sorting.best_sellers")},{id:"reviews_count",name:this.$tc("swpa-sort.sorting.reviews_count")}]},isApplySortActionDisabled:function(){return!!this.hasAccess("editor")&&null===this.sortType},isSearchActionDisabled:function(){return this.hasAccess("editor"),!1},isAddActionDisabled:function(){return this.hasAccess("editor"),!1},isRemoveActionDisabled:function(){return!this.hasAccess("deleter")||this.products.length<=0},currencyRepository:function(){return this.repositoryFactory.create("currency")},assignmentRepository:function(){return this.repositoryFactory.create(this.category.products.entity,this.category.products.source)},searchRepository:function(){return this.repositoryFactory.create(this.category.products.entity)},category:function(){return Shopware.State.get("swCategoryDetail").category},context:function(){return this.category.products.context},maxPage:function(){return Math.ceil(this.total/this.productsLimit)},minPage:function(){return 1},isMoveToPageConfirmAvailable:function(){if(null===this.pageNumberToMove)return!1;return/^([0-9]+)$/i.test(this.pageNumberToMove)},productRepository:function(){return this.repositoryFactory.create("product")},itemCriteria:function(){var t=new F(1,25);return t.getAssociation("media"),t.addAssociation("cover"),t}},methods:{hasAccess:function(t){return void 0===this.acl||this.acl.can(this.SWPA_SORT_PERMISSION_KEY+"."+t)},copyToClipboard:function(t){var e=t.event.item.productNumber;U.copyToClipboard(e),this.createSystemNotificationSuccess({title:this.$tc("swpa-sort.message.copied.title"),message:this.$tc("swpa-sort.message.copied.text")})},moveToTop:function(t){var e=this;if(!this.hasAccess("editor"))return this.noAccessMessage();this.isLoadingProducts=!0,this.SwpaSortSortingService.moveToTop(t.event.item.id,this.category.id,this.category.versionId).then((function(t){t.result||e.fail(t.message),e.doProductSearch()})).catch((function(t){e.isLoadingProducts=!1,e.fail(t.message)}))},moveToPage:function(t){if(!this.hasAccess("editor"))return this.noAccessMessage();this.showPageModal=!0,this.moveToPageEvent=t},moveToEnd:function(t){var e=this;if(!this.hasAccess("editor"))return this.noAccessMessage();this.isLoadingProducts=!0,this.SwpaSortSortingService.moveToEnd(t.event.item.id,this.category.id,this.category.versionId).then((function(t){t.result||e.fail(t.message),e.doProductSearch()})).catch((function(t){e.isLoadingProducts=!1,e.fail(t.message)}))},updateSortType:function(t){this.sortType=t},applySortAction:function(){var t=this;if(!this.hasAccess("editor"))return this.noAccessMessage();this.isSortingLoading=!0,this.SwpaSortSortingService.sort(this.sortType,this.category.id,this.category.versionId).then((function(e){e.result||t.fail(e.message),t.isSortingLoading=!1,t.getList()})).catch((function(e){t.isSortingLoading=!1,t.fail(e.message)}))},updateSearchTerm:function(t){this.searchTerm=t},addAction:function(){this.isAddingLoading=!0,this.$refs.swpaProductSearchRef.showModal=!0,this.$refs.swpaProductSearchRef.getList()},removeAllAction:function(){var t=this;if(!this.hasAccess("deleter"))return this.noAccessMessage();this.isRemovingLoading=!0,this.SwpaSortCategoryAssignService.removeAll(this.category.id,this.category.versionId).then((function(e){t.createSystemNotificationSuccess({title:t.$tc("swpa-sort.message.remove_all_products.success.title"),message:t.$tc("swpa-sort.message.remove_all_products.success.text")}),t.doProductSearch(),t.isRemovingLoading=!1})).catch((function(e){t.createSystemNotificationSuccess({title:t.$tc("swpa-sort.message.remove_all_products.error.title"),message:t.$tc("swpa-sort.message.remove_all_products.error.text")}),t.isRemovingLoading=!1}))},getList:function(){var t=this;if(!this.hasAccess("editor"))return this.noAccessMessage();this.SwpaSortSortingService.init(this.category.id,this.category.versionId).then((function(e){e.result||t.fail(e.message),t.doProductSearch()})).catch((function(e){t.fail(e.message)}))},doProductSearch:function(){var t=this,e=new F(this.page,this.productsLimit);e.addAssociation("cover"),e.addSorting(F.sort("swpaPosition.swpaSorting","ASC",!1)),e.addGrouping("displayGroup"),e.addFilter(F.not("AND",[F.equals("displayGroup",null)]));var n=new F(1,500);return this.isLoadingProducts=!0,Promise.all([this.assignmentRepository.search(e,W(W({},X.api),{},{inheritance:!0})),this.currencyRepository.search(n,this.context),this.SwpaSortSortingService.fetch(this.category.id,this.category.versionId)]).then((function(e){var n=e[0],i=e[1],r=e[2].result,o=t,s=[];o.products=[],n.forEach((function(e,n){var i=parseInt(r[e.id],10);return s.includes(i)||(e.sort=i,s.push(i),o.products.push(t.preloadVariant(e))),e})),t.total=n.total,t.currencies=i,t.isLoadingProducts=!1})).catch((function(){t.isLoadingProducts=!1}))},preloadVariant:function(t){var e=void 0;return t.variantListingConfig&&!1===t.variantListingConfig.displayParent&&(e=this.loadMainVariant(t.variantListingConfig.mainVariantId)),t.variantListingConfig&&!0===t.variantListingConfig.displayParent&&(e=this.loadMainVariant(t.parentId)),void 0!==e&&e.then((function(e){t.cover=e.cover,t.productNumber=e.productNumber})),t},loadMainVariant:function(t){return this.productRepository.get(t,W(W({},Shopware.Context.api),{},{inheritance:!0}),this.itemCriteria)},remove:function(t){var e=this;if(!this.hasAccess("deleter"))return this.doProductSearch(),this.noAccessMessage();var n=t.removeItem.item;this.SwpaSortCategoryAssignService.remove(n.id,n.versionId,this.category.id,this.category.versionId).then((function(t){e.createSystemNotificationSuccess({title:e.$tc("swpa-sort.message.remove_product.success.title"),message:e.$tc("swpa-sort.message.remove_product.success.text")}),e.doProductSearch()})).catch((function(t){e.createSystemNotificationSuccess({title:e.$tc("swpa-sort.message.assign_product.error.title"),message:e.$tc("swpa-sort.message.assign_product.error.text")})}))},sort:function(t){this.isSorting=!0},dragstart:function(t){this.isSorting=!1},dragend:function(t){var e=this;if(this.isSorting){if(!this.hasAccess("editor"))return this.doProductSearch(),this.noAccessMessage();var n=this,i=t.items.map((function(t,e){return{id:t.item.id,parentId:t.item.parentId,position:n.products[e].sort,version_id:t.item.versionId}}));this.SwpaSortSortingService.apply(i,this.page,this.category.id,this.category.versionId).then((function(t){t.result||e.fail(t.message)})).catch((function(t){e.fail(t.message)}))}},productSelectionClose:function(t){this.getList(),this.isAddingLoading=!1},fail:function(t){this.createSystemNotificationError({title:this.$tc("swpa-sort.message.something.title"),message:t})},pagination:function(t){this.page=t.page,this.productsLimit=t.limit,this.getList()},onDeleteElements:function(){this.showDeleteModal=!0},onCloseDeleteModal:function(){this.showDeleteModal=!1},onConfirmDelete:function(){this.removeAllAction(),this.showDeleteModal=!1},onClosePageModal:function(){this.showPageModal=!1},onConfirmMoveToPage:function(){var t=this;this.showPageModal=!1,this.isLoadingProducts=!0,this.SwpaSortSortingService.moveToPage(this.moveToPageEvent.event.item.id,this.pageNumberToMove,this.productsLimit,this.category.id,this.category.versionId).then((function(e){e.result||t.fail(e.message),t.doProductSearch(),t.pageNumberToMove=null,t.moveToPageEvent=null})).catch((function(e){t.isLoadingProducts=!1,t.pageNumberToMove=null,t.moveToPageEvent=null,t.fail(e.message)}))},updatePageValue:function(t){this.pageNumberToMove=t},noAccessMessage:function(){this.createSystemNotificationWarning({title:this.$tc("swpa-sort.message.noAccess.title"),message:this.$tc("swpa-sort.message.noAccess.text")})}}});function G(t){return function(t){if(Array.isArray(t))return q(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return q(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return q(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var K=Shopware,J=K.Component,Q=K.Mixin,Z=(K.State,Shopware.Data.Criteria);Shopware.Utils.dom;J.register("swpa-product-select",{template:'{% block sw_category_detail_modal_product_grid %}\n    <div class="swpa-product-select-container">\n        <sw-modal v-if="showModal"\n                  @modal-close="onModalClose()"\n                  :title="$tc(\'swpa-sort.modal.add_products.title\')"\n                  variant="large"\n                  class="swpa-sort-product-list-modal">\n            {% block swpa_product_list_content %}\n                <div class="sw-product-list__content">\n                    {% block swpa_product_list_grid %}\n                        <swpa-entity-listing ref="swProductGrid"\n                                           class="sw-product-list-grid"\n                                           :items="products"\n                                           :preSelectedItems="selectedProducts"\n                                           :columns="productColumns"\n                                           :repository="productRepository"\n                                           :repositorySelectedProducts="assignmentRepository"\n                                           :category="category"\n                                           detailRoute="sw.product.detail"\n                                           :showSelection="true"\n                                           :showActions="true"\n                                           :isLoading="isLoading"\n                                           identifier="swpa-sort-product-list"\n                                           :allowEdit="acl.can(\'product.editor\')"\n                                           :allowDelete="acl.can(\'product.deleter\')"\n                                           :allowInlineEdit="acl.can(\'product.editor\')"\n                                           @column-sort="onColumnSort"\n                                           @paginate="onColumnSort"\n                                           @inline-edit-save="onInlineEditSave"\n                                           @selection-change="updateSelection"\n                                           @update-records="updateTotal">\n                            <template slot="bulk">\n\n                            </template>\n                            {% block swpa_product_list_grid_columns %}\n\n                                <template #column-name="{ item, isInlineEdit }">\n                                    {{ item.translated ? item.translated.name : item.name }}\n                                </template>\n\n                            {% endblock %}\n                            {% block sw_product_list_grid_columns_price %}\n                                <template v-for="currency in currencies"\n                                          :slot="`column-price-${currency.isoCode}`"\n                                          slot-scope="{ item }">\n                                    {{ getCurrencyPriceByCurrencyId(currency.id, item.price).gross | currency(currency.isoCode) }}\n                                </template>\n                            {% endblock %}\n                        </swpa-entity-listing>\n\n                    {% endblock %}\n\n                    {% block swpa_product_list_empty_state %}\n                        <sw-empty-state v-if="!isLoading && !total"\n                                        :title="$tc(\'sw-product.list.messageEmpty\')">\n                        </sw-empty-state>\n                    {% endblock %}\n\n                    {% block swpa_product_list_grid_loader %}\n                        <sw-loader v-if="isLoading"></sw-loader>\n                    {% endblock %}\n                </div>\n            {% endblock %}\n        </sw-modal>\n    </div>\n{% endblock %}\n',inject:["repositoryFactory","numberRangeService","acl"],mixins:[Q.getByName("notification"),Q.getByName("listing"),Q.getByName("placeholder")],data:function(){return{products:null,currencies:[],sortBy:"productNumber",sortDirection:"DESC",naturalSorting:!0,isLoading:!1,isBulkLoading:!1,total:0,product:null,cloning:!1,productEntityVariantModal:!1,selectedProducts:[],showModal:!1}},metaInfo:function(){return{title:this.$createTitle()}},computed:{productRepository:function(){return this.repositoryFactory.create("product")},assignmentRepository:function(){return this.repositoryFactory.create(this.category.products.entity,this.category.products.source)},category:function(){return Shopware.State.get("swCategoryDetail").category},productColumns:function(){return this.getProductColumns()},currencyRepository:function(){return this.repositoryFactory.create("currency")},currenciesColumns:function(){return this.currencies.sort((function(t,e){return e.isSystemDefault?1:-1})).map((function(t){return{property:"price-".concat(t.isoCode),dataIndex:"price.".concat(t.id),label:"".concat(t.name),routerLink:"sw.product.detail",allowResize:!0,currencyId:t.id,visible:!1,align:"right",useCustomSort:!0}}))},showVariantModal:function(){return!!this.productEntityVariantModal}},filters:{stockColorVariant:function(t){return t>=25?"success":t<25&&t>0?"warning":"error"}},beforeRouteLeave:function(t,e,n){"sw.product.detail.base"===t.name&&this.showVariantModal&&this.closeVariantModal(),this.$nextTick((function(){n()}))},methods:{onModalClose:function(){this.showModal=!1,this.$emit("close",{selection:this.selection})},getList:function(){var t=this;this.isLoading=!0;var e=new Z(this.page,this.limit);this.naturalSorting="productNumber"===this.sortBy,e.setTerm(this.term),e.addFilter(Z.equals("product.parentId",null)),e.addSorting(Z.sort(this.sortBy,this.sortDirection,this.naturalSorting));var n=new Z(1,500);return Promise.all([this.productRepository.search(e,Shopware.Context.api),this.currencyRepository.search(n,Shopware.Context.api),this.assignmentRepository.search(e,Shopware.Context.api)]).then((function(e){var n=e[0],i=e[1],r=e[2];t.total=n.total,t.products=n,t.selectedProducts=[],r.forEach((function(e){t.selectedProducts[e.id]=e})),null!==t.$refs.swProductGrid.selected&&(t.$refs.swProductGrid.selected=t.selectedProducts),t.currencies=i,t.isLoading=!1,t.selection={}})).catch((function(){t.isLoading=!1}))},getCurrencyPriceByCurrencyId:function(t,e){var n=e.find((function(e){return e.currencyId===t}));return n||{currencyId:null,gross:null,linked:!0,net:null}},onInlineEditSave:function(t,e){var n=this,i=e.name||this.placeholder(e,"name");return t.then((function(){n.createNotificationSuccess({message:n.$tc("sw-product.list.messageSaveSuccess",0,{name:i})})})).catch((function(){n.getList(),n.createNotificationError({message:n.$tc("global.notification.notificationSaveErrorMessageRequiredFieldsInvalid")})}))},onInlineEditCancel:function(t){t.discardChanges()},updateTotal:function(t){this.total=t.total},onChangeLanguage:function(t){Shopware.State.commit("context/setApiLanguageId",t),this.getList()},getProductColumns:function(){return[{property:"name",label:this.$tc("sw-product.list.columnName"),routerLink:"sw.product.detail",inlineEdit:"string",allowResize:!0,primary:!0},{property:"productNumber",naturalSorting:!0,label:this.$tc("sw-product.list.columnProductNumber"),align:"right",visible:!0,allowResize:!0},{property:"manufacturer.name",label:this.$tc("sw-product.list.columnManufacturer"),allowResize:!0,visible:!1},{property:"active",label:this.$tc("sw-product.list.columnActive"),inlineEdit:"boolean",allowResize:!0,visible:!1,align:"center"}].concat(G(this.currenciesColumns),[{property:"stock",label:this.$tc("sw-product.list.columnInStock"),inlineEdit:"number",allowResize:!0,visible:!1,align:"right"},{property:"availableStock",label:this.$tc("sw-product.list.columnAvailableStock"),allowResize:!0,visible:!1,align:"right"}])},onDuplicate:function(t){this.product=t,this.cloning=!0},onDuplicateFinish:function(t){var e=this;this.cloning=!1,this.product=null,this.$nextTick((function(){e.$router.push({name:"sw.product.detail",params:{id:t.id}})}))},onColumnSort:function(t){this.$refs.swProductGrid.loading=!0},productHasVariants:function(t){var e=t.childCount;return null!==e&&e>0},openVariantModal:function(t){this.productEntityVariantModal=t},closeVariantModal:function(){this.productEntityVariantModal=null}}}),Shopware.Module.register("swpa-category-detail-sort",{type:"plugin",name:"SwpaSort",title:"swpa-sort.title",description:"swpa-sort.title",version:"1.1.0",targetVersion:"1.1.0",color:"#2b52ff",routeMiddleware:function(t,e){"sw.category.detail"===e.name&&e.children.push({name:"swpa.category.detail.sort",isChildren:!0,path:"sort",component:"swpa-grid-sort",meta:{parentPath:"sw.category.index",privilege:"category.viewer"}}),t(e)}})},Y6C0:function(t,e,n){var i=n("eFFq");i.__esModule&&(i=i.default),"string"==typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);(0,n("P8hj").default)("3769ad3e",i,!0,{})},eFFq:function(t,e,n){},x3Tp:function(t,e,n){}});
//# sourceMappingURL=swpa-sort.js.map