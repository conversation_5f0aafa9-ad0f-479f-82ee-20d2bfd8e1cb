<?php declare(strict_types=1);

namespace Swpa\SwpaSort\DataAbstractionLayer\Field;

use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Flag;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Runtime;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IntField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StorageAware;
use Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\IntFieldSerializer;

class CustomIntField extends IntField implements StorageAware
{
    private bool $isIs=false;

    public function __construct(
        private readonly string $storageName,
        protected string        $propertyName,
        private readonly ?int   $minValue = null,
        private readonly ?int   $maxValue = null
    )
    {
        parent::__construct($storageName,$propertyName,$minValue,$maxValue);
    }

    public function getStorageName(): string
    {
        return $this->storageName;
    }

    public function getMinValue(): ?int
    {
        return $this->minValue;
    }

    public function getMaxValue(): ?int
    {
        return $this->maxValue;
    }

    protected function getSerializerClass(): string
    {
        return IntFieldSerializer::class;
    }
    public function is(string $class): bool
    {
        $this->isIs = true;
        $result = $this->getFlag($class) !== null;
        $this->isIs = false;
        return $result;
    }

    public function getFlag(string $class): ?Flag
    {
        if(!$this->isIs && $class == Runtime::class){
            return null;
        }
        return parent::getFlag($class);
    }
}
