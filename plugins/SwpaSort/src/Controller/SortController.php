<?php declare(strict_types=1);

namespace Swpa\SwpaSort\Controller;


use Exception;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Swpa\SwpaSort\Service\CategoryProductServiceInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class SortController extends AbstractController
{
    public function __construct(
        private readonly CategoryProductServiceInterface $productCategoryService
    )
    {
    }
    
    #[Route(path: '/api/swpa-sort/apply', name: 'api.action.swpa-sort.apply', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function applySorting(RequestDataBag $requestDataBag, Context $context): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $sorting = $this->productCategoryService->fetchSorting($requestDataBag->get('categoryId'), $context->getVersionId());
            $categoryMapping = $this->productCategoryService->fetchCategoryMapping($requestDataBag->get('categoryId'), $context->getVersionId());
            $mapping = [];
            foreach ($categoryMapping as $id => $position) {
                $mapping[$id] = [
                    'id' => $id,
                    'position' => intval($sorting[$id]),
                    'version_id' => $context->getVersionId(),
                ];
            }
            foreach ($requestDataBag->get('items') as $map) {
                if (!array_key_exists($map->get('id'), $mapping) && !array_key_exists($map->get('parentId'), $mapping)) {
                    continue;
                }
                $mapId = (array_key_exists($map->get('parentId'), $mapping)) ? $map->get('parentId') : $map->get('id');
                $mapping[$mapId] = [
                    'id' => $mapId,
                    'position' => intval($map->get('position')),
                    'version_id' => $context->getVersionId(),
                ];
            }
            $this->productCategoryService->applySorting(
                $mapping,
                $requestDataBag->get('categoryId'),
                $context->getVersionId(),
                $context
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/move-to-top', name: 'api.action.swpa-sort.move-to-top', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function moveToTop(RequestDataBag $requestDataBag): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->moveToTop(
                $requestDataBag->get('productId'),
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId')
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/move-to-end', name: 'api.action.swpa-sort.move-to-end', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function moveToEnd(RequestDataBag $requestDataBag): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->moveToEnd(
                $requestDataBag->get('productId'),
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId')
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/move-to-page', name: 'api.action.swpa-sort.move-to-page', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function moveToPage(RequestDataBag $requestDataBag): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->moveToPage(
                $requestDataBag->get('productId'),
                intval($requestDataBag->get('pageNumber')),
                intval($requestDataBag->get('limit')),
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId')
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/sort', name: 'api.action.swpa-sort.sort', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function sort(RequestDataBag $requestDataBag, Context $context): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->sort(
                $requestDataBag->get('type'),
                $requestDataBag->get('categoryId'),
                $context->getVersionId(),
                $context->getVersionId()
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/init', name: 'api.action.swpa-sort.init', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function initCategory(RequestDataBag $requestDataBag, Context $context): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->initSorting(
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId'),
                $context
            );
            $this->productCategoryService->cleanTables(
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId'),
                $context
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/fetch', name: 'api.action.swpa-sort.fetch', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function fetchSorting(RequestDataBag $requestDataBag): JsonResponse
    {
        $response = ['result' => [], 'message' => ''];
        try {
            $response['result'] = $this->productCategoryService->fetchSorting(
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId')
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/assign-product', name: 'api.action.swpa-sort.assign-product', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function assignProduct(RequestDataBag $requestDataBag): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->assignProductToCategory(
                $requestDataBag->get('productId'),
                $requestDataBag->get('productVersionId'),
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId')
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/remove-product', name: 'api.action.swpa-sort.remove-product', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function removeProduct(RequestDataBag $requestDataBag): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->removeProductFromCategory(
                $requestDataBag->get('productId'),
                $requestDataBag->get('productVersionId'),
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId')
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
    
    #[Route(path: '/api/swpa-sort/remove-all-products', name: 'api.action.swpa-sort.remove-all-products', defaults: ['_routeScope' => ['administration']], methods: ['POST'])]
    public function removeAllProducts(RequestDataBag $requestDataBag): JsonResponse
    {
        $response = ['result' => true, 'message' => ''];
        try {
            $this->productCategoryService->removeAllProductsFromCategory(
                $requestDataBag->get('categoryId'),
                $requestDataBag->get('categoryVersionId')
            );
        } catch (Exception $e) {
            $response = ['result' => false, 'message' => $e->getMessage()];
        }
        return new JsonResponse($response);
    }
}
