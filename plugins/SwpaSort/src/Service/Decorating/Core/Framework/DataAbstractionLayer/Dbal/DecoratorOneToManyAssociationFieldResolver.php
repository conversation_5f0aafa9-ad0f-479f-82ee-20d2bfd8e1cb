<?php declare(strict_types=1);

namespace Swpa\SwpaSort\Service\Decorating\Core\Framework\DataAbstractionLayer\Dbal;

use Exception;
use Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityDefinitionQueryHelper;
use Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\AbstractFieldResolver;
use Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\FieldResolverContext;
use Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\OneToManyAssociationFieldResolver as DecoratedOneToManyAssociationFieldResolver;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\CascadeDelete;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Inherited;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\ReverseInherited;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToManyAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\Uuid\Uuid;

class DecoratorOneToManyAssociationFieldResolver extends AbstractFieldResolver
{
    public function __construct(private DecoratedOneToManyAssociationFieldResolver $service)
    {
    }

    public function join(FieldResolverContext $context): string
    {
        $field = $context->getField();
        if (!$field instanceof OneToManyAssociationField) {
            return $context->getAlias();
        }

        $context->getQuery()->addState(EntityDefinitionQueryHelper::HAS_TO_MANY_JOIN);

        $alias = $context->getAlias() . '.' . $field->getPropertyName();
        if ($context->getQuery()->hasState($alias)) {
            return $alias;
        }

        $context->getQuery()->addState($alias);

        $source = $this->getSourceColumn($context, $field);

        $referenceColumn = $this->getReferenceColumn($context, $field);

        $parameters = [
            '#source#' => $source,
            '#alias#' => EntityDefinitionQueryHelper::escape($alias),
            '#reference_column#' => $referenceColumn,
            '#root#' => EntityDefinitionQueryHelper::escape($context->getAlias()),
        ];

        $versionWhere = $this->buildVersionWhere($context, $field);
        $categoryCondition = $this->buildCategoryCondition($context, $field);

        $context->getQuery()->leftJoin(
            EntityDefinitionQueryHelper::escape($context->getAlias()),
            EntityDefinitionQueryHelper::escape($field->getReferenceDefinition()->getEntityName()),
            EntityDefinitionQueryHelper::escape($alias),
            str_replace(
                array_keys($parameters),
                array_values($parameters),
                '#source# = #alias#.#reference_column#' . $versionWhere . $categoryCondition
            )
        );

        return $alias;
    }

    private function buildCategoryCondition(FieldResolverContext $context, OneToManyAssociationField $field): string
    {
        /** @var Criteria $criteria */
        $criteria = $context->getContext()->getExtension('criteria');
        if (!$criteria instanceof Criteria || $field->getPropertyName() !== 'swpaPosition') {
            return "";
        }
        $context->getQuery()->addSelect('`product.swpaPosition`.swpa_sorting as swpa_sorting');
        try {
            $categoryCondition = ' AND #alias#.category_id=:swpaCategoryId';
            $context->getQuery()
                ->setParameter('swpaCategoryId', $this->fetchCategoryIdFromCriteria($criteria));
        } catch (Exception $e) {
            return "";
        }
        return $categoryCondition;
    }

    /**
     * @throws Exception
     */
    private function fetchCategoryIdFromCriteria(Criteria $criteria): string
    {
        $filters = $criteria->getFilters();
        /** @var EqualsFilter $filter */
        foreach ($filters as $filter) {
            if ($filter instanceof EqualsFilter && ($filter->getField() === 'product.categoriesRo.id' || $filter->getField() === 'product.categories.id')) {
                return Uuid::fromHexToBytes($filter->getValue());
            }
            if ($filter instanceof MultiFilter) {
                /** @var EqualsFilter $filter */
                foreach ($filter->getQueries() as $subFilter) {
                    if ($subFilter instanceof EqualsFilter && ($subFilter->getField() === 'product.categories.id' || $subFilter->getField() === 'product.categoriesRo.id')) {
                        return Uuid::fromHexToBytes($subFilter->getValue());
                    }
                }
            }
        }
        throw new Exception("no category found");
    }

    private function buildVersionWhere(FieldResolverContext $context, OneToManyAssociationField $field): string
    {
        if (!$context->getDefinition()->isVersionAware()) {
            return '';
        }
        if (!$field->is(CascadeDelete::class)) {
            return '';
        }

        $fkVersionId = $context->getDefinition()->getEntityName() . '_version_id';

        $reference = $field->getReferenceDefinition();
        if ($reference->getFields()->getByStorageName($fkVersionId) === null) {
            $fkVersionId = 'version_id';
        }

        return ' AND #root#.version_id = #alias#.' . $fkVersionId;
    }

    private function getSourceColumn(FieldResolverContext $context, OneToManyAssociationField $field): string
    {
        if ($field->is(Inherited::class) && $context->getContext()->considerInheritance()) {
            return EntityDefinitionQueryHelper::escape($context->getAlias()) . '.' . EntityDefinitionQueryHelper::escape($field->getPropertyName());
        }

        return EntityDefinitionQueryHelper::escape($context->getAlias()) . '.' . EntityDefinitionQueryHelper::escape($field->getLocalField());
    }

    private function getReferenceColumn(FieldResolverContext $context, OneToManyAssociationField $field): string
    {
        if ($field->is(ReverseInherited::class) && $context->getContext()->considerInheritance()) {
            /** @var ReverseInherited $flag */
            $flag = $field->getFlag(ReverseInherited::class);

            return EntityDefinitionQueryHelper::escape($flag->getReversedPropertyName());
        }

        return EntityDefinitionQueryHelper::escape($field->getReferenceField());
    }
}
