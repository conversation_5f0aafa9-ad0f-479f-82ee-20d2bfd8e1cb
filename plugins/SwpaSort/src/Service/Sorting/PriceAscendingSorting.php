<?php declare(strict_types=1);

namespace Swpa\SwpaSort\Service\Sorting;

use Doctrine\DBAL\Exception;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Uuid\Uuid;

/**
 * sort products by price ASC
 *
 * @package   Swpa\SwpaSort\Service
 * @copyright See COPYING.txt for license details
 * <AUTHOR> <<EMAIL>>
 */
class PriceAscendingSorting extends AbstractSorting implements SortingInterface
{

    const NAME = self::SORTING_TYPE_PRICE_ASC;

    /**
     * @inheritDoc
     */
    public function handles(string $sortingName): bool
    {
        return strtolower($sortingName) === strtolower(static::NAME);
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function sort(string $categoryId, string $categoryVersionId, string $productVersionId): void
    {

        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder->select('t.product_id, t.swpa_sorting')
            ->from('product_category_tree', 't')
            ->leftJoin('t', 'product', 'p', 't.product_id=p.id AND t.product_version_id=p.version_id')
            ->leftJoin('p', 'product_price', 'pp', 'p.id=pp.product_id AND p.version_id=pp.product_version_id')
            ->where('t.category_id=:categoryId')
            ->andWhere('t.category_version_id=:categoryVersionId')
            ->andWhere('p.parent_id IS NULL')
            ->addGroupBy('pp.product_id');
        $currencyID = 'c' . Defaults::CURRENCY;
        $orderField = 'MIN(COALESCE(
            JSON_UNQUOTE(JSON_EXTRACT(`pp`.`price`, "$.' . $currencyID . '.gross")) + 0.0,
            JSON_UNQUOTE(JSON_EXTRACT(`p`.`price`, "$.' . $currencyID . '.gross")) + 0.0
        ))';
        $queryBuilder->orderBy($orderField, 'ASC');
        $queryBuilder->setParameters([
            'categoryId' => Uuid::fromHexToBytes($categoryId),
            'categoryVersionId' => Uuid::fromHexToBytes($categoryVersionId)
        ]);
        $products = $queryBuilder->executeQuery()->fetchAllAssociative();
        $this->applySorting($products, $categoryId, $categoryVersionId, $productVersionId);
    }
}
