<?php declare(strict_types=1);

namespace Swpa\SwpaSort\Elasticsearch\Product;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Elasticsearch\Framework\AbstractElasticsearchDefinition;
use OpenSearchDSL\Query\Compound\BoolQuery;
use Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer;
use Swpa\SwpaSort\Service\CatalogRepositoryInterface;
use Swpa\SwpaSort\Service\Indexing\SwpaSortingMessage;
use Symfony\Component\Messenger\MessageBusInterface;

class ElasticsearchProductDefinitionDecorator extends AbstractElasticsearchDefinition
{
    public function __construct(
        private  readonly Connection                        $connection,
        private  readonly AbstractElasticsearchDefinition   $decorated
    )
    {
    }
    
    /**
     * @throws Exception
     *
     */
    public function fetch(array $ids, Context $context): array
    {
        $documents = $this->decorated->fetch($ids, $context);
        return $this->processDocuments($documents, $context);
    }
    
    /**
     * @throws Exception
     */
    private function processDocuments(array $documents, Context $context): array
    {
        foreach ($documents as &$document) {
            $documentId = $document['parentId']?:$document['id'];
            $sorting = $this->getSortingByProductId($documentId);
            $document['swpaPosition'] = $sorting;
        }
        return $documents;
    }
    
    /**
     * @throws Exception
     */
    private function getSortingByProductId(string $productId): array
    {
        $builder = $this->connection->createQueryBuilder();
        $builder->from("product_category",'pc')
            ->select("LOWER(HEX(pc.category_id)) as category_id,pc.swpa_sorting,c.path")
            ->leftJoin("pc", "category", "c", "c.id = pc.category_id")
            ->where("pc.product_id=:productId");
        $builder->setParameter("productId", Uuid::fromHexToBytes($productId));
        $result = $builder->execute()->fetchAllAssociative();
        $mapping = [];
        foreach ($result as $item) {
            $mapping[$item['category_id']] = $item['swpa_sorting'];
            if(empty($item['path'])){
                continue;
            }
            $categoryIds = explode("|",$item['path']);
            foreach ($categoryIds as $categoryId) {
                if(!empty($categoryId)) {
                    $mapping[$categoryId] = $item['swpa_sorting'];
                }
            }
        }
        return $mapping;
    }
    
    public function extendCriteria(Criteria $criteria): void
    {

    }

    public function extendEntities(EntityCollection $entityCollection): EntityCollection
    {
        return $entityCollection;
    }
    
    public function buildTermQuery(Context $context, Criteria $criteria): BoolQuery
    {
        return $this->decorated->buildTermQuery($context, $criteria);
    }
    
    public function getEntityDefinition(): EntityDefinition
    {
        return $this->decorated->getEntityDefinition();
    }
    
    public function getMapping(Context $context): array
    {
        $mapping = $this->decorated->getMapping($context);
        $mapping['properties'] = $this->addPositionMapping($mapping['properties']);
        return $mapping;
    }
    
    private function addPositionMapping(array $mapping) : array
    {
        $mapping['swpaPosition'] = [
            'type' => 'nested',
            'dynamic' => true
        ];
        return $mapping;
    }
}
