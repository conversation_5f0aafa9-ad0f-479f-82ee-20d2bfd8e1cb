# 1.9.0
- Das Anzeigen des Countdowns in der Kategorie kann im Plugin deaktiviert werden.

# 1.8.16
- <PERSON><PERSON><PERSON><PERSON>, wenn kein Fallback in den Options steht.

# 1.8.15
- <PERSON><PERSON><PERSON><PERSON>, wenn Samstagszustellung aktiv ist.

# 1.8.14
- Bugfix beim Hinzufügen eines Gutscheines in einer bestehenden Bestellung.

# 1.8.13
- Bugfix Lieferzeit bei nur einem Tag.

# 1.8.12
- Bugfix nächster Arbeitstag

# 1.8.11
- Bugfix Lieferzeit bei nur einem Tag.

# 1.8.10
- Kompatibilität mit dem Plugin "Pseudovarianten" hergestellt.

# 1.8.9
- Falls keine Lieferzeit im Produkt vorhanden wird jetzt die Lieferzeit der Versandmethode genommen.
- Das Aktualisieren des Countdowns im Frontend funktioniert jetzt in Shopware 6.5 
- Es wurde ein Problem bei der Berechnung des nächsten Lieferdatums behoben.

# 1.8.8
- Problem mit der Darstellung auf der Kategorie-Seite behoben.

# 1.8.7
- Einige Feiertage werden nicht berücksichtigt
- Fehlermeldung abfangen

# 1.8.6
- Verbesserung der ajax url

# 1.8.5
- Darstellung des Countdowns im Produktlisting verbessert.

# 1.8.4
- Shopware 6.5 kompatibel

# 1.8.3
- Fallback-Datum beim Check-out eines Artikels ohne Lieferzeit.

# 1.8.2
- Fallback-Datum beim Check-out eines Artikels ohne Lieferzeit.

# 1.8.1
- Fehlermeldung beim Check-out eines Artikels ohne Lieferzeit.

# 1.8.0
- Die Anzeige des Countdowns kann für Artikel in Abhängigkeit vom Lagerbestand und Abverkauf deaktiviert werden.

# 1.7.3
- Kompatibilität mit dem Plugin "Erlebniswelten auf Produkten anzeigen" hergestellt.

# 1.7.2
- Probleme mit Rabatten behoben.

# 1.7.1
- Probleme im Warenkorb behoben.

# 1.7.0
- Die angezeigte Lieferzeit ändern sich nun abhängig von der Versandmethode.
- Der Countdown und das Lieferdatum werden nun zum Zeitpunkt der Bestellung, in der Bestellung gespeichert.
- Der Countdown und das Lieferdatum können über die Variablen {{netzhirschOrderCountdown.deadline|date('d.m.Y')}} und {{netzhirschOrderCountdown.deliveryDate|date('d.m.Y')}} in der Bestellbestätigungsmail genutzt werden. Achtung nur ab Shopware 6.4.13

# 1.6.3
- Richtiges Datum bei speziellen Feiertagen
- Verbesserte Berücksichtigung der speziellen Feiertage im Frontend.

# 1.6.2
- Richtige Uhrzeit bei speziellen Feiertagen

# 1.6.1
- Überflüssiges HTMl entfernt, falls der Countdown nicht im Warenkorb angezeigt werden soll.

# 1.6
- Standard Unterstützung von Spanisch
- Menüpunkt "spezielle Feiertage" war nicht mehr vorhanden.

# 1.5
- Option um den Standard "Ausverkauft"-Hinweis anzuzeigen hinzugefügt.

# 1.4.7
- Kompatibilität zum Magnalister Plugin erhöht.

# 1.4.6
- RouteScope not imported

# 1.4.5
- Verhindern das die Route nicht gefunden wird, weil sonst Log-Einträge entstehen
- Bei Shop mit unterschiedlichen Sprachen wurde der Countdown-Text nur in der Standard-Sprache ausgegeben.

# 1.4.4
- Doppelte Anzeige von "Sofort verfügbar" vermeiden.

# 1.4.3
- Kompatibilität zu anderen Plugin erhöht.
- Keine Request senden, falls nicht genügend Daten vorhanden sind.

# 1.4.2
- Bei Produkte ohne Lieferzeit sollte die Lieferzeit des Hauptproduktes genommen werden.
- Zeit bis zum Neuladen des Countdowns wieder auf 1 Minute setzen.
- Warnungen bei der Statischen Codeanalyse beheben.

# 1.4.1
- Standard Versandzeit sowohl mit als auch ohne Countdown im Produkt anzeigen.

# 1.4.0
- Option um die Standard-Lieferzeit von Shopware zu deaktivieren.
- Minimale Höhe für das "order-countdown-div".
- Fix: Falsche Rückgabe-Type, wenn ein Produkt ein Erscheinungsdatum hat.

# 1.3.4
- Produkt-Detail-Ansicht hat nur den Fallback für die Lieferzeit angezeigt.

# 1.3.3
- Deadline falsch, wenn die Zeit schon vorbei ist.
- Spezielle Feiertage können ohne Deadline nicht gespeichert werden. Ohne deadline muss der nächste Tag geprüft werden.
- Template zu oft ausgespielt.

# 1.3.2
- In den Einstellungen werden jetzt, die verschiedenen Verkaufskanäle beachtet.

# 1.3.1
- JavaScript, im Storefront, wurde nicht richtig geladen

# 1.3.0
- Der Countdown/Deadline ist jetzt unabhängig vom Cache.
- Der Countdown/Deadline wird jede Minute aktualisiert.
- Spezielle Feiertage müssen keine Deadline haben.

# 1.2.7
- Im Frontend wird jetzt immer die Europa/Berlin Zeitzone verwendet.

# 1.2.6
- Support für Shopware Version 6.3
- Der Menüpunkt "Spezielle Feiertage" wird, in Shopware 6.3, als Untermenü von Bestellungen angezeigt.
- Der aggregiert Countdown/Deadline aller Produkte kann jetzt über "{% sw_include '@NetzhirschOrderCountdown/storefront/component/order-countdown.html.twig' %}" im Listing eingebunden werden.
- Behebt ein Problem mit der Wiederauffüllzeit.

# 1.2.5
- Behebt ein Problem mit deaktivierten Countdown/Deadline am Produkt.
- 
# 1.2.4
- Behebt ein Problem mit deaktivierten Countdown/Deadline am Produkt.
- 
# 1.2.3
- Behebt ein Problem mit deaktivierten Countdown/Deadline am Produkt.

# 1.2.2
- Behebt ein Problem mit deaktivierten Countdown/Deadline am Produkt.
- Deutscher Hilfetext für die Darstellungsmethoden hinzugefügt.

# 1.2.1
- Behebt ein Problem mit der Pagination im Artikellisting.

# 1.2.0
- Individuelle Feiertage können erstellt werden. Dazu gibt es unter Erweiterungen einen neuen Menüpunkt.
- Die Countdown-Anzeige kann automatisch nach einer festgelegten Zeit in die Deadline-Anzeige umgewandelt werden und umgekehrt.
- Eigene CSS-Klasse zum Stylen der Anzeige im Storefront. ("alert-netzhirsch-order-countdown")

# 1.1.0
- Alternativer Textbaustein für Artikel, die nicht auf Lager sind.
- Warnung anstelle von Erfolgsmeldung anzeigen, wenn das Produkt nicht vorrätig ist (konfigurierbar).

# 1.0.5
- Kleine Fehlerbehebung: Wiederauffüllzeit.

# 1.0.4
- Die Wiederauffüllzeit wird nun auch berücksichtigt, wenn keine Lieferzeit angegeben ist.

# 1.0.3
- Behebt ein Problem mit Rabatten und Aktionen im Warenkorb.

# 1.0.2
- Einstellen, ob Samstag ein Liefertag ist.

# 1.0.1
- Ein Problem mit den Textbausteinen für Countdown ohne Lieferzeit wurde behoben.
- Option ergänzt, um die Anzeige des Lieferdatums verbergen
- Countdown für einzelne Produkte ausblenden.

# 1.0.0
- Erstveröffentlichung
