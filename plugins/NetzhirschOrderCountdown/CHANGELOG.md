# 1.9.0
- Showing the countdown in the category can be disabled in the plugin.

# 1.8.16
- Bugfix when no fallback in options.

# 1.8.15
- Bugfix when saturday delivery is active.

# 1.8.14
- Bugfix when adding a voucher to an existing order.

# 1.8.13
- Bugfix delivery time at only one day.

# 1.8.12
- Bugfix next working day

# 1.8.11
- Bugfix delivery time at only one day.

# 1.8.10
- Added compatibility with the "Pseudovariants" plugin.
 
# 1.8.9
- If there is no delivery time in the product, the delivery time of the shipping method is now used.
- Refreshing the countdown in the frontend now works in Shopware 6.5.
- Fixed an issue with the calculation of the next delivery date.

# 1.8.8
- Fixed display issue on category page.

# 1.8.7
- Some holidays are not taken into account
- Catch error message

# 1.8.6
- Improvement of ajax url

# 1.8.5
- Improved display of the countdown in the product listing.

# 1.8.4
- Shopware 6.5 support

# 1.8.3
- Fallback date when checking out an item without a delivery time.

# 1.8.2
- Fallback date when checking out an item without a delivery time.

# 1.8.1
- Error message when checking out an item without a delivery time.

# 1.8.0
- The display of the countdown can be deactivated for articles depending on stock and sales.

# 1.7.3
- Compatibility with the "Show worlds of experience on products" plugin established.

# 1.7.2
- Fixed issues with discounts.

# 1.7.1
- Problems in the shopping cart fixed.

# 1.7.0
- The displayed delivery time now changes depending on the shipping method.
- The countdown and the delivery date are now saved in the order at the time of the order.
- The countdown and the delivery date can be used via the variables {{netzhirschOrderCountdown.deadline|date('d.m.Y')}} and {{netzhirschOrderCountdown.deliveryDate|date('d.m.Y')}} in the order confirmation email. Attention only from Shopware 6.4.13.

# 1.6.3
- Correct date for special holidays
- Improved consideration of special holidays in the frontend.

# 1.6.2
- Correct time for special holidays.

# 1.6.1
- Removed superfluous HTML if the countdown should not be displayed in the shopping cart.

# 1.6
- Default spanish support.
- Menu item "special holidays" was no longer available.

# 1.5
- Added an option to show the default "Out of Stock" notice.

# 1.4.7
- Increased compatibility with the Magnalister plugin.

# 1.4.6
- RouteScope not imported

# 1.4.5
- Prevent that the route is not found, otherwise log entries will occur
- In shops with different languages, the countdown text was only output in the default language.

# 1.4.4
- Avoid double display of "available immediately".

# 1.4.3
- Increased compatibility with other plugins.
- Do not send a request if there is not enough data.

# 1.4.2
- For products without a delivery time, the delivery time of the main product should be taken.
- Set the countdown reload time back to 1 minute.
- Fix static code analysis warnings.

# 1.4.1
- Show standard shipping time both with and without a countdown in the product.

# 1.4.0
- Option to Switch to show Shopware standard delivery time as well.
- min height for order-countdown-div.
- Fix: wrong return type if a product has a release date.

# 1.3.4
- Product-detail-view only showed fallback for delivery time.

# 1.3.3
- Wrong deadline when the time is over.
- Custom holidays cannot be saved without a deadline. If there is no deadline, the next day must be checked.
- Template played out too often.

# 1.3.2
- The various sales channels are now taken into account in the settings.

# 1.3.1
- JavaScript, in the storefront, was not loaded correctly

# 1.3.0
- The Countdown/Deadline is independent of the cache.
- The Countdown/Deadline is updated every minute.
- Special holidays do not have to have a deadline.

# 1.2.7
- The Europa/Berlin time zone is now always used in the frontend.

# 1.2.6
- Support for Shopware version 6.3
- The menu item "Special Holidays" is, in Shopware 6.3, displayed as a submenu of Orders.
- The aggregated countdown/deadline of all products can now be included in the listing via "{% sw_include '@ NetzhirschOrderCountdown/storefront/component/order-countdown.html.twig'%}".
- Behebt ein Problem mit der Wiederauffüllzeit.

# 1.2.5
- Fixes a problem with disabled countdown/deadline on the product.

# 1.2.4
- Fixes a problem with disabled countdown/deadline on the product.
- 
# 1.2.3
- Fixes a problem with disabled countdown/deadline on the product.

# 1.2.2
- Fixes a problem with disabled countdown/deadline on the product.
- Added German help text for the display methods.

# 1.2.1
- Fixes a problem with pagination in article listing.

# 1.2.0
- Individual holidays can be created. There is a new menu item for this under Extensions.
- The countdown display can be automatically converted to the deadline display after a specified time and vice versa.
- Custom CSS class for styling the display in the storefront.

# 1.1.0
- Alternative snippet for products that are not in stock.
- Show warning instead of success message if product is out of stock (Configurable).

# 1.0.5
- Small bugfix: Restock time.

# 1.0.4
- The restock time is now also taken into account if no delivery time is specified.

# 1.0.3
- Fixes a problem with discounts and promotions in the shopping cart.

# 1.0.2
- Set whether Saturday is a delivery day. 

# 1.0.1
- A problem with the text modules for countdown without delivery time has been fixed.
- Option added to hide the delivery date display.
- Hide countdown for individual products.

# 1.0.0
- Initial release
