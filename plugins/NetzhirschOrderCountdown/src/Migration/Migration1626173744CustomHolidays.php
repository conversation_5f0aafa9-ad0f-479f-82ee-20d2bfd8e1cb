<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1626173744CustomHolidays extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1626173744;
    }

    public function update(Connection $connection): void
    {
        $connection->executeUpdate(
            "
            CREATE TABLE `netzhirsch_order_countdown_holiday` (
                `id` BINARY(16) NOT NULL,
                `date` DATETIME(3) NOT NULL,
                `name` VARCHAR(255) NOT NULL,
                `deadline` DATETIME(3) NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3) NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            "
        );

        $connection->executeUpdate(
            "
            ALTER TABLE `netzhirsch_order_countdown_information`
                ADD `minutes_to_switch_display_mode` INT NULL AFTER `product_id`;
            "
        );
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}
