<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1669817426Order extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1626173744;
    }

    public function update(Connection $connection): void
    {
        $connection->executeStatement(
            "
            CREATE TABLE `netzhirsch_order_countdown_order` (
                `id` BINARY(16) NOT NULL,
                `date` DATETIME(3) NOT NULL,
                `deadline` DATETIME(3) NOT NULL,
                `delivery_date` DATETIME(3) NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3) NULL,
                `order_id` BINARY(16) NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            "
        );
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}
