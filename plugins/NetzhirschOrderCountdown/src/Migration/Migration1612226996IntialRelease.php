<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1612226996IntialRelease extends MigrationStep {
    public function getCreationTimestamp():int {
        return 1612226996;
    }

    public function update(Connection $connection):void {
        $connection->executeUpdate(
            "
            CREATE TABLE `netzhirsch_order_countdown_information` (
                `id` BINARY(16) NOT NULL,
                `hide_order_countdown` TINYINT(1) NOT NULL DEFAULT '0',
                `product_id` BINARY(16) NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3) NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            "
        );
    }

    public function updateDestructive(Connection $connection):void {
        // implement update destructive
    }
}
