<?php

namespace NetzhirschOrderCountdown\Service;

use DateTime;
use DateTimeInterface;
use DateTimeZone;
use Exception;
use NetzhirschOrderCountdown\Entities\Holiday\HolidayEntity;
use NetzhirschOrderCountdown\Entities\OrderCountdownInformation\OrderCountdownInformationEntity;
use NetzhirschOrderCountdown\NetzhirschOrderCountdown;
use Psr\Log\LoggerInterface;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\System\DeliveryTime\DeliveryTimeEntity;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;

class OrderCountdownService
{

    private const DISPLAY_MODE_COUNTDOWN = 'countdown';
    private const DISPLAY_MODE_COUNTDOWN_TO_DEADLINE = 'countdownToDeadline';
    private const DISPLAY_MODE_DEADLINE_TO_COUNTDOWN = 'deadlineToCountdown';
    private const DISPLAY_MODE_DEADLINE = 'deadline';

    private const DELIVERY_DATE_CALCULATION_MODE_OPTIMISTIC = 'optimistic';
    private const DELIVERY_DATE_CALCULATION_MODE_REALISTIC = 'realistic';
    private const DELIVERY_DATE_CALCULATION_MODE_PESSIMISTIC = 'pessimistic';
    private const DELIVERY_DATE_CALCULATION_MODE_INTERVAL = 'interval';
    private const DELIVERY_DATE_CALCULATION_MODE_NONE = 'none';
    private const MAX_DAYS_TO_LOOK_UP = 14;

    private const WEEKDAYS_IN_CONFIG = [
        1 => 'deadlineOnMondays',
        2 => 'deadlineOnTuesdays',
        3 => 'deadlineOnWednesdays',
        4 => 'deadlineOnThursdays',
        5 => 'deadlineOnFridays',
        6 => 'deadlineOnSaturdays',
        7 => 'deadlineOnSundays',
    ];

    private const HOLIDAYS_IN_CONFIG = [
        'deadlineOnNewYearsDay',
        'deadlineOnEpiphany',
        'deadlineOnWomensDay',
        'deadlineOnGoodFriday',
        'deadlineOnEasterSunday',
        'deadlineOnEasterMonday',
        'deadlineOnLabourDay',
        'deadlineOnAscensionDay',
        'deadlineOnWhitMonday',
        'deadlineOnCorpusChristi',
        'deadlineOnAssumptionDay',
        'deadlineOnGermanUnityDay',
        'deadlineOnWorldReformationDay',
        'deadlineOnAllSaintsDay',
        'deadlineOnChristmasEve',
        'deadlineOnChristmasDay',
        'deadlineOnBoxingDay',
        'deadlineOnNewYearsEve',
    ];

    /** @var SystemConfigService */
    private $systemConfigService;

    /** @var LoggerInterface */
    private $logger;
    private $holidayRepository;
    /**
     * @var EntityRepository
     */
    private $productRepository;

    public function __construct(
        SystemConfigService $systemConfigService,
        LoggerInterface $logger,
        EntityRepository $holidayRepository,
        $productRepository
    )
    {
        $this->systemConfigService = $systemConfigService;
        $this->logger = $logger;
        $this->holidayRepository = $holidayRepository;
        $this->productRepository = $productRepository;
    }

    /**
     * @throws Exception
     */
    public static function getNextDeliveryDate(?int $counter,SystemConfigService $systemConfigService,string $salesChannelId,$date = null)
    {
        $date = self::getNextHowDayNoSpecialHolidays($counter,$systemConfigService,$salesChannelId,$date);
        $deliveryOnSaturday = self::config($systemConfigService,'deliveryOnSaturday',$salesChannelId)??null;
        if ((empty($deliveryOnSaturday) && $date->format('N') == 6) || $date->format('N') == 7) {
            $date = self::getNextDeliveryDate($counter + 1,$systemConfigService,$salesChannelId);
        }

        return $date;
    }

    /**
     * @throws Exception
     */
    private static function getNextHowDayNoSpecialHolidays(?int $counter,SystemConfigService $systemConfigService,string $salesChannelId,$date = null)
    {
        if (empty($date)) {
            $date = new DateTime();
        }
        if (empty($counter)) {
            $counter = 1;
        }
        $date = $date->modify('+ ' . $counter . ' days');
        $year = $date->format('Y');
        foreach (self::HOLIDAYS_IN_CONFIG as $holiday) {
            if (!empty(self::getMatchedHoliday($holiday,$date->format('m-d'), self::getEasterDate($year)))) {
                $date = self::getNextDeliveryDate($counter + 1,$systemConfigService,$salesChannelId);
            }
        }
        return $date;
    }

    /**
     * @param string $holiday
     * @param        $dayInYear
     * @param string $matchedHoliday
     * @param        $easter
     *
     * @return string
     * @throws Exception
     */
    private static function getMatchedHoliday(string $holiday, $dayInYear, $easter): string
    {
        $matchedHoliday = '';
        switch ($holiday) {
            case 'deadlineOnNewYearsDay':
                if ($dayInYear === '01-01') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnEpiphany':
                if ($dayInYear === '01-06') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnWomensDay':
                if ($dayInYear === '03-08') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnGoodFriday':
                $goodFriday = new DateTime('@' . strtotime('-2 days', $easter));
                if ($dayInYear === $goodFriday->format('m-d')) {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnEasterSunday':
                $easterSunday = new DateTime('@' . $easter);
                if ($dayInYear === $easterSunday->format('m-d')) {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnEasterMonday':
                $easterMonday = new DateTime('@' . strtotime('+1 day', $easter));
                if ($dayInYear === $easterMonday->format('m-d')) {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnLabourDay':
                if ($dayInYear === '05-01') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnAscensionDay':
                $ascensionDay = new DateTime('@' . strtotime('+39 days', $easter));
                if ($dayInYear === $ascensionDay->format('m-d')) {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnWhitMonday':
                $whitMonday = new DateTime('@' . strtotime('+50 days', $easter));
                if ($dayInYear === $whitMonday->format('m-d')) {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnCorpusChristi':
                $corpusChristi = new DateTime('@' . strtotime('+60 days', $easter));
                if ($dayInYear === $corpusChristi->format('m-d')) {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnAssumptionDay':
                if ($dayInYear === '08-15') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnGermanUnityDay':
                if ($dayInYear === '10-03') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnWorldReformationDay':
                if ($dayInYear === '10-31') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnAllSaintsDay':
                if ($dayInYear === '11-01') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnChristmasEve':
                if ($dayInYear === '12-24') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnChristmasDay':
                if ($dayInYear === '12-25') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnBoxingDay':
                if ($dayInYear === '12-26') {
                    $matchedHoliday = $holiday;
                }
                break;

            case 'deadlineOnNewYearsEve':
                if ($dayInYear === '12-31') {
                    $matchedHoliday = $holiday;
                }
                break;
        }

        return $matchedHoliday;
    }

    public function getProductWithDeliveryTimeAndCountdownInformation(
        string $productNumber,
        Context $context,
        SalesChannelContext $salesChannelContext
    ): ProductEntity
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('productNumber', $productNumber));
        $criteria->addAssociation('orderCountdownInformation');
        $criteria->getAssociation('orderCountdownInformation');
        $criteria->addAssociation('deliveryTime');
        $criteria->getAssociation('deliveryTime');
        $criteria->addAssociation('children');
        $criteria->addAssociation('children.deliveryTime');
        $products = $this->productRepository->search($criteria, $context);
        /** @var ProductEntity $product */
        $product = $products->first();
        if ($product->getProductNumber() != $productNumber) {
            foreach ($product->getChildren() as $child) {
                if ($child->getProductNumber() == $productNumber) {
                    $deliverTime = $child->getDeliveryTime();
                    if (empty($deliverTime))
                        $deliverTime = $product->getDeliveryTime();
                    $child->setDeliveryTime($deliverTime);
                    return $child;
                }
            }
        }

        if (empty($product->getDeliveryTime())) {
            $criteria = new Criteria();
            $criteria->addFilter(new EqualsFilter('children.productNumber', $productNumber));
            $criteria->addAssociation('children');
            $criteria->addAssociation('deliveryTime');
            $criteria->getAssociation('deliveryTime');
            $parents = $this->productRepository->search($criteria, $context);
            /** @var ProductEntity $parent */
            $parent = $parents->first();
            if (!empty($parent))
                $product->setDeliveryTime($parent->getDeliveryTime());
        }
        if (empty($product->getDeliveryTime())) {
            $shippingMethod = $salesChannelContext->getShippingMethod();
            $product->setDeliveryTime($shippingMethod->getDeliveryTime());
        }
        return $product;
    }

    /**
     * @throws Exception
     */
    public function collectOrderCountdownDataForProduct(
        ProductEntity $product,
        $showWarningIfOutOfStock,
        $showNoCountdownOutOfStock,
        string $salesChannelId
    ): array
    {
        /** @var OrderCountdownInformationEntity $orderCountdownInformation */
        $orderCountdownInformation = $product->getExtension('orderCountdownInformation');
        if (!empty($orderCountdownInformation) && $orderCountdownInformation->isHideOrderCountdown()) {
            $this->logger->info('OrderCountdown hidden in the product config.');
            return [];
        }

        $buyable =
            $product->getAvailable()
            && $product->getChildCount() <= 0;
        if (!$buyable) {
                $this->logger->info('Product is no available, product is parent product or Product is out of stock Product-ID: '.$product->getId());
            return [];
        }

        if (
            (
                $showNoCountdownOutOfStock == 'outOfStock'
                && $product->getAvailableStock() <= 0
            )
            || (
                $showNoCountdownOutOfStock == 'outOfStockAndIsCloseout'
                && $product->getAvailableStock() <= 0
                && $product->getIsCloseout()
            )
        )
            return [];

        $minDeliveryTime = null;
        $maxDeliveryTime = null;
        $deliveryTimeFromProduct = $product->getDeliveryTime();
        $restockTime = 0;
        if ($product->getAvailableStock() <= 0) {
            $restockTime = $product->getRestockTime();
        }
        if (!empty($deliveryTimeFromProduct)) {
            $minDeliveryTime = $deliveryTimeFromProduct->getMin() ?? 1;
            $maxDeliveryTime = $deliveryTimeFromProduct->getMax() ?? $minDeliveryTime;

            switch ($deliveryTimeFromProduct->getUnit()) {
                case DeliveryTimeEntity::DELIVERY_TIME_MONTH:
                    $minDeliveryTime *= 30;
                    $maxDeliveryTime *= 30;
                    break;
                case DeliveryTimeEntity::DELIVERY_TIME_WEEK:
                    $minDeliveryTime *= 7;
                    $maxDeliveryTime *= 7;
                    break;
            }

            if ($product->getAvailableStock() <= 0 && !empty($restockTime)) {
                $minDeliveryTime += $restockTime;
                $maxDeliveryTime += $restockTime;
            }
        }

        $now = new Datetime();
        $releaseDate = $product->getReleaseDate();
        $start = (empty($releaseDate) || $releaseDate < $now) ? $now : $releaseDate;

        $alertType = ($product->getAvailableStock() > 0 || $restockTime == 0 || !$showWarningIfOutOfStock)
            ? 'success'
            : 'warning'
        ;
        $outOfStock = $product->getAvailableStock() <= 0 && ($restockTime > 0 || is_null($restockTime));
        $restockTime = self::getNextHowDayNoSpecialHolidays($restockTime,$this->systemConfigService,$salesChannelId);

        return [
            'start' => $start,
            'minDeliveryTime' => $minDeliveryTime,
            'maxDeliveryTime' => $maxDeliveryTime,
            'restockTime' => $restockTime,
            'alertType' => $alertType,
            'outOfStock' => $outOfStock,
        ];
    }

    /**
     * @throws Exception
     */
    public function getOptionsToAssign(
        array $collectOrderCountdownDataForProduct,
        Context $context,
        string $salesChannelId
    ): array
    {

        $systemConfigService = $this->systemConfigService;

        $holidayRepository = $this->holidayRepository;
        $logger = $this->logger;

        if (!isset($collectOrderCountdownDataForProduct['start']))
            return [];

        $start = $collectOrderCountdownDataForProduct['start'];
        $minDeliveryTime = $collectOrderCountdownDataForProduct['minDeliveryTime'];
        $maxDeliveryTime = $collectOrderCountdownDataForProduct['maxDeliveryTime'];
        $restockTime = $collectOrderCountdownDataForProduct['restockTimeMax'] ?? 3;
        $restockTime = self::getNextHowDayNoSpecialHolidays($restockTime,$systemConfigService,$salesChannelId);

        $alertType = $collectOrderCountdownDataForProduct['alertType'] ?? 'success';
        $outOfStock = $collectOrderCountdownDataForProduct['outOfStock'] ?? false;
        $timezone = new DateTimeZone('Europe/Berlin');
        $now = new Datetime('now',$timezone);

        $deadline = self::getDeadline($start,$holidayRepository,$logger,$context,$systemConfigService,$salesChannelId);
        if ($deadline == new DateTime('yesterday')) {
            return [];
        }

        if (!empty($deadline)) {
            $timezoneString = 'Europe/Berlin';
            if (isset($_COOKIE['timezone']))
                $timezoneString = $_COOKIE['timezone'];
            $timezone = new DateTimeZone($timezoneString);
            $deadline->setTimezone($timezone);
            $interval = date_diff($now, $deadline);
            $tomorrow = new DateTime('+1 day',$timezone);
            $isToday = $now->format('Y-m-d') == $deadline->format('Y-m-d');
            $isTomorrow = $tomorrow->format('Y-m-d') == $deadline->format('Y-m-d');

            [$earliestDeliveryDate, $latestDeliveryDate] = self::getDeliveryDates(
                $deadline,
                $minDeliveryTime,
                $maxDeliveryTime,
                $systemConfigService,
                $salesChannelId
            );
        } else {
            return [];
        }

        $displayMode = self::config($systemConfigService,'displayMode',$salesChannelId);
        if (empty($displayMode)) {
            $displayMode = self::DISPLAY_MODE_COUNTDOWN;
        }
        if ($displayMode != self::DISPLAY_MODE_COUNTDOWN && $displayMode != self::DISPLAY_MODE_DEADLINE) {
            $minutesToSwitchDisplayMode = self::config($systemConfigService,'minutesToSwitchDisplayMode',$salesChannelId);
            $minutesLeft = $interval->h*60 + $interval->i;

            if ($minutesToSwitchDisplayMode >= $minutesLeft) {
                $displayMode
                    = ($displayMode == self::DISPLAY_MODE_COUNTDOWN_TO_DEADLINE)?self::DISPLAY_MODE_DEADLINE:$displayMode;
                $displayMode
                    = ($displayMode == self::DISPLAY_MODE_DEADLINE_TO_COUNTDOWN)?self::DISPLAY_MODE_COUNTDOWN:$displayMode;
            } else {
                $displayMode
                    = ($displayMode == self::DISPLAY_MODE_COUNTDOWN_TO_DEADLINE)?self::DISPLAY_MODE_COUNTDOWN:$displayMode;
                $displayMode
                    = ($displayMode == self::DISPLAY_MODE_DEADLINE_TO_COUNTDOWN)?self::DISPLAY_MODE_DEADLINE:$displayMode;
            }
        }

        return
            [
                'displayMode' => $displayMode,
                'deadline' => $deadline,
                'isToday' => $isToday,
                'isTomorrow' => $isTomorrow,
                'interval' => $interval,
                'earliestDeliveryDate' => $earliestDeliveryDate,
                'latestDeliveryDate' => $latestDeliveryDate,
                'restockForFallback' => $restockTime,
                'alertType' => $alertType,
                'outOfStock' => $outOfStock,
                'showSWAGDeliveryDate' => self::config($systemConfigService,'showSWAGDeliveryDate',$salesChannelId),
                'showSWAGSoldOut' => self::config($systemConfigService,'showSWAGSoldOut',$salesChannelId)?true:false,
            ]
            ;
    }

    /**
     * @param DateTimeInterface $now
     * @param EntityRepository $holidayRepository
     * @param LoggerInterface $logger
     * @param Context $context
     * @param SystemConfigService $systemConfigService
     * @return DateTime|null
     */
    public static function getDeadline(
        DateTimeInterface $now,
        EntityRepository $holidayRepository,
        LoggerInterface $logger,
        Context $context,
        SystemConfigService $systemConfigService,
        string $salesChannelId
    ): ?DateTime
    {
        $dayOfShipping = clone $now;
        $daysLookedUp = 0;
        $maxDayToLookUp = self::config($systemConfigService, 'longestDeadlineInDays', $salesChannelId);
        if (empty($maxDayToLookUp))
            $maxDayToLookUp = self::MAX_DAYS_TO_LOOK_UP;
        while ($maxDayToLookUp > $daysLookedUp) {
            $isHoliday = false;
            try {
                [$isHoliday, $deadlineFromHoliday]
                    = self::getDeadlineFromCustomHoliday($dayOfShipping,$holidayRepository,$context);
                if ($isHoliday && $deadlineFromHoliday == '00:00') {
                    $dayOfShipping->modify('+1 day');
                    $daysLookedUp++;
                    continue;
                }
                if (
                    (
                        $isHoliday && !empty($deadlineFromHoliday) && $deadlineFromHoliday > $now)
                    || ($isHoliday && !empty($deadlineFromHoliday) && $deadlineFromHoliday == new DateTime('yesterday'))
                ) {
                    return $deadlineFromHoliday;
                }
            } catch (Exception $exception) {
                $logger->error('Error while getDeadlineCustomFromHoliday: '.$exception->getMessage());
            }
            try {
                [$isHoliday, $deadlineFromHoliday] = self::getDeadlineFromHoliday($dayOfShipping,$systemConfigService,$salesChannelId);
                if (!$isHoliday && !empty($deadlineFromHoliday) && $deadlineFromHoliday > $now) {
                    return $deadlineFromHoliday;
                }
            } catch (Exception $exception) {
                $logger->error('Error while getDeadlineFromHoliday: '.$exception->getMessage());
            }

            try {
                $deadlineFromWeekday = self::getDeadlineFromWeekday($dayOfShipping,$systemConfigService,$salesChannelId);
                if (!$isHoliday && !empty($deadlineFromWeekday) && $deadlineFromWeekday > $now) {
                    return $deadlineFromWeekday;
                }
            } catch (Exception $exception) {
                $logger->error('Error while getDeadlineFromWeekday: '.$exception->getMessage());
            }
            $dayOfShipping->modify('+1 day');
            $daysLookedUp++;
        }
        return null;
    }

    /**
     * @param DateTimeInterface $day
     * @param SystemConfigService $systemConfigService
     * @param string $salesChannelId
     * @return DateTime|null
     * @throws Exception
     */
    private static function getDeadlineFromWeekday(
        DateTimeInterface $day,
        SystemConfigService $systemConfigService,
        string $salesChannelId
    ): ?DateTimeInterface
    {
        $dayOfTheWeek = $day->format('N');
        $configName = self::WEEKDAYS_IN_CONFIG[$dayOfTheWeek];
        $deadlineTime = self::config($systemConfigService,$configName,$salesChannelId);
        if (empty($deadlineTime)) {
            return null;
        }

        return self::getBerlinDateTimeFromConfigString($deadlineTime,$day);
    }

    /**
     * @param DateTimeInterface $day
     * @param SystemConfigService $systemConfigService
     * @param string $salesChannelId
     * @return array
     * @throws Exception
     */
    private static function getDeadlineFromHoliday
    (
        DateTimeInterface $day,SystemConfigService
        $systemConfigService,
        string $salesChannelId
    ): array
    {
        $currentYear = intval($day->format('Y'));
        $easter = self::getEasterDate($currentYear);
        $dayInYear = $day->format('m-d');

        /* Iterate threw holidays and return deadline */
        foreach (self::HOLIDAYS_IN_CONFIG as $holiday) {
            $matchedHoliday = self::getMatchedHoliday($holiday, $dayInYear, $easter);

            if (!empty($matchedHoliday)) {
                $deadline = null;
                $deadlineTime = self::config($systemConfigService,$matchedHoliday,$salesChannelId);
                if (!empty($deadlineTime)) {
                    $deadline = self::getBerlinDateTimeFromConfigString($deadlineTime,$day);
                }

                return [true, $deadline];
            }
        }

        return [false, null];
    }

    /**
     * @throws Exception
     */
    private static function getDeadlineFromCustomHoliday(
        DateTimeInterface $day,
        EntityRepository $holidayRepository,
        Context $context
    ): array
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('date', $day->format('Y-m-d')));
        $criteria->setLimit(1);
        $criteria->addSorting(new FieldSorting('deadline', 'ASC'));
        $result = $holidayRepository->search($criteria, $context);
        /** @var HolidayEntity $customHoliday */
        $customHoliday = $result->getEntities()->first();
        if (!empty($customHoliday)) {
            $deadlineTime = $customHoliday->getDeadline();
            if (!empty($deadlineTime)) {
                $deadlineDateTimestamp = strtotime($day->format('Y-m-d').' '.$deadlineTime->format('H:i'));
                $deadline = new DateTime('@'.$deadlineDateTimestamp);
                $deadline = self::getBerlinDateTimeFromConfigString($deadlineTime->format('H:i'), $deadline);
            } else {
                return [true, '00:00'];
            }

            return [true, $deadline];
        }

        return [false, null];
    }

    /**
     * @param DateTime|null $deadline
     * @param $minDeliveryTime
     * @param $maxDeliveryTime
     * @param SystemConfigService $systemConfigService
     * @return array
     */
    public static function getDeliveryDates(
        ?DateTime $deadline,
        $minDeliveryTime,
        $maxDeliveryTime,
        SystemConfigService $systemConfigService,
        string $salesChannelId
    ): array
    {
        $deliveryDateCalculationMode = self::config($systemConfigService,'deliveryDateCalculationMode',$salesChannelId);
        if (empty($deliveryDateCalculationMode))
            $deliveryDateCalculationMode = self::DELIVERY_DATE_CALCULATION_MODE_OPTIMISTIC;

        if (
            $deliveryDateCalculationMode == self::DELIVERY_DATE_CALCULATION_MODE_NONE
            || (empty($minDeliveryTime) && empty($maxDeliveryTime))
        ) {
            return [null, null];
        }

        $minDeliveryTime = $minDeliveryTime ?? $maxDeliveryTime;
        $minDeliveryTime = $minDeliveryTime==1?0:$minDeliveryTime;
        $maxDeliveryTime = $maxDeliveryTime ?? $minDeliveryTime;


        $earliestDelivery = null;
        $latestDelivery = null;
        $tmpDate = clone $deadline;
        switch ($deliveryDateCalculationMode) {
            case self::DELIVERY_DATE_CALCULATION_MODE_OPTIMISTIC:
                $earliestDelivery = self::getNextDeliveryDate($minDeliveryTime,$systemConfigService,$salesChannelId,$tmpDate);
                break;
            case self::DELIVERY_DATE_CALCULATION_MODE_REALISTIC:
                $earliestDelivery = self::getNextDeliveryDate(floor(($minDeliveryTime + $maxDeliveryTime) / 2),$systemConfigService,$salesChannelId,$tmpDate);
                break;
            case self::DELIVERY_DATE_CALCULATION_MODE_PESSIMISTIC:
                $earliestDelivery = self::getNextDeliveryDate($maxDeliveryTime,$systemConfigService,$salesChannelId,$tmpDate);
                break;
            case self::DELIVERY_DATE_CALCULATION_MODE_INTERVAL:
                $earliestDelivery = self::getNextDeliveryDate($minDeliveryTime,$systemConfigService,$salesChannelId,$tmpDate);
                $latestDelivery = self::getNextDeliveryDate($maxDeliveryTime,$systemConfigService,$salesChannelId,$tmpDate);
                break;
        }

        if (
            $minDeliveryTime == $maxDeliveryTime
            || empty($earliestDelivery)
            || empty($latestDelivery) ||
            $earliestDelivery->format('dmY') == $latestDelivery->format('dmY')
        ) {
            $latestDelivery = null;
        }
        return [$earliestDelivery, $latestDelivery];
    }

    /**
     * @param SystemConfigService $systemConfigService
     * @param $string
     * @param string $salesChannelId
     * @return array|bool|float|int|string|null
     */
    public static function config(SystemConfigService $systemConfigService,$string,string $salesChannelId)
    {
        return $systemConfigService->get(NetzhirschOrderCountdown::TECHNICAL_NAME.'.config.'.$string,$salesChannelId);
    }

    /**
     * @param $year
     *
     * @return false|int
     * The MIT License (MIT)
     * Copyright (c) 2017 Marcel Steinger
     */
    private static function getEasterDate($year)
    {
        $J = intval(date("Y", mktime(0, 0, 0, 1, 1, $year)));
        $K = floor($J / 100);
        $M = 15 + floor((3 * $K + 3) / 4) - floor((8 * $K + 13) / 25);
        $S = 2 - floor((3 * $K + 3) / 4);
        $A = $J % 19;
        $D = (19 * $A + $M) % 30;
        $R = floor($D / 29) + (floor($D / 28) - floor($D / 29)) * floor($A / 11);
        $OG = 21 + $D - $R;
        $SZ = 7 - (($J + floor($J / 4) + $S) % 7);
        $OE = 7 - (($OG - $SZ) % 7);
        $OS = $OG + $OE;

        return mktime(0, 0, 0, 3, (int)$OS, $J);
    }

    /**
     * @param $deadlineTime
     * @param $date
     * @return DateTimeInterface
     */
    private static function getBerlinDateTimeFromConfigString($deadlineTime,$date): DateTimeInterface
    {
        $timezoneString = 'Europe/Berlin';
        if (isset($_COOKIE['timezone']))
            $timezoneString = $_COOKIE['timezone'];
        $timezone = new DateTimeZone($timezoneString);
        $timezoneUtc = new DateTimeZone('UTC');
        $date->setTimezone($timezone);
        $timeString = explode(':', $deadlineTime);
        $hours = (int)$timeString[0];
        $minutes = (int)$timeString[1];
        $date->setTime($hours, $minutes);
        $date->setTimezone($timezoneUtc);

        return $date;
    }
}
