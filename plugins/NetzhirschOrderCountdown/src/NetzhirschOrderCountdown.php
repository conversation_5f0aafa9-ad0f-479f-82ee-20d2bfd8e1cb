<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;

class NetzhirschOrderCountdown extends Plugin {

    public const TECHNICAL_NAME = 'NetzhirschOrderCountdown';


    public function uninstall(UninstallContext $uninstallContext):void {
        parent::uninstall($uninstallContext);

        if ($uninstallContext->keepUserData())
            return;

        $connection = $this->container->get(Connection::class);
        $connection->executeUpdate('DROP TABLE IF EXISTS `netzhirsch_order_countdown_information`');
        $connection->executeUpdate('DROP TABLE IF EXISTS `netzhirsch_order_countdown_holiday`');
        $connection->executeUpdate('DROP TABLE IF EXISTS `netzhirsch_order_countdown_order`');
    }
}
