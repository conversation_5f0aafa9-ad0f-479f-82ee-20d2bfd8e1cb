<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\OrderCountdownInformation;

use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;

class OrderCountdownInformationEntity extends Entity {
    use EntityIdTrait;

    /**
     * @var bool $active
     */
    protected $hideOrderCountdown = false;

    /**
     * @var string $productId
     */
    protected $productId;

    /**
     * @var ProductEntity|null
     */
    protected $product;

    protected $minutesToSwitchDisplayMode;

    /**
     * @return bool
     */
    public function isHideOrderCountdown():bool {
        return $this->hideOrderCountdown;
    }

    /**
     * @param bool $hideOrderCountdown
     */
    public function setHideOrderCountdown(bool $hideOrderCountdown):void {
        $this->hideOrderCountdown = $hideOrderCountdown;
    }

    /**
     * @return string
     */
    public function getProductId():string {
        return $this->productId;
    }

    /**
     * @param string $productId
     */
    public function setProductId(string $productId):void {
        $this->productId = $productId;
    }

    /**
     * @return ProductEntity|null
     */
    public function getProduct():?ProductEntity {
        return $this->product;
    }

    /**
     * @param ProductEntity|null $product
     */
    public function setProduct(?ProductEntity $product):void {
        $this->product = $product;
    }

    /**
     * @return int|null
     */
    public function getMinutesToSwitchDisplayMode(): ?int
    {
        return $this->minutesToSwitchDisplayMode;
    }

    /**
     * @param int|null $minutesToSwitchDisplayMode
     */
    public function setMinutesToSwitchDisplayMode(?int $minutesToSwitchDisplayMode): void
    {
        $this->minutesToSwitchDisplayMode = $minutesToSwitchDisplayMode;
    }

}
