<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\OrderCountdownInformation;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void              add(OrderCountdownInformationEntity $entity)
 * @method void              set(string $key, OrderCountdownInformationEntity $entity)
 * @method OrderCountdownInformationEntity[]    getIterator()
 * @method OrderCountdownInformationEntity[]    getElements()
 * @method OrderCountdownInformationEntity|null get(string $key)
 * @method OrderCountdownInformationEntity|null first()
 * @method OrderCountdownInformationEntity|null last()
 */
class OrderCountdownInformationCollection extends EntityCollection {
    protected function getExpectedClass():string {
        return OrderCountdownInformationEntity::class;
    }
}
