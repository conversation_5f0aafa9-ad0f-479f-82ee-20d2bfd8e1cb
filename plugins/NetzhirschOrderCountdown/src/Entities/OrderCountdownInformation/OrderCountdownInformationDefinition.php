<?php

declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\OrderCountdownInformation;

use Shopware\Core\Content\Product\ProductDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\BoolField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\RestrictDelete;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IntField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class OrderCountdownInformationDefinition extends EntityDefinition {
    public const ENTITY_NAME = 'netzhirsch_order_countdown_information';

    public function getEntityName():string {
        return self::ENTITY_NAME;
    }

    public function getCollectionClass():string {
        return OrderCountdownInformationCollection::class;
    }

    public function getEntityClass():string {
        return OrderCountdownInformationEntity::class;
    }

    protected function defineFields():FieldCollection {
        return new FieldCollection(
            [
                (new IdField('id', 'id'))->addFlags(new PrimaryKey(), new Required()),

                (new BoolField('hide_order_countdown', 'hideOrderCountdown'))->addFlags(new Required()),
                (new IntField('minutes_to_switch_display_mode', 'minutesToSwitchDisplayMode')),

                (new FkField('product_id', 'productId', ProductDefinition::class)),
                (new OneToOneAssociationField(
                    'product',
                    'product_id',
                    'id',
                    ProductDefinition::class,
                    false
                ))->addFlags(new RestrictDelete()),

            ]
        );
    }
}
