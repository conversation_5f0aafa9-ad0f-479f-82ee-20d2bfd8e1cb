<?php

declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\OrderCountdownOrder;

use Shopware\Core\Checkout\Order\OrderDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\DateField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\DateTimeField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\RestrictDelete;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class OrderCountdownOrderDefinition extends EntityDefinition {
    public const ENTITY_NAME = 'netzhirsch_order_countdown_order';

    public function getEntityName():string {
        return self::ENTITY_NAME;
    }

    public function getCollectionClass():string {
        return OrderCountdownOrderCollection::class;
    }

    public function getEntityClass():string {
        return OrderCountdownOrderEntity::class;
    }

    protected function defineFields():FieldCollection {
        return new FieldCollection(
            [
                (new IdField('id', 'id'))->addFlags(new PrimaryKey(), new Required()),

                (new DateTimeField('date', 'date'))->addFlags(new Required()),
                (new DateTimeField('deadline', 'deadline'))->addFlags(new Required()),
                (new DateTimeField('delivery_date', 'deliveryDate'))->addFlags(new Required()),
                (new FkField('order_id', 'orderId', OrderDefinition::class)),
                (new OneToOneAssociationField(
                    'order',
                    'order_id',
                    'id',
                    OrderDefinition::class,
                    true
                ))->addFlags(new RestrictDelete()),

            ]
        );
    }
}
