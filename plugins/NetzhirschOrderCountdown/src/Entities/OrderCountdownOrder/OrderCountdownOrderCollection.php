<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\OrderCountdownOrder;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void              add(OrderCountdownOrderEntity $entity)
 * @method void              set(string $key, OrderCountdownOrderEntity $entity)
 * @method OrderCountdownOrderEntity[]    getIterator()
 * @method OrderCountdownOrderEntity[]    getElements()
 * @method OrderCountdownOrderEntity|null get(string $key)
 * @method OrderCountdownOrderEntity|null first()
 * @method OrderCountdownOrderEntity|null last()
 */
class OrderCountdownOrderCollection extends EntityCollection {
    protected function getExpectedClass():string {
        return OrderCountdownOrderEntity::class;
    }
}
