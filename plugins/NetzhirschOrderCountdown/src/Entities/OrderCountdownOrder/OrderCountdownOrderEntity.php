<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\OrderCountdownOrder;

use DateTimeInterface;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;

class OrderCountdownOrderEntity extends Entity {
    use EntityIdTrait;

    /**
     * @var DateTimeInterface $deadline
     */
    protected $deadline;

    /**
     * @var DateTimeInterface $productId
     */
    protected $deliveryDate;

    /**
     * @var OrderEntity|null
     */
    protected $order;

    /**
     * @var string $orderId
     */
    protected $orderId;

    /**
     * @return string
     */
    public function getOrderId():string {
        return $this->orderId;
    }

    /**
     * @param string $orderId
     */
    public function setOrderId(string $orderId):void {
        $this->orderId = $orderId;
    }

    /**
     * @return OrderEntity|null
     */
    public function getOrder():?OrderEntity {
        return $this->order;
    }

    /**
     * @param OrderEntity|null $order
     */
    public function setOrder(?OrderEntity $order):void {
        $this->order = $order;
    }

    /**
     * @return DateTimeInterface
     */
    public function getDeadline(): DateTimeInterface
    {
        return $this->deadline;
    }

    /**
     * @param DateTimeInterface $deadline
     */
    public function setDeadline(DateTimeInterface $deadline): void
    {
        $this->deadline = $deadline;
    }

    /**
     * @return DateTimeInterface
     */
    public function getDeliveryDate(): DateTimeInterface
    {
        return $this->deliveryDate;
    }

    /**
     * @param DateTimeInterface $deliveryDate
     */
    public function setDeliveryDate(DateTimeInterface $deliveryDate): void
    {
        $this->deliveryDate = $deliveryDate;
    }
}
