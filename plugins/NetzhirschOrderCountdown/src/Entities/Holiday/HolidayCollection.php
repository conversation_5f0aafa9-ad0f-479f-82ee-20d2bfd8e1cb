<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\Holiday;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void              add(HolidayEntity $entity)
 * @method void              set(string $key, HolidayEntity $entity)
 * @method HolidayEntity[]    getIterator()
 * @method HolidayEntity[]    getElements()
 * @method HolidayEntity|null get(string $key)
 * @method HolidayEntity|null first()
 * @method HolidayEntity|null last()
 */
class HolidayCollection extends EntityCollection {
    protected function getExpectedClass():string {
        return HolidayEntity::class;
    }
}
