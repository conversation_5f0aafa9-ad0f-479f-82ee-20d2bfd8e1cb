<?php

declare(strict_types=1);

namespace NetzhirschOrderCountdown\Entities\Holiday;

use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\DateField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\DateTimeField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class HolidayDefinition extends EntityDefinition {
    public const ENTITY_NAME = 'netzhirsch_order_countdown_holiday';

    public function getEntityName():string {
        return self::ENTITY_NAME;
    }

    public function getCollectionClass():string {
        return HolidayCollection::class;
    }

    public function getEntityClass():string {
        return HolidayEntity::class;
    }

    protected function defineFields():FieldCollection {
        return new FieldCollection(
            [
                (new IdField('id', 'id'))->addFlags(new PrimaryKey(), new Required()),

                (new DateField('date', 'date'))->addFlags(new Required()),
                (new DateTimeField('deadline', 'deadline')),
                (new StringField('name', 'name'))->addFlags(new Required())

            ]
        );
    }
}
