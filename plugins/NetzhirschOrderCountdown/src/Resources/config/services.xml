<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="NetzhirschOrderCountdown\Extension\ProductExtension">
            <tag name="shopware.entity.extension"/>
        </service>

        <service id="NetzhirschOrderCountdown\Entities\OrderCountdownInformation\OrderCountdownInformationDefinition">
            <tag name="shopware.entity.definition" entity="netzhirsch_order_countdown_information" />
        </service>

        <service id="NetzhirschOrderCountdown\Entities\OrderCountdownOrder\OrderCountdownOrderDefinition">
            <tag name="shopware.entity.definition" entity="netzhirsch_order_countdown_order" />
        </service>

        <service id="NetzhirschOrderCountdown\Entities\Holiday\HolidayDefinition">
            <tag name="shopware.entity.definition" entity="netzhirsch_order_countdown_holiday" />
        </service>

        <service id="NetzhirschOrderCountdown\Util\Logger" class="Monolog\Logger">
            <factory service="Shopware\Core\Framework\Log\LoggerFactory" method="createRotating"/>
            <argument type="string">netzhirsch_order_countdown</argument>
        </service>

        <service id="NetzhirschOrderCountdown\Subscriber\CountdownSubscriber">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="NetzhirschOrderCountdown\Util\Logger"/>
            <argument type="service" id="netzhirsch_order_countdown_holiday.repository"/>
            <argument type="service" id="NetzhirschOrderCountdown\Service\OrderCountdownService"/>
            <argument type="service" id="netzhirsch_order_countdown_order.repository"/>
            <argument type="service" id="product.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="NetzhirschOrderCountdown\Storefront\Controller\NetzhirschOrderCountdownController" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="NetzhirschOrderCountdown\Service\OrderCountdownService"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextFactory"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <call method="setTwig">
                <argument type="service" id="twig"/>
            </call>
        </service>

        <service id="NetzhirschOrderCountdown\Service\OrderCountdownService">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="NetzhirschOrderCountdown\Util\Logger"/>
            <argument type="service" id="netzhirsch_order_countdown_holiday.repository"/>
            <argument type="service" id="product.repository"/>
        </service>
    </services>
</container>
