import template from './netzhirsch-order-countdown-holiday-list.html.twig';

const {Component, Mixin} = Shopware;
const {Criteria} = Shopware.Data;

Component.register('netzhirsch-order-countdown-holiday-list', {
    template,

    metaInfo() {
        return {
            title: this.$createTitle()
        };
    },

    inject: ['repositoryFactory'],

    mixins: [
        Mixin.getByName('listing')
    ],

    data() {
        return {
            sortBy: 'date',
            sortDirection: 'ASC',
            isLoading: true,
            holidays: null
        };
    },

    computed: {
        repository() {
            return this.repositoryFactory.create('netzhirsch_order_countdown_holiday');
        },

        columns() {
            return [
                {
                    property: 'date',
                    label: 'netzhirsch-order-countdown-holiday.list.columnDate',
                    allowResize: true
                },
                {
                    property: 'name',
                    label: 'netzhirsch-order-countdown-holiday.list.columnName',
                    routerLink: 'netzhirsch.promocode.group.detail',
                    allowResize: true,
                    primary: true
                },
                {
                    property: 'deadline',
                    label: 'netzhirsch-order-countdown-holiday.list.columnDeadline',
                    allowResize: true
                }
            ];

        }
    },

    methods: {
        getList() {
            this.isLoading = true;
            const criteria = new Criteria(this.page, this.limit);
            criteria.setTerm(this.term);
            criteria.addSorting(Criteria.sort(this.sortBy, this.sortDirection));

            return this.repository
                .search(criteria, Shopware.Context.api)
                .then((searchResult) => {
                    this.total = searchResult.total;
                    this.holidays = searchResult;
                    this.isLoading = false;
                    return this.holidays;
                });
        },

        updateTotal({total}) {
            this.total = total;
        }
    }
});
