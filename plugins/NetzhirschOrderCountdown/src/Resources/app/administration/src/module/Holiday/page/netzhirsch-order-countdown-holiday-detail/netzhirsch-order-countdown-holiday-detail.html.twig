{% block netzhirsch_order_countdown_holiday_list_detail %}
    <sw-page>
        <template slot="smart-bar-actions">
            <sw-button :routerLink="{ name: 'netzhirsch.order.countdown.holiday.list' }">
                {{ $t('netzhirsch-order-countdown-holiday.detail.cancelButtonText') }}
            </sw-button>

            <sw-button-process
                :isLoading="isLoading"
                :processSuccess="processSuccess"
                variant="primary"
                @process-finish="saveFinish"
                @click.prevent="onClickSave">
                {{ $t('netzhirsch-order-countdown-holiday.detail.saveButtonText') }}
            </sw-button-process>
        </template>

        <template slot="content">
            <sw-card-view>
                <sw-card v-if="holiday" :isLoading="isLoading">

                    <sw-container columns="3fr" gap="0px 30px">

                        <sw-field
                            :label="$t('netzhirsch-order-countdown-holiday.detail.nameLabel')"
                            v-model="holiday.name"
                            maxLength="255"
                            required
                        ></sw-field>
                    </sw-container>

                    <sw-container columns="1fr 1fr" gap="0px 30px">
                        <sw-datepicker
                            bordered
                            :label="$t('netzhirsch-order-countdown-holiday.detail.dateLabel')"
                            v-model="holiday.date"
                            required
                        ></sw-datepicker>
                        <sw-datepicker
                            bordered
                            date-type="time"
                            :label="$t('netzhirsch-order-countdown-holiday.detail.deadlineLabel')"
                            v-model="holiday.deadline"
                        ></sw-datepicker>

                    </sw-container>

                </sw-card>
            </sw-card-view>
        </template>
    </sw-page>
{% endblock %}
