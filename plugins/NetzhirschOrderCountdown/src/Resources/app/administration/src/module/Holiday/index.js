import './page/netzhirsch-order-countdown-holiday-list';
import './page/netzhirsch-order-countdown-holiday-detail';

import deDE from './snippet/de-DE.json';
import enGB from './snippet/en-GB.json';

const { Module } = Shopware;

let parent = 'sw-extension';
let baseURL = Shopware.ApiService.getServices().aclApiService.httpClient.defaults.baseURL;
if (baseURL.includes('v2'))
    parent = 'sw-order';

Module.register('netzhirsch-order-countdown-holiday', {

    type: 'plugin',
    name: 'holidays',
    title: 'netzhirsch-order-countdown.general.mainMenuItemGeneral',
    description: 'netzhirsch-order-countdown.general.descriptionTextModule',
    color: '#189EFF',
    icon: 'default-calendar-full',
    entity: 'holiday',

    snippets: {
        'de-DE': deDE,
        'en-GB': enGB
    },

    routes: {
        list: {
            component: 'netzhirsch-order-countdown-holiday-list',
            path: 'list',
            meta: {
                appSystem: {
                    view: 'list'
                }
            }
        },

        create: {
            component: 'netzhirsch-order-countdown-holiday-detail',
            path: 'create',
            meta: {
                parentPath: 'netzhirsch.order.countdown.holiday.list'
            }
        },

        detail: {
            component: 'netzhirsch-order-countdown-holiday-detail',
            path: 'detail/:id',
            meta: {
                parentPath: 'netzhirsch.order.countdown.holiday.list'
            }
        },
    },

    navigation: [
        {
            label: 'netzhirsch-order-countdown.general.mainMenuItemGeneral',
            color: '#FFD700',
            path: 'netzhirsch.order.countdown.holiday.list',
            icon: 'default-package-gift',
            parent: parent,
            position: 3,
        }],
});
