{% block netzhirsch_order_countdown_holiday_list %}
    <sw-page>
        <template slot="smart-bar-actions">
            <sw-button variant="primary" :routerLink="{ name: 'netzhirsch.order.countdown.holiday.create' }">
                {{ $t('netzhirsch-order-countdown-holiday.list.addButtonText') }}
            </sw-button>
        </template>

        <template slot="content">
            <sw-entity-listing
                v-if="holidays"
                :items="holidays"
                :repository="repository"
                :showSelection="false"
                :columns="columns"
                :isLoading="isLoading"
                detailRoute="netzhirsch.order.countdown.holiday.detail"
                @update-records="updateTotal"
                fullPage
            >
                <template #column-name="{ item }">
                    <span>{{ item.name }}</span>
                </template>

                <template #column-date="{ item }">
                    <span>{{ item.date|date({ year: 'numeric',month: 'numeric',day: '2-digit',hour: undefined, minute: undefined, second: undefined }) }}</span>
                </template>

                <template #column-deadline="{ item }">
                    <span v-if="item.deadline">{{ item.deadline|date(
                            { year: undefined,month: undefined,day: undefined,hour: '2-digit', minute: '2-digit', second: undefined }
                        ) }}</span>
                </template>

            </sw-entity-listing>

            <sw-empty-state
                v-if="!isLoading && !total"
                :title="$tc('netzhirsch-order-countdown-holiday.list.messageEmpty')">
            </sw-empty-state>

            <sw-loader v-if="isLoading"></sw-loader>
        </template>
    </sw-page>
{% endblock %}
