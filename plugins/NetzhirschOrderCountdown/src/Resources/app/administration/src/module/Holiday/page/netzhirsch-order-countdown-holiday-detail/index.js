import template from './netzhirsch-order-countdown-holiday-detail.html.twig';

const { Component, Mixin } = Shopware;
const { Criteria } = Shopware.Data;

Component.register('netzhirsch-order-countdown-holiday-detail', {
    template,

    inject: [
        'repositoryFactory'
    ],

    mixins: [
        Mixin.getByName('notification')
    ],

    metaInfo() {
        return {
            title: this.$createTitle()
        };
    },

    data() {
        return {
            holiday: null,
            isLoading: false,
            processSuccess: false,
            repository: null,
            datepickerConfig: {
                enableTime: true,
                dateFormat: 'H:i',
                altFormat: 'H:i',
            },
        };
    },

    computed: {},

    created() {
        this.repository = this.repositoryFactory.create('netzhirsch_order_countdown_holiday');
        this.getHoliday();
    },

    methods: {
        getHoliday() {
            if (this.$route.params.id) {
                const criteria = new Criteria();
                this.repository
                    .get(this.$route.params.id, Shopware.Context.api,criteria)
                    .then((entity) => {
                        // workaround um Zeitzonen-<PERSON><PERSON> von sw-datepicker data-type=time zu korrigieren
                        this.holiday = entity;
                        if (typeof this.holiday.deadline !== 'undefined' && this.holiday.deadline !== null) {
                            let tmpDate = new Date(this.holiday.deadline);
                            let version = Shopware.Context.app.config.version;
                            let isFixedVersion = false;
                            let bugVersion = [6,4,5,0];
                            for (let i = 0; i < version.length; i++) {
                                if (bugVersion[i] < version[i]) {
                                    isFixedVersion = true;
                                    break;
                                }
                            }

                            if (isFixedVersion) {
                                this.holiday.deadline = tmpDate.getHours() + ':' + tmpDate.getMinutes();
                            } else {
                                this.holiday.deadline = tmpDate.getHours() + ':' + tmpDate.getMinutes() + '+00:00';
                            }
                        } else {
                            this.holiday.deadline = null;
                        }
                    });
            } else {
                this.holiday = this.repository.create(Shopware.Context.api);
            }
        },

        onClickSave() {
            if (this.holiday.name == null  || this.holiday.date == null) {
                this.isLoading = false;
                this.processSuccess = false;
            }

            if (this.holiday.name == null) {
                this.createNotificationError({
                    title: this.$root.$t('global.default.error'),
                    message: this.$root.$t('netzhirsch-order-countdown-holiday.detail.saved.error.missing.name')
                });
                return;
            }
            if (this.holiday.date == null) {
                this.createNotificationError({
                    title: this.$root.$t('global.default.error'),
                    message: this.$root.$t('netzhirsch-order-countdown-holiday.detail.saved.error.missing.date')
                });
                return;
            }

            let tmpDate = new Date(this.holiday.date);
            let day = tmpDate.getDate();
            if (day < 10)
                day = '0'+day;
            let month = tmpDate.getMonth() + 1;
            if (month < 10)
                month = '0'+month;

            this.holiday.date = tmpDate.getFullYear()+'-'+month+'-'+day;

            const criteria = new Criteria();
            criteria.addFilter(Criteria.equals('date', this.holiday.date));
            criteria.addFilter(Criteria.not('AND', [Criteria.equals('id', this.holiday.id)]),);

            this.holiday.date += 'T00:00:00.000Z';

            this.repository
                .search(criteria, Shopware.Context.api)
                .then((result) => {
                    if (result.first() !== null) {
                        this.isLoading = false;
                        this.processSuccess = false;
                        this.createNotificationError({
                            title: this.$root.$t('global.default.error'),
                            message: this.$root.$t('netzhirsch-order-countdown-holiday.detail.saved.error.dateExist')
                        });
                    } else {

                        if (typeof this.holiday.deadline !== 'undefined' && this.holiday.deadline !== null) {
                            // workaround um Zeitzonen-Fehler von sw-datepicker data-type=time zu korrigieren
                            let timeZoneOffset = new Date().getTimezoneOffset();
                            let timeDiff = timeZoneOffset/-60;
                            if (timeDiff < 10)
                                timeDiff = '0'+timeDiff;

                            let version = Shopware.Context.app.config.version;
                            version = version.split('.');

                            let isFixedVersion = false;
                            let bugVersion = [6,4,5,0];
                            for (let i = 0; i < version.length; i++) {
                                if (bugVersion[i] < version[i]) {
                                    isFixedVersion = true;
                                    break;
                                }
                            }

                            if (!isFixedVersion) {
                                let prefix = '+';
                                if (timeZoneOffset > 0) {
                                    prefix = '-';
                                }
                                this.holiday.deadline
                                    = this.holiday.deadline.replace('+00:00',prefix+timeDiff+':00');
                            } else {

                                let tmp = this.holiday.deadline.split(':');

                                let hours = tmp[0];
                                hours = parseInt(hours,10);
                                hours -= timeDiff;

                                if (hours < 0) {
                                    hours = '0'+hours;
                                }
                                this.holiday.deadline = hours+':'+tmp[1]+':00';
                            }
                        } else {
                            this.holiday.deadline = null;
                        }

                        // noinspection JSVoidFunctionReturnValueUsed
                        this.repository
                            .save(this.holiday, Shopware.Context.api)
                            .then(() => {
                                this.isLoading = false;
                                this.processSuccess = true;
                                this.createNotificationSuccess({
                                    title: this.$root.$t('global.default.success'),
                                    message: this.$root.$t('netzhirsch-order-countdown-holiday.detail.saved.success')
                                });
                                this.$router.push({ name: 'netzhirsch.order.countdown.holiday.list' });

                            }).catch(() => {
                                this.isLoading = false;
                                this.createNotificationError({
                                    title: this.$root.$t('global.default.error'),
                                    message: this.$root.$t('netzhirsch-order-countdown-holiday.detail.saved.error.error')
                                });
                        });
                    }
            });

        },

        saveFinish() {
            this.processSuccess = false;

        }
    }
});
