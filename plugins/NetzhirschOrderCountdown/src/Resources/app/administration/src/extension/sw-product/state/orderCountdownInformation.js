export default {
    namespaced: true,

    state() {
        return {
            orderCountdownInformation: {}
        };
    },

    getters: {
        orderCountdownInformation: state => state.orderCountdownInformation
    },

    mutations: {
        setOrderCountdownInformation(state, orderCountdownInformation) {
            state.orderCountdownInformation = orderCountdownInformation;
        },
    }
};
