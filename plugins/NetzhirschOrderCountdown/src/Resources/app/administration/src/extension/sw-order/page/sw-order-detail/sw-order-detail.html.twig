{% block sw_order_detail_content_view %}
    {% parent() %}
    <sw-card :hero="false" :isLoading="false" :large="false" title="Order Countdown Info">
        <sw-data-grid
            :dataSource="[
            { id: 'uuid1', deadline: deadline, deliveryDate: deliveryDate },
        ]"
            :columns="[
             { property: 'deadline', label: $tc('netzhirsch-order-countdown.order.deadline') },
             { property: 'deliveryDate', label: $tc('netzhirsch-order-countdown.order.deliveryDate') }
        ]"
            :showSelection="false"
            :showActions="false"
            :large="false"
        >
        </sw-data-grid>
    </sw-card>
{% endblock %}
