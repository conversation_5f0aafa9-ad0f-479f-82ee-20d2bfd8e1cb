const {Entity} = Shopware;
const {Criteria} = Shopware.Data;
const {mapState, mapGetters} = Shopware.Component.getComponentHelper();

Shopware.Component.override('sw-product-detail', {

    watch: {
        isLoading: {
            handler() {
                if (!this.isLoading && typeof this.product !== 'undefined' && this.product !== null) {
                    let orderCountdownInformation =
                        !this.product.extensions.orderCountdownInformation || this.product.extensions.orderCountdownInformation._isNew === undefined
                            ? this.repositoryFactory.create('netzhirsch_order_countdown_information').create(Shopware.Context.api)
                            : this.product.extensions.orderCountdownInformation;
                    Shopware.State.commit('orderCountdownInformation/setOrderCountdownInformation', orderCountdownInformation);
                }
            }
        }
    },

    computed: {
        orderCountdownInformation() {
            if (!Shopware.State.get('orderCountdownInformation'))
                return {};
            return Shopware.State.get('orderCountdownInformation').orderCountdownInformation;
        },
    },

    methods: {
        onSave() {
            this.product.extensions.orderCountdownInformation = this.orderCountdownInformation;
            return this.$super('onSave');
        },
    }

});
