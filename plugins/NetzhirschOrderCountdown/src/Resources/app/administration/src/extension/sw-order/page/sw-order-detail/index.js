import template from './sw-order-detail.html.twig';

const {Component} = Shopware;
const {mapState} = Shopware.Component.getComponentHelper();
const {Criteria} = Shopware.Data;

Component.override('sw-order-detail', {
    template,

    inject: ['repositoryFactory'],

    data() {
        return {
            deadline: "Datum unbekannt",
            deliveryDate: "Datum unbekannt"
        }
    },
    computed: {
        ...mapState('orderCountdownInformation', [
            'orderCountdownInformation'
        ]),

        repository() {
            return this.repositoryFactory.create('netzhirsch_order_countdown_order');
        },
    },
    created() {
        this.createdComponent();
        let orderId = this.$options.propsData.orderId;

        const criteria = new Criteria(this.page, this.limit);
        criteria.addFilter(
            Criteria.equals('orderId', orderId)
        );

        this.repository
            .search(criteria, Shopware.Context.api)
            .then((searchResult) => {
                let first = searchResult.first();
                let date = new Date(first.deadline)
                this.deadline = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
                date = new Date(first.deliveryDate)
                this.deliveryDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        });
    }
});
