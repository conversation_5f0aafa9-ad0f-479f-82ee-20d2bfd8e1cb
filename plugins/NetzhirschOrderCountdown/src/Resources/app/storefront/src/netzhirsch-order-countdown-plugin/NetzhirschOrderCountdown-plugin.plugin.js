import Plugin from 'src/plugin-system/plugin.class';
import HttpClient from 'src/service/http-client.service';

export default class NetzhirschOrderCountdownPlugin extends Plugin {
    init() {
        this.container = document.querySelector('#netzhirsch-order-countdown');
        this.start();
    }

    start(){
        let that = this;
        this._ajaxDeadline(that,false);
        setInterval(function() {
            that._ajaxDeadline(that,true);
        },60000,that);
    }

    _setContent(data) {
        if (typeof data === 'string' || data instanceof String) {
            data = JSON.parse(data);
            if (!data.hideOrderCountdown) {
                let replace;
                let toReplace;
                let alertContainer = document.querySelector('.alert-netzhirsch-order-countdown');
                let newHtml = document.createElement('div.alert-netzhirsch-order-countdown');
                newHtml.innerHTML = (data.html).trim();
                if (alertContainer.length === 1) {
                    toReplace = alertContainer;
                    replace = newHtml.querySelector('.alert-netzhirsch-order-countdown');
                } else {
                    replace = newHtml;
                    toReplace = document.querySelector('#netzhirsch-order-countdown');
                }
                toReplace.innerHTML = replace.innerHTML;
            }
        } else {
            console.log(data);
        }
    }

    _ajaxDeadline(that,afterFirstLoad) {
        let productId = that.container.dataset.netzhirschOrderCountdownProductId;
        if  (productId === '')
            return;

        let productNumber = that.container.dataset.netzhirschOrderCountdownProductProductNumber;
        if (productNumber === '')
            return;

        let salesChannelId = that.container.dataset.netzhirschOrderCountdownSwSalesChannelId;
        if (salesChannelId === '')
            return;

        let token = that.container.dataset.netzhirschOrderCountdownToken;
        if (token === '')
            return;

        let seoUrl = that.container.dataset.netzhirschOrderCountdownSeoUrl;
        let url =
            seoUrl
            + '/'
            + productId
            + '/'
            + productNumber
            + '/'
            + salesChannelId
            + '/'
            + afterFirstLoad
            + '/'
            + token
        ;

        that._client = new HttpClient();
        that._client.get(url, that._setContent.bind(that), 'application/json', false);
    }
}
