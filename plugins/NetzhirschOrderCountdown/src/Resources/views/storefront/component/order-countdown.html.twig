{% if page.product.id is defined %}
    {% set productId = page.product.id %}
{% elseif app.request.attributes.get('id') is not empty %}
    {% set productId = app.request.attributes.get('id') %}
{% elseif id is defined %}
    {% set productId = id %}
{% endif %}


<div id="netzhirsch-order-countdown"
     data-netzhirsch-order-countdown-product-id="{{ productId }}"
     data-netzhirsch-order-countdown-product-product-number="{{ page.product.productNumber??app.request.attributes.get('productNumber') }}"
     data-netzhirsch-order-countdown-sw-sales-channel-id="{{ app.request.attributes.get('sw-sales-channel-id') }}"
     data-netzhirsch-order-countdown-token="{{ app.request.attributes.get('sw-sales-channel-context').token }}"
     data-netzhirsch-order-countdown-seo-url="{{ seoUrl('frontend.netzhirsch.ordercountdown.widget') }}"
>
    {% set data = null %}
    {% if page.orderCountdownDatum is defined and page.orderCountdownDatum[productId] is defined %}
        {% set data = page.orderCountdownDatum[productId] %}
    {% else %}
        {% set data = page %}
    {% endif %}
    {% if data.deadline is defined %}
        <div class="alert alert-{{ data.alertType }} alert-has-icon alert-netzhirsch-order-countdown">
            {% sw_icon 'clock' %}
            <div class="alert-content-container">
                <div class="alert-content">

                    {{ data.outOfStock ? "netzhirsch-order-countdown.outOfStock"|trans()|sw_sanitize }}

                    {% if data.displayMode == 'countdown' %}
                        {{ "netzhirsch-order-countdown.countdown.text"|trans()|sw_sanitize }}
                        <strong>
                            {% if data.interval.m > 0 %}
                                {{ "netzhirsch-order-countdown.countdown.months"|trans({'%count%': data.interval.m })|sw_sanitize }}
                            {% endif %}
                            {% if data.interval.d > 0 %}
                                {{ "netzhirsch-order-countdown.countdown.days"|trans({'%count%': data.interval.d })|sw_sanitize }}
                            {% endif %}
                            {% if data.interval.h > 0 %}
                                {{ "netzhirsch-order-countdown.countdown.hours"|trans({'%count%': data.interval.h })|sw_sanitize }}
                            {% endif %}
                            {% if data.interval.i > 0 %}
                                {{ "netzhirsch-order-countdown.countdown.minutes"|trans({'%count%': data.interval.i })|sw_sanitize }}
                            {% endif %}
                        </strong>
                    {% else %}
                        {{ "netzhirsch-order-countdown.deadline.text"|trans()|sw_sanitize }}
                        {% if data.isToday %}
                            {{ "netzhirsch-order-countdown.deadline.today"|trans({'%datetime%': data.deadline|date('H:i') })|sw_sanitize }}
                        {% elseif data.isTomorrow %}
                            {{ "netzhirsch-order-countdown.deadline.tomorrow"|trans({'%datetime%': data.deadline|date('H:i') })|sw_sanitize }}
                        {% else %}
                            {{ "netzhirsch-order-countdown.deadline.default"|trans({'%datetime%': data.deadline|date('d.m.Y H:i') })|sw_sanitize }}
                        {% endif %}
                    {% endif %}
                    {% if data.latestDeliveryDate is null and data.earliestDeliveryDate is null %}
                        {{ "netzhirsch-order-countdown.delivery.fallback"|trans({'%date%': data.restockForFallback|date('d.m.Y'),'%count%': data.interval.d })|sw_sanitize }}
                    {% elseif data.latestDeliveryDate is null %}
                        {{ "netzhirsch-order-countdown.delivery.text"|trans()|sw_sanitize }}
                        {{ "netzhirsch-order-countdown.delivery.single"|trans({'%date%': data.earliestDeliveryDate|date('d.m.Y') })|sw_sanitize }}
                    {% else %}
                        {{ "netzhirsch-order-countdown.delivery.text"|trans()|sw_sanitize }}
                        {{ "netzhirsch-order-countdown.delivery.double"|trans({
                            '%date1%': data.earliestDeliveryDate|date('d.m.Y'),
                            '%date2%': data.latestDeliveryDate|date('d.m.Y')
                        })|sw_sanitize }}
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% if afterFristLoad is empty %}
    <template data-netzhirsch-order-countdown-plugin></template>
{% endif %}
