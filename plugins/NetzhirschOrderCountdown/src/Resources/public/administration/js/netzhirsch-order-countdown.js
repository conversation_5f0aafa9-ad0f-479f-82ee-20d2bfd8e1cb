!function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p=(window.__sw__.assetPath + '/bundles/netzhirschordercountdown/'),n(n.s="NNv7")}({"/csb":function(e){e.exports=JSON.parse('{"netzhirsch-order-countdown":{"general":{"mainMenuItemGeneral":"Custom Holidays","descriptionTextModule":"For order countdown"},"product":{"hideOrderCountdown":{"label":"hide order countdown","helpText":"Here you can hide the countdown or deadline display for this product."}},"order":{"deliveryDate":"Delivery date","shippingDate":"Shipping date"},"list":{"addButtonText":"Add Holiday","columnDate":"Date","columnTime":"Deadline","messageEmpty":"No Entries found."}}}')},"8ijS":function(e){e.exports=JSON.parse('{"netzhirsch-order-countdown-holiday":{"list":{"addButtonText":"Feiertag hinzufügen","columnDate":"Datum","columnName":"Name","columnDeadline":"Deadline","messageEmpty":"Keine Einträge vorhanden."},"detail":{"cancelButtonText":"Abbrechen","saveButtonText":"Speichern","nameLabel":"Bezeichnung","dateLabel":"Datum","deadlineLabel":"Deadline","saved":{"success":"Der Feiertag wurde erfolgreich gespeichert.","error":{"missing":{"name":"Bitte eine Bezeichnung eintragen","date":"Bitte ein Datum eintragen"},"error":"Der Feiertag konnte nicht gespeichert werden.","dateExist":"An diesem Tag ist bereits ein Feiertag eingetragen."}}}}}')},Llle:function(e,t){Shopware.Entity,Shopware.Data.Criteria;var n=Shopware.Component.getComponentHelper();n.mapState,n.mapGetters;Shopware.Component.override("sw-product-detail",{watch:{isLoading:{handler:function(){if(!this.isLoading&&void 0!==this.product&&null!==this.product){var e=this.product.extensions.orderCountdownInformation&&void 0!==this.product.extensions.orderCountdownInformation._isNew?this.product.extensions.orderCountdownInformation:this.repositoryFactory.create("netzhirsch_order_countdown_information").create(Shopware.Context.api);Shopware.State.commit("orderCountdownInformation/setOrderCountdownInformation",e)}}}},computed:{orderCountdownInformation:function(){return Shopware.State.get("orderCountdownInformation")?Shopware.State.get("orderCountdownInformation").orderCountdownInformation:{}}},methods:{onSave:function(){return this.product.extensions.orderCountdownInformation=this.orderCountdownInformation,this.$super("onSave")}}})},NNv7:function(e,t,n){"use strict";n.r(t);var o=Shopware,r=o.Component,i=o.Mixin,a=Shopware.Data.Criteria;r.register("netzhirsch-order-countdown-holiday-list",{template:'{% block netzhirsch_order_countdown_holiday_list %}\n    <sw-page>\n        <template slot="smart-bar-actions">\n            <sw-button variant="primary" :routerLink="{ name: \'netzhirsch.order.countdown.holiday.create\' }">\n                {{ $t(\'netzhirsch-order-countdown-holiday.list.addButtonText\') }}\n            </sw-button>\n        </template>\n\n        <template slot="content">\n            <sw-entity-listing\n                v-if="holidays"\n                :items="holidays"\n                :repository="repository"\n                :showSelection="false"\n                :columns="columns"\n                :isLoading="isLoading"\n                detailRoute="netzhirsch.order.countdown.holiday.detail"\n                @update-records="updateTotal"\n                fullPage\n            >\n                <template #column-name="{ item }">\n                    <span>{{ item.name }}</span>\n                </template>\n\n                <template #column-date="{ item }">\n                    <span>{{ item.date|date({ year: \'numeric\',month: \'numeric\',day: \'2-digit\',hour: undefined, minute: undefined, second: undefined }) }}</span>\n                </template>\n\n                <template #column-deadline="{ item }">\n                    <span v-if="item.deadline">{{ item.deadline|date(\n                            { year: undefined,month: undefined,day: undefined,hour: \'2-digit\', minute: \'2-digit\', second: undefined }\n                        ) }}</span>\n                </template>\n\n            </sw-entity-listing>\n\n            <sw-empty-state\n                v-if="!isLoading && !total"\n                :title="$tc(\'netzhirsch-order-countdown-holiday.list.messageEmpty\')">\n            </sw-empty-state>\n\n            <sw-loader v-if="isLoading"></sw-loader>\n        </template>\n    </sw-page>\n{% endblock %}\n',metaInfo:function(){return{title:this.$createTitle()}},inject:["repositoryFactory"],mixins:[i.getByName("listing")],data:function(){return{sortBy:"date",sortDirection:"ASC",isLoading:!0,holidays:null}},computed:{repository:function(){return this.repositoryFactory.create("netzhirsch_order_countdown_holiday")},columns:function(){return[{property:"date",label:"netzhirsch-order-countdown-holiday.list.columnDate",allowResize:!0},{property:"name",label:"netzhirsch-order-countdown-holiday.list.columnName",routerLink:"netzhirsch.promocode.group.detail",allowResize:!0,primary:!0},{property:"deadline",label:"netzhirsch-order-countdown-holiday.list.columnDeadline",allowResize:!0}]}},methods:{getList:function(){var e=this;this.isLoading=!0;var t=new a(this.page,this.limit);return t.setTerm(this.term),t.addSorting(a.sort(this.sortBy,this.sortDirection)),this.repository.search(t,Shopware.Context.api).then((function(t){return e.total=t.total,e.holidays=t,e.isLoading=!1,e.holidays}))},updateTotal:function(e){var t=e.total;this.total=t}}});var d=Shopware,l=d.Component,s=d.Mixin,c=Shopware.Data.Criteria;l.register("netzhirsch-order-countdown-holiday-detail",{template:'{% block netzhirsch_order_countdown_holiday_list_detail %}\n    <sw-page>\n        <template slot="smart-bar-actions">\n            <sw-button :routerLink="{ name: \'netzhirsch.order.countdown.holiday.list\' }">\n                {{ $t(\'netzhirsch-order-countdown-holiday.detail.cancelButtonText\') }}\n            </sw-button>\n\n            <sw-button-process\n                :isLoading="isLoading"\n                :processSuccess="processSuccess"\n                variant="primary"\n                @process-finish="saveFinish"\n                @click.prevent="onClickSave">\n                {{ $t(\'netzhirsch-order-countdown-holiday.detail.saveButtonText\') }}\n            </sw-button-process>\n        </template>\n\n        <template slot="content">\n            <sw-card-view>\n                <sw-card v-if="holiday" :isLoading="isLoading">\n\n                    <sw-container columns="3fr" gap="0px 30px">\n\n                        <sw-field\n                            :label="$t(\'netzhirsch-order-countdown-holiday.detail.nameLabel\')"\n                            v-model="holiday.name"\n                            maxLength="255"\n                            required\n                        ></sw-field>\n                    </sw-container>\n\n                    <sw-container columns="1fr 1fr" gap="0px 30px">\n                        <sw-datepicker\n                            bordered\n                            :label="$t(\'netzhirsch-order-countdown-holiday.detail.dateLabel\')"\n                            v-model="holiday.date"\n                            required\n                        ></sw-datepicker>\n                        <sw-datepicker\n                            bordered\n                            date-type="time"\n                            :label="$t(\'netzhirsch-order-countdown-holiday.detail.deadlineLabel\')"\n                            v-model="holiday.deadline"\n                        ></sw-datepicker>\n\n                    </sw-container>\n\n                </sw-card>\n            </sw-card-view>\n        </template>\n    </sw-page>\n{% endblock %}\n',inject:["repositoryFactory"],mixins:[s.getByName("notification")],metaInfo:function(){return{title:this.$createTitle()}},data:function(){return{holiday:null,isLoading:!1,processSuccess:!1,repository:null,datepickerConfig:{enableTime:!0,dateFormat:"H:i",altFormat:"H:i"}}},computed:{},created:function(){this.repository=this.repositoryFactory.create("netzhirsch_order_countdown_holiday"),this.getHoliday()},methods:{getHoliday:function(){var e=this;if(this.$route.params.id){var t=new c;this.repository.get(this.$route.params.id,Shopware.Context.api,t).then((function(t){if(e.holiday=t,void 0!==e.holiday.deadline&&null!==e.holiday.deadline){for(var n=new Date(e.holiday.deadline),o=Shopware.Context.app.config.version,r=!1,i=[6,4,5,0],a=0;a<o.length;a++)if(i[a]<o[a]){r=!0;break}e.holiday.deadline=r?n.getHours()+":"+n.getMinutes():n.getHours()+":"+n.getMinutes()+"+00:00"}else e.holiday.deadline=null}))}else this.holiday=this.repository.create(Shopware.Context.api)},onClickSave:function(){var e=this;if(null!=this.holiday.name&&null!=this.holiday.date||(this.isLoading=!1,this.processSuccess=!1),null!=this.holiday.name)if(null!=this.holiday.date){var t=new Date(this.holiday.date),n=t.getDate();n<10&&(n="0"+n);var o=t.getMonth()+1;o<10&&(o="0"+o),this.holiday.date=t.getFullYear()+"-"+o+"-"+n;var r=new c;r.addFilter(c.equals("date",this.holiday.date)),r.addFilter(c.not("AND",[c.equals("id",this.holiday.id)])),this.holiday.date+="T00:00:00.000Z",this.repository.search(r,Shopware.Context.api).then((function(t){if(null!==t.first())e.isLoading=!1,e.processSuccess=!1,e.createNotificationError({title:e.$root.$t("global.default.error"),message:e.$root.$t("netzhirsch-order-countdown-holiday.detail.saved.error.dateExist")});else{if(void 0!==e.holiday.deadline&&null!==e.holiday.deadline){var n=(new Date).getTimezoneOffset(),o=n/-60;o<10&&(o="0"+o);var r=Shopware.Context.app.config.version;r=r.split(".");for(var i=!1,a=[6,4,5,0],d=0;d<r.length;d++)if(a[d]<r[d]){i=!0;break}if(i){var l=e.holiday.deadline.split(":"),s=l[0];s=parseInt(s,10),(s-=o)<0&&(s="0"+s),e.holiday.deadline=s+":"+l[1]+":00"}else{var c="+";n>0&&(c="-"),e.holiday.deadline=e.holiday.deadline.replace("+00:00",c+o+":00")}}else e.holiday.deadline=null;e.repository.save(e.holiday,Shopware.Context.api).then((function(){e.isLoading=!1,e.processSuccess=!0,e.createNotificationSuccess({title:e.$root.$t("global.default.success"),message:e.$root.$t("netzhirsch-order-countdown-holiday.detail.saved.success")}),e.$router.push({name:"netzhirsch.order.countdown.holiday.list"})})).catch((function(){e.isLoading=!1,e.createNotificationError({title:e.$root.$t("global.default.error"),message:e.$root.$t("netzhirsch-order-countdown-holiday.detail.saved.error.error")})}))}}))}else this.createNotificationError({title:this.$root.$t("global.default.error"),message:this.$root.$t("netzhirsch-order-countdown-holiday.detail.saved.error.missing.date")});else this.createNotificationError({title:this.$root.$t("global.default.error"),message:this.$root.$t("netzhirsch-order-countdown-holiday.detail.saved.error.missing.name")})},saveFinish:function(){this.processSuccess=!1}}});var u=n("8ijS"),h=n("uViL"),p=Shopware.Module,m="sw-extension";Shopware.ApiService.getServices().aclApiService.httpClient.defaults.baseURL.includes("v2")&&(m="sw-order"),p.register("netzhirsch-order-countdown-holiday",{type:"plugin",name:"holidays",title:"netzhirsch-order-countdown.general.mainMenuItemGeneral",description:"netzhirsch-order-countdown.general.descriptionTextModule",color:"#189EFF",icon:"default-calendar-full",entity:"holiday",snippets:{"de-DE":u,"en-GB":h},routes:{list:{component:"netzhirsch-order-countdown-holiday-list",path:"list",meta:{appSystem:{view:"list"}}},create:{component:"netzhirsch-order-countdown-holiday-detail",path:"create",meta:{parentPath:"netzhirsch.order.countdown.holiday.list"}},detail:{component:"netzhirsch-order-countdown-holiday-detail",path:"detail/:id",meta:{parentPath:"netzhirsch.order.countdown.holiday.list"}}},navigation:[{label:"netzhirsch-order-countdown.general.mainMenuItemGeneral",color:"#FFD700",path:"netzhirsch.order.countdown.holiday.list",icon:"default-package-gift",parent:m,position:3}]});n("Llle");function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==f(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==f(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===f(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=Shopware.Component,v=Shopware.Component.getComponentHelper().mapState,S=Shopware.Data.Criteria;b.override("sw-order-detail",{template:'{% block sw_order_detail_content_view %}\n    {% parent() %}\n    <sw-card :hero="false" :isLoading="false" :large="false" title="Order Countdown Info">\n        <sw-data-grid\n            :dataSource="[\n            { id: \'uuid1\', deadline: deadline, deliveryDate: deliveryDate },\n        ]"\n            :columns="[\n             { property: \'deadline\', label: $tc(\'netzhirsch-order-countdown.order.deadline\') },\n             { property: \'deliveryDate\', label: $tc(\'netzhirsch-order-countdown.order.deliveryDate\') }\n        ]"\n            :showSelection="false"\n            :showActions="false"\n            :large="false"\n        >\n        </sw-data-grid>\n    </sw-card>\n{% endblock %}\n',inject:["repositoryFactory"],data:function(){return{deadline:"Datum unbekannt",deliveryDate:"Datum unbekannt"}},computed:w(w({},v("orderCountdownInformation",["orderCountdownInformation"])),{},{repository:function(){return this.repositoryFactory.create("netzhirsch_order_countdown_order")}}),created:function(){var e=this;this.createdComponent();var t=this.$options.propsData.orderId,n=new S(this.page,this.limit);n.addFilter(S.equals("orderId",t)),this.repository.search(n,Shopware.Context.api).then((function(t){var n=t.first(),o=new Date(n.deadline);e.deadline=o.toLocaleDateString()+" "+o.toLocaleTimeString(),o=new Date(n.deliveryDate),e.deliveryDate=o.toLocaleDateString()+" "+o.toLocaleTimeString()}))}});function D(e){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function C(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function x(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==D(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==D(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===D(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var z=Shopware.Component,O=Shopware.Component.getComponentHelper().mapState;z.override("sw-product-deliverability-form",{template:'{% block sw_product_deliverability_form_max_purchase_field %}\n    {% parent() %}\n    </sw-container>\n    <sw-container columns="2fr 1fr" gap="0px 30px">\n\n        <sw-field\n            type="switch"\n            bordered\n            v-model="orderCountdownInformation.hideOrderCountdown"\n            :label="$tc(\'netzhirsch-order-countdown.product.hideOrderCountdown.label\')"\n            :help-text="$tc(\'netzhirsch-order-countdown.product.hideOrderCountdown.helpText\')"\n            :disabled="!allowEdit"\n        ></sw-field>\n{% endblock %}\n',computed:function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?C(Object(n),!0).forEach((function(t){x(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},O("orderCountdownInformation",["orderCountdownInformation"]))});var L=n("XFr0"),j=n("/csb");Shopware.Locale.extend("de-DE",L),Shopware.Locale.extend("en-GB",j),Shopware.State.registerModule("orderCountdownInformation",{namespaced:!0,state:function(){return{orderCountdownInformation:{}}},getters:{orderCountdownInformation:function(e){return e.orderCountdownInformation}},mutations:{setOrderCountdownInformation:function(e,t){e.orderCountdownInformation=t}}})},XFr0:function(e){e.exports=JSON.parse('{"netzhirsch-order-countdown":{"general":{"mainMenuItemGeneral":"Spezielle Feiertage","descriptionTextModule":"Für Liefercountdown"},"product":{"hideOrderCountdown":{"label":"Countdown ausblenden","helpText":"Hier können Sie die Anzeige des Countdowns bzw. der Deadline für dieses Produkt ausblenden."}},"order":{"deliveryDate":"Versanddatum","deadline":"Deadline"},"list":{"addButtonText":"Feiertag hinzufügen","columnDate":"Datum","columnTime":"Deadline","messageEmpty":"Keine Einträge vorhanden."}}}')},uViL:function(e){e.exports=JSON.parse('{"netzhirsch-order-countdown-holiday":{"list":{"addButtonText":"Add holiday","columnDate":"Date","columnName":"Name","columnDeadline":"Deadline","messageEmpty":"No Entries found."},"detail":{"cancelButtonText":"Cancel","saveButtonText":"Save","nameLabel":"Description","dateLabel":"Date","deadlineLabel":"Deadline","saved":{"success":"The holiday was successfully saved.","error":{"missing":{"name":"Please enter a name.","date":"Please enter a date."},"error":"The holiday could not be saved.","dateExist":"On this day is already registered a holiday."}}}}}')}});
//# sourceMappingURL=netzhirsch-order-countdown.js.map