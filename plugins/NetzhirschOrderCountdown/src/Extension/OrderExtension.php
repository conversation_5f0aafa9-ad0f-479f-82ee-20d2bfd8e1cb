<?php

namespace NetzhirschOrderCountdown\Extension;

use NetzhirschOrderCountdown\Entities\OrderCountdownOrder\OrderCountdownOrderDefinition;
use Shopware\Core\Checkout\Order\OrderDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityExtension;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\CascadeDelete;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class OrderExtension extends EntityExtension {

    public function extendFields(FieldCollection $collection):void {
        $collection->add(
            (new OneToOneAssociationField(
                'orderCountdownOrder',
                'id',
                'order_id',
                OrderCountdownOrderDefinition::class,
                true
            ))->addFlags(new CascadeDelete())
        );
    }

    public function getDefinitionClass():string {
        return OrderDefinition::class;
    }
}
