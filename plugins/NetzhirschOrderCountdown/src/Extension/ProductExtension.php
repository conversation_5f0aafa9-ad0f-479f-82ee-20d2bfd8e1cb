<?php

namespace NetzhirschOrderCountdown\Extension;

use NetzhirschOrderCountdown\Entities\OrderCountdownInformation\OrderCountdownInformationDefinition;
use Shopware\Core\Content\Product\ProductDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityExtension;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\CascadeDelete;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class ProductExtension extends EntityExtension {

    public function extendFields(FieldCollection $collection):void {
        $collection->add(
            (new OneToOneAssociationField(
                'orderCountdownInformation',
                'id',
                'product_id',
                OrderCountdownInformationDefinition::class,
                true
            ))->addFlags(new CascadeDelete())
        );
    }

    public function getDefinitionClass():string {
        return ProductDefinition::class;
    }
}
