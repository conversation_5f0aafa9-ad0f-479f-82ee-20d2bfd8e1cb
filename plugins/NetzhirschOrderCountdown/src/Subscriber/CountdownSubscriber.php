<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Subscriber;

use DateTime;
use Exception;
use NetzhirschOrderCountdown\Entities\OrderCountdownOrder\OrderCountdownOrderEntity;
use NetzhirschOrderCountdown\Service\OrderCountdownService;
use Psr\Log\LoggerInterface;
use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Cart\Order\CartConvertedEvent;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Content\MailTemplate\Service\Event\MailBeforeValidateEvent;
use Shopware\Core\Content\Product\Events\ProductListingResultEvent;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\DeliveryTime\DeliveryTimeEntity;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Event\StorefrontRenderEvent;
use Shopware\Storefront\Page\Checkout\Cart\CheckoutCartPageLoadedEvent;
use Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoadedEvent;
use Shopware\Storefront\Page\Checkout\Offcanvas\OffcanvasCartPageLoadedEvent;
use Shopware\Storefront\Page\Checkout\Register\CheckoutRegisterPageLoadedEvent;
use Shopware\Storefront\Page\Navigation\NavigationPage;
use Shopware\Storefront\Page\PageLoadedEvent;
use Shopware\Storefront\Page\Product\ProductPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class CountdownSubscriber implements EventSubscriberInterface
{

    private const DISPLAY_MODE_COUNTDOWN = 'countdown';

    /** @var SystemConfigService */
    private $systemConfigService;

    /** @var LoggerInterface */
    private $logger;
    private $holidayRepository;

    private $orderCountdownDatum = [];
    private $collectOrderCountdownDataForProduct = [];
    /** @var OrderCountdownService $orderCountdownService */
    private $orderCountdownService;
    private $orderCountDownOrderRepository;
    private $productRepository;

    public function __construct(
        SystemConfigService $systemConfigService,
        LoggerInterface $logger,
        EntityRepository $holidayRepository,
        OrderCountdownService $orderCountdownService,
        EntityRepository $orderCountDownOrderRepository,
        $productRepository
    )
    {
        $this->systemConfigService = $systemConfigService;
        $this->logger = $logger;
        $this->holidayRepository = $holidayRepository;
        $this->orderCountdownService = $orderCountdownService;
        $this->orderCountDownOrderRepository = $orderCountDownOrderRepository;
        $this->productRepository = $productRepository;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ProductPageLoadedEvent::class => 'onProductPageLoaded',
            CheckoutCartPageLoadedEvent::class => 'onCheckoutPageLoaded',
            CheckoutConfirmPageLoadedEvent::class => 'onCheckoutPageLoaded',
            CheckoutRegisterPageLoadedEvent::class => 'onCheckoutPageLoaded',
            OffcanvasCartPageLoadedEvent::class => 'onCheckoutPageLoaded',
            ProductListingResultEvent::class => 'onProductListingLoaded',
            StorefrontRenderEvent::class => 'onStorefrontRender',
            CartConvertedEvent::class => 'onOrderWritten',
            MailBeforeValidateEvent::class => 'onMailSend'
        ];
    }

    public function onMailSend(MailBeforeValidateEvent $event)
    {
        $templateData = $event->getTemplateData();
        $templateData['netzhirschOrderCountdown'] = [
            'deadline' => (new DateTime()),
            'deliveryDate' => (new DateTime()),
        ];
        $event->setTemplateData($templateData);

        if (empty($templateData))
            return;
        $order = $templateData['order']??null;
        if (empty($order))
            return;
        if (is_array($order)) {
            $orderCustomer = $order['orderCustomer']??null;
            if (empty($orderCustomer))
                return;
            $orderId = $orderCustomer['orderId'];
        } else {
            /** @var OrderEntity $order */
            $orderId = $order->getId();
        }

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('orderId', $orderId));
        $result = $this->orderCountDownOrderRepository->search($criteria, $event->getContext());
        /** @var OrderCountdownOrderEntity $countdownOrderEntity */
        $countdownOrderEntity = $result->first();
        if (empty($countdownOrderEntity))
            return;

        $templateData['netzhirschOrderCountdown']['deadline'] = $countdownOrderEntity->getDeadline();
        $templateData['netzhirschOrderCountdown']['deliveryDate'] = $countdownOrderEntity->getDeliveryDate();

        $event->setTemplateData($templateData);
    }

    public function onOrderWritten(CartConvertedEvent $event)
    {
        $context = $event->getContext();
        $convertedCart = $event->getConvertedCart();
        $now = new DateTime();
        $minDeliveryTimes = [];
        $maxDeliveryTimes = [];
        foreach ($convertedCart['deliveries'] as $delivery) {
            /** Shopware has already taken the delivery time in the following order. The period doesn't matter:
            / 1. Produkt
            / 2. Versandart */
            $minDeliveryTime = new DateTime($delivery['shippingDateEarliest']);
            $diff = $minDeliveryTime->diff($now);
            $minDeliveryTimes[] = $diff->d;
            $maxDeliveryTime = new DateTime($delivery['shippingDateLatest']);
            $diff = $maxDeliveryTime->diff($now);
            $maxDeliveryTimes[] = $diff->d;
        }
        $restockTimes = [];
        foreach ($convertedCart['lineItems'] as $lineItem) {
            //Without a productId, it's a discount or something.
            if (!isset($lineItem['productId']))
                continue;

            $criteria = new Criteria();
            $criteria->addFilter(new EqualsFilter('id', $lineItem['productId']));
            $result = $this->productRepository->search($criteria, $context);
            /** @var ProductEntity $product */
            $product = $result->first();
            if (empty($product)) {
                $restockTimes[] = 0;
            } else {
                $restockTimes[] = $product->getRestockTime();
            }
        }
        if(empty($minDeliveryTimes)) {
            return;
        }
        $restockTimeMax = max($restockTimes);
        $orderId = $event->getConvertedCart()['id'];


        $minDeliveryTimeMax = max($minDeliveryTimes);
        $maxDeliveryTimeMax = max($maxDeliveryTimes);

        $collectOrderCountdownDataForProduct = [
            'start' => new DateTime(),
            'minDeliveryTime' => $minDeliveryTimeMax,
            'maxDeliveryTime' => $maxDeliveryTimeMax,
            'restockTimeMax' => $restockTimeMax,
        ];

        $options = $this->orderCountdownService->getOptionsToAssign(
            $collectOrderCountdownDataForProduct,
            $event->getContext(),
            $event->getSalesChannelContext()->getSalesChannelId()
        );

	    $deliveryDate = null;
        if (empty($options['latestDeliveryDate']) && empty($options['earliestDeliveryDate']) && !empty($options['restockForFallback'])) {
            $deliveryDate = $options['restockForFallback'];
        } elseif (empty($options['latestDeliveryDate']) && !empty($options['earliestDeliveryDate'])) {
            $deliveryDate = $options['earliestDeliveryDate'];
        } elseif (!empty($options['latestDeliveryDate'])) {
            $deliveryDate = $options['latestDeliveryDate'];
        }

        if(empty($options['deadline'])) {
		    return;
	    }
        
        /** @var DateTime $deadline */
        $deadline = $options['deadline'];

        $this->orderCountDownOrderRepository->create([
            [
                'id' => Uuid::randomHex(),
                'date' => (new DateTime()),
                'deadline' => $deadline,
                'deliveryDate' => $deliveryDate,
                'orderId' => $orderId,
            ]
        ], $context);
    }

    /**
     * @throws Exception
     */
    public function onProductPageLoaded(ProductPageLoadedEvent $event): void
    {
        $salesChannelContext = $event->getSalesChannelContext();
        $salesChannelId = $salesChannelContext->getSalesChannelId();
        if (empty($this->orderCountdownService->config($this->systemConfigService,'showOnProductPage',$salesChannelId)))
            return;

        $page = $event->getPage();
        $product = $page->getProduct();
        $context = $event->getContext();
        /** @var ProductEntity $product */
        $product = $this->orderCountdownService->getProductWithDeliveryTimeAndCountdownInformation(
                $product->getProductNumber(),
                $context,
                $salesChannelContext
        );

        $orderCountdownInformation = $product->getExtension('orderCountdownInformation');
        if (!empty($orderCountdownInformation)) {
            $hideOrderCountdown = $orderCountdownInformation->get('hideOrderCountdown');
            if ($hideOrderCountdown)
                return;
        }

        $collectOrderCountdownDataForProduct
            = $this->orderCountdownService->collectOrderCountdownDataForProduct(
                $product,
                $this->orderCountdownService->config(
                    $this->systemConfigService,'showWarningIfOutOfStock',$salesChannelId
                ),
                $this->orderCountdownService->config(
                    $this->systemConfigService,'showNoCountdownOutOfStock',$salesChannelId
                ),
            $salesChannelId
        );
        if (empty($collectOrderCountdownDataForProduct))
            return;

        $options = $this->orderCountdownService->getOptionsToAssign(
            $collectOrderCountdownDataForProduct,
            $event->getContext(),
            $salesChannelId
        );

        $page->assign($options);
    }

    /**
     * @throws Exception
     */
    public function onStorefrontRender(StorefrontRenderEvent $event)
    {
        if (empty($this->orderCountdownDatum))
            return;

        $params = $event->getParameters();
        if (isset($params['page']) && !empty($params['page'])) {
            /** @var NavigationPage $page */
            $page = $params['page'];
            $page->assign(['orderCountdownDatum' => $this->orderCountdownDatum]);
            $options = [];
            foreach ($this->orderCountdownDatum as $productId => $productCountDown) {
                if (isset($this->collectOrderCountdownDataForProduct[$productId])) {
                    $options[$productId] = array_merge($this->orderCountdownService->getOptionsToAssign(
                        $this->collectOrderCountdownDataForProduct[$productId],
                        $event->getContext(),
                        $event->getSalesChannelContext()->getSalesChannelId()
                    ),$options);
                }
            }
            $page->assign($options);
        }
    }

    /**
     * @throws Exception
     */
    public function onCheckoutPageLoaded(PageLoadedEvent $event): void
    {
        $salesChannelId = $event->getSalesChannelContext()->getSalesChannelId();
        if (
            get_class($event) === OffcanvasCartPageLoadedEvent::class
            && empty($this->orderCountdownService->config($this->systemConfigService,'showInOffcanvasCart',$salesChannelId))
        ) {
            return;
        }
        if (
            get_class($event) !== OffcanvasCartPageLoadedEvent::class
            && empty($this->orderCountdownService->config($this->systemConfigService,'showInCart',$salesChannelId))
        ) {
            return;
        }

        $page = $event->getPage();
        /** @var Cart $cart */
        $cart = $page->getCart();
        $lineItems = $page->getCart()->getLineItems();
        if (count($lineItems) <= 0) {
            return;
        }

        $minDeliveryTimeMax = null;
        $maxDeliveryTimeMax = null;
        $restockTimeMax = 0;
        $noDeliveryTimeAvailable = false;

        /** @var LineItem $lineItem */
        foreach ($lineItems as $lineItem) {
            $deliveryInformation = $lineItem->getDeliveryInformation();
            if (empty($deliveryInformation)) {
                continue;
            }

            $restockTime = 0;
            if ($deliveryInformation->getStock() <= 0) {
                $restockTime = $deliveryInformation->getRestockTime() ?? 0;
            }
            $restockTimeMax = max($restockTimeMax, $restockTime);

            $minDeliveryTime = null;
            $maxDeliveryTime = null;
            $deliveryTime = $deliveryInformation->getDeliveryTime();
            if (!empty($deliveryTime)) {
                $minDeliveryTime = $deliveryTime->getMin();
                $maxDeliveryTime = $deliveryTime->getMax();
            }

            if (empty($minDeliveryTime) || empty($maxDeliveryTime)) {
                $deliveries = $cart->getDeliveries();
                foreach ($deliveries as $delivery) {
                    foreach ($delivery->getPositions() as $position) {
                        if ($position->getLineItem()->getId() == $lineItem->getId()) {
                            /** Shopware has already taken the delivery time in the following order. The period doesn't matter:
                            / 1. product
                            / 2. shipping method */
                            $deliveryShippingTime = $delivery->getDeliveryDate();
                            $minDeliveryTime = $deliveryShippingTime->getEarliest();
                            $maxDeliveryTime = $deliveryShippingTime->getLatest();
                            $now = new DateTime();
                            $diff = $minDeliveryTime->diff($now);
                            $minDeliveryTime = $diff->d;
                            $diff = $maxDeliveryTime->diff($now);
                            $maxDeliveryTime = $diff->d;
                        }
                    }
                }
            }

            if (empty($minDeliveryTime) || empty($maxDeliveryTime))
                continue;


            if ($deliveryInformation->getStock() <= 0 && !empty($restockTime)) {
                $minDeliveryTime += $restockTime;
                $maxDeliveryTime += $restockTime;
            }

            $minDeliveryTimeMax = max($minDeliveryTimeMax, $minDeliveryTime);
            $maxDeliveryTimeMax = max($maxDeliveryTimeMax, $maxDeliveryTime);
        }
        if ($noDeliveryTimeAvailable) {
            $minDeliveryTimeMax = null;
            $maxDeliveryTimeMax = null;
        }

        $start = new Datetime('');

        $collectOrderCountdownDataForProduct = [
            'start' => $start,
            'minDeliveryTime' => $minDeliveryTimeMax,
            'maxDeliveryTime' => $maxDeliveryTimeMax,
            'restockTimeMax' => $restockTimeMax,
        ];

        $options = $this->orderCountdownService->getOptionsToAssign(
            $collectOrderCountdownDataForProduct,
            $event->getContext(),
            $salesChannelId
        );

        $this->orderCountdownDatum = $options;

        $page->assign($options);
    }

    public function onProductListingLoaded(ProductListingResultEvent $event)
    {
        $result = $event->getResult();
        $orderCountdownData = [];
        $minDeliveryTimeMax = null;
        $maxDeliveryTimeMax = null;
        $restockTimeMax = 0;
        $noDeliveryTimeAvailable = false;
        $salesChannelId = $event->getSalesChannelContext()->getSalesChannelId();
        $showCatalog = $this->orderCountdownService->config(
            $this->systemConfigService,'showCatalog',$salesChannelId
        );
        if ($showCatalog === false) {
            return;
        }
        /** @var ProductEntity $product */
        foreach ($result->getEntities() as $product) {
            $orderCountdownData[$product->getId()]
                = $this->orderCountdownService->collectOrderCountdownDataForProduct(
                    $product,
                    $this->orderCountdownService->config(
                        $this->systemConfigService,'showWarningIfOutOfStock',$salesChannelId
                    ),
                    $this->orderCountdownService->config(
                        $this->systemConfigService,'showNoCountdownOutOfStock',$salesChannelId
                    ),
                $salesChannelId
            );

            $restockTime = 0;
            if ($product->getStock() <= 0) {
                $restockTime = $product->getRestockTime() ?? 0;
            }
            $restockTimeMax = max($restockTimeMax, $restockTime);
            $deliveryTimeFromLineItem = $product->getDeliveryTime();
            if (empty($deliveryTimeFromLineItem)) {
                $noDeliveryTimeAvailable = true;
                continue;
            }

            $minDeliveryTime = $deliveryTimeFromLineItem->getMin();
            $maxDeliveryTime = $deliveryTimeFromLineItem->getMax();

            switch ($deliveryTimeFromLineItem->getUnit()) {
                case DeliveryTimeEntity::DELIVERY_TIME_MONTH:
                    $minDeliveryTime *= 30;
                    $maxDeliveryTime *= 30;
                    break;
                case DeliveryTimeEntity::DELIVERY_TIME_WEEK:
                    $minDeliveryTime *= 7;
                    $maxDeliveryTime *= 7;
                    break;
            }

            if ($product->getStock() <= 0 && !empty($restockTime)) {
                $minDeliveryTime += $restockTime;
                $maxDeliveryTime += $restockTime;
            }

            $minDeliveryTimeMax = max($minDeliveryTimeMax, $minDeliveryTime);
            $maxDeliveryTimeMax = max($maxDeliveryTimeMax, $maxDeliveryTime);

            if ($noDeliveryTimeAvailable) {
                $minDeliveryTimeMax = null;
                $maxDeliveryTimeMax = null;
            }
            $start = new Datetime();

            $collectOrderCountdownDataForProduct[$product->getId()] = [
                'start' => $start,
                'minDeliveryTime' => $minDeliveryTimeMax,
                'maxDeliveryTime' => $maxDeliveryTimeMax,
                'restockTimeMax' => $restockTimeMax,
            ];

            $this->collectOrderCountdownDataForProduct = $collectOrderCountdownDataForProduct;
        }

        $this->createWidgets(
            $orderCountdownData,
            $this->holidayRepository,
            $this->logger,
            $event->getContext(),
            $this->systemConfigService,
            $salesChannelId
        );

    }

    private function createWidgets(
        array $orderCountdownData,
        EntityRepository $holidayRepository,
        LoggerInterface $logger,
        Context $context,
        SystemConfigService $systemConfigService,
        string $salesChannelId
    ): void {
        $assignData = [];
        foreach ($orderCountdownData as $productId => $orderCountdownDatum) {
            if (empty($orderCountdownDatum))
                continue;
            $now = new Datetime();
            $deadline = $this->orderCountdownService->getDeadline(
                $orderCountdownDatum['start'],
                $holidayRepository,
                $logger,
                $context,
                $systemConfigService,
                $salesChannelId
            );

            if (!empty($deadline)) {
                $interval = date_diff($now, $deadline);
                $tomorrow = new DateTime('+1 day');
                $isToday = $now->format('Y-m-d') == $deadline->format('Y-m-d');
                $isTomorrow = $tomorrow->format('Y-m-d') == $deadline->format('Y-m-d');

                [$earliestDeliveryDate, $latestDeliveryDate]
                    = $this->orderCountdownService->getDeliveryDates(
                    $deadline,
                    $orderCountdownDatum['minDeliveryTime'],
                    $orderCountdownDatum['maxDeliveryTime'],
                    $systemConfigService,
                    $salesChannelId
                );

            } else {
                continue;
            }

            $displayMode = $this->orderCountdownService->config($systemConfigService,'displayMode',$salesChannelId);
            if (!empty($displayMode)) {
                $displayMode = self::DISPLAY_MODE_COUNTDOWN;
            }
            $assignData[$productId] =
                    [
                        'displayMode' => $displayMode,
                        'deadline' => $deadline,
                        'isToday' => $isToday,
                        'isTomorrow' => $isTomorrow,
                        'interval' => $interval,
                        'earliestDeliveryDate' => $earliestDeliveryDate,
                        'latestDeliveryDate' => $latestDeliveryDate,
                        'restockForFallback' => $orderCountdownDatum['restockTime'] ?? 0,
                        'alertType' => $orderCountdownDatum['alertType'],
                        'outOfStock' => $orderCountdownDatum['outOfStock'],
                    ];
        }
        $this->orderCountdownDatum = $assignData;
    }




}
