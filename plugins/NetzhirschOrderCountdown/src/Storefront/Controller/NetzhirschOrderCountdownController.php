<?php declare(strict_types=1);

namespace NetzhirschOrderCountdown\Storefront\Controller;

use Exception;
use NetzhirschOrderCountdown\Service\OrderCountdownService;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\System\SalesChannel\Context\CachedSalesChannelContextFactory;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route(defaults={"_routeScope"={"storefront"}})
 */
class NetzhirschOrderCountdownController extends StorefrontController {

    /** @var SystemConfigService */
    private $systemConfigService;
    /** @var OrderCountdownService $orderCountdownService */
    private $orderCountdownService;

    /** @var CachedSalesChannelContextFactory $salesChannelContextFactory */
    private $salesChannelContextFactory;

    public function __construct(
        SystemConfigService $systemConfigService,
        OrderCountdownService $orderCountdownService,
        $salesChannelContextFactory
    )
    {
        $this->systemConfigService = $systemConfigService;
        $this->orderCountdownService = $orderCountdownService;
        $this->salesChannelContextFactory = $salesChannelContextFactory;
    }

    /**
     * @Route("/netzhirsch/ordercountdown/{productId}/{productNumber}/{salesChannelId}/{afterFristLoad}/{token}", name="frontend.netzhirsch.ordercountdown.widget", defaults={"XmlHttpRequest"=true,"productId"=null,"productNumber"=null,"salesChannelId"=null,"afterFristLoad"=null,"token"=null} , methods={"GET"} )
     * @param string|null $productId
     * @param string|null $productNumber
     * @param string|null $salesChannelId
     * @param string|null $afterFirstLoad
     * @param string|null $token
     * @return JsonResponse
     * @throws Exception
     */
    public function getOrderCountdown(
        string $productId = null,
        string $productNumber = null,
        string $salesChannelId = null,
        string $afterFirstLoad = null,
        string $token = null
    ): JsonResponse
    {
        /** In order not to generate any log entries, the route must also be found without parameters. */
        if (empty($productId)|| empty($productNumber) || empty($salesChannelId) || empty($token))
            return new JsonResponse([$productId,$productNumber,$salesChannelId,$afterFirstLoad]);

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('id', $salesChannelId));
        $salesChannelContext = $this->salesChannelContextFactory->create($token,$salesChannelId);
        $context = $salesChannelContext->getContext();
        $product = $this->orderCountdownService->getProductWithDeliveryTimeAndCountdownInformation($productNumber,$context,$salesChannelContext);

        if (empty($product))
            return new JsonResponse(['error' => 'Kein Produkt mit folgender Id gefunde : '.$productId]);
        $orderCountdownInformation = $product->getExtension('orderCountdownInformation');
        if (!empty($orderCountdownInformation)) {
            $hideOrderCountdown = $orderCountdownInformation->get('hideOrderCountdown');
            if ($hideOrderCountdown)
                return new JsonResponse(['hideOrderCountdown' => true]);
        }
        $orderCountdownService = $this->orderCountdownService;
        $collectOrderCountdownDataForProduct
            = $orderCountdownService->collectOrderCountdownDataForProduct(
                $product,
                $orderCountdownService->config(
                    $this->systemConfigService,'showWarningIfOutOfStock',$salesChannelId
                ),
                $orderCountdownService->config(
                    $this->systemConfigService,
                    'showNoCountdownOutOfStock',$salesChannelId
                ),
                $salesChannelId
        );

        if (empty($collectOrderCountdownDataForProduct))
            return new JsonResponse(['collectOrderCountdownDataForProduct' => false]);

        $options = $orderCountdownService->getOptionsToAssign($collectOrderCountdownDataForProduct,$context,$salesChannelId);
        $html = $this->render('@NetzhirschOrderCountdown/storefront/component/order-countdown.html.twig',
            ['page' => $options,'afterFristLoad' => $afterFirstLoad]
        );
        return new JsonResponse(['hideOrderCountdown' => false,'html' => $html->getContent()]);
    }
}
