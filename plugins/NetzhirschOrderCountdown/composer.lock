{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "cf45335983c92a06845eb060e0efb083", "packages": [{"name": "aws/aws-crt-php", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "3942776a8c99209908ee0b287746263725685732"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/3942776a8c99209908ee0b287746263725685732", "reference": "3942776a8c99209908ee0b287746263725685732", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.0.2"}, "time": "2021-09-03T22:57:30+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.212.0", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "d7670125548a886f90e889e08ae0ae7be4958908"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/d7670125548a886f90e889e08ae0ae7be4958908", "reference": "d7670125548a886f90e889e08ae0ae7be4958908", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.0.2", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^5.3.3|^6.2.1|^7.0", "guzzlehttp/promises": "^1.4.0", "guzzlehttp/psr7": "^1.7.0|^2.0", "mtdowling/jmespath.php": "^2.6", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.212.0"}, "time": "2022-03-02T19:29:21+00:00"}, {"name": "brick/math", "version": "0.9.3", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/ca57d18f028f84f777b2168cd1911b0dee2343ae", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.0", "vimeo/psalm": "4.9.2"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.9.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/brick/math", "type": "tidelift"}], "time": "2021-08-15T20:50:18+00:00"}, {"name": "cocur/slugify", "version": "v4.0.0", "source": {"type": "git", "url": "https://github.com/cocur/slugify.git", "reference": "3f1ffc300f164f23abe8b64ffb3f92d35cec8307"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cocur/slugify/zipball/3f1ffc300f164f23abe8b64ffb3f92d35cec8307", "reference": "3f1ffc300f164f23abe8b64ffb3f92d35cec8307", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.0"}, "conflict": {"symfony/config": "<3.4 || >=4,<4.3", "symfony/dependency-injection": "<3.4 || >=4,<4.3", "symfony/http-kernel": "<3.4 || >=4,<4.3", "twig/twig": "<2.12.1"}, "require-dev": {"laravel/framework": "~5.1", "latte/latte": "~2.2", "league/container": "^2.2.0", "mikey179/vfsstream": "~1.6.8", "mockery/mockery": "^1.3", "nette/di": "~2.4", "phpunit/phpunit": "^5.7.27", "pimple/pimple": "~1.1", "plumphp/plum": "~0.1", "symfony/config": "^3.4 || ^4.3 || ^5.0", "symfony/dependency-injection": "^3.4 || ^4.3 || ^5.0", "symfony/http-kernel": "^3.4 || ^4.3 || ^5.0", "twig/twig": "^2.12.1 || ~3.0", "zendframework/zend-modulemanager": "~2.2", "zendframework/zend-servicemanager": "~2.2", "zendframework/zend-view": "~2.2"}, "type": "library", "autoload": {"psr-4": {"Cocur\\Slugify\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://florian.ec"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Converts a string into a slug.", "keywords": ["slug", "slugify"], "support": {"issues": "https://github.com/cocur/slugify/issues", "source": "https://github.com/cocur/slugify/tree/master"}, "time": "2019-12-14T13:04:14+00:00"}, {"name": "composer/ca-bundle", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b", "reference": "4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-10-28T20:44:15+00:00"}, {"name": "composer/composer", "version": "2.2.7", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "061d154dfdde157cbf453c4695e6af21c0e93903"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/061d154dfdde157cbf453c4695e6af21c0e93903", "reference": "061d154dfdde157cbf453c4695e6af21c0e93903", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^1.0", "composer/semver": "^3.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^2.0 || ^3.0", "justinrainbow/json-schema": "^5.2.11", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0 || ^2.0", "react/promise": "^1.2 || ^2.7", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0", "symfony/filesystem": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0", "symfony/finder": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0", "symfony/process": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0"}, "require-dev": {"phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.2-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "source": "https://github.com/composer/composer/tree/2.2.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T10:12:27+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/composer/metadata-minifier.git", "reference": "c549d23829536f0d0e984aaabbf02af91f443207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^2", "phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "support": {"issues": "https://github.com/composer/metadata-minifier/issues", "source": "https://github.com/composer/metadata-minifier/tree/1.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/67a32d7d6f9f560b726ab25a061b38ff3a80c560", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/1.0.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-21T20:24:37+00:00"}, {"name": "composer/semver", "version": "3.2.9", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "a951f614bd64dcd26137bc9b7b2637ddcfc57649"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/a951f614bd64dcd26137bc9b7b2637ddcfc57649", "reference": "a951f614bd64dcd26137bc9b7b2637ddcfc57649", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-04T13:58:43+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.6", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "a30d487169d799745ca7280bc90fdfa693536901"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/a30d487169d799745ca7280bc90fdfa693536901", "reference": "a30d487169d799745ca7280bc90fdfa693536901", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.6"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-11-18T10:14:14+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ced299686f41dce890debac69273b47ffe98a40c", "reference": "ced299686f41dce890debac69273b47ffe98a40c", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T21:32:43+00:00"}, {"name": "defuse/php-encryption", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "77880488b9954b7884c25555c2a0ea9e7053f9d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/77880488b9954b7884c25555c2a0ea9e7053f9d2", "reference": "77880488b9954b7884c25555c2a0ea9e7053f9d2", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^4|^5|^6|^7|^8|^9"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "support": {"issues": "https://github.com/defuse/php-encryption/issues", "source": "https://github.com/defuse/php-encryption/tree/v2.3.1"}, "time": "2021-04-09T23:57:26+00:00"}, {"name": "doctrine/annotations", "version": "1.13.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "e6e7b7d5b45a2f2abc5460cc6396480b2b1d321f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/e6e7b7d5b45a2f2abc5460cc6396480b2b1d321f", "reference": "e6e7b7d5b45a2f2abc5460cc6396480b2b1d321f", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.1"}, "time": "2021-05-16T18:07:53+00:00"}, {"name": "doctrine/cache", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/331b4d5dbaeab3827976273e9356b3b453c300ce", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^8.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2021-07-17T14:49:29+00:00"}, {"name": "doctrine/collections", "version": "1.6.8", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/1958a744696c6bb3bb0d28db2611dc11610e78af", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.2.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.8"}, "time": "2021-08-10T18:51:53+00:00"}, {"name": "doctrine/dbal", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "0d7adf4cadfee6f70850e5b163e6cdd706417838"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/0d7adf4cadfee6f70850e5b163e6cdd706417838", "reference": "0d7adf4cadfee6f70850e5b163e6cdd706417838", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "0.12.96", "phpunit/phpunit": "^7.5.20|^8.5|9.5.5", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.0", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.10.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2021-09-12T19:11:48+00:00"}, {"name": "doctrine/deprecations", "version": "v0.5.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/9504165960a1f83cc1480e2be1dd0a0478561314", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0|^7.0|^8.0", "phpunit/phpunit": "^7.0|^8.0|^9.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.5.3"}, "time": "2021-03-21T12:59:47+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/persistence", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "f8af155c1e7963f3d2b4415097d55757bbaa53d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/f8af155c1e7963f3d2b4415097d55757bbaa53d8", "reference": "f8af155c1e7963f3d2b4415097d55757bbaa53d8", "shasum": ""}, "require": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.0", "doctrine/deprecations": "^0.5.3", "doctrine/event-manager": "^1.0", "php": "^7.1 || ^8.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/annotations": "<1.0 || >=2.0", "doctrine/common": "<2.10@dev"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.0", "doctrine/coding-standard": "^6.0 || ^9.0", "doctrine/common": "^3.0", "phpstan/phpstan": "1.2.0", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "symfony/cache": "^4.4 || ^5.0 || ^6.0", "vimeo/psalm": "4.13.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.3.0"}, "time": "2022-01-09T19:58:46+00:00"}, {"name": "dompdf/dompdf", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "8768448244967a46d6e67b891d30878e0e15d25c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/8768448244967a46d6e67b891d30878e0e15d25c", "reference": "8768448244967a46d6e67b891d30878e0e15d25c", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "^0.5.2", "phenx/php-svg-lib": "^0.3.3", "php": "^7.1 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/v1.0.2"}, "time": "2021-01-08T14:18:52+00:00"}, {"name": "egulias/email-validator", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ee0db30118f661fb166bcffbf5d82032df484697"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ee0db30118f661fb166bcffbf5d82032df484697", "reference": "ee0db30118f661fb166bcffbf5d82032df484697", "shasum": ""}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.1.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2021-10-11T09:18:27+00:00"}, {"name": "enqueue/amqp-tools", "version": "0.10.9", "source": {"type": "git", "url": "https://github.com/php-enqueue/amqp-tools.git", "reference": "1a2bdcacf0b56c677df965544baa1801d8dd95e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/amqp-tools/zipball/1a2bdcacf0b56c677df965544baa1801d8dd95e1", "reference": "1a2bdcacf0b56c677df965544baa1801d8dd95e1", "shasum": ""}, "require": {"enqueue/dsn": "^0.10", "php": "^7.3|^8.0", "queue-interop/amqp-interop": "^0.8.2", "queue-interop/queue-interop": "^0.8"}, "require-dev": {"enqueue/null": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\AmqpTools\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message Queue Amqp Tools", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["AMQP", "messaging", "queue"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2021-02-17T21:24:06+00:00"}, {"name": "enqueue/dbal", "version": "0.10.11", "source": {"type": "git", "url": "https://github.com/php-enqueue/dbal.git", "reference": "cd98c02c951978eaf5d1e226fbef29bd7723484b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/dbal/zipball/cd98c02c951978eaf5d1e226fbef29bd7723484b", "reference": "cd98c02c951978eaf5d1e226fbef29bd7723484b", "shasum": ""}, "require": {"doctrine/dbal": "^2.12", "doctrine/persistence": "^1.3.3|^2.0", "php": "^7.3|^8.0", "queue-interop/queue-interop": "^0.8", "ramsey/uuid": "^3.5|^4"}, "require-dev": {"enqueue/null": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5", "queue-interop/queue-spec": "^0.6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\Dbal\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message Queue Doctrine DBAL Transport", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["dbal", "doctrine", "messaging", "queue"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2021-04-28T17:38:10+00:00"}, {"name": "enqueue/dsn", "version": "0.10.8", "source": {"type": "git", "url": "https://github.com/php-enqueue/dsn.git", "reference": "729fabaae6b24189d14598033b174bd72e825e9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/dsn/zipball/729fabaae6b24189d14598033b174bd72e825e9a", "reference": "729fabaae6b24189d14598033b174bd72e825e9a", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\Dsn\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Parse DSN", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["dsn", "parse"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2021-02-09T12:01:28+00:00"}, {"name": "enqueue/enqueue", "version": "0.10.15", "source": {"type": "git", "url": "https://github.com/php-enqueue/enqueue.git", "reference": "406190fc7b3458777e497512f3c3addbd4d13f31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/enqueue/zipball/406190fc7b3458777e497512f3c3addbd4d13f31", "reference": "406190fc7b3458777e497512f3c3addbd4d13f31", "shasum": ""}, "require": {"enqueue/dsn": "^0.10", "enqueue/null": "^0.10", "php": "^7.3|^8.0", "psr/container": "^1 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "queue-interop/amqp-interop": "^0.8.2", "queue-interop/queue-interop": "^0.8", "ramsey/uuid": "^3.5|^4"}, "require-dev": {"empi89/php-amqp-stubs": "*@dev", "enqueue/amqp-bunny": "0.10.x-dev", "enqueue/amqp-ext": "0.10.x-dev", "enqueue/amqp-lib": "0.10.x-dev", "enqueue/dbal": "0.10.x-dev", "enqueue/dsn": "0.10.x-dev", "enqueue/fs": "0.10.x-dev", "enqueue/gearman": "0.10.x-dev", "enqueue/gps": "0.10.x-dev", "enqueue/mongodb": "0.10.x-dev", "enqueue/pheanstalk": "0.10.x-dev", "enqueue/rdkafka": "0.10.x-dev", "enqueue/redis": "0.10.x-dev", "enqueue/simple-client": "0.10.x-dev", "enqueue/sqs": "0.10.x-dev", "enqueue/stomp": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5", "symfony/config": "^4.3|^5", "symfony/console": "^4.3|^5", "symfony/dependency-injection": "^4.3|^5", "symfony/event-dispatcher": "^4.3|^5", "symfony/http-kernel": "^4.3|^5", "symfony/yaml": "^4.3|^5"}, "suggest": {"enqueue/amqp-ext": "AMQP transport (based on php extension)", "enqueue/dbal": "Doctrine DBAL transport", "enqueue/fs": "Filesystem transport", "enqueue/redis": "Redis transport", "enqueue/sqs": "Amazon AWS SQS transport", "enqueue/stomp": "STOMP transport", "symfony/config": "^4.3|^5", "symfony/console": "^2.8|^3|^4|^5 If you want to use cli commands", "symfony/dependency-injection": "^4.3|^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message Queue Library", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["AMQP", "messaging", "queue", "rabbitmq"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2021-11-02T16:38:53+00:00"}, {"name": "enqueue/enqueue-bundle", "version": "0.10.15", "source": {"type": "git", "url": "https://github.com/php-enqueue/enqueue-bundle.git", "reference": "fb61202d6fa4d3f8fb0e54330a26202ed7a7e894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/enqueue-bundle/zipball/fb61202d6fa4d3f8fb0e54330a26202ed7a7e894", "reference": "fb61202d6fa4d3f8fb0e54330a26202ed7a7e894", "shasum": ""}, "require": {"enqueue/enqueue": "^0.10", "enqueue/null": "^0.10", "php": "^7.3|^8.0", "queue-interop/amqp-interop": "^0.8.2", "queue-interop/queue-interop": "^0.8", "symfony/framework-bundle": "^4.3|^5"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.0", "doctrine/doctrine-bundle": "^2.0", "doctrine/mongodb-odm-bundle": "^3.5|^4.3", "enqueue/amqp-bunny": "0.10.x-dev", "enqueue/amqp-ext": "0.10.x-dev", "enqueue/amqp-lib": "0.10.x-dev", "enqueue/async-command": "0.10.x-dev", "enqueue/async-event-dispatcher": "0.10.x-dev", "enqueue/dbal": "0.10.x-dev", "enqueue/fs": "0.10.x-dev", "enqueue/gps": "0.10.x-dev", "enqueue/job-queue": "0.10.x-dev", "enqueue/redis": "0.10.x-dev", "enqueue/sqs": "0.10.x-dev", "enqueue/stomp": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "php-amqplib/php-amqplib": "^3.0", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "^4.3|^5", "symfony/expression-language": "^4.3|^5", "symfony/yaml": "^4.3|^5"}, "suggest": {"enqueue/async-command": "If want to run Symfony command via message queue", "enqueue/async-event-dispatcher": "If you want dispatch and process events asynchronously"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\Bundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message Queue Bundle", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["AMQP", "messaging", "queue", "rabbitmq"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2021-10-29T20:09:38+00:00"}, {"name": "enqueue/null", "version": "0.10.15", "source": {"type": "git", "url": "https://github.com/php-enqueue/null.git", "reference": "1f7b17d43b7dfdce9047a484625db3cb888e0dc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/null/zipball/1f7b17d43b7dfdce9047a484625db3cb888e0dc7", "reference": "1f7b17d43b7dfdce9047a484625db3cb888e0dc7", "shasum": ""}, "require": {"php": "^7.3|^8.0", "queue-interop/queue-interop": "^0.8"}, "require-dev": {"enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5", "queue-interop/queue-spec": "^0.6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\Null\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Enqueue Null transport", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["messaging", "queue", "testing"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2021-10-29T20:37:39+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/master"}, "time": "2020-06-29T00:56:53+00:00"}, {"name": "firebase/php-jwt", "version": "v5.5.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/83b609028194aa042ea33b5af2d41a7427de80e6", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v5.5.1"}, "time": "2021-11-08T20:18:51+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "c828ced1f932094ab79e4120a106a666565e4d9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/c828ced1f932094ab79e4120a106a666565e4d9c", "reference": "c828ced1f932094ab79e4120a106a666565e4d9c", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0|^6.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.4|^6.0"}, "type": "library", "extra": {"thanks": {"name": "ocramius/proxy-manager", "url": "https://github.com/Ocramius/ProxyManager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.7"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-02T09:29:19+00:00"}, {"name": "google/auth", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "21dd478e77b0634ed9e3a68613f74ed250ca9347"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/21dd478e77b0634ed9e3a68613f74ed250ca9347", "reference": "21dd478e77b0634ed9e3a68613f74ed250ca9347", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0|~3.0|~4.0|~5.0", "guzzlehttp/guzzle": "^5.3.1|^6.2.1|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": ">=5.4", "psr/cache": "^1.0|^2.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "^0.2.5|^0.5.1", "phpseclib/phpseclib": "^2.0.31", "phpunit/phpunit": "^4.8.36|^5.7", "sebastian/comparator": ">=1.2.3"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/master/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.18.0"}, "time": "2021-08-24T18:03:18+00:00"}, {"name": "google/cloud-core", "version": "v1.44.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-core.git", "reference": "72706f7d1824777f42294a3c9ccdaddaad670017"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-core/zipball/72706f7d1824777f42294a3c9ccdaddaad670017", "reference": "72706f7d1824777f42294a3c9ccdaddaad670017", "shasum": ""}, "require": {"google/auth": "^1.18", "guzzlehttp/guzzle": "^5.3|^6.0|^7.0", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.7|^2.0", "monolog/monolog": "^1.1|^2.0", "php": ">=5.5", "psr/http-message": "1.0.*", "rize/uri-template": "~0.3"}, "require-dev": {"erusev/parsedown": "^1.6", "google/common-protos": "^1.0||^2.0", "google/gax": "^1.9", "opis/closure": "^3", "phpdocumentor/reflection": "^3.0", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"opis/closure": "May be used to serialize closures to process jobs in the batch daemon. Please require version ^3.", "symfony/lock": "Required for the Spanner cached based session pool. Please require the following commit: 3.3.x-dev#1ba6ac9"}, "bin": ["bin/google-cloud-batch"], "type": "library", "extra": {"component": {"id": "cloud-core", "target": "googleapis/google-cloud-php-core.git", "path": "Core", "entry": "src/ServiceBuilder.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Cloud PHP shared dependency, providing functionality useful to all components.", "support": {"source": "https://github.com/googleapis/google-cloud-php-core/tree/v1.44.0"}, "time": "2022-01-31T21:39:13+00:00"}, {"name": "google/cloud-storage", "version": "v1.25.2", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-storage.git", "reference": "d040368605ce3b8c2e6f2f7c03eb4046e9e0b951"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-storage/zipball/d040368605ce3b8c2e6f2f7c03eb4046e9e0b951", "reference": "d040368605ce3b8c2e6f2f7c03eb4046e9e0b951", "shasum": ""}, "require": {"google/cloud-core": "^1.43", "google/crc32": "^0.1.0"}, "require-dev": {"erusev/parsedown": "^1.6", "google/cloud-pubsub": "^1.0", "phpdocumentor/reflection": "^3.0", "phpseclib/phpseclib": "^2", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"google/cloud-pubsub": "May be used to register a topic to receive bucket notifications.", "phpseclib/phpseclib": "May be used in place of OpenSSL for creating signed Cloud Storage URLs. Please require version ^2."}, "type": "library", "extra": {"component": {"id": "cloud-storage", "target": "googleapis/google-cloud-php-storage.git", "path": "Storage", "entry": "src/StorageClient.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\Storage\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Cloud Storage Client for PHP", "support": {"source": "https://github.com/googleapis/google-cloud-php-storage/tree/v1.25.2"}, "time": "2021-10-20T17:52:15+00:00"}, {"name": "google/crc32", "version": "v0.1.0", "source": {"type": "git", "url": "https://github.com/google/php-crc32.git", "reference": "a8525f0dea6fca1893e1bae2f6e804c5f7d007fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/php-crc32/zipball/a8525f0dea6fca1893e1bae2f6e804c5f7d007fb", "reference": "a8525f0dea6fca1893e1bae2f6e804c5f7d007fb", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.13 || v2.14.2", "paragonie/random_compat": ">=2", "phpunit/phpunit": "^4"}, "type": "library", "autoload": {"psr-4": {"Google\\CRC32\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Various CRC32 implementations", "homepage": "https://github.com/google/php-crc32", "support": {"issues": "https://github.com/google/php-crc32/issues", "source": "https://github.com/google/php-crc32/tree/v0.1.0"}, "time": "2019-05-09T06:24:58+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.4.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "ee0a041b1760e6a53d2a39c8c34115adc2af2c79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/ee0a041b1760e6a53d2a39c8c34115adc2af2c79", "reference": "ee0a041b1760e6a53d2a39c8c34115adc2af2c79", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.4-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2021-12-06T18:43:05+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "1afdd860a2566ed3c2b0b4a3de6e23434a79ec85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/1afdd860a2566ed3c2b0b4a3de6e23434a79ec85", "reference": "1afdd860a2566ed3c2b0b4a3de6e23434a79ec85", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.8.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2021-10-05T13:56:00+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/jdorn/sql-formatter/issues", "source": "https://github.com/jdorn/sql-formatter/tree/master"}, "time": "2014-01-12T16:20:24+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.11", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "2ab6744b7296ded80f8cc4f9509abbff393399aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/2ab6744b7296ded80f8cc4f9509abbff393399aa", "reference": "2ab6744b7296ded80f8cc4f9509abbff393399aa", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/justinrainbow/json-schema/issues", "source": "https://github.com/justinrainbow/json-schema/tree/5.2.11"}, "time": "2021-07-22T09:24:00+00:00"}, {"name": "laminas/laminas-code", "version": "4.5.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "6fd96d4d913571a2cd056a27b123fa28cb90ac4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/6fd96d4d913571a2cd056a27b123fa28cb90ac4e", "reference": "6fd96d4d913571a2cd056a27b123fa28cb90ac4e", "shasum": ""}, "require": {"php": ">=7.4, <8.2"}, "require-dev": {"doctrine/annotations": "^1.13.2", "ext-phar": "*", "laminas/laminas-coding-standard": "^2.3.0", "laminas/laminas-stdlib": "^3.6.1", "phpunit/phpunit": "^9.5.10", "psalm/plugin-phpunit": "^0.16.1", "vimeo/psalm": "^4.13.1"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "type": "library", "autoload": {"files": ["polyfill/ReflectionEnumPolyfill.php"], "psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-12-19T18:06:55+00:00"}, {"name": "lcobucci/clock", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/lcobucci/clock.git", "reference": "353d83fe2e6ae95745b16b3d911813df6a05bfb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/clock/zipball/353d83fe2e6ae95745b16b3d911813df6a05bfb3", "reference": "353d83fe2e6ae95745b16b3d911813df6a05bfb3", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"infection/infection": "^0.17", "lcobucci/coding-standard": "^6.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/php-code-coverage": "9.1.4", "phpunit/phpunit": "9.3.7"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\Clock\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yet another clock abstraction", "support": {"issues": "https://github.com/lcobucci/clock/issues", "source": "https://github.com/lcobucci/clock/tree/2.0.x"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2020-08-27T18:56:02+00:00"}, {"name": "lcobucci/jwt", "version": "4.1.5", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "fe2d89f2eaa7087af4aa166c6f480ef04e000582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/fe2d89f2eaa7087af4aa166c6f480ef04e000582", "reference": "fe2d89f2eaa7087af4aa166c6f480ef04e000582", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-sodium": "*", "lcobucci/clock": "^2.0", "php": "^7.4 || ^8.0"}, "require-dev": {"infection/infection": "^0.21", "lcobucci/coding-standard": "^6.0", "mikey179/vfsstream": "^1.6.7", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/php-invoker": "^3.1", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/4.1.5"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2021-09-28T19:34:56+00:00"}, {"name": "league/event", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/event.git", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/event/zipball/d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"League\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Event package", "keywords": ["emitter", "event", "listener"], "support": {"issues": "https://github.com/thephpleague/event/issues", "source": "https://github.com/thephpleague/event/tree/master"}, "time": "2018-11-26T11:52:41+00:00"}, {"name": "league/flysystem", "version": "1.1.9", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "094defdb4a7001845300334e7c1ee2335925ef99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/094defdb4a7001845300334e7c1ee2335925ef99", "reference": "094defdb4a7001845300334e7c1ee2335925ef99", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.9"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2021-12-09T09:40:50+00:00"}, {"name": "league/flysystem-aws-s3-v3", "version": "1.0.29", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-aws-s3-v3.git", "reference": "4e25cc0582a36a786c31115e419c6e40498f6972"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-aws-s3-v3/zipball/4e25cc0582a36a786c31115e419c6e40498f6972", "reference": "4e25cc0582a36a786c31115e419c6e40498f6972", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.20.0", "league/flysystem": "^1.0.40", "php": ">=5.5.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\AwsS3v3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Flysystem adapter for the AWS S3 SDK v3.x", "support": {"issues": "https://github.com/thephpleague/flysystem-aws-s3-v3/issues", "source": "https://github.com/thephpleague/flysystem-aws-s3-v3/tree/1.0.29"}, "time": "2020-10-08T18:58:37+00:00"}, {"name": "league/flysystem-memory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-memory.git", "reference": "d0e87477c32e29f999b4de05e64c1adcddb51757"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-memory/zipball/d0e87477c32e29f999b4de05e64c1adcddb51757", "reference": "d0e87477c32e29f999b4de05e64c1adcddb51757", "shasum": ""}, "require": {"league/flysystem": "~1.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\Memory\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "An in-memory adapter for Flysystem.", "homepage": "https://github.com/thephpleague/flysystem-memory", "keywords": ["Flysystem", "adapter", "memory"], "support": {"issues": "https://github.com/thephpleague/flysystem-memory/issues", "source": "https://github.com/thephpleague/flysystem-memory/tree/1.0.2"}, "time": "2019-05-30T21:34:13+00:00"}, {"name": "league/mime-type-detection", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "aa70e813a6ad3d1558fc927863d47309b4c23e69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/aa70e813a6ad3d1558fc927863d47309b4c23e69", "reference": "aa70e813a6ad3d1558fc927863d47309b4c23e69", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.9.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2021-11-21T11:48:40+00:00"}, {"name": "league/oauth2-server", "version": "8.3.3", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-server.git", "reference": "f5698a3893eda9a17bcd48636990281e7ca77b2a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-server/zipball/f5698a3893eda9a17bcd48636990281e7ca77b2a", "reference": "f5698a3893eda9a17bcd48636990281e7ca77b2a", "shasum": ""}, "require": {"defuse/php-encryption": "^2.2.1", "ext-json": "*", "ext-openssl": "*", "lcobucci/jwt": "^3.4.6 || ^4.0.4", "league/event": "^2.2", "php": "^7.2 || ^8.0", "psr/http-message": "^1.0.1"}, "replace": {"league/oauth2server": "*", "lncd/oauth2": "*"}, "require-dev": {"laminas/laminas-diactoros": "^2.4.1", "phpstan/phpstan": "^0.12.57", "phpstan/phpstan-phpunit": "^0.12.16", "phpunit/phpunit": "^8.5.13", "roave/security-advisories": "dev-master"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.noexceptions.io", "role": "Developer"}], "description": "A lightweight and powerful OAuth 2.0 authorization and resource server library with support for all the core specification grants. This library will allow you to secure your API with OAuth and allow your applications users to approve apps that want to access their data from your API.", "homepage": "https://oauth2.thephpleague.com/", "keywords": ["Authentication", "api", "auth", "authorisation", "authorization", "o<PERSON>h", "oauth 2", "oauth 2.0", "oauth2", "protect", "resource", "secure", "server"], "support": {"issues": "https://github.com/thephpleague/oauth2-server/issues", "source": "https://github.com/thephpleague/oauth2-server/tree/8.3.3"}, "funding": [{"url": "https://github.com/sephster", "type": "github"}], "time": "2021-10-11T20:41:49+00:00"}, {"name": "marc1706/fast-image-size", "version": "v1.1.6", "source": {"type": "git", "url": "https://github.com/marc1706/fast-image-size.git", "reference": "3a3a2b036be20f43fa06ce00dfa754df503e6684"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc1706/fast-image-size/zipball/3a3a2b036be20f43fa06ce00dfa754df503e6684", "reference": "3a3a2b036be20f43fa06ce00dfa754df503e6684", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"FastImageSize\\": "lib", "FastImageSize\\tests\\": "tests"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.m-a-styles.de", "role": "Developer"}], "description": "fast-image-size is a PHP library that does almost everything PHP's getimagesize() does but without the large overhead of downloading the complete file.", "homepage": "https://www.m-a-styles.de", "keywords": ["fast", "getimagesize", "image", "imagesize", "php", "size"], "support": {"issues": "https://github.com/marc1706/fast-image-size/issues", "source": "https://github.com/marc1706/fast-image-size/tree/v1.1.6"}, "time": "2019-12-07T08:02:07+00:00"}, {"name": "monolog/monolog", "version": "2.3.5", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/fd4380d6fc37626e2f799f29d91195040137eba9", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.5"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2021-10-01T21:08:31+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.6.1"}, "time": "2021-06-14T00:11:39+00:00"}, {"name": "nyholm/psr7", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "23ae1f00fbc6a886cbe3062ca682391b9cc7c37b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/23ae1f00fbc6a886cbe3062ca682391b9cc7c37b", "reference": "23ae1f00fbc6a886cbe3062ca682391b9cc7c37b", "shasum": ""}, "require": {"php": ">=7.1", "php-http/message-factory": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.8", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || 8.5 || 9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.4.0"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2021-02-18T15:41:32+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.4", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/dd448ad1ce34c63d09baccd05415e361300c35b4", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4", "shasum": ""}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/0.5.4"}, "time": "2021-12-17T19:44:54+00:00"}, {"name": "phenx/php-svg-lib", "version": "0.3.4", "source": {"type": "git", "url": "https://github.com/PhenX/php-svg-lib.git", "reference": "f627771eb854aa7f45f80add0f23c6c4d67ea0f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-svg-lib/zipball/f627771eb854aa7f45f80add0f23c6c4d67ea0f2", "reference": "f627771eb854aa7f45f80add0f23c6c4d67ea0f2", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "sabberworm/php-css-parser": "^8.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/PhenX/php-svg-lib/issues", "source": "https://github.com/PhenX/php-svg-lib/tree/0.3.4"}, "time": "2021-10-18T02:13:32+00:00"}, {"name": "php-http/message-factory", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/master"}, "time": "2015-12-19T14:08:53+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "446d54b4cb6bf489fc9d75f55843658e6f25d801"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/446d54b4cb6bf489fc9d75f55843658e6f25d801", "reference": "446d54b4cb6bf489fc9d75f55843658e6f25d801", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.2"}, "time": "2019-11-01T11:05:21+00:00"}, {"name": "queue-interop/amqp-interop", "version": "0.8.2", "source": {"type": "git", "url": "https://github.com/queue-interop/amqp-interop.git", "reference": "a893c72832784ca846fcc1bd86274a6a449a1ab7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/queue-interop/amqp-interop/zipball/a893c72832784ca846fcc1bd86274a6a449a1ab7", "reference": "a893c72832784ca846fcc1bd86274a6a449a1ab7", "shasum": ""}, "require": {"php": "^7.3 | ^8.0", "queue-interop/queue-interop": "^0.7|^0.8|^0.9"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.8.x-dev"}}, "autoload": {"psr-4": {"Interop\\Amqp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "AMQP interop", "support": {"issues": "https://github.com/queue-interop/amqp-interop/issues", "source": "https://github.com/queue-interop/amqp-interop/tree/0.8.2"}, "time": "2021-01-21T11:33:30+00:00"}, {"name": "queue-interop/queue-interop", "version": "0.8.1", "source": {"type": "git", "url": "https://github.com/queue-interop/queue-interop.git", "reference": "117043fd38490f8b5516622cd4b697b33a89ce2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/queue-interop/queue-interop/zipball/117043fd38490f8b5516622cd4b697b33a89ce2b", "reference": "117043fd38490f8b5516622cd4b697b33a89ce2b", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.5 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "queue-interop/queue-spec": "^0.6@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.7-dev"}}, "autoload": {"psr-4": {"Interop\\Queue\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Promoting the interoperability of MQs objects. Based on Java JMS", "homepage": "https://github.com/queue-interop/queue-interop", "keywords": ["MQ", "jms", "message queue", "messaging", "queue"], "support": {"issues": "https://github.com/queue-interop/queue-interop/issues", "source": "https://github.com/queue-interop/queue-interop/tree/0.8.1"}, "time": "2020-12-21T13:14:51+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "cccc74ee5e328031b15640b51056ee8d3bb66c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/cccc74ee5e328031b15640b51056ee8d3bb66c0a", "reference": "cccc74ee5e328031b15640b51056ee8d3bb66c0a", "shasum": ""}, "require": {"php": "^7.3 || ^8", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/captainhook": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "ergebnis/composer-normalize": "^2.6", "fakerphp/faker": "^1.5", "hamcrest/hamcrest-php": "^2", "jangregor/phpstan-prophecy": "^0.8", "mockery/mockery": "^1.3", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^0.12.32", "phpstan/phpstan-mockery": "^0.12.5", "phpstan/phpstan-phpunit": "^0.12.11", "phpunit/phpunit": "^8.5 || ^9", "psy/psysh": "^0.10.4", "slevomat/coding-standard": "^6.3", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.4"}, "type": "library", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.2.2"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2021-10-10T03:01:02+00:00"}, {"name": "ramsey/uuid", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df", "shasum": ""}, "require": {"brick/math": "^0.8 || ^0.9", "ext-json": "*", "php": "^7.2 || ^8.0", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php80": "^1.14"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5 || ^9", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}, "captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.2.3"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2021-09-25T23:10:38+00:00"}, {"name": "react/promise", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/234f8fd1023c9158e2314fa9d7d0e6a83db42910", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.9.0"}, "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-11T10:27:51+00:00"}, {"name": "rize/uri-template", "version": "0.3.4", "source": {"type": "git", "url": "https://github.com/rize/UriTemplate.git", "reference": "2a874863c48d643b9e2e254ab288ec203060a0b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rize/UriTemplate/zipball/2a874863c48d643b9e2e254ab288ec203060a0b8", "reference": "2a874863c48d643b9e2e254ab288ec203060a0b8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "type": "library", "autoload": {"psr-4": {"Rize\\": "src/Rize"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>ut <PERSON>", "homepage": "http://twitter.com/rezigned"}], "description": "PHP URI Template (RFC 6570) supports both expansion & extraction", "keywords": ["RFC 6570", "template", "uri"], "support": {"issues": "https://github.com/rize/UriTemplate/issues", "source": "https://github.com/rize/UriTemplate/tree/0.3.4"}, "funding": [{"url": "https://www.paypal.me/rezigned", "type": "custom"}, {"url": "https://opencollective.com/rize-uri-template", "type": "open_collective"}], "time": "2021-10-09T06:30:16+00:00"}, {"name": "sabberworm/php-css-parser", "version": "8.4.0", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/e41d2140031d533348b2192a83f02d8dd8a71d30", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "^4.8.36"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/sabberworm/PHP-CSS-Parser/issues", "source": "https://github.com/sabberworm/PHP-CSS-Parser/tree/8.4.0"}, "time": "2021-12-11T13:40:54+00:00"}, {"name": "seld/jsonlint", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "9ad6ce79c342fbd44df10ea95511a1b24dee5b57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/9ad6ce79c342fbd44df10ea95511a1b24dee5b57", "reference": "9ad6ce79c342fbd44df10ea95511a1b24dee5b57", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.8.3"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2020-11-11T09:19:24+00:00"}, {"name": "seld/phar-utils", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "9f3452c93ff423469c0d56450431562ca423dcee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/9f3452c93ff423469c0d56450431562ca423dcee", "reference": "9f3452c93ff423469c0d56450431562ca423dcee", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "support": {"issues": "https://github.com/Seldaek/phar-utils/issues", "source": "https://github.com/Seldaek/phar-utils/tree/1.2.0"}, "time": "2021-12-10T11:20:11+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v6.2.6", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "6bd976c99ef3f78e31c9490a10ba6dd8901076eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/6bd976c99ef3f78e31c9490a10ba6dd8901076eb", "reference": "6bd976c99ef3f78e31c9490a10ba6dd8901076eb", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "php": ">=7.2.5", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/framework-bundle": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0"}, "conflict": {"doctrine/doctrine-cache-bundle": "<1.3.1", "doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/dbal": "^2.10|^3.0", "doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.5", "symfony/browser-kit": "^4.4|^5.0|^6.0", "symfony/doctrine-bridge": "^4.4|^5.0|^6.0", "symfony/dom-crawler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/monolog-bridge": "^4.0|^5.0|^6.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0", "symfony/security-bundle": "^4.4|^5.0|^6.0", "symfony/twig-bundle": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/twig": "^1.34|^2.4|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "6.1.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": "src/"}, "exclude-from-classmap": ["/tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/v6.2.6"}, "time": "2022-01-14T11:51:13+00:00"}, {"name": "shopware/conflicts", "version": "0.0.1", "source": {"type": "git", "url": "https://github.com/shopware/conflicts.git", "reference": "a66242b05e7b25d538a1ab1cf568587be767fa33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/shopware/conflicts/zipball/a66242b05e7b25d538a1ab1cf568587be767fa33", "reference": "a66242b05e7b25d538a1ab1cf568587be767fa33", "shasum": ""}, "conflict": {"symfony/notifier": "v5.3.8", "symfony/symfony": "*"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Shopware 6 conflicting packages", "support": {"issues": "https://github.com/shopware/conflicts/issues", "source": "https://github.com/shopware/conflicts/tree/0.0.1"}, "time": "2021-12-20T12:19:56+00:00"}, {"name": "shopware/core", "version": "*******", "source": {"type": "git", "url": "https://github.com/shopware/core.git", "reference": "14834abeebefbcd8cf24c63f9f7a0aa2a1de8b28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/shopware/core/zipball/14834abeebefbcd8cf24c63f9f7a0aa2a1de8b28", "reference": "14834abeebefbcd8cf24c63f9f7a0aa2a1de8b28", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.198.5", "cocur/slugify": "4.0.0", "composer-runtime-api": "^2.0", "composer/composer": "^2.1.9", "composer/semver": "^3.2", "doctrine/annotations": "1.13.1", "doctrine/dbal": "2.13.3", "doctrine/inflector": "~1.4.4", "dompdf/dompdf": "1.0.2", "enqueue/dbal": "0.10.11", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-session": "*", "ext-simplexml": "*", "ext-sodium": "*", "ext-xml": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "4.13.0", "google/cloud-storage": "~1.25.1", "guzzlehttp/guzzle": "~7.2", "guzzlehttp/psr7": "^1.8.0", "jdorn/sql-formatter": "1.2.17", "lcobucci/jwt": "~4.1.5", "league/flysystem": "~1.1.4", "league/flysystem-aws-s3-v3": "~1.0.29", "league/flysystem-memory": "~1.0.2", "league/oauth2-server": "~8.3.2", "marc1706/fast-image-size": "1.1.6", "nyholm/psr7": "1.4.0", "php": "^7.4.3 || ^8.0", "psr/cache": "1.0.1", "psr/event-dispatcher": "1.0.0", "psr/http-factory": "1.0.1", "psr/http-message": "1.0.1", "psr/log": "1.1.2", "sensio/framework-extra-bundle": "~6.2.1", "shopware/conflicts": "*", "squirrelphp/twig-php-syntax": "1.7.0", "sroze/messenger-enqueue-transport": "0.4.0", "superbalist/flysystem-google-storage": "~7.2.2", "symfony/asset": "~5.4.0", "symfony/cache": "~5.4.0", "symfony/cache-contracts": "2.2.0", "symfony/config": "~5.4.0", "symfony/console": "~5.4.1", "symfony/debug-bundle": "~5.4.0", "symfony/dependency-injection": "~5.4.1", "symfony/deprecation-contracts": "2.2.0", "symfony/dotenv": "~5.4.0", "symfony/error-handler": "~5.4.1", "symfony/event-dispatcher": "~5.4.0", "symfony/event-dispatcher-contracts": "2.2.0", "symfony/filesystem": "~5.4.0", "symfony/finder": "~5.4.0", "symfony/framework-bundle": "~5.4.1", "symfony/http-foundation": "~5.4.1", "symfony/http-kernel": "~5.4.2", "symfony/inflector": "~5.4.0", "symfony/mailer": "~5.4.0", "symfony/messenger": "~5.4.0", "symfony/mime": "~5.4.0", "symfony/monolog-bridge": "~5.4.0", "symfony/monolog-bundle": "3.6.0", "symfony/options-resolver": "~5.4.0", "symfony/polyfill-php80": "~1.23.1", "symfony/polyfill-php81": "~v1.23.0", "symfony/process": "~5.4.0", "symfony/property-access": "~5.4.0", "symfony/property-info": "~5.4.0", "symfony/proxy-manager-bridge": "~5.4.0", "symfony/psr-http-message-bridge": "2.1.0", "symfony/rate-limiter": "~5.4.0", "symfony/routing": "~5.4.0", "symfony/security-core": "~5.4.0", "symfony/serializer": "~5.4.0", "symfony/service-contracts": "2.2.0", "symfony/stopwatch": "~5.4.0", "symfony/translation": "~5.4.1", "symfony/translation-contracts": "2.3.0", "symfony/twig-bridge": "~5.4.0", "symfony/twig-bundle": "~5.4.0", "symfony/validator": "~5.4.1", "symfony/var-exporter": "~5.4.0", "symfony/yaml": "~5.4.0", "true/punycode": "2.1.1", "twig/intl-extra": "~3.3.5", "twig/string-extra": "~3.3.5", "twig/twig": "~3.3.8", "zircote/swagger-php": "3.3.2"}, "require-dev": {"bheller/images-generator": "~1.0.1", "brianium/paratest": "^6.2", "dms/phpunit-arraysubset-asserts": "^0.2.1", "ext-tokenizer": "*", "ext-xmlwriter": "*", "fzaninotto/faker": "~1.9.1", "johnkary/phpunit-speedtrap": "~4.0.0", "mbezhanov/faker-provider-collection": "~2.0.1", "nikic/php-parser": "~4.10.4", "opis/json-schema": "~1.0.19", "phpunit/php-code-coverage": "~9.2.5", "phpunit/phpunit": "~9.5.6", "smalot/pdfparser": "~0.14.0", "symfony/browser-kit": "~5.4.0", "symfony/dom-crawler": "~5.4.0", "symfony/phpunit-bridge": "~5.4.0", "symfony/var-dumper": "~5.4.0", "symfony/web-profiler-bundle": "~5.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.4.x-dev", "dev-trunk": "6.4.x-dev"}}, "autoload": {"psr-4": {"Shopware\\Core\\": ""}, "exclude-from-classmap": ["*/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Shopware platform is the core for all Shopware ecommerce products.", "homepage": "http://www.shopware.com", "keywords": ["shop", "shopware"], "support": {"chat": "https://slack.shopware.com", "docs": "https://developer.shopware.com", "forum": "https://forum.shopware.com", "issues": "https://issues.shopware.com", "source": "https://github.com/shopware/core/tree/*******", "wiki": "https://developer.shopware.com"}, "time": "2022-02-14T13:23:00+00:00"}, {"name": "squirrelphp/twig-php-syntax", "version": "v1.7", "source": {"type": "git", "url": "https://github.com/squirrelphp/twig-php-syntax.git", "reference": "97738f7533de0fd5ed8f03d052d219bd23398976"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squirrelphp/twig-php-syntax/zipball/97738f7533de0fd5ed8f03d052d219bd23398976", "reference": "97738f7533de0fd5ed8f03d052d219bd23398976", "shasum": ""}, "require": {"php": ">=7.2.5", "twig/twig": "^3.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.3", "captainhook/plugin-composer": "^5.0", "phpunit/phpunit": "^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Squirrel\\TwigPhpSyntax\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adds common PHP syntax to twig templates, like ===, foreach and continue/break.", "homepage": "https://github.com/squirrelphp/twig-php-syntax", "keywords": ["foreach", "php", "syntax", "twig"], "support": {"issues": "https://github.com/squirrelphp/twig-php-syntax/issues", "source": "https://github.com/squirrelphp/twig-php-syntax/tree/v1.7"}, "time": "2021-11-29T23:24:13+00:00"}, {"name": "sroze/messenger-enqueue-transport", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/sroze/messenger-enqueue-transport.git", "reference": "a698418e702ecb29aa51ee2e40e40c90798057f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sroze/messenger-enqueue-transport/zipball/a698418e702ecb29aa51ee2e40e40c90798057f0", "reference": "a698418e702ecb29aa51ee2e40e40c90798057f0", "shasum": ""}, "require": {"enqueue/amqp-tools": "^0.10", "enqueue/enqueue-bundle": "^0.10", "symfony/messenger": "^4.3|^5", "symfony/options-resolver": "^3.4|^4.2|^5"}, "replace": {"enqueue/messenger-adapter": ">0.2.2"}, "require-dev": {"phpspec/prophecy": "^1.8.0", "phpunit/phpunit": "^7.1", "symfony/yaml": "^3.4|^4.1|^5"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Enqueue\\MessengerAdapter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Enqueue adapter for Symfony Messenger component", "homepage": "http://symfony.com", "keywords": ["<PERSON>", "enqueue", "symfony"], "support": {"issues": "https://github.com/sroze/messenger-enqueue-transport/issues", "source": "https://github.com/sroze/messenger-enqueue-transport/tree/master"}, "time": "2020-01-22T14:45:26+00:00"}, {"name": "superbalist/flysystem-google-storage", "version": "7.2.2", "source": {"type": "git", "url": "https://github.com/Superbalist/flysystem-google-cloud-storage.git", "reference": "87e2f450c0e4b5200fef9ffe6863068cc873d734"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Superbalist/flysystem-google-cloud-storage/zipball/87e2f450c0e4b5200fef9ffe6863068cc873d734", "reference": "87e2f450c0e4b5200fef9ffe6863068cc873d734", "shasum": ""}, "require": {"google/cloud-storage": "~1.0", "league/flysystem": "~1.0", "php": ">=5.5.0"}, "require-dev": {"mockery/mockery": "0.9.*", "phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Superbalist\\Flysystem\\GoogleStorage\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Superbalist.com a division of Takealot Online (Pty) Ltd", "email": "<EMAIL>"}], "description": "Flysystem adapter for Google Cloud Storage", "support": {"issues": "https://github.com/Superbalist/flysystem-google-cloud-storage/issues", "source": "https://github.com/Superbalist/flysystem-google-cloud-storage/tree/7.2.2"}, "time": "2019-10-10T12:22:54+00:00"}, {"name": "symfony/amqp-messenger", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/amqp-messenger.git", "reference": "4175a0a98507e7ec575dca9b36e6c0a5a072d3fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/amqp-messenger/zipball/4175a0a98507e7ec575dca9b36e6c0a5a072d3fd", "reference": "4175a0a98507e7ec575dca9b36e6c0a5a072d3fd", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.3|^6.0"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Amqp\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony AMQP extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/amqp-messenger/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-09T15:49:12+00:00"}, {"name": "symfony/asset", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "156e45cba14f9be3fe8b987e86f482a76aa078f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/156e45cba14f9be3fe8b987e86f482a76aa078f3", "reference": "156e45cba14f9be3fe8b987e86f482a76aa078f3", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/http-foundation": "<5.3"}, "require-dev": {"symfony/http-client": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^5.3|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/cache", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "b954c9397b226df319c6dfd002636ddaa4d848ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/b954c9397b226df319c6dfd002636ddaa4d848ef", "reference": "b954c9397b226df319c6dfd002636ddaa4d848ef", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.13.1|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-21T15:00:19+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "8034ca0b61d4dd967f3698aaa1da2507b631d0cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/8034ca0b61d4dd967f3698aaa1da2507b631d0cb", "reference": "8034ca0b61d4dd967f3698aaa1da2507b631d0cb", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/config", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "d65e1bd990c740e31feb07d2b0927b8d4df9956f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/d65e1bd990c740e31feb07d2b0927b8d4df9956f", "reference": "d65e1bd990c740e31feb07d2b0927b8d4df9956f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-03T09:50:52+00:00"}, {"name": "symfony/console", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "d8111acc99876953f52fe16d4c50eb60940d49ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/d8111acc99876953f52fe16d4c50eb60940d49ad", "reference": "d8111acc99876953f52fe16d4c50eb60940d49ad", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-24T12:45:35+00:00"}, {"name": "symfony/debug-bundle", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/debug-bundle.git", "reference": "6f508169752ed2c0d0d8a6641c4cca39a8f1dfcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug-bundle/zipball/6f508169752ed2c0d0d8a6641c4cca39a8f1dfcb", "reference": "6f508169752ed2c0d0d8a6641c4cca39a8f1dfcb", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.2.5", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/twig-bridge": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "conflict": {"symfony/config": "<4.4", "symfony/dependency-injection": "<5.2"}, "require-dev": {"symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/web-profiler-bundle": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/config": "For service container configuration", "symfony/dependency-injection": "For using as a service from the container"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\DebugBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Symfony VarDumper component and the ServerLogCommand from MonologBridge into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug-bundle/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/dependency-injection", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "17f31bc13ef2b577d3c652d71af49d000cbd5894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/17f31bc13ef2b577d3c652d71af49d000cbd5894", "reference": "17f31bc13ef2b577d3c652d71af49d000cbd5894", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<5.3", "symfony/finder": "<4.4", "symfony/proxy-manager-bridge": "<4.4", "symfony/yaml": "<4.4.26"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "require-dev": {"symfony/config": "^5.3|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4.26|^5.0|^6.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-24T09:30:07+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5fa56b4074d1ae755beb55617ddafe6f5d78f665", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/doctrine-messenger", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-messenger.git", "reference": "335428f424807178c57b89fd2bed714fba4bf4af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-messenger/zipball/335428f424807178c57b89fd2bed714fba4bf4af", "reference": "335428f424807178c57b89fd2bed714fba4bf4af", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/messenger": "^5.1|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"doctrine/dbal": "<2.13", "doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/dbal": "^2.13|^3.0", "doctrine/persistence": "^1.3|^2", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-messenger/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/dotenv", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "83a2310904a4f5d4f42526227b5a578ac82232a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/83a2310904a4f5d4f42526227b5a578ac82232a9", "reference": "83a2310904a4f5d4f42526227b5a578ac82232a9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3"}, "require-dev": {"symfony/console": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-15T17:04:12+00:00"}, {"name": "symfony/error-handler", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "c4ffc2cd919950d13c8c9ce32a70c70214c3ffc5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/c4ffc2cd919950d13c8c9ce32a70c70214c3ffc5", "reference": "c4ffc2cd919950d13c8c9ce32a70c70214c3ffc5", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1|^3", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "dec8a9f58d20df252b9cd89f1c6c1530f747685d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/dec8a9f58d20df252b9cd89f1c6c1530f747685d", "reference": "dec8a9f58d20df252b9cd89f1c6c1530f747685d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/0ba7d54483095a198fa51781bc608d17e84dffa2", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "797680071ea8f71b94eb958680c50d0e002638f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/797680071ea8f71b94eb958680c50d0e002638f5", "reference": "797680071ea8f71b94eb958680c50d0e002638f5", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-27T10:31:47+00:00"}, {"name": "symfony/finder", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "231313534dded84c7ecaa79d14bc5da4ccb69b7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/231313534dded84c7ecaa79d14bc5da4ccb69b7d", "reference": "231313534dded84c7ecaa79d14bc5da4ccb69b7d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-26T16:34:36+00:00"}, {"name": "symfony/framework-bundle", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "59eda813401a947efefa8509322b9adee576e926"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/59eda813401a947efefa8509322b9adee576e926", "reference": "59eda813401a947efefa8509322b9adee576e926", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.2.5", "symfony/cache": "^5.2|^6.0", "symfony/config": "^5.3|^6.0", "symfony/dependency-injection": "^5.4.5|^6.0.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/error-handler": "^4.4.1|^5.0.1|^6.0", "symfony/event-dispatcher": "^5.1|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^5.3|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/routing": "^5.3|^6.0"}, "conflict": {"doctrine/annotations": "<1.13.1", "doctrine/cache": "<1.11", "doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "phpunit/phpunit": "<5.4.3", "symfony/asset": "<5.3", "symfony/console": "<5.2.5", "symfony/dom-crawler": "<4.4", "symfony/dotenv": "<5.1", "symfony/form": "<5.2", "symfony/http-client": "<4.4", "symfony/lock": "<4.4", "symfony/mailer": "<5.2", "symfony/messenger": "<5.4", "symfony/mime": "<4.4", "symfony/property-access": "<5.3", "symfony/property-info": "<4.4", "symfony/security-csrf": "<5.3", "symfony/serializer": "<5.2", "symfony/service-contracts": ">=3.0", "symfony/stopwatch": "<4.4", "symfony/translation": "<5.3", "symfony/twig-bridge": "<4.4", "symfony/twig-bundle": "<4.4", "symfony/validator": "<5.2", "symfony/web-profiler-bundle": "<4.4", "symfony/workflow": "<5.2"}, "require-dev": {"doctrine/annotations": "^1.13.1", "doctrine/cache": "^1.11|^2.0", "doctrine/persistence": "^1.3|^2.0", "paragonie/sodium_compat": "^1.8", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^5.3|^6.0", "symfony/browser-kit": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/dom-crawler": "^4.4.30|^5.3.7|^6.0", "symfony/dotenv": "^5.1|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/form": "^5.2|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/mailer": "^5.2|^6.0", "symfony/messenger": "^5.4|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/notifier": "^5.4|^6.0", "symfony/phpunit-bridge": "^5.3|^6.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-info": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0", "symfony/security-bundle": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/string": "^5.0|^6.0", "symfony/translation": "^5.3|^6.0", "symfony/twig-bundle": "^4.4|^5.0|^6.0", "symfony/validator": "^5.2|^6.0", "symfony/web-link": "^4.4|^5.0|^6.0", "symfony/workflow": "^5.2|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/twig": "^2.10|^3.0"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-21T15:00:19+00:00"}, {"name": "symfony/http-foundation", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "dd68a3b24262a902bc338fc7c9a2a61b7ab2029f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/dd68a3b24262a902bc338fc7c9a2a61b7ab2029f", "reference": "dd68a3b24262a902bc338fc7c9a2a61b7ab2029f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-21T15:00:19+00:00"}, {"name": "symfony/http-kernel", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "c770c90bc71f1db911e2d996c991fdafe273ac84"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/c770c90bc71f1db911e2d996c991fdafe273ac84", "reference": "c770c90bc71f1db911e2d996c991fdafe273ac84", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^5.0|^6.0", "symfony/http-foundation": "^5.3.7|^6.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.0", "symfony/config": "<5.0", "symfony/console": "<4.4", "symfony/dependency-injection": "<5.3", "symfony/doctrine-bridge": "<5.0", "symfony/form": "<5.0", "symfony/http-client": "<5.0", "symfony/mailer": "<5.0", "symfony/messenger": "<5.0", "symfony/translation": "<5.0", "symfony/twig-bridge": "<5.0", "symfony/validator": "<5.0", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0", "symfony/config": "^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/dom-crawler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2|^3", "symfony/process": "^4.4|^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/translation": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2|^3", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-28T07:57:55+00:00"}, {"name": "symfony/inflector", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "6157dac05bbd287d341b82d67a549fdf468f86d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/6157dac05bbd287d341b82d67a549fdf468f86d1", "reference": "6157dac05bbd287d341b82d67a549fdf468f86d1", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/string": "^5.3.10|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts words between their singular and plural forms (English only)", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "support": {"source": "https://github.com/symfony/inflector/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "EnglishInflector from the String component", "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/intl", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "47a1413da15ff840d7c419fa704d32caba3276ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/47a1413da15ff840d7c419fa704d32caba3276ac", "reference": "47a1413da15ff840d7c419fa704d32caba3276ac", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/filesystem": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a PHP replacement layer for the C intl extension that includes additional data from the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-25T13:55:17+00:00"}, {"name": "symfony/lock", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/lock.git", "reference": "f03b561c3bff8dc3a8ed0ff960f8a41bb14337d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lock/zipball/f03b561c3bff8dc3a8ed0ff960f8a41bb14337d8", "reference": "f03b561c3bff8dc3a8ed0ff960f8a41bb14337d8", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/dbal": "<2.13"}, "require-dev": {"doctrine/dbal": "^2.13|^3.0", "predis/predis": "~1.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Lock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Creates and manages locks, a mechanism to provide exclusive access to a shared resource", "homepage": "https://symfony.com", "keywords": ["cas", "flock", "locking", "mutex", "redlock", "semaphore"], "support": {"source": "https://github.com/symfony/lock/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-24T09:45:49+00:00"}, {"name": "symfony/mailer", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "f6e927ec95c957131e6b2c78790e1a6d4c576447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/f6e927ec95c957131e6b2c78790e1a6d4c576447", "reference": "f6e927ec95c957131e6b2c78790e1a6d4c576447", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3", "php": ">=7.2.5", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2.6|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<4.4"}, "require-dev": {"symfony/http-client-contracts": "^1.1|^2|^3", "symfony/messenger": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-25T10:48:33+00:00"}, {"name": "symfony/messenger", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "4319c25b76573cff46f112ee8cc83fffa4b97579"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/4319c25b76573cff46f112ee8cc83fffa4b97579", "reference": "4319c25b76573cff46f112ee8cc83fffa4b97579", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/amqp-messenger": "^5.1|^6.0", "symfony/deprecation-contracts": "^2.1|^3", "symfony/doctrine-messenger": "^5.1|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/redis-messenger": "^5.1|^6.0"}, "conflict": {"symfony/event-dispatcher": "<4.4", "symfony/framework-bundle": "<4.4", "symfony/http-kernel": "<4.4", "symfony/serializer": "<5.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/serializer": "^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/validator": "^4.4|^5.0|^6.0"}, "suggest": {"enqueue/messenger-adapter": "For using the php-enqueue library as a transport."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-12T18:55:10+00:00"}, {"name": "symfony/mime", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "e1503cfb5c9a225350f549d3bb99296f4abfb80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/e1503cfb5c9a225350f549d3bb99296f4abfb80f", "reference": "e1503cfb5c9a225350f549d3bb99296f4abfb80f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.2|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/monolog-bridge", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "4b56e17c443e7092895477f047f2a70f324f984c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/4b56e17c443e7092895477f047f2a70f324f984c", "reference": "4b56e17c443e7092895477f047f2a70f324f984c", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1|^2", "php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-kernel": "^5.3|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/console": "<4.4", "symfony/http-foundation": "<5.3"}, "require-dev": {"symfony/console": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/mailer": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/security-core": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/e495f5c7e4e672ffef4357d4a4d85f010802f940", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^4.4 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-06T15:12:11+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "cc1147cb11af1b43f503ac18f31aa3bec213aba8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/cc1147cb11af1b43f503ac18f31aa3bec213aba8", "reference": "cc1147cb11af1b43f503ac18f31aa3bec213aba8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/password-hasher", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/password-hasher.git", "reference": "b5ed59c4536d8386cd37bb86df2b7bd5fbbd46d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/password-hasher/zipball/b5ed59c4536d8386cd37bb86df2b7bd5fbbd46d4", "reference": "b5ed59c4536d8386cd37bb86df2b7bd5fbbd46d4", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/security-core": "<5.3"}, "require-dev": {"symfony/console": "^5", "symfony/security-core": "^5.3|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PasswordHasher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides password hashing utilities", "homepage": "https://symfony.com", "keywords": ["hashing", "password"], "support": {"source": "https://github.com/symfony/password-hasher/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "30885182c981ab175d4d034db0f6f469898070ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/30885182c981ab175d4d034db0f6f469898070ab", "reference": "30885182c981ab175d4d034db0f6f469898070ab", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-10-20T20:35:02+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "81b86b50cf841a64252b439e738e97f4a34e2783"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/81b86b50cf841a64252b439e738e97f4a34e2783", "reference": "81b86b50cf841a64252b439e738e97f4a34e2783", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-23T21:10:46+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "749045c69efb97c70d25d7463abba812e91f3a44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/749045c69efb97c70d25d7463abba812e91f3a44", "reference": "749045c69efb97c70d25d7463abba812e91f3a44", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-14T14:02:44+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8590a5f561694770bdcd3f9b5c69dde6945028e8", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/0abb51d2f102e00a4eefcf46ba7fec406d245825", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-30T18:21:41+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/9a142215a36a3888e30d0a9eeea9766764e96976", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:17:38+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/cc5db0e22b3cb4111010e48785a97f670b350ca5", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-06-05T21:20:04+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/1100343ed1a92e3a38f9ae122fc0eb21602547be", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.23.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-07-28T13:41:28+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "e66119f3de95efc359483f810c4c3e6436279436"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/e66119f3de95efc359483f810c4c3e6436279436", "reference": "e66119f3de95efc359483f810c4c3e6436279436", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-21T13:25:03+00:00"}, {"name": "symfony/process", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "95440409896f90a5f85db07a32b517ecec17fa4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/95440409896f90a5f85db07a32b517ecec17fa4c", "reference": "95440409896f90a5f85db07a32b517ecec17fa4c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-30T18:16:22+00:00"}, {"name": "symfony/property-access", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "95534d912f61117d3bce2d4456419ee2ee548d7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/95534d912f61117d3bce2d4456419ee2ee548d7a", "reference": "95534d912f61117d3bce2d4456419ee2ee548d7a", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/property-info": "^5.2|^6.0"}, "require-dev": {"symfony/cache": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-04T18:39:09+00:00"}, {"name": "symfony/property-info", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "bcc2b6904cbcf16b2e5d618da16117cd8e132f9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/bcc2b6904cbcf16b2e5d618da16117cd8e132f9a", "reference": "bcc2b6904cbcf16b2e5d618da16117cd8e132f9a", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/string": "^5.1|^6.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/proxy-manager-bridge", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/proxy-manager-bridge.git", "reference": "efb82e176cd47426193ad047635ba5181dae089f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/proxy-manager-bridge/zipball/efb82e176cd47426193ad047635ba5181dae089f", "reference": "efb82e176cd47426193ad047635ba5181dae089f", "shasum": ""}, "require": {"friendsofphp/proxy-manager-lts": "^1.0.2", "php": ">=7.2.5", "symfony/dependency-injection": "^5.0|^6.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/config": "^4.4|^5.0|^6.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\ProxyManager\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for ProxyManager with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/proxy-manager-bridge/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "81db2d4ae86e9f0049828d9343a72b9523884e5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/81db2d4ae86e9f0049828d9343a72b9523884e5d", "reference": "81db2d4ae86e9f0049828d9343a72b9523884e5d", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1", "symfony/browser-kit": "^4.4 || ^5.0", "symfony/config": "^4.4 || ^5.0", "symfony/event-dispatcher": "^4.4 || ^5.0", "symfony/framework-bundle": "^4.4 || ^5.0", "symfony/http-kernel": "^4.4 || ^5.0", "symfony/phpunit-bridge": "^4.4.19 || ^5.2"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-17T10:35:25+00:00"}, {"name": "symfony/rate-limiter", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/rate-limiter.git", "reference": "8962f50180ae6df30b4a1a50c0005182a3303764"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/rate-limiter/zipball/8962f50180ae6df30b4a1a50c0005182a3303764", "reference": "8962f50180ae6df30b4a1a50c0005182a3303764", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/lock": "^5.2|^6.0", "symfony/options-resolver": "^5.1|^6.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\RateLimiter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a Token Bucket implementation to rate limit input and output in your application", "homepage": "https://symfony.com", "keywords": ["limiter", "rate-limiter"], "support": {"source": "https://github.com/symfony/rate-limiter/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-24T06:21:58+00:00"}, {"name": "symfony/redis-messenger", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/redis-messenger.git", "reference": "08234f89126043948b37cdbc870167c1c68689e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/redis-messenger/zipball/08234f89126043948b37cdbc870167c1c68689e1", "reference": "08234f89126043948b37cdbc870167c1c68689e1", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.1|^6.0"}, "require-dev": {"symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Redis\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Redis extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/redis-messenger/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-21T17:07:16+00:00"}, {"name": "symfony/routing", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "44b29c7a94e867ccde1da604792f11a469958981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/44b29c7a94e867ccde1da604792f11a469958981", "reference": "44b29c7a94e867ccde1da604792f11a469958981", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.3", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12", "psr/log": "^1|^2|^3", "symfony/config": "^5.3|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/security-core", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "11d815ccbff929899a4ec545f9f85185071abd12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/11d815ccbff929899a4ec545f9f85185071abd12", "reference": "11d815ccbff929899a4ec545f9f85185071abd12", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^1.1|^2|^3", "symfony/password-hasher": "^5.3|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1.6|^2|^3"}, "conflict": {"symfony/event-dispatcher": "<4.4", "symfony/http-foundation": "<5.3", "symfony/ldap": "<4.4", "symfony/security-guard": "<4.4", "symfony/validator": "<5.2"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.0|^2.0", "psr/log": "^1|^2|^3", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^5.3|^6.0", "symfony/ldap": "^4.4|^5.0|^6.0", "symfony/translation": "^4.4|^5.0|^6.0", "symfony/validator": "^5.2|^6.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-18T16:06:09+00:00"}, {"name": "symfony/serializer", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "e3ec068106e4cc7d17c45656bd8823788e7dc369"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/e3ec068106e4cc7d17c45656bd8823788e7dc369", "reference": "e3ec068106e4cc7d17c45656bd8823788e7dc369", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.3.13", "symfony/uid": "<5.3", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/form": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.3.13|^6.0", "symfony/uid": "^5.3|^6.0", "symfony/validator": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0", "symfony/var-exporter": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/mime": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/var-exporter": "For using the metadata compiler.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-24T09:30:07+00:00"}, {"name": "symfony/service-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d15da7ba4957ffb8f1747218be9e1a121fd298a1", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/stopwatch", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "4d04b5c24f3c9a1a168a131f6cbe297155bc0d30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/4d04b5c24f3c9a1a168a131f6cbe297155bc0d30", "reference": "4d04b5c24f3c9a1a168a131f6cbe297155bc0d30", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/service-contracts": "^1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-18T16:06:09+00:00"}, {"name": "symfony/string", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "92043b7d8383e48104e411bc9434b260dbeb5a10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/92043b7d8383e48104e411bc9434b260dbeb5a10", "reference": "92043b7d8383e48104e411bc9434b260dbeb5a10", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/translation", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "7e4d52d39e5d86f3f04bef46fa29a1091786bc73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/7e4d52d39e5d86f3f04bef46fa29a1091786bc73", "reference": "7e4d52d39e5d86f3f04bef46fa29a1091786bc73", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-09T15:49:12+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/e2eaa60b558f26a4b0354e1bbb25636efaaad105", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-28T13:05:58+00:00"}, {"name": "symfony/twig-bridge", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "648c8694a9470ae4aaf64cbce1b640f5941fd7c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/648c8694a9470ae4aaf64cbce1b640f5941fd7c9", "reference": "648c8694a9470ae4aaf64cbce1b640f5941fd7c9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1|^2|^3", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<5.3", "symfony/form": "<5.3", "symfony/http-foundation": "<5.3", "symfony/http-kernel": "<4.4", "symfony/translation": "<5.2", "symfony/workflow": "<5.2"}, "require-dev": {"doctrine/annotations": "^1.12", "egulias/email-validator": "^2.1.10|^3", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^4.4|^5.0|^6.0", "symfony/console": "^5.3|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/form": "^5.3|^6.0", "symfony/http-foundation": "^5.3|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2|^6.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^4.4|^5.0|^6.0", "symfony/security-csrf": "^4.4|^5.0|^6.0", "symfony/security-http": "^4.4|^5.0|^6.0", "symfony/serializer": "^5.2|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/translation": "^5.2|^6.0", "symfony/web-link": "^4.4|^5.0|^6.0", "symfony/workflow": "^5.2|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security-core": "For using the SecurityExtension", "symfony/security-csrf": "For using the CsrfExtension", "symfony/security-http": "For using the LogoutUrlExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-09T08:59:58+00:00"}, {"name": "symfony/twig-bundle", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "45ae3ee8155f93042a1033b166a7a3ed57b96a92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/45ae3ee8155f93042a1033b166a7a3ed57b96a92", "reference": "45ae3ee8155f93042a1033b166a7a3ed57b96a92", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/config": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/twig-bridge": "^5.3|^6.0", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"symfony/dependency-injection": "<5.3", "symfony/framework-bundle": "<5.0", "symfony/service-contracts": ">=3.0", "symfony/translation": "<5.0"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.0|^2.0", "symfony/asset": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/form": "^4.4|^5.0|^6.0", "symfony/framework-bundle": "^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/translation": "^5.0|^6.0", "symfony/web-link": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/validator", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "06c3d849fa19a0ece7b6eed240036772c4b5faaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/06c3d849fa19a0ece7b6eed240036772c4b5faaf", "reference": "06c3d849fa19a0ece7b6eed240036772c4b5faaf", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/translation-contracts": "^1.1|^2|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/cache": "<1.11", "doctrine/lexer": "<1.1", "phpunit/phpunit": "<5.4.3", "symfony/dependency-injection": "<4.4", "symfony/expression-language": "<5.1", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.4", "symfony/property-info": "<5.3", "symfony/translation": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.13", "doctrine/cache": "^1.11|^2.0", "egulias/email-validator": "^2.1.10|^3", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^5.1|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/property-info": "^5.3|^6.0", "symfony/translation": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator and the ExpressionLanguageSyntax constraints", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-25T08:05:40+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.5", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "6efddb1cf6af5270b21c48c6103e81f920c220f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/6efddb1cf6af5270b21c48c6103e81f920c220f0", "reference": "6efddb1cf6af5270b21c48c6103e81f920c220f0", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-21T15:00:19+00:00"}, {"name": "symfony/var-exporter", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "b199936b7365be36663532e547812d3abb10234a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/b199936b7365be36663532e547812d3abb10234a", "reference": "b199936b7365be36663532e547812d3abb10234a", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/yaml", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "e80f87d2c9495966768310fc531b487ce64237a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/e80f87d2c9495966768310fc531b487ce64237a2", "reference": "e80f87d2c9495966768310fc531b487ce64237a2", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-26T16:32:32+00:00"}, {"name": "true/punycode", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/true/php-punycode.git", "reference": "a4d0c11a36dd7f4e7cd7096076cab6d3378a071e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/true/php-punycode/zipball/a4d0c11a36dd7f4e7cd7096076cab6d3378a071e", "reference": "a4d0c11a36dd7f4e7cd7096076cab6d3378a071e", "shasum": ""}, "require": {"php": ">=5.3.0", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpunit/phpunit": "~4.7", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "autoload": {"psr-4": {"TrueBV\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>an <PERSON>", "email": "<EMAIL>"}], "description": "A Bootstring encoding of Unicode for Internationalized Domain Names in Applications (IDNA)", "homepage": "https://github.com/true/php-punycode", "keywords": ["idna", "punycode"], "support": {"issues": "https://github.com/true/php-punycode/issues", "source": "https://github.com/true/php-punycode/tree/master"}, "time": "2016-11-16T10:37:54+00:00"}, {"name": "twig/intl-extra", "version": "v3.3.5", "source": {"type": "git", "url": "https://github.com/twigphp/intl-extra.git", "reference": "8dca6f4c5a00cdd3c43b6bd080f50d32aca33a84"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/intl-extra/zipball/8dca6f4c5a00cdd3c43b6bd080f50d32aca33a84", "reference": "8dca6f4c5a00cdd3c43b6bd080f50d32aca33a84", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/intl": "^4.4|^5.0|^6.0", "twig/twig": "^2.7|^3.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Twig\\Extra\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Twig extension for Intl", "homepage": "https://twig.symfony.com", "keywords": ["intl", "twig"], "support": {"source": "https://github.com/twigphp/intl-extra/tree/v3.3.5"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-01-02T10:02:25+00:00"}, {"name": "twig/string-extra", "version": "v3.3.5", "source": {"type": "git", "url": "https://github.com/twigphp/string-extra.git", "reference": "03608ae2e9c270a961e8cf1b75751e8635ad3e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/string-extra/zipball/03608ae2e9c270a961e8cf1b75751e8635ad3e3c", "reference": "03608ae2e9c270a961e8cf1b75751e8635ad3e3c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/string": "^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2|^3", "twig/twig": "^2.7|^3.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Twig\\Extra\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Twig extension for Symfony String", "homepage": "https://twig.symfony.com", "keywords": ["html", "string", "twig", "unicode"], "support": {"source": "https://github.com/twigphp/string-extra/tree/v3.3.5"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-01-02T10:02:25+00:00"}, {"name": "twig/twig", "version": "v3.3.8", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "972d8604a92b7054828b539f2febb0211dd5945c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/972d8604a92b7054828b539f2febb0211dd5945c", "reference": "972d8604a92b7054828b539f2febb0211dd5945c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.3.8"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-02-04T06:59:48+00:00"}, {"name": "zircote/swagger-php", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "68c76ce2bb43fb4603315fb973d4595711dcbfd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/68c76ce2bb43fb4603315fb973d4595711dcbfd3", "reference": "68c76ce2bb43fb4603315fb973d4595711dcbfd3", "shasum": ""}, "require": {"doctrine/annotations": "^1.7", "ext-json": "*", "php": ">=7.2", "psr/log": "^1.1", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "*********", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8"}, "bin": ["bin/openapi"], "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bfanger.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://radebatz.net"}], "description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.2"}, "time": "2021-11-15T20:45:42+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.0.0"}