<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Core\Framework\DataAbstractionLayer\Write;

use A<PERSON>ris\ProductDisplayImage\Components\TruncateProductImageLanguageService;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Write\EntityExistence;
use Shopware\Core\Framework\DataAbstractionLayer\Write\WriteParameterBag;
use Shopware\Core\Framework\DataAbstractionLayer\Write\WriteCommandExtractor as ParentClass;

class WriteCommandExtractor extends ParentClass
{
    public function __construct(
        private readonly ParentClass $parent,
        private readonly TruncateProductImageLanguageService $truncateService
    ) {
    }

    public function normalize(EntityDefinition $definition, array $rawData, WriteParameterBag $parameters): array
    {
        return $this->parent->normalize($definition, $rawData, $parameters);
    }

    public function normalizeSingle(EntityDefinition $definition, array $data, WriteParameterBag $parameters): array
    {
        return $this->parent->normalizeSingle($definition, $data, $parameters);
    }

    public function extract(array $rawData, WriteParameterBag $parameters): array
    {
        if (!$this->truncateService->allowTruncate()) return $this->parent->extract($rawData,$parameters);

        $definition = $parameters->getDefinition();

        if ($definition->getEntityName() !== 'product' || empty($rawData) || !array_key_exists('media', $rawData) || empty($rawData['media']) || !is_array($rawData['media'])) {
            return $this->parent->extract($rawData, $parameters);
        }

        foreach ($rawData['media'] as $key => $media) {
            if (empty($media) || !is_array($media) || !array_key_exists('acrisProductDisplayImage', $media) || empty($media['acrisProductDisplayImage'])
                || !is_array($media['acrisProductDisplayImage']) || !array_key_exists('productMediaId', $media['acrisProductDisplayImage'])
                || empty($media['acrisProductDisplayImage']['productMediaId'])) {
                continue;
            }

            $rawData = $this->truncateService->truncateDisplayImageLanguages($media['acrisProductDisplayImage']['productMediaId'], $key, $rawData);
        }

        return $this->parent->extract($rawData,$parameters);
    }

    public function extractJsonUpdate($data, EntityExistence $existence, WriteParameterBag $parameters): void
    {
        $this->parent->extractJsonUpdate($data, $existence, $parameters);
    }
}
