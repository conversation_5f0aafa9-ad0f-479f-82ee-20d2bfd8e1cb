<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Core\Content\Media\Cms\Type;

use Acris\ProductDisplayImage\Components\ProductDisplayImageService;
use Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotEntity;
use Shopware\Core\Content\Cms\DataResolver\Element\CmsElementResolverInterface;
use Shopware\Core\Content\Cms\DataResolver\Element\ElementDataCollection;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\EntityResolverContext;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\ResolverContext;
use Shopware\Core\Content\Cms\SalesChannel\Struct\ImageSliderItemStruct;
use Shopware\Core\Content\Media\Cms\Type\ImageGalleryTypeDataResolver as ParentClass;
use Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaCollection;
use Shopware\Core\Content\Product\ProductEntity;

class ImageGalleryTypeDataResolver extends ParentClass implements CmsElementResolverInterface
{
    private CmsElementResolverInterface $parent;

    private ProductDisplayImageService $productDisplayImageService;

    public function __construct(
        CmsElementResolverInterface $parent,
        ProductDisplayImageService $productDisplayImageService
    )
    {
        $this->parent = $parent;
        $this->productDisplayImageService = $productDisplayImageService;
    }

    public function enrich(CmsSlotEntity $slot, ResolverContext $resolverContext, ElementDataCollection $result): void
    {
        $this->parent->enrich($slot, $resolverContext, $result);

        $imageSlider = $slot->getData();
        if (empty($imageSlider)) return;

        $sliderItems = $imageSlider->getSliderItems();
        $config = $slot->getFieldConfig();
        $sliderItemsConfig = $config->get('sliderItems');

        if (empty($sliderItems) || empty($config) || empty($sliderItemsConfig)) return;

        if (!is_array($sliderItems) || \count($sliderItems) === 0) return;

        if ($sliderItemsConfig->isMapped() && $resolverContext instanceof EntityResolverContext) {
            $sliderItems = $this->resolveEntityValue($resolverContext->getEntity(), $sliderItemsConfig->getStringValue());

            if ($sliderItems === null || \count($sliderItems) < 1 || !$sliderItems instanceof ProductMediaCollection) {
                return;
            }

            $imageSlider->setSliderItems([]);
            $this->sortItemsByPosition($sliderItems);

            $coverId = null;

            if ($sliderItemsConfig->getStringValue() === 'product.media') {
                /** @var ProductEntity $productEntity */
                $productEntity = $resolverContext->getEntity();

                $coverId = $productEntity->getCoverId();
            }

            $productMediaCollection = $this->productDisplayImageService->checkLanguageForGaleryImage($sliderItems, $coverId, $resolverContext->getSalesChannelContext()->getContext()->getLanguageId(), $resolverContext->getSalesChannelContext());
            if ($productMediaCollection->count() === 0) return;

            foreach ($productMediaCollection->getMedia() as $media) {
                $imageSliderItem = new ImageSliderItemStruct();
                $imageSliderItem->setMedia($media);
                $imageSlider->addSliderItem($imageSliderItem);
            }
        }
    }
}
