<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Core\Content\Product\Cart;

use Acris\ProductDisplayImage\Components\ProductDisplayImageService;
use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\CartBehavior;
use Shopware\Core\Checkout\Cart\CartDataCollectorInterface;
use Shopware\Core\Checkout\Cart\CartProcessorInterface;
use Shopware\Core\Checkout\Cart\LineItem\CartDataCollection;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\Content\Product\Cart\ProductCartProcessor as ParentClass;

class ProductCartProcessor extends ParentClass implements CartProcessorInterface, CartDataCollectorInterface
{
    private ProductDisplayImageService $productDisplayImageService;

    private ParentClass $parent;

    public function __construct(
        ProductDisplayImageService $productDisplayImageService,
        ParentClass $parent
    ) {
        $this->productDisplayImageService = $productDisplayImageService;
        $this->parent = $parent;
    }

    public function collect(
        CartDataCollection $data,
        Cart $original,
        SalesChannelContext $context,
        CartBehavior $behavior
    ): void {
        $this->productDisplayImageService->checkLanguageForProductImageInCart($data, $original, $context);
        $this->parent->collect($data, $original, $context, $behavior);
    }

    public function process(
        CartDataCollection $data,
        Cart $original,
        Cart $toCalculate,
        SalesChannelContext $context,
        CartBehavior $behavior
    ): void {
        $this->parent->process($data, $original, $toCalculate, $context, $behavior);
    }
}
