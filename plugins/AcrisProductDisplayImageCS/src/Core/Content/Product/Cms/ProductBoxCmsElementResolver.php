<?php
declare(strict_types=1);

namespace Acris\ProductDisplayImage\Core\Content\Product\Cms;

use Acris\ProductDisplayImage\Components\ProductDisplayImageService;
use Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotEntity;
use Shopware\Core\Content\Cms\DataResolver\Element\CmsElementResolverInterface;
use Shopware\Core\Content\Cms\DataResolver\Element\ElementDataCollection;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\ResolverContext;
use Shopware\Core\Content\Cms\DataResolver\CriteriaCollection;
use Shopware\Core\Content\Product\Cms\ProductBoxCmsElementResolver as ParentClass;

class ProductBoxCmsElementResolver extends ParentClass implements CmsElementResolverInterface
{
    private CmsElementResolverInterface $parent;

    private ProductDisplayImageService $productDisplayImageService;

    public function __construct(
        CmsElementResolverInterface $parent,
        ProductDisplayImageService $productDisplayImageService
    )
    {
        $this->parent = $parent;
        $this->productDisplayImageService = $productDisplayImageService;
    }

    public function collect(CmsSlotEntity $slot, ResolverContext $resolverContext): ?CriteriaCollection
    {
        $criteriaCollection = $this->parent->collect($slot, $resolverContext);

        if(empty($criteriaCollection)) {
            return $criteriaCollection;
        }

        foreach ($criteriaCollection->all() as $criterias) {
            if(is_array($criterias)) {
                foreach ($criterias as $criteria) {
                    $this->productDisplayImageService->addProductImageAssociationCriteria($criteria);
                }
            }
        }

        return $criteriaCollection;
    }

    public function enrich(CmsSlotEntity $slot, ResolverContext $resolverContext, ElementDataCollection $result): void
    {
        $this->parent->enrich($slot, $resolverContext, $result);

        $product = $slot->getData()->getProduct();
        if (empty($product)) return;

        $this->productDisplayImageService->checkLanguageForProductImage($product, $resolverContext->getSalesChannelContext()->getContext()->getLanguageId(), $resolverContext->getSalesChannelContext());
    }
}
