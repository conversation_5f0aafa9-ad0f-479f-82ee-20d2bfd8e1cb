<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Core\Content\Product\SalesChannel\Listing;

use Acris\ProductDisplayImage\Components\ProductDisplayImageService;
use Shopware\Core\Content\Product\SalesChannel\SalesChannelProductEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingLoader as ParentClass;

class ProductListingLoader extends ParentClass
{
    private ParentClass $parent;

    private ProductDisplayImageService $productDisplayImageService;

    public function __construct(
        ParentClass $parent,
        ProductDisplayImageService $productDisplayImageService
    ) {
        $this->parent = $parent;
        $this->productDisplayImageService = $productDisplayImageService;
    }

    public function load(Criteria $origin, SalesChannelContext $salesChannelContext): EntitySearchResult
    {
        $this->productDisplayImageService->addProductImageAssociationCriteria($origin);
        $entitySearchResult = $this->parent->load($origin, $salesChannelContext);

        /** @var SalesChannelProductEntity $product */
        foreach ($entitySearchResult->getElements() as $product) {
            $this->productDisplayImageService->checkLanguageForProductImage($product, $salesChannelContext->getContext()->getLanguageId(), $salesChannelContext);
        }

        return $entitySearchResult;
    }
}
