<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Shopware\Core\Framework\Plugin\Context\UpdateContext;

class AcrisProductDisplayImageCS extends Plugin
{
    public function uninstall(UninstallContext $context): void
    {
        if ($context->keepUserData()) {
            return;
        }
        $this->cleanupDatabase();
    }

    public function update(UpdateContext $updateContext): void
    {
        if((version_compare($updateContext->getCurrentPluginVersion(), '1.3.1', '<') && version_compare($updateContext->getUpdatePluginVersion(), '1.3.1', '>='))) {
            $connection = $this->container->get(Connection::class);
            try {
                $connection->executeStatement("UPDATE acris_product_display_image, acris_product_display_image_language SET acris_product_display_image.language_ids = (
                SELECT CONCAT('[', GROUP_CONCAT(JSON_QUOTE(LOWER(HEX(acris_product_display_image_language.language_id)))), ']')
                    FROM acris_product_display_image_language
                        WHERE acris_product_display_image_language.display_image_id = acris_product_display_image.id)
                        WHERE acris_product_display_image_language.display_image_id = acris_product_display_image.id;");
            } catch (\Throwable $e) {}
        }
    }

    private function cleanupDatabase(): void
    {
        $connection = $this->container->get(Connection::class);
        $connection->executeStatement('DROP TABLE IF EXISTS acris_product_display_image_language');
        $connection->executeStatement('DROP TABLE IF EXISTS acris_product_display_image');

        $this->removeInheritance($connection, 'product_media', 'acrisProductDisplayImage');
        $this->removeInheritance($connection, 'language', 'acrisProductDisplayImages');
    }

    private function removeInheritance(Connection $connection, string $entity, string $propertyName): void
    {
        $sql = str_replace(
            ['#table#', '#column#'],
            [$entity, $propertyName],
            'ALTER TABLE `#table#` DROP `#column#`'
        );

        $connection->executeStatement($sql);
    }
}
