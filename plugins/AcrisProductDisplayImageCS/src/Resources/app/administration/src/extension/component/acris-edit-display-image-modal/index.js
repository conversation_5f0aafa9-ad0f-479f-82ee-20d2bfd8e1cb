import template from './acris-edit-display-image-modal.html.twig';
import './acris-edit-display-image-modal.scss';

const { Component } = Shopware;
const { Criteria } = Shopware.Data;
const { cloneDeep } = Shopware.Utils.object;
const { isArray } = Shopware.Utils.types;

Component.register('acris-edit-display-image-modal', {
    template,

    inject: ['repositoryFactory'],

    props: {
        displayImageItem: {
            type: Object,
            required: true
        },

        productMedia: {
            type: Object,
            required: true
        },

        product: {
            type: Object,
            required: true
        }
    },

    data() {
        return {
            isLoading: false,
            isStandardCoverSet: false,
            displayImageConfiguration: null,
            preConfiguredCover: [],
            configureCoverForLanguages: []
        }
    },

    computed: {
        isStandardCover() {
            const coverId = this.product.cover ? this.product.cover.id : this.product.coverId;

            if (this.product.media.length === 0 || this.productMedia.isPlaceholder || !coverId) {
                return false;
            }

            return this.productMedia.id === coverId;
        },

        displayImageRepository() {
            return this.repositoryFactory.create('acris_product_display_image');
        },

        languageCriteria() {
            const criteria = new Criteria();
            criteria.addAssociation('locale');

            return criteria;
        },
    },

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            if (this.displayImageItem._isNew) {
                this.isLoading = true;
                const criteria = new Criteria();
                criteria.addFilter(Criteria.equals('productMediaId', this.productMedia.id));
                this.displayImageRepository.search(criteria, Shopware.Context.api).then((res) => {
                    if (res.length <= 0) {
                        return this.displayImageRepository.save(this.displayImageItem).then(() => {
                            this.displayImageRepository.get(
                                this.displayImageItem.id,
                                Shopware.Context.api
                            ).then((displayImage) => {
                                this.isLoading = false;
                                this.displayImageItem = displayImage;
                                this.displayImageConfiguration = cloneDeep(this.displayImageItem);
                                this.displayImageConfiguration.languages = this.displayImageItem.languages;
                                this.checkPreConfiguredCover();
                                this.isStandardCoverSet = this.isStandardCover;
                            }).catch(() => {
                                this.isLoading = false;
                            });
                        }).catch(() => {
                            this.isLoading = false;
                        });
                    } else {
                        this.isLoading = false;
                        this.displayImageItem = res.first();
                        this.displayImageConfiguration = cloneDeep(this.displayImageItem);
                        this.displayImageConfiguration.languages = this.displayImageItem.languages;
                        this.checkPreConfiguredCover();
                        this.isStandardCoverSet = this.isStandardCover;
                    }
                });
            } else {
                this.displayImageConfiguration = cloneDeep(this.displayImageItem);
                this.displayImageConfiguration.languages = this.displayImageItem.languages;
                this.checkPreConfiguredCover();
                this.isStandardCoverSet = this.isStandardCover;
            }
        },

        checkPreConfiguredCover() {
            if (!this.displayImageConfiguration.languages || this.displayImageConfiguration.languages.length === 0) {
                this.displayImageConfiguration.useImageAsCoverForLanguage = [];
            }

            if (this.displayImageConfiguration.useImageAsCoverForLanguage && this.displayImageConfiguration.useImageAsCoverForLanguage.length > 0) {
                this.displayImageConfiguration.useImageAsCoverForLanguage.forEach((cover) => {
                    if (cover.value === true) {
                        this.preConfiguredCover.push({languageId: cover.languageId, value: cover.value})
                    }
                });
            }
        },

        onCancel() {
            this.configureCoverForLanguages = [];
            this.preConfiguredCover = [];
            this.displayImageConfiguration = null;
            this.$emit('modal-close');
        },

        onApply() {
            Object.assign(this.displayImageItem, this.displayImageConfiguration);

            if (this.isStandardCoverSet) {
                this.markMediaAsCover(this.productMedia);
            }

            if (this.configureCoverForLanguages && this.configureCoverForLanguages.length > 0) {
                this.markMediaAsConfiguredCover();
            }

            Shopware.State.commit('acrisProductDisplayImageState/addNewEntity', this.displayImageItem);
            this.$emit('modal-save', this.displayImageItem);
        },

        markMediaAsConfiguredCover() {
            if (this.product && this.product.media) {
                this.product.media.forEach((productMedia) => {
                    if (productMedia.id !== this.productMedia.id && productMedia.extensions && productMedia.extensions.acrisProductDisplayImage && productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage && isArray(productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage) && productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.length > 0) {
                        productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.forEach((useImageAsCoverForLanguage) => {
                            if (this.configureCoverForLanguages.find(configuredImageAsCoverForLanguage => configuredImageAsCoverForLanguage.languageId === useImageAsCoverForLanguage.languageId)) {
                                useImageAsCoverForLanguage.value = false;
                            }
                        });
                    }
                });
            }
        },

        useForCoverLabel(language) {
            return this.$tc(
                'acris-product-display-image.modal.fieldLabelUseForCover', 0, {language: language.name}
            );
        },

        useForCoverHelpText(language) {
            return this.$tc(
                'acris-product-display-image.modal.fieldHelpTextUseForCover', 0, {language: language.name}
            );
        },

        async onCoverChange(value, index) {
            this.displayImageConfiguration.useImageAsCoverForLanguage[index].value = value
            if (value === true) {
                if (this.preConfiguredCover.length === 0 || !this.preConfiguredCover.find(preConfigured => preConfigured.languageId === this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId) || !this.configureCoverForLanguages.find(preConfigured => preConfigured.languageId === this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId)) {
                    this.configureCoverForLanguages.push({languageId: this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId});
                }
            }
        },

        async onStandardCoverChange(value) {
            this.isStandardCoverSet = value;
        },

        markMediaAsCover(productMedia) {
            this.product.cover = productMedia;
            this.product.coverId = productMedia.id;
        },

        async onAssignLanguagesChange(entityCollection) {
            this.displayImageConfiguration.languages = entityCollection;

            if (!this.displayImageConfiguration.useImageAsCoverForLanguage || !isArray(this.displayImageConfiguration.useImageAsCoverForLanguage)) {
                this.displayImageConfiguration.useImageAsCoverForLanguage = [];
            }

            const codes = [];

            if (entityCollection && entityCollection.length > 0) {
                entityCollection.forEach((entity) => {
                    if (this.displayImageConfiguration.useImageAsCoverForLanguage.length === 0 || !this.displayImageConfiguration.useImageAsCoverForLanguage.find(o => o.languageId === entity.id)) {
                        this.displayImageConfiguration.useImageAsCoverForLanguage.push({languageId: entity.id, name: entity.name, value: false});
                    }

                    if (entity.locale) {
                        const code = entity.locale.code.split('-')[1].toLowerCase();
                        codes.push(code);
                    }
                });
            }

            Shopware.State.commit('acrisProductDisplayImageState/addConfigurationItem', {mediaId: this.productMedia.mediaId, codes: codes});

            if (this.displayImageConfiguration.useImageAsCoverForLanguage.length > 0) {
                this.displayImageConfiguration.useImageAsCoverForLanguage.forEach((fallbackCover, index) => {
                    if (!entityCollection.find(o => o.id === fallbackCover.languageId)) {
                        this.displayImageConfiguration.useImageAsCoverForLanguage.splice(index, 1);
                    }
                });
            }
        }
    }
});
