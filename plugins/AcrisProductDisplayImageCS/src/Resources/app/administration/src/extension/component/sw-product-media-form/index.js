import template from './sw-product-media-form.html.twig';

const { Component } = Shopware;

Component.override('sw-product-media-form', {
    template,

    computed: {
        mediaItems() {
            const mediaItems = this.$super('mediaItems');
            const configuredImages = [];
            const configuredISO = [];

            if (this.newEntities) {
                this.newEntities.forEach((entity) => {
                    mediaItems.forEach((productMedia) => {
                        if (productMedia.id === entity.productMediaId) {
                            productMedia.extensions.acrisProductDisplayImage = entity;
                        }
                    });
                });
            }

            mediaItems.forEach((productMedia) => {
                const codes = [];
                if (productMedia.extensions && productMedia.extensions.acrisProductDisplayImage && productMedia.extensions.acrisProductDisplayImage.languages && productMedia.extensions.acrisProductDisplayImage.languages.length > 0) {
                    configuredImages.push(productMedia.mediaId);

                    productMedia.extensions.acrisProductDisplayImage.languages.forEach((language) => {
                        if (language.locale) {
                            const code = language.locale.code.split('-')[1].toLowerCase();
                            codes.push(code);
                        }
                    });

                    configuredISO.push({mediaId: productMedia.mediaId, codes: codes});
                }
            });

            Shopware.State.commit('acrisProductDisplayImageState/setConfiguredItems', configuredImages);
            Shopware.State.commit('acrisProductDisplayImageState/setConfiguredISO', configuredISO);

            return mediaItems;
        },

        newEntities() {
            return Shopware.State.get('acrisProductDisplayImageState').newEntities;
        },

        isConfigured() {
            return Shopware.State.get('acrisProductDisplayImageState').configuredImages.length > 0;
        },

        product() {
            const state = Shopware.State.get('swProductDetail');

            if (this.isInherited) {
                return state.parentProduct;
            }

            return state.product;
        },

        isEditMode() {
            return Shopware.State.get('acrisProductDisplayImageState').isEditMode;
        },

        displayImageMediaId() {
            return Shopware.State.get('acrisProductDisplayImageState').mediaId;
        },

        displayImageRepository() {
            return this.repositoryFactory.create('acris_product_display_image');
        },

        displayImageMedia() {
            let displayImageMedia = this.mediaItems.find(o => o.mediaId === this.displayImageMediaId);

            if (!displayImageMedia.extensions.acrisProductDisplayImage || !displayImageMedia.extensions.acrisProductDisplayImage.id) {
                displayImageMedia.extensions.acrisProductDisplayImage = this.displayImageRepository.create(Shopware.Context.api);
                displayImageMedia.extensions.acrisProductDisplayImage.productMediaId = displayImageMedia.id;
            }

            return displayImageMedia;
        }
    },

    methods: {
        isCover() {
            return false;
        },

        onEditProductDisplayImageSave() {
            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', false);
            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', null);
        },

        onEditProductDisplayImageClose() {
            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', false);
            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', null);
        }
    }
});
