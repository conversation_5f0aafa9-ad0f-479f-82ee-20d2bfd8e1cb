import template from './sw-product-image.html.twig';
import './sw-product-image.scss';

const { Component } = Shopware;

Component.override('sw-product-image', {
    template,

    created() {
        this.createdComponent();
    },

    computed: {
        isConfigured() {
            return Shopware.State.get('acrisProductDisplayImageState').configuredImages.length > 0 && Shopware.State.get('acrisProductDisplayImageState').configuredImages.includes(this.mediaId) && Shopware.State.get('acrisProductDisplayImageState').configuredISO.length > 0 && this.getCodesById(Shopware.State.get('acrisProductDisplayImageState').configuredISO, this.mediaId) && this.getCodesById(Shopware.State.get('acrisProductDisplayImageState').configuredISO, this.mediaId).length > 0;
        },

        ISOCodes () {
            const configuredISO = Shopware.State.get('acrisProductDisplayImageState').configuredISO;
            return this.getCodesById(configuredISO, this.mediaId);
        }
    },

    methods: {
        createdComponent() {
            this.isCover = false;
        },

        getCodesById(mediaArray, id) {
            const item = mediaArray.find(entry => entry.mediaId === id);
            return item ? item.codes : []; // Return codes if found, otherwise an empty array
        },


        onProductMediaEdit(){
            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', true);
            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', this.mediaId);
        },

        getCountryFlag(code) {
            return "https://flagcdn.com/w20/{{ countryCode }}.png".replace('{{ countryCode }}', code);
        }
    }
});
