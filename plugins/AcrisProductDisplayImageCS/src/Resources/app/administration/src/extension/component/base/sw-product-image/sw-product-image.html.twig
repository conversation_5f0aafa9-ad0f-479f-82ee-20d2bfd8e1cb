{% block sw_product_image_context_cover_action %}{% endblock %}

{% block sw_product_image_context_delete_action %}
    <sw-context-menu-item
        @click="onProductMediaEdit"
    >
        {{ $tc('acris-product-display-image.product-media.buttonEdit') }}
    </sw-context-menu-item>
    {% parent %}
{% endblock %}

{% block sw_product_image_preview %}
    {% block acris_product_display_image_langauge_flags %}
        <div v-if="isConfigured" class="acris-product-display-image-flag-container">
            <span v-for="code in ISOCodes" class="acris-product-display-image-flag">
                <img :src="getCountryFlag(code)">
            </span>
        </div>
    {% endblock %}

    {% parent %}
{% endblock %}
