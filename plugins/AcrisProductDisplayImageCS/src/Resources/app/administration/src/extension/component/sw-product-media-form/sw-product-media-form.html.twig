{% block sw_product_media_form_grid %}
    {% block acris_product_display_image_form_grid_configured_info %}
        <div v-if="isConfigured">
            <sw-alert class="acris-product-display-image-grid__configured-info" variant="info">
                {{ $tc('acris-product-display-image.product-media.configuredInfo') }}
            </sw-alert>
        </div>
    {% endblock %}

    {% parent %}

    {% block acris_product_display_image_form_edit_modal %}
        <acris-edit-display-image-modal v-if="isEditMode"
                                   :displayImageItem="displayImageMedia.extensions.acrisProductDisplayImage"
                                   :productMedia="displayImageMedia"
                                   :product="product"
                                   @modal-save="onEditProductDisplayImageSave"
                                   @modal-close="onEditProductDisplayImageClose">
        </acris-edit-display-image-modal>
    {% endblock %}
{% endblock %}
