{"acris-product-display-image": {"product-media": {"buttonEdit": "Configure image/cover", "configuredBadge": "Configured", "configuredInfo": "There are some images configured for languages, please check the configured images to see which image is set as cover for which language."}, "modal": {"title": "Display image for assigned languages", "languageLabelField": "Image should be available in the following languages", "languagePlaceholderField": "Select languages...", "languageHelpTextField": "If no language is selected, the product image is automatically available in all languages.", "fieldLabelUseForCover": "Use this image as cover for \"{language}\"", "fieldHelpTextUseForCover": "This image will be used as cover instead of standard cover, when language \"{language}\" is active in Storefront.", "useAsStandardCoverLabelField": "Use this image as standard cover", "useAsStandardCoverHelpTextField": "The standard cover will be used as product cover only if there is no other image set as cover for active language in Storefront."}}, "sw-product": {"mediaForm": {"coverSubline": "Standard Cover"}}}