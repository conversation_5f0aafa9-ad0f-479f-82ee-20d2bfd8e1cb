const { Component } = Shopware;

Component.override('sw-product-detail', {
    computed: {
        productCriteria() {
            const criteria = this.$super('productCriteria');
            criteria.addAssociation('media.acrisProductDisplayImage.languages.locale');
            criteria.addAssociation('cover.acrisProductDisplayImage.languages.locale');
            return criteria;
        },

        newEntities() {
            return Shopware.State.get('acrisProductDisplayImageState').newEntities;
        }
    },

    methods: {
        onSave() {
            if (this.product && this.newEntities) {
                this.newEntities.forEach((entity) => {
                    this.product.media.forEach((productMedia) => {
                        if (productMedia.id === entity.productMediaId) {
                            productMedia.extensions.acrisProductDisplayImage = entity;
                        }
                    });

                    if (this.product.cover && this.product.cover.id === entity.productMediaId) {
                        this.product.cover.extensions.acrisProductDisplayImage = entity;
                    }
                });
                Shopware.State.commit('acrisProductDisplayImageState/resetNewEntities');
            }
            this.$super('onSave');
        }
    }
});
