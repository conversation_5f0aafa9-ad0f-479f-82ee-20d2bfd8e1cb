{% block acris_product_display_image_modal %}
    <sw-modal class="acris-product-display-image-modal"
              v-if="displayImageConfiguration && !isLoading"
              :title="$tc('acris-product-display-image.modal.title')"
              @modal-close="onCancel">

        {% block acris_product_display_image_modal_form %}
            {% block acris_product_display_image_field_use_as_default_cover %}
                <sw-field type="switch"
                          @change="onStandardCoverChange"
                          :disabled="isStandardCover"
                          :value="isStandardCoverSet"
                          class="acris-product-display-image-use-for-standard-cover-field"
                          :label="$tc('acris-product-display-image.modal.useAsStandardCoverLabelField')"
                          :helpText="$tc('acris-product-display-image.modal.useAsStandardCoverHelpTextField')">
                </sw-field>
            {% endblock %}

            {% block acris_product_display_image_modal_form_language %}
                <sw-entity-many-to-many-select
                    :label="$tc('acris-product-display-image.modal.languageLabelField')"
                    :placeholder="$tc('acris-product-display-image.modal.languagePlaceholderField')"
                    :helpText="$tc('acris-product-display-image.modal.languageHelpTextField')"
                    :localMode="true"
                    :criteria="languageCriteria"
                    @change="onAssignLanguagesChange"
                    v-model="displayImageConfiguration.languages">
                </sw-entity-many-to-many-select>
            {% endblock %}

            {% block acris_product_display_image_field_use_cover %}
                <sw-field v-if="displayImageConfiguration.useImageAsCoverForLanguage.length > 0"
                          v-for="(useCoverForLanguage, index) in displayImageConfiguration.useImageAsCoverForLanguage"
                          type="switch"
                          @change="onCoverChange($event, index)"
                          :value="useCoverForLanguage.value"
                          class="acris-product-display-image-use-for-cover-field"
                          :label="useForCoverLabel(useCoverForLanguage)"
                          :helpText="useForCoverHelpText(useCoverForLanguage)">
                </sw-field>
            {% endblock %}
        {% endblock %}

        {% block acris_product_display_image_modal_footer %}
            <template slot="modal-footer">

                {% block acris_product_display_image_modal_footer_button_cancel %}
                    <sw-button size="small" @click="onCancel">
                        {{ $tc('global.default.cancel') }}
                    </sw-button>
                {% endblock %}

                {% block acris_product_display_image_modal_footer_button_apply %}
                    <sw-button variant="primary" size="small" @click="onApply">
                        {{ $tc('global.default.apply') }}
                    </sw-button>
                {% endblock %}
            </template>
        {% endblock %}
    </sw-modal>
{% endblock %}
