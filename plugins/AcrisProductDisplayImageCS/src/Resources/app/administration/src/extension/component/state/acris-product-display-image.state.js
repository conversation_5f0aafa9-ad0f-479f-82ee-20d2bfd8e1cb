Shopware.State.registerModule('acrisProductDisplayImageState', {
    namespaced: true,

    state: {
        isEditMode: false,
        mediaId: null,
        newEntities: [],
        configuredImages: [],
        configuredISO: []
    },

    mutations: {
        setConfiguredItems(state, configuredItems) {
            state.configuredImages = configuredItems;
        },

        addConfigurationItem(state, configuredItem) {
            const index = state.configuredISO.findIndex(item => item.mediaId === configuredItem.mediaId);

            if (index !== -1) {
                state.configuredISO[index] = configuredItem;
            } else {
                state.configuredISO.push(configuredItem);
            }
        },

        setConfiguredISO(state, configuredItems) {
            state.configuredISO = configuredItems;
        },

        setEditMode(state, isEditMode) {
            state.isEditMode = isEditMode;
        },

        setMediaId(state, mediaId) {
            state.mediaId = mediaId;
        },

        addNewEntity(state, addNewEntity) {
            if (!state.newEntities.find(o => o.id === addNewEntity.productMediaId)) {
                state.newEntities.push(addNewEntity);
            }
        },

        resetNewEntities(state) {
            state.newEntities = [];
        },

        closeEditMode(state) {
            state.isEditMode = false;
        },
    }
});
