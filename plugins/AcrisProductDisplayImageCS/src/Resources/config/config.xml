<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>Basic Configuration</title>
        <title lang="de-DE">Grundeinstellungen</title>

        <input-field type="bool">
            <name>truncateProductDisplayImage</name>
            <label>Truncate the existing display image language configuration on product import</label>
            <label lang="de-DE">Abschneiden der vorhandenen Konfiguration der Anzeigebildsprache beim Produktimport</label>
            <helpText>If active, then on product import with assigned display image language configuration the existing display image language configuration in the database will be replaced with the new entries.</helpText>
            <helpText lang="de-DE">Wenn aktiv, dann wird beim Produktimport mit zugewiesener Bildsprachkonfiguration die bestehende Bildsprachkonfiguration in der Datenbank durch die neuen Einträge ersetzt.</helpText>
            <defaultValue>false</defaultValue>
        </input-field>
    </card>
</config>
