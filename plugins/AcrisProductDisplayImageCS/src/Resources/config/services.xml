<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <!-- Subscriber -->
        <service id="Acris\ProductDisplayImage\Storefront\Subscriber\ProductPageSubscriber">
            <argument type="service" id="Acris\ProductDisplayImage\Components\ProductDisplayImageService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Acris\ProductDisplayImage\Storefront\Subscriber\ProductExportSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Acris\ProductDisplayImage\Components\TruncateProductImageLanguageService">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Acris\ProductDisplayImage\Core\Framework\DataAbstractionLayer\Write\WriteCommandExtractor" decorates="Shopware\Core\Framework\DataAbstractionLayer\Write\WriteCommandExtractor">
            <argument type="service" id="Acris\ProductDisplayImage\Core\Framework\DataAbstractionLayer\Write\WriteCommandExtractor.inner"/>
            <argument type="service" id="Acris\ProductDisplayImage\Components\TruncateProductImageLanguageService"/>
        </service>

        <!-- Services -->
        <service id="Acris\ProductDisplayImage\Components\ProductDisplayImageService" />

        <!-- ProductDisplayImage indexer -->
        <service id="Acris\ProductDisplayImage\Core\Content\ProductDisplayImage\ProductDisplayImageIndexer">
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory"/>
            <argument type="service" id="acris_product_display_image.repository"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Indexing\ManyToManyIdFieldUpdater"/>
            <argument type="service" id="messenger.bus.shopware"/>
            <tag name="shopware.entity_indexer" priority="100"/>
        </service>

        <!-- Entities -->
        <service id="Acris\ProductDisplayImage\Custom\ProductDisplayImageDefinition">
            <tag name="shopware.entity.definition" entity="acris_product_display_image" />
        </service>
        <service id="Acris\ProductDisplayImage\Custom\Aggregate\ProductDisplayImageLanguage\ProductDisplayImageLanguageDefinition">
            <tag name="shopware.entity.definition" entity="acris_product_display_image_language" />
        </service>
        <service id="Acris\ProductDisplayImage\Custom\LanguageExtension">
            <tag name="shopware.entity.extension"/>
        </service>
        <service id="Acris\ProductDisplayImage\Custom\ProductMediaExtension">
            <tag name="shopware.entity.extension"/>
        </service>
    </services>
</container>
