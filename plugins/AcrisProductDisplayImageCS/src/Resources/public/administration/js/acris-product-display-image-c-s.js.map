{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/base/sw-product-image/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/base/sw-product-image/sw-product-image.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/acris-edit-display-image-modal/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/acris-edit-display-image-modal/acris-edit-display-image-modal.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/sw-product-media-form/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/sw-product-media-form/sw-product-media-form.html.twig", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/base/sw-product-image/sw-product-image.scss", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/sw-product/page/sw-product-detail/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/state/acris-product-display-image.state.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/AcrisProductDisplayImageCS/src/Resources/app/administration/src/extension/component/acris-edit-display-image-modal/acris-edit-display-image-modal.scss"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Shopware", "Component", "override", "template", "created", "this", "createdComponent", "computed", "isConfigured", "State", "configuredImages", "length", "includes", "mediaId", "configuredISO", "getCodesById", "ISOCodes", "methods", "isCover", "mediaArray", "id", "item", "find", "entry", "codes", "onProductMediaEdit", "commit", "getCountryFlag", "code", "replace", "_regeneratorRuntime", "Op", "hasOwn", "obj", "desc", "$Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "define", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "context", "Context", "makeInvokeMethod", "tryCatch", "fn", "arg", "type", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "_typeof", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "state", "Error", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "undefined", "return", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "_asyncToGenerator", "args", "arguments", "apply", "Criteria", "Data", "cloneDeep", "Utils", "isArray", "types", "register", "inject", "props", "displayImageItem", "required", "productMedia", "product", "data", "isLoading", "isStandardCoverSet", "displayImageConfiguration", "preConfiguredCover", "configureCoverForLanguages", "isStandardCover", "coverId", "cover", "media", "isPlaceholder", "displayImageRepository", "repositoryFactory", "languageCriteria", "criteria", "addAssociation", "_this", "_isNew", "addFilter", "equals", "search", "api", "res", "save", "displayImage", "languages", "checkPreConfiguredCover", "first", "_this2", "useImageAsCoverForLanguage", "languageId", "onCancel", "$emit", "onApply", "assign", "markMediaAsCover", "markMediaAsConfiguredCover", "_this3", "extensions", "acrisProductDisplayImage", "configuredImageAsCoverForLanguage", "useForCoverLabel", "language", "$tc", "useForCoverHelpText", "onCoverChange", "index", "_this4", "_callee", "_context", "preConfigured", "onStandardCoverChange", "_this5", "_callee2", "_context2", "onAssignLanguagesChange", "entityCollection", "_this6", "_callee3", "_context3", "entity", "locale", "split", "toLowerCase", "fallbackCover", "splice", "mediaItems", "$super", "newEntities", "productMediaId", "isInherited", "parentProduct", "isEditMode", "displayImageMediaId", "displayImageMedia", "onEditProductDisplayImageSave", "onEditProductDisplayImageClose", "listToStyles", "parentId", "list", "styles", "newStyles", "part", "css", "sourceMap", "parts", "hasDocument", "document", "DEBUG", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "append<PERSON><PERSON><PERSON>", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "applyToTag", "newObj", "textStore", "replaceText", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "content", "default", "locals", "add", "productCriteria", "onSave", "registerModule", "namespaced", "mutations", "setConfiguredItems", "configuredItems", "addConfigurationItem", "configuredItem", "findIndex", "setConfiguredISO", "setEditMode", "setMediaId", "addNewEntity", "resetNewEntities", "closeEditMode"], "mappings": ";aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,uCAIjBlC,EAAoBA,EAAoBmC,EAAI,Q,iEC/E/BC,SAAdC,UAEEC,SAAS,mBAAoB,CACnCC,SCNW,6uBDQXC,QAAO,WACHC,KAAKC,oBAGTC,SAAU,CACNC,aAAY,WACR,OAAOR,SAASS,MAAM5B,IAAI,iCAAiC6B,iBAAiBC,OAAS,GAAKX,SAASS,MAAM5B,IAAI,iCAAiC6B,iBAAiBE,SAASP,KAAKQ,UAAYb,SAASS,MAAM5B,IAAI,iCAAiCiC,cAAcH,OAAS,GAAKN,KAAKU,aAAaf,SAASS,MAAM5B,IAAI,iCAAiCiC,cAAeT,KAAKQ,UAAYR,KAAKU,aAAaf,SAASS,MAAM5B,IAAI,iCAAiCiC,cAAeT,KAAKQ,SAASF,OAAS,GAG/dK,SAAQ,WACJ,IAAMF,EAAgBd,SAASS,MAAM5B,IAAI,iCAAiCiC,cAC1E,OAAOT,KAAKU,aAAaD,EAAeT,KAAKQ,WAIrDI,QAAS,CACLX,iBAAgB,WACZD,KAAKa,SAAU,GAGnBH,aAAY,SAACI,EAAYC,GACrB,IAAMC,EAAOF,EAAWG,MAAK,SAAAC,GAAK,OAAIA,EAAMV,UAAYO,KACxD,OAAOC,EAAOA,EAAKG,MAAQ,IAI/BC,mBAAkB,WACdzB,SAASS,MAAMiB,OAAO,6CAA6C,GACnE1B,SAASS,MAAMiB,OAAO,2CAA4CrB,KAAKQ,UAG3Ec,eAAc,SAACC,GACX,MAAO,gDAAgDC,QAAQ,oBAAqBD,O,4PEvChGE,EAAA,kBAAAhE,GAAA,IAAAA,EAAA,GAAAiE,EAAArD,OAAAkB,UAAAoC,EAAAD,EAAAlC,eAAAlB,EAAAD,OAAAC,gBAAA,SAAAsD,EAAA1C,EAAA2C,GAAAD,EAAA1C,GAAA2C,EAAAjD,OAAAkD,EAAA,mBAAApD,cAAA,GAAAqD,EAAAD,EAAAE,UAAA,aAAAC,EAAAH,EAAAI,eAAA,kBAAAC,EAAAL,EAAAnD,aAAA,yBAAAyD,EAAAR,EAAA1C,EAAAN,GAAA,OAAAP,OAAAC,eAAAsD,EAAA1C,EAAA,CAAAN,QAAAL,YAAA,EAAA8D,cAAA,EAAAC,UAAA,IAAAV,EAAA1C,GAAA,IAAAkD,EAAA,aAAAG,GAAAH,EAAA,SAAAR,EAAA1C,EAAAN,GAAA,OAAAgD,EAAA1C,GAAAN,GAAA,SAAA4D,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAH,KAAAnD,qBAAAuD,EAAAJ,EAAAI,EAAAC,EAAA1E,OAAAY,OAAA4D,EAAAtD,WAAAyD,EAAA,IAAAC,EAAAL,GAAA,WAAAtE,EAAAyE,EAAA,WAAAnE,MAAAsE,EAAAT,EAAAE,EAAAK,KAAAD,EAAA,SAAAI,EAAAC,EAAAxB,EAAAyB,GAAA,WAAAC,KAAA,SAAAD,IAAAD,EAAAtF,KAAA8D,EAAAyB,IAAA,MAAAd,GAAA,OAAAe,KAAA,QAAAD,IAAAd,IAAA9E,EAAA+E,OAAA,IAAAe,EAAA,YAAAT,KAAA,SAAAU,KAAA,SAAAC,KAAA,IAAAC,EAAA,GAAAtB,EAAAsB,EAAA3B,GAAA,8BAAA4B,EAAAtF,OAAAuF,eAAAC,EAAAF,OAAAG,EAAA,MAAAD,OAAAnC,GAAAC,EAAA7D,KAAA+F,EAAA9B,KAAA2B,EAAAG,GAAA,IAAAE,EAAAN,EAAAlE,UAAAuD,EAAAvD,UAAAlB,OAAAY,OAAAyE,GAAA,SAAAM,EAAAzE,GAAA,0BAAA0E,SAAA,SAAAC,GAAA9B,EAAA7C,EAAA2E,GAAA,SAAAb,GAAA,YAAAc,QAAAD,EAAAb,SAAA,SAAAe,EAAArB,EAAAsB,GAAA,SAAAC,EAAAJ,EAAAb,EAAAkB,EAAAC,GAAA,IAAAC,EAAAtB,EAAAJ,EAAAmB,GAAAnB,EAAAM,GAAA,aAAAoB,EAAAnB,KAAA,KAAAoB,EAAAD,EAAApB,IAAAzE,EAAA8F,EAAA9F,MAAA,OAAAA,GAAA,UAAA+F,EAAA/F,IAAA+C,EAAA7D,KAAAc,EAAA,WAAAyF,EAAAE,QAAA3F,EAAAgG,SAAAC,MAAA,SAAAjG,GAAA0F,EAAA,OAAA1F,EAAA2F,EAAAC,MAAA,SAAAjC,GAAA+B,EAAA,QAAA/B,EAAAgC,EAAAC,MAAAH,EAAAE,QAAA3F,GAAAiG,MAAA,SAAAC,GAAAJ,EAAA9F,MAAAkG,EAAAP,EAAAG,MAAA,SAAAK,GAAA,OAAAT,EAAA,QAAAS,EAAAR,EAAAC,QAAAC,EAAApB,KAAA,IAAA2B,EAAA1G,EAAA,gBAAAM,MAAA,SAAAsF,EAAAb,GAAA,SAAA4B,IAAA,WAAAZ,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAb,EAAAkB,EAAAC,MAAA,OAAAQ,MAAAH,KAAAI,YAAA,SAAA/B,EAAAT,EAAAE,EAAAK,GAAA,IAAAkC,EAAA,iCAAAhB,EAAAb,GAAA,iBAAA6B,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAhB,EAAA,MAAAb,EAAA,OAAA+B,IAAA,IAAApC,EAAAkB,SAAAlB,EAAAK,QAAA,KAAAgC,EAAArC,EAAAqC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAArC,GAAA,GAAAsC,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,GAAA,YAAAtC,EAAAkB,OAAAlB,EAAAwC,KAAAxC,EAAAyC,MAAAzC,EAAAK,SAAA,aAAAL,EAAAkB,OAAA,uBAAAgB,EAAA,MAAAA,EAAA,YAAAlC,EAAAK,IAAAL,EAAA0C,kBAAA1C,EAAAK,SAAA,WAAAL,EAAAkB,QAAAlB,EAAA2C,OAAA,SAAA3C,EAAAK,KAAA6B,EAAA,gBAAAT,EAAAtB,EAAAV,EAAAE,EAAAK,GAAA,cAAAyB,EAAAnB,KAAA,IAAA4B,EAAAlC,EAAA4C,KAAA,6BAAAnB,EAAApB,MAAAE,EAAA,gBAAA3E,MAAA6F,EAAApB,IAAAuC,KAAA5C,EAAA4C,MAAA,UAAAnB,EAAAnB,OAAA4B,EAAA,YAAAlC,EAAAkB,OAAA,QAAAlB,EAAAK,IAAAoB,EAAApB,OAAA,SAAAkC,EAAAF,EAAArC,GAAA,IAAA6C,EAAA7C,EAAAkB,SAAAmB,EAAArD,SAAA6D,GAAA,QAAAC,IAAA5B,EAAA,OAAAlB,EAAAqC,SAAA,eAAAQ,GAAAR,EAAArD,SAAA+D,SAAA/C,EAAAkB,OAAA,SAAAlB,EAAAK,SAAAyC,EAAAP,EAAAF,EAAArC,GAAA,UAAAA,EAAAkB,SAAA,WAAA2B,IAAA7C,EAAAkB,OAAA,QAAAlB,EAAAK,IAAA,IAAA2C,UAAA,oCAAAH,EAAA,aAAAtC,EAAA,IAAAkB,EAAAtB,EAAAe,EAAAmB,EAAArD,SAAAgB,EAAAK,KAAA,aAAAoB,EAAAnB,KAAA,OAAAN,EAAAkB,OAAA,QAAAlB,EAAAK,IAAAoB,EAAApB,IAAAL,EAAAqC,SAAA,KAAA9B,EAAA,IAAA0C,EAAAxB,EAAApB,IAAA,OAAA4C,IAAAL,MAAA5C,EAAAqC,EAAAa,YAAAD,EAAArH,MAAAoE,EAAAmD,KAAAd,EAAAe,QAAA,WAAApD,EAAAkB,SAAAlB,EAAAkB,OAAA,OAAAlB,EAAAK,SAAAyC,GAAA9C,EAAAqC,SAAA,KAAA9B,GAAA0C,GAAAjD,EAAAkB,OAAA,QAAAlB,EAAAK,IAAA,IAAA2C,UAAA,oCAAAhD,EAAAqC,SAAA,KAAA9B,GAAA,SAAA8C,EAAAC,GAAA,IAAApF,EAAA,CAAAqF,OAAAD,EAAA,SAAAA,IAAApF,EAAAsF,SAAAF,EAAA,SAAAA,IAAApF,EAAAuF,WAAAH,EAAA,GAAApF,EAAAwF,SAAAJ,EAAA,SAAAK,WAAAC,KAAA1F,GAAA,SAAA2F,EAAA3F,GAAA,IAAAuD,EAAAvD,EAAA4F,YAAA,GAAArC,EAAAnB,KAAA,gBAAAmB,EAAApB,IAAAnC,EAAA4F,WAAArC,EAAA,SAAAxB,EAAAL,GAAA,KAAA+D,WAAA,EAAAJ,OAAA,SAAA3D,EAAAqB,QAAAoC,EAAA,WAAAU,OAAA,YAAAjD,EAAAkD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAAjF,GAAA,GAAAkF,EAAA,OAAAA,EAAAnJ,KAAAkJ,GAAA,sBAAAA,EAAAb,KAAA,OAAAa,EAAA,IAAAE,MAAAF,EAAA1G,QAAA,KAAA3C,GAAA,EAAAwI,EAAA,SAAAA,IAAA,OAAAxI,EAAAqJ,EAAA1G,QAAA,GAAAqB,EAAA7D,KAAAkJ,EAAArJ,GAAA,OAAAwI,EAAAvH,MAAAoI,EAAArJ,GAAAwI,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAvH,WAAAkH,EAAAK,EAAAP,MAAA,EAAAO,GAAA,OAAAA,UAAA,OAAAA,KAAAf,GAAA,SAAAA,IAAA,OAAAxG,WAAAkH,EAAAF,MAAA,UAAApC,EAAAjE,UAAAkE,EAAAnF,EAAAyF,EAAA,eAAAnF,MAAA6E,EAAApB,cAAA,IAAA/D,EAAAmF,EAAA,eAAA7E,MAAA4E,EAAAnB,cAAA,IAAAmB,EAAA2D,YAAA/E,EAAAqB,EAAAtB,EAAA,qBAAA1E,EAAA2J,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,KAAAE,YAAA,QAAAD,QAAA9D,GAAA,uBAAA8D,EAAAH,aAAAG,EAAApJ,QAAAT,EAAA+J,KAAA,SAAAH,GAAA,OAAAhJ,OAAAoJ,eAAApJ,OAAAoJ,eAAAJ,EAAA5D,IAAA4D,EAAAK,UAAAjE,EAAArB,EAAAiF,EAAAlF,EAAA,sBAAAkF,EAAA9H,UAAAlB,OAAAY,OAAA8E,GAAAsD,GAAA5J,EAAAkK,MAAA,SAAAtE,GAAA,OAAAuB,QAAAvB,IAAAW,EAAAI,EAAA7E,WAAA6C,EAAAgC,EAAA7E,UAAA0C,GAAA,0BAAAxE,EAAA2G,gBAAA3G,EAAAmK,MAAA,SAAAnF,EAAAC,EAAAC,EAAAC,EAAAyB,QAAA,IAAAA,MAAAwD,SAAA,IAAAC,EAAA,IAAA1D,EAAA5B,EAAAC,EAAAC,EAAAC,EAAAC,GAAAyB,GAAA,OAAA5G,EAAA2J,oBAAA1E,GAAAoF,IAAA3B,OAAAtB,MAAA,SAAAH,GAAA,OAAAA,EAAAkB,KAAAlB,EAAA9F,MAAAkJ,EAAA3B,WAAAnC,EAAAD,GAAA3B,EAAA2B,EAAA5B,EAAA,aAAAC,EAAA2B,EAAAhC,GAAA,0BAAAK,EAAA2B,EAAA,qDAAAtG,EAAAsK,KAAA,SAAAC,GAAA,IAAA3I,EAAAhB,OAAA2J,GAAAD,EAAA,WAAA7I,KAAAG,EAAA0I,EAAAnB,KAAA1H,GAAA,OAAA6I,EAAAE,UAAA,SAAA9B,IAAA,KAAA4B,EAAAzH,QAAA,KAAApB,EAAA6I,EAAAG,MAAA,GAAAhJ,KAAAG,EAAA,OAAA8G,EAAAvH,MAAAM,EAAAiH,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAP,MAAA,EAAAO,IAAA1I,EAAAqG,SAAAb,EAAA1D,UAAA,CAAAgI,YAAAtE,EAAA8D,MAAA,SAAAoB,GAAA,QAAAC,KAAA,OAAAjC,KAAA,OAAAX,KAAA,KAAAC,WAAAK,EAAA,KAAAF,MAAA,OAAAP,SAAA,UAAAnB,OAAA,YAAAb,SAAAyC,EAAA,KAAAa,WAAA1C,QAAA4C,IAAAsB,EAAA,QAAAjK,KAAA,WAAAA,EAAAmK,OAAA,IAAA1G,EAAA7D,KAAA,KAAAI,KAAAgJ,OAAAhJ,EAAAoK,MAAA,WAAApK,QAAA4H,IAAAyC,KAAA,gBAAA3C,MAAA,MAAA4C,EAAA,KAAA7B,WAAA,GAAAG,WAAA,aAAA0B,EAAAlF,KAAA,MAAAkF,EAAAnF,IAAA,YAAAoF,MAAA/C,kBAAA,SAAAgD,GAAA,QAAA9C,KAAA,MAAA8C,EAAA,IAAA1F,EAAA,cAAA2F,EAAAC,EAAAC,GAAA,OAAApE,EAAAnB,KAAA,QAAAmB,EAAApB,IAAAqF,EAAA1F,EAAAmD,KAAAyC,EAAAC,IAAA7F,EAAAkB,OAAA,OAAAlB,EAAAK,SAAAyC,KAAA+C,EAAA,QAAAlL,EAAA,KAAAgJ,WAAArG,OAAA,EAAA3C,GAAA,IAAAA,EAAA,KAAAuD,EAAA,KAAAyF,WAAAhJ,GAAA8G,EAAAvD,EAAA4F,WAAA,YAAA5F,EAAAqF,OAAA,OAAAoC,EAAA,UAAAzH,EAAAqF,QAAA,KAAA6B,KAAA,KAAAU,EAAAnH,EAAA7D,KAAAoD,EAAA,YAAA6H,EAAApH,EAAA7D,KAAAoD,EAAA,iBAAA4H,GAAAC,EAAA,SAAAX,KAAAlH,EAAAsF,SAAA,OAAAmC,EAAAzH,EAAAsF,UAAA,WAAA4B,KAAAlH,EAAAuF,WAAA,OAAAkC,EAAAzH,EAAAuF,iBAAA,GAAAqC,GAAA,QAAAV,KAAAlH,EAAAsF,SAAA,OAAAmC,EAAAzH,EAAAsF,UAAA,YAAAuC,EAAA,UAAA5D,MAAA,kDAAAiD,KAAAlH,EAAAuF,WAAA,OAAAkC,EAAAzH,EAAAuF,gBAAAd,OAAA,SAAArC,EAAAD,GAAA,QAAA1F,EAAA,KAAAgJ,WAAArG,OAAA,EAAA3C,GAAA,IAAAA,EAAA,KAAAuD,EAAA,KAAAyF,WAAAhJ,GAAA,GAAAuD,EAAAqF,QAAA,KAAA6B,MAAAzG,EAAA7D,KAAAoD,EAAA,oBAAAkH,KAAAlH,EAAAuF,WAAA,KAAAuC,EAAA9H,EAAA,OAAA8H,IAAA,UAAA1F,GAAA,aAAAA,IAAA0F,EAAAzC,QAAAlD,MAAA2F,EAAAvC,aAAAuC,EAAA,UAAAvE,EAAAuE,IAAAlC,WAAA,UAAArC,EAAAnB,OAAAmB,EAAApB,MAAA2F,GAAA,KAAA9E,OAAA,YAAAiC,KAAA6C,EAAAvC,WAAAlD,GAAA,KAAA0F,SAAAxE,IAAAwE,SAAA,SAAAxE,EAAAiC,GAAA,aAAAjC,EAAAnB,KAAA,MAAAmB,EAAApB,IAAA,gBAAAoB,EAAAnB,MAAA,aAAAmB,EAAAnB,KAAA,KAAA6C,KAAA1B,EAAApB,IAAA,WAAAoB,EAAAnB,MAAA,KAAAmF,KAAA,KAAApF,IAAAoB,EAAApB,IAAA,KAAAa,OAAA,cAAAiC,KAAA,kBAAA1B,EAAAnB,MAAAoD,IAAA,KAAAP,KAAAO,GAAAnD,GAAA2F,OAAA,SAAAzC,GAAA,QAAA9I,EAAA,KAAAgJ,WAAArG,OAAA,EAAA3C,GAAA,IAAAA,EAAA,KAAAuD,EAAA,KAAAyF,WAAAhJ,GAAA,GAAAuD,EAAAuF,eAAA,YAAAwC,SAAA/H,EAAA4F,WAAA5F,EAAAwF,UAAAG,EAAA3F,GAAAqC,IAAA4F,MAAA,SAAA5C,GAAA,QAAA5I,EAAA,KAAAgJ,WAAArG,OAAA,EAAA3C,GAAA,IAAAA,EAAA,KAAAuD,EAAA,KAAAyF,WAAAhJ,GAAA,GAAAuD,EAAAqF,WAAA,KAAA9B,EAAAvD,EAAA4F,WAAA,aAAArC,EAAAnB,KAAA,KAAA8F,EAAA3E,EAAApB,IAAAwD,EAAA3F,GAAA,OAAAkI,GAAA,UAAAjE,MAAA,0BAAAkE,cAAA,SAAArC,EAAAd,EAAAE,GAAA,YAAAf,SAAA,CAAArD,SAAA8B,EAAAkD,GAAAd,aAAAE,WAAA,cAAAlC,SAAA,KAAAb,SAAAyC,GAAAvC,IAAA9F,EAAA,SAAA6L,EAAAC,EAAAhF,EAAAC,EAAAgF,EAAAC,EAAAvK,EAAAmE,GAAA,QAAA4C,EAAAsD,EAAArK,GAAAmE,GAAAzE,EAAAqH,EAAArH,MAAA,MAAAmG,GAAA,YAAAP,EAAAO,GAAAkB,EAAAL,KAAArB,EAAA3F,GAAAiJ,QAAAtD,QAAA3F,GAAAiG,KAAA2E,EAAAC,GAAA,SAAAC,EAAAtG,GAAA,sBAAAT,EAAA,KAAAgH,EAAAC,UAAA,WAAA/B,SAAA,SAAAtD,EAAAC,GAAA,IAAA+E,EAAAnG,EAAAyG,MAAAlH,EAAAgH,GAAA,SAAAH,EAAA5K,GAAA0K,EAAAC,EAAAhF,EAAAC,EAAAgF,EAAAC,EAAA,OAAA7K,GAAA,SAAA6K,EAAAlH,GAAA+G,EAAAC,EAAAhF,EAAAC,EAAAgF,EAAAC,EAAA,QAAAlH,GAAAiH,OAAA1D,OAEA,IAAQlG,EAAcD,SAAdC,UACAkK,EAAanK,SAASoK,KAAtBD,SACAE,EAAcrK,SAASsK,MAAM5K,OAA7B2K,UACAE,EAAYvK,SAASsK,MAAME,MAA3BD,QAERtK,EAAUwK,SAAS,iCAAkC,CACjDtK,SCTW,mrGDWXuK,OAAQ,CAAC,qBAETC,MAAO,CACHC,iBAAkB,CACdjH,KAAMjF,OACNmM,UAAU,GAGdC,aAAc,CACVnH,KAAMjF,OACNmM,UAAU,GAGdE,QAAS,CACLpH,KAAMjF,OACNmM,UAAU,IAIlBG,KAAI,WACA,MAAO,CACHC,WAAW,EACXC,oBAAoB,EACpBC,0BAA2B,KAC3BC,mBAAoB,GACpBC,2BAA4B,KAIpC9K,SAAU,CACN+K,gBAAe,WACX,IAAMC,EAAUlL,KAAK0K,QAAQS,MAAQnL,KAAK0K,QAAQS,MAAMpK,GAAKf,KAAK0K,QAAQQ,QAE1E,QAAkC,IAA9BlL,KAAK0K,QAAQU,MAAM9K,QAAgBN,KAAKyK,aAAaY,gBAAkBH,IAIpElL,KAAKyK,aAAa1J,KAAOmK,GAGpCI,uBAAsB,WAClB,OAAOtL,KAAKuL,kBAAkBtM,OAAO,gCAGzCuM,iBAAgB,WACZ,IAAMC,EAAW,IAAI3B,EAGrB,OAFA2B,EAASC,eAAe,UAEjBD,IAIf1L,QAAO,WACHC,KAAKC,oBAGTW,QAAS,CACLX,iBAAgB,WAAI,IAAD0L,EAAA,KACf,GAAI3L,KAAKuK,iBAAiBqB,OAAQ,CAC9B5L,KAAK4K,WAAY,EACjB,IAAMa,EAAW,IAAI3B,EACrB2B,EAASI,UAAU/B,EAASgC,OAAO,iBAAkB9L,KAAKyK,aAAa1J,KACvEf,KAAKsL,uBAAuBS,OAAON,EAAU9L,SAASsD,QAAQ+I,KAAKnH,MAAK,SAACoH,GACrE,GAAIA,EAAI3L,QAAU,EACd,OAAOqL,EAAKL,uBAAuBY,KAAKP,EAAKpB,kBAAkB1F,MAAK,WAChE8G,EAAKL,uBAAuB9M,IACxBmN,EAAKpB,iBAAiBxJ,GACtBpB,SAASsD,QAAQ+I,KACnBnH,MAAK,SAACsH,GACJR,EAAKf,WAAY,EACjBe,EAAKpB,iBAAmB4B,EACxBR,EAAKb,0BAA4Bd,EAAU2B,EAAKpB,kBAChDoB,EAAKb,0BAA0BsB,UAAYT,EAAKpB,iBAAiB6B,UACjET,EAAKU,0BACLV,EAAKd,mBAAqBc,EAAKV,mBAChC9B,OAAM,WACLwC,EAAKf,WAAY,QAEtBzB,OAAM,WACLwC,EAAKf,WAAY,KAGrBe,EAAKf,WAAY,EACjBe,EAAKpB,iBAAmB0B,EAAIK,QAC5BX,EAAKb,0BAA4Bd,EAAU2B,EAAKpB,kBAChDoB,EAAKb,0BAA0BsB,UAAYT,EAAKpB,iBAAiB6B,UACjET,EAAKU,0BACLV,EAAKd,mBAAqBc,EAAKV,wBAIvCjL,KAAK8K,0BAA4Bd,EAAUhK,KAAKuK,kBAChDvK,KAAK8K,0BAA0BsB,UAAYpM,KAAKuK,iBAAiB6B,UACjEpM,KAAKqM,0BACLrM,KAAK6K,mBAAqB7K,KAAKiL,iBAIvCoB,wBAAuB,WAAI,IAADE,EAAA,KACjBvM,KAAK8K,0BAA0BsB,WAAiE,IAApDpM,KAAK8K,0BAA0BsB,UAAU9L,SACtFN,KAAK8K,0BAA0B0B,2BAA6B,IAG5DxM,KAAK8K,0BAA0B0B,4BAA8BxM,KAAK8K,0BAA0B0B,2BAA2BlM,OAAS,GAChIN,KAAK8K,0BAA0B0B,2BAA2BvI,SAAQ,SAACkH,IAC3C,IAAhBA,EAAMvM,OACN2N,EAAKxB,mBAAmBnE,KAAK,CAAC6F,WAAYtB,EAAMsB,WAAY7N,MAAOuM,EAAMvM,YAMzF8N,SAAQ,WACJ1M,KAAKgL,2BAA6B,GAClChL,KAAK+K,mBAAqB,GAC1B/K,KAAK8K,0BAA4B,KACjC9K,KAAK2M,MAAM,gBAGfC,QAAO,WACHvO,OAAOwO,OAAO7M,KAAKuK,iBAAkBvK,KAAK8K,2BAEtC9K,KAAK6K,oBACL7K,KAAK8M,iBAAiB9M,KAAKyK,cAG3BzK,KAAKgL,4BAA8BhL,KAAKgL,2BAA2B1K,OAAS,GAC5EN,KAAK+M,6BAGTpN,SAASS,MAAMiB,OAAO,6CAA8CrB,KAAKuK,kBACzEvK,KAAK2M,MAAM,aAAc3M,KAAKuK,mBAGlCwC,2BAA0B,WAAI,IAADC,EAAA,KACrBhN,KAAK0K,SAAW1K,KAAK0K,QAAQU,OAC7BpL,KAAK0K,QAAQU,MAAMnH,SAAQ,SAACwG,GACpBA,EAAa1J,KAAOiM,EAAKvC,aAAa1J,IAAM0J,EAAawC,YAAcxC,EAAawC,WAAWC,0BAA4BzC,EAAawC,WAAWC,yBAAyBV,4BAA8BtC,EAAQO,EAAawC,WAAWC,yBAAyBV,6BAA+B/B,EAAawC,WAAWC,yBAAyBV,2BAA2BlM,OAAS,GACvXmK,EAAawC,WAAWC,yBAAyBV,2BAA2BvI,SAAQ,SAACuI,GAC7EQ,EAAKhC,2BAA2B/J,MAAK,SAAAkM,GAAiC,OAAIA,EAAkCV,aAAeD,EAA2BC,gBACtJD,EAA2B5N,OAAQ,UAQ3DwO,iBAAgB,SAACC,GACb,OAAOrN,KAAKsN,IACR,0DAA2D,EAAG,CAACD,SAAUA,EAASnP,QAI1FqP,oBAAmB,SAACF,GAChB,OAAOrN,KAAKsN,IACR,6DAA8D,EAAG,CAACD,SAAUA,EAASnP,QAIvFsP,cAAa,SAAC5O,EAAO6O,GAAQ,IAADC,EAAA,YAAAhE,EAAAjI,IAAA+F,MAAA,SAAAmG,IAAA,OAAAlM,IAAAe,MAAA,SAAAoL,GAAA,cAAAA,EAAAxF,KAAAwF,EAAAzH,MAAA,OAC9BuH,EAAK5C,0BAA0B0B,2BAA2BiB,GAAO7O,MAAQA,GAC3D,IAAVA,IACuC,IAAnC8O,EAAK3C,mBAAmBzK,QAAiBoN,EAAK3C,mBAAmB9J,MAAK,SAAA4M,GAAa,OAAIA,EAAcpB,aAAeiB,EAAK5C,0BAA0B0B,2BAA2BiB,GAAOhB,eAAgBiB,EAAK1C,2BAA2B/J,MAAK,SAAA4M,GAAa,OAAIA,EAAcpB,aAAeiB,EAAK5C,0BAA0B0B,2BAA2BiB,GAAOhB,eACzViB,EAAK1C,2BAA2BpE,KAAK,CAAC6F,WAAYiB,EAAK5C,0BAA0B0B,2BAA2BiB,GAAOhB,cAE1H,wBAAAmB,EAAArF,UAAAoF,MAN6BjE,IAS5BoE,sBAAqB,SAAClP,GAAQ,IAADmP,EAAA,YAAArE,EAAAjI,IAAA+F,MAAA,SAAAwG,IAAA,OAAAvM,IAAAe,MAAA,SAAAyL,GAAA,cAAAA,EAAA7F,KAAA6F,EAAA9H,MAAA,OAC/B4H,EAAKlD,mBAAqBjM,EAAM,wBAAAqP,EAAA1F,UAAAyF,MADDtE,IAInCoD,iBAAgB,SAACrC,GACbzK,KAAK0K,QAAQS,MAAQV,EACrBzK,KAAK0K,QAAQQ,QAAUT,EAAa1J,IAGlCmN,wBAAuB,SAACC,GAAmB,IAADC,EAAA,YAAA1E,EAAAjI,IAAA+F,MAAA,SAAA6G,IAAA,IAAAlN,EAAA,OAAAM,IAAAe,MAAA,SAAA8L,GAAA,cAAAA,EAAAlG,KAAAkG,EAAAnI,MAAA,OAC5CiI,EAAKtD,0BAA0BsB,UAAY+B,EAEtCC,EAAKtD,0BAA0B0B,4BAA+BtC,EAAQkE,EAAKtD,0BAA0B0B,8BACtG4B,EAAKtD,0BAA0B0B,2BAA6B,IAG1DrL,EAAQ,GAEVgN,GAAoBA,EAAiB7N,OAAS,GAC9C6N,EAAiBlK,SAAQ,SAACsK,GAKtB,GAJyE,IAArEH,EAAKtD,0BAA0B0B,2BAA2BlM,QAAiB8N,EAAKtD,0BAA0B0B,2BAA2BvL,MAAK,SAAA7C,GAAC,OAAIA,EAAEqO,aAAe8B,EAAOxN,OACvKqN,EAAKtD,0BAA0B0B,2BAA2B5F,KAAK,CAAC6F,WAAY8B,EAAOxN,GAAI7C,KAAMqQ,EAAOrQ,KAAMU,OAAO,IAGjH2P,EAAOC,OAAQ,CACf,IAAMjN,EAAOgN,EAAOC,OAAOjN,KAAKkN,MAAM,KAAK,GAAGC,cAC9CvN,EAAMyF,KAAKrF,OAKvB5B,SAASS,MAAMiB,OAAO,qDAAsD,CAACb,QAAS4N,EAAK3D,aAAajK,QAASW,MAAOA,IAEpHiN,EAAKtD,0BAA0B0B,2BAA2BlM,OAAS,GACnE8N,EAAKtD,0BAA0B0B,2BAA2BvI,SAAQ,SAAC0K,EAAelB,GACzEU,EAAiBlN,MAAK,SAAA7C,GAAC,OAAIA,EAAE2C,KAAO4N,EAAclC,eACnD2B,EAAKtD,0BAA0B0B,2BAA2BoC,OAAOnB,EAAO,MAGnF,wBAAAa,EAAA/F,UAAA8F,MA9B2C3E,OE3LlC/J,SAAdC,UAEEC,SAAS,wBAAyB,CACxCC,SCLW,u/BDOXI,SAAU,CACN2O,WAAU,WACN,IAAMA,EAAa7O,KAAK8O,OAAO,cACzBzO,EAAmB,GACnBI,EAAgB,GA+BtB,OA7BIT,KAAK+O,aACL/O,KAAK+O,YAAY9K,SAAQ,SAACsK,GACtBM,EAAW5K,SAAQ,SAACwG,GACZA,EAAa1J,KAAOwN,EAAOS,iBAC3BvE,EAAawC,WAAWC,yBAA2BqB,SAMnEM,EAAW5K,SAAQ,SAACwG,GAChB,IAAMtJ,EAAQ,GACVsJ,EAAawC,YAAcxC,EAAawC,WAAWC,0BAA4BzC,EAAawC,WAAWC,yBAAyBd,WAAa3B,EAAawC,WAAWC,yBAAyBd,UAAU9L,OAAS,IACjND,EAAiBuG,KAAK6D,EAAajK,SAEnCiK,EAAawC,WAAWC,yBAAyBd,UAAUnI,SAAQ,SAACoJ,GAChE,GAAIA,EAASmB,OAAQ,CACjB,IAAMjN,EAAO8L,EAASmB,OAAOjN,KAAKkN,MAAM,KAAK,GAAGC,cAChDvN,EAAMyF,KAAKrF,OAInBd,EAAcmG,KAAK,CAACpG,QAASiK,EAAajK,QAASW,MAAOA,QAIlExB,SAASS,MAAMiB,OAAO,mDAAoDhB,GAC1EV,SAASS,MAAMiB,OAAO,iDAAkDZ,GAEjEoO,GAGXE,YAAW,WACP,OAAOpP,SAASS,MAAM5B,IAAI,iCAAiCuQ,aAG/D5O,aAAY,WACR,OAAOR,SAASS,MAAM5B,IAAI,iCAAiC6B,iBAAiBC,OAAS,GAGzFoK,QAAO,WACH,IAAMxF,EAAQvF,SAASS,MAAM5B,IAAI,mBAEjC,OAAIwB,KAAKiP,YACE/J,EAAMgK,cAGVhK,EAAMwF,SAGjByE,WAAU,WACN,OAAOxP,SAASS,MAAM5B,IAAI,iCAAiC2Q,YAG/DC,oBAAmB,WACf,OAAOzP,SAASS,MAAM5B,IAAI,iCAAiCgC,SAG/D8K,uBAAsB,WAClB,OAAOtL,KAAKuL,kBAAkBtM,OAAO,gCAGzCoQ,kBAAiB,WAAI,IAAD1D,EAAA,KACZ0D,EAAoBrP,KAAK6O,WAAW5N,MAAK,SAAA7C,GAAC,OAAIA,EAAEoC,UAAYmL,EAAKyD,uBAOrE,OALKC,EAAkBpC,WAAWC,0BAA6BmC,EAAkBpC,WAAWC,yBAAyBnM,KACjHsO,EAAkBpC,WAAWC,yBAA2BlN,KAAKsL,uBAAuBrM,OAAOU,SAASsD,QAAQ+I,KAC5GqD,EAAkBpC,WAAWC,yBAAyB8B,eAAiBK,EAAkBtO,IAGtFsO,IAIfzO,QAAS,CACLC,QAAO,WACH,OAAO,GAGXyO,8BAA6B,WACzB3P,SAASS,MAAMiB,OAAO,6CAA6C,GACnE1B,SAASS,MAAMiB,OAAO,2CAA4C,OAGtEkO,+BAA8B,WAC1B5P,SAASS,MAAMiB,OAAO,6CAA6C,GACnE1B,SAASS,MAAMiB,OAAO,2CAA4C,U,sEE/F/D,SAASmO,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPjS,EAAI,EAAGA,EAAI+R,EAAKpP,OAAQ3C,IAAK,CACpC,IAAIqD,EAAO0O,EAAK/R,GACZoD,EAAKC,EAAK,GAIV6O,EAAO,CACT9O,GAAI0O,EAAW,IAAM9R,EACrBmS,IALQ9O,EAAK,GAMboK,MALUpK,EAAK,GAMf+O,UALc/O,EAAK,IAOhB4O,EAAU7O,GAGb6O,EAAU7O,GAAIiP,MAAMpJ,KAAKiJ,GAFzBF,EAAO/I,KAAKgJ,EAAU7O,GAAM,CAAEA,GAAIA,EAAIiP,MAAO,CAACH,KAKlD,OAAOF,E,+CCjBT,IAAIM,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAI9K,MACV,2JAkBJ,IAAIiL,EAAc,GAQdC,EAAOJ,IAAgBC,SAASG,MAAQH,SAASI,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUtC,eAE3E,SAASuC,EAAiBxB,EAAUC,EAAMwB,EAAeC,GACtEV,EAAeS,EAEfP,EAAUQ,GAAY,GAEtB,IAAIxB,EAASH,EAAaC,EAAUC,GAGpC,OAFA0B,EAAezB,GAER,SAAiB0B,GAEtB,IADA,IAAIC,EAAY,GACP3T,EAAI,EAAGA,EAAIgS,EAAOrP,OAAQ3C,IAAK,CACtC,IAAIqD,EAAO2O,EAAOhS,IACd4T,EAAWnB,EAAYpP,EAAKD,KACvByQ,OACTF,EAAU1K,KAAK2K,GAEbF,EAEFD,EADAzB,EAASH,EAAaC,EAAU4B,IAGhC1B,EAAS,GAEX,IAAShS,EAAI,EAAGA,EAAI2T,EAAUhR,OAAQ3C,IAAK,CACzC,IAAI4T,EACJ,GAAsB,KADlBA,EAAWD,EAAU3T,IACZ6T,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASvB,MAAM1P,OAAQmR,IACzCF,EAASvB,MAAMyB,YAEVrB,EAAYmB,EAASxQ,OAMpC,SAASqQ,EAAgBzB,GACvB,IAAK,IAAIhS,EAAI,EAAGA,EAAIgS,EAAOrP,OAAQ3C,IAAK,CACtC,IAAIqD,EAAO2O,EAAOhS,GACd4T,EAAWnB,EAAYpP,EAAKD,IAChC,GAAIwQ,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASvB,MAAM1P,OAAQmR,IACzCF,EAASvB,MAAMyB,GAAGzQ,EAAKgP,MAAMyB,IAE/B,KAAOA,EAAIzQ,EAAKgP,MAAM1P,OAAQmR,IAC5BF,EAASvB,MAAMpJ,KAAK8K,EAAS1Q,EAAKgP,MAAMyB,KAEtCF,EAASvB,MAAM1P,OAASU,EAAKgP,MAAM1P,SACrCiR,EAASvB,MAAM1P,OAASU,EAAKgP,MAAM1P,YAEhC,CACL,IAAI0P,EAAQ,GACZ,IAASyB,EAAI,EAAGA,EAAIzQ,EAAKgP,MAAM1P,OAAQmR,IACrCzB,EAAMpJ,KAAK8K,EAAS1Q,EAAKgP,MAAMyB,KAEjCrB,EAAYpP,EAAKD,IAAM,CAAEA,GAAIC,EAAKD,GAAIyQ,KAAM,EAAGxB,MAAOA,KAK5D,SAAS2B,IACP,IAAIC,EAAe1B,SAAS2B,cAAc,SAG1C,OAFAD,EAAatO,KAAO,WACpB+M,EAAKyB,YAAYF,GACVA,EAGT,SAASF,EAAU9P,GACjB,IAAImQ,EAAQC,EACRJ,EAAe1B,SAAS+B,cAAc,SAAWrB,EAAW,MAAQhP,EAAIb,GAAK,MAEjF,GAAI6Q,EAAc,CAChB,GAAInB,EAGF,OAAOC,EAOPkB,EAAaM,WAAWC,YAAYP,GAIxC,GAAIf,EAAS,CAEX,IAAIuB,EAAa5B,IACjBoB,EAAerB,IAAqBA,EAAmBoB,KACvDI,EAASM,EAAoBlT,KAAK,KAAMyS,EAAcQ,GAAY,GAClEJ,EAASK,EAAoBlT,KAAK,KAAMyS,EAAcQ,GAAY,QAGlER,EAAeD,IACfI,EAASO,EAAWnT,KAAK,KAAMyS,GAC/BI,EAAS,WACPJ,EAAaM,WAAWC,YAAYP,IAMxC,OAFAG,EAAOnQ,GAEA,SAAsB2Q,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAOzC,MAAQlO,EAAIkO,KACnByC,EAAOnH,QAAUxJ,EAAIwJ,OACrBmH,EAAOxC,YAAcnO,EAAImO,UAC3B,OAEFgC,EAAOnQ,EAAM2Q,QAEbP,KAKN,IACMQ,EADFC,GACED,EAAY,GAET,SAAU/E,EAAOiF,GAEtB,OADAF,EAAU/E,GAASiF,EACZF,EAAUG,OAAOC,SAASC,KAAK,QAI1C,SAASR,EAAqBT,EAAcnE,EAAOuE,EAAQpQ,GACzD,IAAIkO,EAAMkC,EAAS,GAAKpQ,EAAIkO,IAE5B,GAAI8B,EAAakB,WACflB,EAAakB,WAAWC,QAAUN,EAAYhF,EAAOqC,OAChD,CACL,IAAIkD,EAAU9C,SAAS+C,eAAenD,GAClCoD,EAAatB,EAAasB,WAC1BA,EAAWzF,IAAQmE,EAAaO,YAAYe,EAAWzF,IACvDyF,EAAW5S,OACbsR,EAAauB,aAAaH,EAASE,EAAWzF,IAE9CmE,EAAaE,YAAYkB,IAK/B,SAASV,EAAYV,EAAchQ,GACjC,IAAIkO,EAAMlO,EAAIkO,IACV1E,EAAQxJ,EAAIwJ,MACZ2E,EAAYnO,EAAImO,UAiBpB,GAfI3E,GACFwG,EAAawB,aAAa,QAAShI,GAEjCuF,EAAQ0C,OACVzB,EAAawB,aAAaxC,EAAUhP,EAAIb,IAGtCgP,IAGFD,GAAO,mBAAqBC,EAAUuD,QAAQ,GAAK,MAEnDxD,GAAO,uDAAyDyD,KAAKC,SAASC,mBAAmBC,KAAKC,UAAU5D,MAAgB,OAG9H6B,EAAakB,WACflB,EAAakB,WAAWC,QAAUjD,MAC7B,CACL,KAAO8B,EAAagC,YAClBhC,EAAaO,YAAYP,EAAagC,YAExChC,EAAaE,YAAY5B,SAAS+C,eAAenD,O,qBCxNrD,IAAI+D,EAAU,EAAQ,QACnBA,EAAQ9U,aAAY8U,EAAUA,EAAQC,SACnB,iBAAZD,IAAsBA,EAAU,CAAC,CAACnW,EAAOC,EAAIkW,EAAS,MAC7DA,EAAQE,SAAQrW,EAAOD,QAAUoW,EAAQE,SAG/BC,EADH,EAAQ,QAAyJF,SAC1J,WAAYD,GAAS,EAAM,K,0CCTtBlU,SAAdC,UAEEC,SAAS,oBAAqB,CACpCK,SAAU,CACN+T,gBAAe,WACX,IAAMxI,EAAWzL,KAAK8O,OAAO,mBAG7B,OAFArD,EAASC,eAAe,mDACxBD,EAASC,eAAe,mDACjBD,GAGXsD,YAAW,WACP,OAAOpP,SAASS,MAAM5B,IAAI,iCAAiCuQ,cAInEnO,QAAS,CACLsT,OAAM,WAAI,IAADvI,EAAA,KACD3L,KAAK0K,SAAW1K,KAAK+O,cACrB/O,KAAK+O,YAAY9K,SAAQ,SAACsK,GACtB5C,EAAKjB,QAAQU,MAAMnH,SAAQ,SAACwG,GACpBA,EAAa1J,KAAOwN,EAAOS,iBAC3BvE,EAAawC,WAAWC,yBAA2BqB,MAIvD5C,EAAKjB,QAAQS,OAASQ,EAAKjB,QAAQS,MAAMpK,KAAOwN,EAAOS,iBACvDrD,EAAKjB,QAAQS,MAAM8B,WAAWC,yBAA2BqB,MAGjE5O,SAASS,MAAMiB,OAAO,mDAE1BrB,KAAK8O,OAAO,e,qBChCxBnP,SAASS,MAAM+T,eAAe,gCAAiC,CAC3DC,YAAY,EAEZlP,MAAO,CACHiK,YAAY,EACZ3O,QAAS,KACTuO,YAAa,GACb1O,iBAAkB,GAClBI,cAAe,IAGnB4T,UAAW,CACPC,mBAAkB,SAACpP,EAAOqP,GACtBrP,EAAM7E,iBAAmBkU,GAG7BC,qBAAoB,SAACtP,EAAOuP,GACxB,IAAMhH,EAAQvI,EAAMzE,cAAciU,WAAU,SAAA1T,GAAI,OAAIA,EAAKR,UAAYiU,EAAejU,YAErE,IAAXiN,EACAvI,EAAMzE,cAAcgN,GAASgH,EAE7BvP,EAAMzE,cAAcmG,KAAK6N,IAIjCE,iBAAgB,SAACzP,EAAOqP,GACpBrP,EAAMzE,cAAgB8T,GAG1BK,YAAW,SAAC1P,EAAOiK,GACfjK,EAAMiK,WAAaA,GAGvB0F,WAAU,SAAC3P,EAAO1E,GACd0E,EAAM1E,QAAUA,GAGpBsU,aAAY,SAAC5P,EAAO4P,GACX5P,EAAM6J,YAAY9N,MAAK,SAAA7C,GAAC,OAAIA,EAAE2C,KAAO+T,EAAa9F,mBACnD9J,EAAM6J,YAAYnI,KAAKkO,IAI/BC,iBAAgB,SAAC7P,GACbA,EAAM6J,YAAc,IAGxBiG,cAAa,SAAC9P,GACVA,EAAMiK,YAAa,O,qBC9C/B,IAAI0E,EAAU,EAAQ,QACnBA,EAAQ9U,aAAY8U,EAAUA,EAAQC,SACnB,iBAAZD,IAAsBA,EAAU,CAAC,CAACnW,EAAOC,EAAIkW,EAAS,MAC7DA,EAAQE,SAAQrW,EAAOD,QAAUoW,EAAQE,SAG/BC,EADH,EAAQ,QAAsJF,SACvJ,WAAYD,GAAS,EAAM", "file": "static/js/acris-product-display-image-c-s.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/bundles/acrisproductdisplayimagecs/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"2qTK\");\n", "import template from './sw-product-image.html.twig';\nimport './sw-product-image.scss';\n\nconst { Component } = Shopware;\n\nComponent.override('sw-product-image', {\n    template,\n\n    created() {\n        this.createdComponent();\n    },\n\n    computed: {\n        isConfigured() {\n            return Shopware.State.get('acrisProductDisplayImageState').configuredImages.length > 0 && Shopware.State.get('acrisProductDisplayImageState').configuredImages.includes(this.mediaId) && Shopware.State.get('acrisProductDisplayImageState').configuredISO.length > 0 && this.getCodesById(Shopware.State.get('acrisProductDisplayImageState').configuredISO, this.mediaId) && this.getCodesById(Shopware.State.get('acrisProductDisplayImageState').configuredISO, this.mediaId).length > 0;\n        },\n\n        ISOCodes () {\n            const configuredISO = Shopware.State.get('acrisProductDisplayImageState').configuredISO;\n            return this.getCodesById(configuredISO, this.mediaId);\n        }\n    },\n\n    methods: {\n        createdComponent() {\n            this.isCover = false;\n        },\n\n        getCodesById(mediaArray, id) {\n            const item = mediaArray.find(entry => entry.mediaId === id);\n            return item ? item.codes : []; // Return codes if found, otherwise an empty array\n        },\n\n\n        onProductMediaEdit(){\n            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', true);\n            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', this.mediaId);\n        },\n\n        getCountryFlag(code) {\n            return \"https://flagcdn.com/w20/{{ countryCode }}.png\".replace('{{ countryCode }}', code);\n        }\n    }\n});\n", "export default \"{% block sw_product_image_context_cover_action %}{% endblock %}\\n\\n{% block sw_product_image_context_delete_action %}\\n    <sw-context-menu-item\\n        @click=\\\"onProductMediaEdit\\\"\\n    >\\n        {{ $tc('acris-product-display-image.product-media.buttonEdit') }}\\n    </sw-context-menu-item>\\n    {% parent %}\\n{% endblock %}\\n\\n{% block sw_product_image_preview %}\\n    {% block acris_product_display_image_langauge_flags %}\\n        <div v-if=\\\"isConfigured\\\" class=\\\"acris-product-display-image-flag-container\\\">\\n            <span v-for=\\\"code in ISOCodes\\\" class=\\\"acris-product-display-image-flag\\\">\\n                <img :src=\\\"getCountryFlag(code)\\\">\\n            </span>\\n        </div>\\n    {% endblock %}\\n\\n    {% parent %}\\n{% endblock %}\\n\";", "import template from './acris-edit-display-image-modal.html.twig';\nimport './acris-edit-display-image-modal.scss';\n\nconst { Component } = Shopware;\nconst { Criteria } = Shopware.Data;\nconst { cloneDeep } = Shopware.Utils.object;\nconst { isArray } = Shopware.Utils.types;\n\nComponent.register('acris-edit-display-image-modal', {\n    template,\n\n    inject: ['repositoryFactory'],\n\n    props: {\n        displayImageItem: {\n            type: Object,\n            required: true\n        },\n\n        productMedia: {\n            type: Object,\n            required: true\n        },\n\n        product: {\n            type: Object,\n            required: true\n        }\n    },\n\n    data() {\n        return {\n            isLoading: false,\n            isStandardCoverSet: false,\n            displayImageConfiguration: null,\n            preConfiguredCover: [],\n            configureCoverForLanguages: []\n        }\n    },\n\n    computed: {\n        isStandardCover() {\n            const coverId = this.product.cover ? this.product.cover.id : this.product.coverId;\n\n            if (this.product.media.length === 0 || this.productMedia.isPlaceholder || !coverId) {\n                return false;\n            }\n\n            return this.productMedia.id === coverId;\n        },\n\n        displayImageRepository() {\n            return this.repositoryFactory.create('acris_product_display_image');\n        },\n\n        languageCriteria() {\n            const criteria = new Criteria();\n            criteria.addAssociation('locale');\n\n            return criteria;\n        },\n    },\n\n    created() {\n        this.createdComponent();\n    },\n\n    methods: {\n        createdComponent() {\n            if (this.displayImageItem._isNew) {\n                this.isLoading = true;\n                const criteria = new Criteria();\n                criteria.addFilter(Criteria.equals('productMediaId', this.productMedia.id));\n                this.displayImageRepository.search(criteria, Shopware.Context.api).then((res) => {\n                    if (res.length <= 0) {\n                        return this.displayImageRepository.save(this.displayImageItem).then(() => {\n                            this.displayImageRepository.get(\n                                this.displayImageItem.id,\n                                Shopware.Context.api\n                            ).then((displayImage) => {\n                                this.isLoading = false;\n                                this.displayImageItem = displayImage;\n                                this.displayImageConfiguration = cloneDeep(this.displayImageItem);\n                                this.displayImageConfiguration.languages = this.displayImageItem.languages;\n                                this.checkPreConfiguredCover();\n                                this.isStandardCoverSet = this.isStandardCover;\n                            }).catch(() => {\n                                this.isLoading = false;\n                            });\n                        }).catch(() => {\n                            this.isLoading = false;\n                        });\n                    } else {\n                        this.isLoading = false;\n                        this.displayImageItem = res.first();\n                        this.displayImageConfiguration = cloneDeep(this.displayImageItem);\n                        this.displayImageConfiguration.languages = this.displayImageItem.languages;\n                        this.checkPreConfiguredCover();\n                        this.isStandardCoverSet = this.isStandardCover;\n                    }\n                });\n            } else {\n                this.displayImageConfiguration = cloneDeep(this.displayImageItem);\n                this.displayImageConfiguration.languages = this.displayImageItem.languages;\n                this.checkPreConfiguredCover();\n                this.isStandardCoverSet = this.isStandardCover;\n            }\n        },\n\n        checkPreConfiguredCover() {\n            if (!this.displayImageConfiguration.languages || this.displayImageConfiguration.languages.length === 0) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage = [];\n            }\n\n            if (this.displayImageConfiguration.useImageAsCoverForLanguage && this.displayImageConfiguration.useImageAsCoverForLanguage.length > 0) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage.forEach((cover) => {\n                    if (cover.value === true) {\n                        this.preConfiguredCover.push({languageId: cover.languageId, value: cover.value})\n                    }\n                });\n            }\n        },\n\n        onCancel() {\n            this.configureCoverForLanguages = [];\n            this.preConfiguredCover = [];\n            this.displayImageConfiguration = null;\n            this.$emit('modal-close');\n        },\n\n        onApply() {\n            Object.assign(this.displayImageItem, this.displayImageConfiguration);\n\n            if (this.isStandardCoverSet) {\n                this.markMediaAsCover(this.productMedia);\n            }\n\n            if (this.configureCoverForLanguages && this.configureCoverForLanguages.length > 0) {\n                this.markMediaAsConfiguredCover();\n            }\n\n            Shopware.State.commit('acrisProductDisplayImageState/addNewEntity', this.displayImageItem);\n            this.$emit('modal-save', this.displayImageItem);\n        },\n\n        markMediaAsConfiguredCover() {\n            if (this.product && this.product.media) {\n                this.product.media.forEach((productMedia) => {\n                    if (productMedia.id !== this.productMedia.id && productMedia.extensions && productMedia.extensions.acrisProductDisplayImage && productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage && isArray(productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage) && productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.length > 0) {\n                        productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.forEach((useImageAsCoverForLanguage) => {\n                            if (this.configureCoverForLanguages.find(configuredImageAsCoverForLanguage => configuredImageAsCoverForLanguage.languageId === useImageAsCoverForLanguage.languageId)) {\n                                useImageAsCoverForLanguage.value = false;\n                            }\n                        });\n                    }\n                });\n            }\n        },\n\n        useForCoverLabel(language) {\n            return this.$tc(\n                'acris-product-display-image.modal.fieldLabelUseForCover', 0, {language: language.name}\n            );\n        },\n\n        useForCoverHelpText(language) {\n            return this.$tc(\n                'acris-product-display-image.modal.fieldHelpTextUseForCover', 0, {language: language.name}\n            );\n        },\n\n        async onCoverChange(value, index) {\n            this.displayImageConfiguration.useImageAsCoverForLanguage[index].value = value\n            if (value === true) {\n                if (this.preConfiguredCover.length === 0 || !this.preConfiguredCover.find(preConfigured => preConfigured.languageId === this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId) || !this.configureCoverForLanguages.find(preConfigured => preConfigured.languageId === this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId)) {\n                    this.configureCoverForLanguages.push({languageId: this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId});\n                }\n            }\n        },\n\n        async onStandardCoverChange(value) {\n            this.isStandardCoverSet = value;\n        },\n\n        markMediaAsCover(productMedia) {\n            this.product.cover = productMedia;\n            this.product.coverId = productMedia.id;\n        },\n\n        async onAssignLanguagesChange(entityCollection) {\n            this.displayImageConfiguration.languages = entityCollection;\n\n            if (!this.displayImageConfiguration.useImageAsCoverForLanguage || !isArray(this.displayImageConfiguration.useImageAsCoverForLanguage)) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage = [];\n            }\n\n            const codes = [];\n\n            if (entityCollection && entityCollection.length > 0) {\n                entityCollection.forEach((entity) => {\n                    if (this.displayImageConfiguration.useImageAsCoverForLanguage.length === 0 || !this.displayImageConfiguration.useImageAsCoverForLanguage.find(o => o.languageId === entity.id)) {\n                        this.displayImageConfiguration.useImageAsCoverForLanguage.push({languageId: entity.id, name: entity.name, value: false});\n                    }\n\n                    if (entity.locale) {\n                        const code = entity.locale.code.split('-')[1].toLowerCase();\n                        codes.push(code);\n                    }\n                });\n            }\n\n            Shopware.State.commit('acrisProductDisplayImageState/addConfigurationItem', {mediaId: this.productMedia.mediaId, codes: codes});\n\n            if (this.displayImageConfiguration.useImageAsCoverForLanguage.length > 0) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage.forEach((fallbackCover, index) => {\n                    if (!entityCollection.find(o => o.id === fallbackCover.languageId)) {\n                        this.displayImageConfiguration.useImageAsCoverForLanguage.splice(index, 1);\n                    }\n                });\n            }\n        }\n    }\n});\n", "export default \"{% block acris_product_display_image_modal %}\\n    <sw-modal class=\\\"acris-product-display-image-modal\\\"\\n              v-if=\\\"displayImageConfiguration && !isLoading\\\"\\n              :title=\\\"$tc('acris-product-display-image.modal.title')\\\"\\n              @modal-close=\\\"onCancel\\\">\\n\\n        {% block acris_product_display_image_modal_form %}\\n            {% block acris_product_display_image_field_use_as_default_cover %}\\n                <sw-field type=\\\"switch\\\"\\n                          @change=\\\"onStandardCoverChange\\\"\\n                          :disabled=\\\"isStandardCover\\\"\\n                          :value=\\\"isStandardCoverSet\\\"\\n                          class=\\\"acris-product-display-image-use-for-standard-cover-field\\\"\\n                          :label=\\\"$tc('acris-product-display-image.modal.useAsStandardCoverLabelField')\\\"\\n                          :helpText=\\\"$tc('acris-product-display-image.modal.useAsStandardCoverHelpTextField')\\\">\\n                </sw-field>\\n            {% endblock %}\\n\\n            {% block acris_product_display_image_modal_form_language %}\\n                <sw-entity-many-to-many-select\\n                    :label=\\\"$tc('acris-product-display-image.modal.languageLabelField')\\\"\\n                    :placeholder=\\\"$tc('acris-product-display-image.modal.languagePlaceholderField')\\\"\\n                    :helpText=\\\"$tc('acris-product-display-image.modal.languageHelpTextField')\\\"\\n                    :localMode=\\\"true\\\"\\n                    :criteria=\\\"languageCriteria\\\"\\n                    @change=\\\"onAssignLanguagesChange\\\"\\n                    v-model=\\\"displayImageConfiguration.languages\\\">\\n                </sw-entity-many-to-many-select>\\n            {% endblock %}\\n\\n            {% block acris_product_display_image_field_use_cover %}\\n                <sw-field v-if=\\\"displayImageConfiguration.useImageAsCoverForLanguage.length > 0\\\"\\n                          v-for=\\\"(useCoverForLanguage, index) in displayImageConfiguration.useImageAsCoverForLanguage\\\"\\n                          type=\\\"switch\\\"\\n                          @change=\\\"onCoverChange($event, index)\\\"\\n                          :value=\\\"useCoverForLanguage.value\\\"\\n                          class=\\\"acris-product-display-image-use-for-cover-field\\\"\\n                          :label=\\\"useForCoverLabel(useCoverForLanguage)\\\"\\n                          :helpText=\\\"useForCoverHelpText(useCoverForLanguage)\\\">\\n                </sw-field>\\n            {% endblock %}\\n        {% endblock %}\\n\\n        {% block acris_product_display_image_modal_footer %}\\n            <template slot=\\\"modal-footer\\\">\\n\\n                {% block acris_product_display_image_modal_footer_button_cancel %}\\n                    <sw-button size=\\\"small\\\" @click=\\\"onCancel\\\">\\n                        {{ $tc('global.default.cancel') }}\\n                    </sw-button>\\n                {% endblock %}\\n\\n                {% block acris_product_display_image_modal_footer_button_apply %}\\n                    <sw-button variant=\\\"primary\\\" size=\\\"small\\\" @click=\\\"onApply\\\">\\n                        {{ $tc('global.default.apply') }}\\n                    </sw-button>\\n                {% endblock %}\\n            </template>\\n        {% endblock %}\\n    </sw-modal>\\n{% endblock %}\\n\";", "import template from './sw-product-media-form.html.twig';\n\nconst { Component } = Shopware;\n\nComponent.override('sw-product-media-form', {\n    template,\n\n    computed: {\n        mediaItems() {\n            const mediaItems = this.$super('mediaItems');\n            const configuredImages = [];\n            const configuredISO = [];\n\n            if (this.newEntities) {\n                this.newEntities.forEach((entity) => {\n                    mediaItems.forEach((productMedia) => {\n                        if (productMedia.id === entity.productMediaId) {\n                            productMedia.extensions.acrisProductDisplayImage = entity;\n                        }\n                    });\n                });\n            }\n\n            mediaItems.forEach((productMedia) => {\n                const codes = [];\n                if (productMedia.extensions && productMedia.extensions.acrisProductDisplayImage && productMedia.extensions.acrisProductDisplayImage.languages && productMedia.extensions.acrisProductDisplayImage.languages.length > 0) {\n                    configuredImages.push(productMedia.mediaId);\n\n                    productMedia.extensions.acrisProductDisplayImage.languages.forEach((language) => {\n                        if (language.locale) {\n                            const code = language.locale.code.split('-')[1].toLowerCase();\n                            codes.push(code);\n                        }\n                    });\n\n                    configuredISO.push({mediaId: productMedia.mediaId, codes: codes});\n                }\n            });\n\n            Shopware.State.commit('acrisProductDisplayImageState/setConfiguredItems', configuredImages);\n            Shopware.State.commit('acrisProductDisplayImageState/setConfiguredISO', configuredISO);\n\n            return mediaItems;\n        },\n\n        newEntities() {\n            return Shopware.State.get('acrisProductDisplayImageState').newEntities;\n        },\n\n        isConfigured() {\n            return Shopware.State.get('acrisProductDisplayImageState').configuredImages.length > 0;\n        },\n\n        product() {\n            const state = Shopware.State.get('swProductDetail');\n\n            if (this.isInherited) {\n                return state.parentProduct;\n            }\n\n            return state.product;\n        },\n\n        isEditMode() {\n            return Shopware.State.get('acrisProductDisplayImageState').isEditMode;\n        },\n\n        displayImageMediaId() {\n            return Shopware.State.get('acrisProductDisplayImageState').mediaId;\n        },\n\n        displayImageRepository() {\n            return this.repositoryFactory.create('acris_product_display_image');\n        },\n\n        displayImageMedia() {\n            let displayImageMedia = this.mediaItems.find(o => o.mediaId === this.displayImageMediaId);\n\n            if (!displayImageMedia.extensions.acrisProductDisplayImage || !displayImageMedia.extensions.acrisProductDisplayImage.id) {\n                displayImageMedia.extensions.acrisProductDisplayImage = this.displayImageRepository.create(Shopware.Context.api);\n                displayImageMedia.extensions.acrisProductDisplayImage.productMediaId = displayImageMedia.id;\n            }\n\n            return displayImageMedia;\n        }\n    },\n\n    methods: {\n        isCover() {\n            return false;\n        },\n\n        onEditProductDisplayImageSave() {\n            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', false);\n            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', null);\n        },\n\n        onEditProductDisplayImageClose() {\n            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', false);\n            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', null);\n        }\n    }\n});\n", "export default \"{% block sw_product_media_form_grid %}\\n    {% block acris_product_display_image_form_grid_configured_info %}\\n        <div v-if=\\\"isConfigured\\\">\\n            <sw-alert class=\\\"acris-product-display-image-grid__configured-info\\\" variant=\\\"info\\\">\\n                {{ $tc('acris-product-display-image.product-media.configuredInfo') }}\\n            </sw-alert>\\n        </div>\\n    {% endblock %}\\n\\n    {% parent %}\\n\\n    {% block acris_product_display_image_form_edit_modal %}\\n        <acris-edit-display-image-modal v-if=\\\"isEditMode\\\"\\n                                   :displayImageItem=\\\"displayImageMedia.extensions.acrisProductDisplayImage\\\"\\n                                   :productMedia=\\\"displayImageMedia\\\"\\n                                   :product=\\\"product\\\"\\n                                   @modal-save=\\\"onEditProductDisplayImageSave\\\"\\n                                   @modal-close=\\\"onEditProductDisplayImageClose\\\">\\n        </acris-edit-display-image-modal>\\n    {% endblock %}\\n{% endblock %}\\n\";", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-product-image.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"4f254878\", content, true, {});", "const { Component } = Shopware;\n\nComponent.override('sw-product-detail', {\n    computed: {\n        productCriteria() {\n            const criteria = this.$super('productCriteria');\n            criteria.addAssociation('media.acrisProductDisplayImage.languages.locale');\n            criteria.addAssociation('cover.acrisProductDisplayImage.languages.locale');\n            return criteria;\n        },\n\n        newEntities() {\n            return Shopware.State.get('acrisProductDisplayImageState').newEntities;\n        }\n    },\n\n    methods: {\n        onSave() {\n            if (this.product && this.newEntities) {\n                this.newEntities.forEach((entity) => {\n                    this.product.media.forEach((productMedia) => {\n                        if (productMedia.id === entity.productMediaId) {\n                            productMedia.extensions.acrisProductDisplayImage = entity;\n                        }\n                    });\n\n                    if (this.product.cover && this.product.cover.id === entity.productMediaId) {\n                        this.product.cover.extensions.acrisProductDisplayImage = entity;\n                    }\n                });\n                Shopware.State.commit('acrisProductDisplayImageState/resetNewEntities');\n            }\n            this.$super('onSave');\n        }\n    }\n});\n", "Shopware.State.registerModule('acrisProductDisplayImageState', {\n    namespaced: true,\n\n    state: {\n        isEditMode: false,\n        mediaId: null,\n        newEntities: [],\n        configuredImages: [],\n        configuredISO: []\n    },\n\n    mutations: {\n        setConfiguredItems(state, configuredItems) {\n            state.configuredImages = configuredItems;\n        },\n\n        addConfigurationItem(state, configuredItem) {\n            const index = state.configuredISO.findIndex(item => item.mediaId === configuredItem.mediaId);\n\n            if (index !== -1) {\n                state.configuredISO[index] = configuredItem;\n            } else {\n                state.configuredISO.push(configuredItem);\n            }\n        },\n\n        setConfiguredISO(state, configuredItems) {\n            state.configuredISO = configuredItems;\n        },\n\n        setEditMode(state, isEditMode) {\n            state.isEditMode = isEditMode;\n        },\n\n        setMediaId(state, mediaId) {\n            state.mediaId = mediaId;\n        },\n\n        addNewEntity(state, addNewEntity) {\n            if (!state.newEntities.find(o => o.id === addNewEntity.productMediaId)) {\n                state.newEntities.push(addNewEntity);\n            }\n        },\n\n        resetNewEntities(state) {\n            state.newEntities = [];\n        },\n\n        closeEditMode(state) {\n            state.isEditMode = false;\n        },\n    }\n});\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./acris-edit-display-image-modal.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"88d2fb96\", content, true, {});"], "sourceRoot": ""}