{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/base/sw-product-image/sw-product-image.scss", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/state/acris-product-display-image.state.js", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/base/sw-product-image/index.js", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/base/sw-product-image/sw-product-image.html.twig", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/acris-edit-display-image-modal/index.js", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/acris-edit-display-image-modal/acris-edit-display-image-modal.html.twig", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/sw-product-media-form/index.js", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/sw-product-media-form/sw-product-media-form.html.twig", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/sw-product/page/sw-product-detail/index.js", "webpack:////var/www/html/custom/plugins/AcrisProductDisplayImage/src/Resources/app/administration/src/extension/component/acris-edit-display-image-modal/acris-edit-display-image-modal.scss"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "content", "default", "locals", "add", "Shopware", "State", "registerModule", "namespaced", "state", "isEditMode", "mediaId", "newEntities", "configuredImages", "configuredISO", "mutations", "setConfiguredItems", "configuredItems", "addConfigurationItem", "configuredItem", "index", "findIndex", "item", "push", "setConfiguredISO", "setEditMode", "setMediaId", "addNewEntity", "find", "id", "productMediaId", "resetNewEntities", "closeEditMode", "listToStyles", "parentId", "list", "styles", "newStyles", "length", "part", "css", "media", "sourceMap", "parts", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "type", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "applyToTag", "newObj", "textStore", "replaceText", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "Component", "override", "template", "created", "this", "createdComponent", "computed", "isConfigured", "includes", "getCodesById", "ISOCodes", "methods", "isCover", "mediaArray", "entry", "codes", "onProductMediaEdit", "commit", "getCountryFlag", "code", "replace", "_regeneratorRuntime", "Op", "hasOwn", "desc", "$Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "define", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "context", "Context", "makeInvokeMethod", "tryCatch", "fn", "arg", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "_typeof", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "undefined", "return", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "_asyncToGenerator", "args", "arguments", "apply", "Criteria", "Data", "cloneDeep", "Utils", "isArray", "types", "register", "inject", "props", "displayImageItem", "required", "productMedia", "product", "data", "isLoading", "isStandardCoverSet", "displayImageConfiguration", "preConfiguredCover", "configureCoverForLanguages", "isStandardCover", "coverId", "cover", "isPlaceholder", "displayImageRepository", "repositoryFactory", "languageCriteria", "criteria", "addAssociation", "_this", "_isNew", "addFilter", "equals", "search", "api", "res", "save", "displayImage", "languages", "checkPreConfiguredCover", "first", "_this2", "useImageAsCoverForLanguage", "languageId", "onCancel", "$emit", "onApply", "assign", "markMediaAsCover", "markMediaAsConfiguredCover", "_this3", "extensions", "acrisProductDisplayImage", "configuredImageAsCoverForLanguage", "useForCoverLabel", "language", "$tc", "useForCoverHelpText", "onCoverChange", "_this4", "_callee", "_context", "preConfigured", "onStandardCoverChange", "_this5", "_callee2", "_context2", "onAssignLanguagesChange", "entityCollection", "_this6", "_callee3", "_context3", "entity", "locale", "split", "fallbackCover", "splice", "mediaItems", "$super", "isInherited", "parentProduct", "displayImageMediaId", "displayImageMedia", "onEditProductDisplayImageSave", "onEditProductDisplayImageClose", "productCriteria", "onSave"], "mappings": ";aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,qCAIjBlC,EAAoBA,EAAoBmC,EAAI,Q,uBC/ErD,IAAIC,EAAU,EAAQ,QACnBA,EAAQZ,aAAYY,EAAUA,EAAQC,SACnB,iBAAZD,IAAsBA,EAAU,CAAC,CAACjC,EAAOC,EAAIgC,EAAS,MAC7DA,EAAQE,SAAQnC,EAAOD,QAAUkC,EAAQE,SAG/BC,EADH,EAAQ,QAAyJF,SAC1J,WAAYD,GAAS,EAAM,K,mBCT5CI,SAASC,MAAMC,eAAe,gCAAiC,CAC3DC,YAAY,EAEZC,MAAO,CACHC,YAAY,EACZC,QAAS,KACTC,YAAa,GACbC,iBAAkB,GAClBC,cAAe,IAGnBC,UAAW,CACPC,mBAAkB,SAACP,EAAOQ,GACtBR,EAAMI,iBAAmBI,GAG7BC,qBAAoB,SAACT,EAAOU,GACxB,IAAMC,EAAQX,EAAMK,cAAcO,WAAU,SAAAC,GAAI,OAAIA,EAAKX,UAAYQ,EAAeR,YAErE,IAAXS,EACAX,EAAMK,cAAcM,GAASD,EAE7BV,EAAMK,cAAcS,KAAKJ,IAIjCK,iBAAgB,SAACf,EAAOQ,GACpBR,EAAMK,cAAgBG,GAG1BQ,YAAW,SAAChB,EAAOC,GACfD,EAAMC,WAAaA,GAGvBgB,WAAU,SAACjB,EAAOE,GACdF,EAAME,QAAUA,GAGpBgB,aAAY,SAAClB,EAAOkB,GACXlB,EAAMG,YAAYgB,MAAK,SAAAlD,GAAC,OAAIA,EAAEmD,KAAOF,EAAaG,mBACnDrB,EAAMG,YAAYW,KAAKI,IAI/BI,iBAAgB,SAACtB,GACbA,EAAMG,YAAc,IAGxBoB,cAAa,SAACvB,GACVA,EAAMC,YAAa,O,kCC7ChB,SAASuB,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPpE,EAAI,EAAGA,EAAIkE,EAAKG,OAAQrE,IAAK,CACpC,IAAIqD,EAAOa,EAAKlE,GACZ4D,EAAKP,EAAK,GAIViB,EAAO,CACTV,GAAIK,EAAW,IAAMjE,EACrBuE,IALQlB,EAAK,GAMbmB,MALUnB,EAAK,GAMfoB,UALcpB,EAAK,IAOhBe,EAAUR,GAGbQ,EAAUR,GAAIc,MAAMpB,KAAKgB,GAFzBH,EAAOb,KAAKc,EAAUR,GAAM,CAAEA,GAAIA,EAAIc,MAAO,CAACJ,KAKlD,OAAOH,E,+CCjBT,IAAIQ,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiB5B,EAAUC,EAAM4B,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAI5B,EAASH,EAAaC,EAAUC,GAGpC,OAFA8B,EAAe7B,GAER,SAAiB8B,GAEtB,IADA,IAAIC,EAAY,GACPlG,EAAI,EAAGA,EAAImE,EAAOE,OAAQrE,IAAK,CACtC,IAAIqD,EAAOc,EAAOnE,IACdmG,EAAWpB,EAAY1B,EAAKO,KACvBwC,OACTF,EAAU5C,KAAK6C,GAEbF,EAEFD,EADA7B,EAASH,EAAaC,EAAUgC,IAGhC9B,EAAS,GAEX,IAASnE,EAAI,EAAGA,EAAIkG,EAAU7B,OAAQrE,IAAK,CACzC,IAAImG,EACJ,GAAsB,KADlBA,EAAWD,EAAUlG,IACZoG,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASzB,MAAML,OAAQgC,IACzCF,EAASzB,MAAM2B,YAEVtB,EAAYoB,EAASvC,OAMpC,SAASoC,EAAgB7B,GACvB,IAAK,IAAInE,EAAI,EAAGA,EAAImE,EAAOE,OAAQrE,IAAK,CACtC,IAAIqD,EAAOc,EAAOnE,GACdmG,EAAWpB,EAAY1B,EAAKO,IAChC,GAAIuC,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASzB,MAAML,OAAQgC,IACzCF,EAASzB,MAAM2B,GAAGhD,EAAKqB,MAAM2B,IAE/B,KAAOA,EAAIhD,EAAKqB,MAAML,OAAQgC,IAC5BF,EAASzB,MAAMpB,KAAKgD,EAASjD,EAAKqB,MAAM2B,KAEtCF,EAASzB,MAAML,OAAShB,EAAKqB,MAAML,SACrC8B,EAASzB,MAAML,OAAShB,EAAKqB,MAAML,YAEhC,CACL,IAAIK,EAAQ,GACZ,IAAS2B,EAAI,EAAGA,EAAIhD,EAAKqB,MAAML,OAAQgC,IACrC3B,EAAMpB,KAAKgD,EAASjD,EAAKqB,MAAM2B,KAEjCtB,EAAY1B,EAAKO,IAAM,CAAEA,GAAIP,EAAKO,GAAIwC,KAAM,EAAG1B,MAAOA,KAK5D,SAAS6B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAaE,KAAO,WACpB1B,EAAK2B,YAAYH,GACVA,EAGT,SAASF,EAAUM,GACjB,IAAIC,EAAQC,EACRN,EAAe5B,SAASmC,cAAc,SAAWxB,EAAW,MAAQqB,EAAIhD,GAAK,MAEjF,GAAI4C,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaQ,WAAWC,YAAYT,GAIxC,GAAIhB,EAAS,CAEX,IAAI0B,EAAa/B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDM,EAASM,EAAoB3F,KAAK,KAAMgF,EAAcU,GAAY,GAClEJ,EAASK,EAAoB3F,KAAK,KAAMgF,EAAcU,GAAY,QAGlEV,EAAeD,IACfM,EAASO,EAAW5F,KAAK,KAAMgF,GAC/BM,EAAS,WACPN,EAAaQ,WAAWC,YAAYT,IAMxC,OAFAK,EAAOD,GAEA,SAAsBS,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO9C,MAAQqC,EAAIrC,KACnB8C,EAAO7C,QAAUoC,EAAIpC,OACrB6C,EAAO5C,YAAcmC,EAAInC,UAC3B,OAEFoC,EAAOD,EAAMS,QAEbP,KAKN,IACMQ,EADFC,GACED,EAAY,GAET,SAAUnE,EAAOqE,GAEtB,OADAF,EAAUnE,GAASqE,EACZF,EAAUG,OAAOC,SAASC,KAAK,QAI1C,SAASR,EAAqBX,EAAcrD,EAAO2D,EAAQF,GACzD,IAAIrC,EAAMuC,EAAS,GAAKF,EAAIrC,IAE5B,GAAIiC,EAAaoB,WACfpB,EAAaoB,WAAWC,QAAUN,EAAYpE,EAAOoB,OAChD,CACL,IAAIuD,EAAUlD,SAASmD,eAAexD,GAClCyD,EAAaxB,EAAawB,WAC1BA,EAAW7E,IAAQqD,EAAaS,YAAYe,EAAW7E,IACvD6E,EAAW3D,OACbmC,EAAayB,aAAaH,EAASE,EAAW7E,IAE9CqD,EAAaG,YAAYmB,IAK/B,SAASV,EAAYZ,EAAcI,GACjC,IAAIrC,EAAMqC,EAAIrC,IACVC,EAAQoC,EAAIpC,MACZC,EAAYmC,EAAInC,UAiBpB,GAfID,GACFgC,EAAa0B,aAAa,QAAS1D,GAEjCc,EAAQ6C,OACV3B,EAAa0B,aAAa3C,EAAUqB,EAAIhD,IAGtCa,IAGFF,GAAO,mBAAqBE,EAAU2D,QAAQ,GAAK,MAEnD7D,GAAO,uDAAyD8D,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUhE,MAAgB,OAG9H+B,EAAaoB,WACfpB,EAAaoB,WAAWC,QAAUtD,MAC7B,CACL,KAAOiC,EAAakC,YAClBlC,EAAaS,YAAYT,EAAakC,YAExClC,EAAaG,YAAY/B,SAASmD,eAAexD,O,2GCxN/BnC,SAAduG,UAEEC,SAAS,mBAAoB,CACnCC,SCNW,yxBDQXC,QAAO,WACHC,KAAKC,oBAGTC,SAAU,CACNC,aAAY,WACR,OAAO9G,SAASC,MAAMxB,IAAI,iCAAiC+B,iBAAiByB,OAAS,GAAKjC,SAASC,MAAMxB,IAAI,iCAAiC+B,iBAAiBuG,SAASJ,KAAKrG,UAAYN,SAASC,MAAMxB,IAAI,iCAAiCgC,cAAcwB,OAAS,GAAK0E,KAAKK,aAAahH,SAASC,MAAMxB,IAAI,iCAAiCgC,cAAekG,KAAKrG,UAAYqG,KAAKK,aAAahH,SAASC,MAAMxB,IAAI,iCAAiCgC,cAAekG,KAAKrG,SAAS2B,OAAS,GAG/dgF,SAAQ,WACJ,IAAMxG,EAAgBT,SAASC,MAAMxB,IAAI,iCAAiCgC,cAC1E,OAAOkG,KAAKK,aAAavG,EAAekG,KAAKrG,WAIrD4G,QAAS,CACLN,iBAAgB,WACZD,KAAKQ,SAAU,GAGnBH,aAAY,SAACI,EAAY5F,GACrB,IAAMP,EAAOmG,EAAW7F,MAAK,SAAA8F,GAAK,OAAIA,EAAM/G,UAAYkB,KACxD,OAAOP,EAAOA,EAAKqG,MAAQ,IAI/BC,mBAAkB,WACdvH,SAASC,MAAMuH,OAAO,6CAA6C,GACnExH,SAASC,MAAMuH,OAAO,2CAA4Cb,KAAKrG,UAG3EmH,eAAc,SAACC,GACX,MAAO,gDAAgDC,QAAQ,oBAAqBD,O,4PEvChGE,EAAA,kBAAAlK,GAAA,IAAAA,EAAA,GAAAmK,EAAAvJ,OAAAkB,UAAAsI,EAAAD,EAAApI,eAAAlB,EAAAD,OAAAC,gBAAA,SAAAiG,EAAArF,EAAA4I,GAAAvD,EAAArF,GAAA4I,EAAAlJ,OAAAmJ,EAAA,mBAAArJ,cAAA,GAAAsJ,EAAAD,EAAAE,UAAA,aAAAC,EAAAH,EAAAI,eAAA,kBAAAC,EAAAL,EAAApJ,aAAA,yBAAA0J,EAAA9D,EAAArF,EAAAN,GAAA,OAAAP,OAAAC,eAAAiG,EAAArF,EAAA,CAAAN,QAAAL,YAAA,EAAA+J,cAAA,EAAAC,UAAA,IAAAhE,EAAArF,GAAA,IAAAmJ,EAAA,aAAAG,GAAAH,EAAA,SAAA9D,EAAArF,EAAAN,GAAA,OAAA2F,EAAArF,GAAAN,GAAA,SAAA6J,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAH,KAAApJ,qBAAAwJ,EAAAJ,EAAAI,EAAAC,EAAA3K,OAAAY,OAAA6J,EAAAvJ,WAAA0J,EAAA,IAAAC,EAAAL,GAAA,WAAAvK,EAAA0K,EAAA,WAAApK,MAAAuK,EAAAT,EAAAE,EAAAK,KAAAD,EAAA,SAAAI,EAAAC,EAAA9E,EAAA+E,GAAA,WAAAjF,KAAA,SAAAiF,IAAAD,EAAAvL,KAAAyG,EAAA+E,IAAA,MAAAd,GAAA,OAAAnE,KAAA,QAAAiF,IAAAd,IAAA/K,EAAAgL,OAAA,IAAAc,EAAA,YAAAR,KAAA,SAAAS,KAAA,SAAAC,KAAA,IAAAC,EAAA,GAAArB,EAAAqB,EAAA1B,GAAA,8BAAA2B,EAAAtL,OAAAuL,eAAAC,EAAAF,OAAAG,EAAA,MAAAD,OAAAjC,GAAAC,EAAA/J,KAAA+L,EAAA7B,KAAA0B,EAAAG,GAAA,IAAAE,EAAAN,EAAAlK,UAAAwJ,EAAAxJ,UAAAlB,OAAAY,OAAAyK,GAAA,SAAAM,EAAAzK,GAAA,0BAAA0K,SAAA,SAAAC,GAAA7B,EAAA9I,EAAA2K,GAAA,SAAAZ,GAAA,YAAAa,QAAAD,EAAAZ,SAAA,SAAAc,EAAApB,EAAAqB,GAAA,SAAAC,EAAAJ,EAAAZ,EAAAiB,EAAAC,GAAA,IAAAC,EAAArB,EAAAJ,EAAAkB,GAAAlB,EAAAM,GAAA,aAAAmB,EAAApG,KAAA,KAAAqG,EAAAD,EAAAnB,IAAA1K,EAAA8L,EAAA9L,MAAA,OAAAA,GAAA,UAAA+L,EAAA/L,IAAAiJ,EAAA/J,KAAAc,EAAA,WAAAyL,EAAAE,QAAA3L,EAAAgM,SAAAC,MAAA,SAAAjM,GAAA0L,EAAA,OAAA1L,EAAA2L,EAAAC,MAAA,SAAAhC,GAAA8B,EAAA,QAAA9B,EAAA+B,EAAAC,MAAAH,EAAAE,QAAA3L,GAAAiM,MAAA,SAAAC,GAAAJ,EAAA9L,MAAAkM,EAAAP,EAAAG,MAAA,SAAAK,GAAA,OAAAT,EAAA,QAAAS,EAAAR,EAAAC,QAAAC,EAAAnB,KAAA,IAAA0B,EAAA1M,EAAA,gBAAAM,MAAA,SAAAsL,EAAAZ,GAAA,SAAA2B,IAAA,WAAAZ,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAZ,EAAAiB,EAAAC,MAAA,OAAAQ,MAAAH,KAAAI,YAAA,SAAA9B,EAAAT,EAAAE,EAAAK,GAAA,IAAA9I,EAAA,iCAAA+J,EAAAZ,GAAA,iBAAAnJ,EAAA,UAAAsC,MAAA,iDAAAtC,EAAA,cAAA+J,EAAA,MAAAZ,EAAA,OAAA4B,IAAA,IAAAjC,EAAAiB,SAAAjB,EAAAK,QAAA,KAAA6B,EAAAlC,EAAAkC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAlC,GAAA,GAAAmC,EAAA,IAAAA,IAAA7B,EAAA,gBAAA6B,GAAA,YAAAnC,EAAAiB,OAAAjB,EAAAqC,KAAArC,EAAAsC,MAAAtC,EAAAK,SAAA,aAAAL,EAAAiB,OAAA,uBAAA/J,EAAA,MAAAA,EAAA,YAAA8I,EAAAK,IAAAL,EAAAuC,kBAAAvC,EAAAK,SAAA,WAAAL,EAAAiB,QAAAjB,EAAAwC,OAAA,SAAAxC,EAAAK,KAAAnJ,EAAA,gBAAAsK,EAAArB,EAAAV,EAAAE,EAAAK,GAAA,cAAAwB,EAAApG,KAAA,IAAAlE,EAAA8I,EAAAyC,KAAA,6BAAAjB,EAAAnB,MAAAC,EAAA,gBAAA3K,MAAA6L,EAAAnB,IAAAoC,KAAAzC,EAAAyC,MAAA,UAAAjB,EAAApG,OAAAlE,EAAA,YAAA8I,EAAAiB,OAAA,QAAAjB,EAAAK,IAAAmB,EAAAnB,OAAA,SAAA+B,EAAAF,EAAAlC,GAAA,IAAA0C,EAAA1C,EAAAiB,SAAAiB,EAAAlD,SAAA0D,GAAA,QAAAC,IAAA1B,EAAA,OAAAjB,EAAAkC,SAAA,eAAAQ,GAAAR,EAAAlD,SAAA4D,SAAA5C,EAAAiB,OAAA,SAAAjB,EAAAK,SAAAsC,EAAAP,EAAAF,EAAAlC,GAAA,UAAAA,EAAAiB,SAAA,WAAAyB,IAAA1C,EAAAiB,OAAA,QAAAjB,EAAAK,IAAA,IAAAwC,UAAA,oCAAAH,EAAA,aAAApC,EAAA,IAAAkB,EAAArB,EAAAc,EAAAiB,EAAAlD,SAAAgB,EAAAK,KAAA,aAAAmB,EAAApG,KAAA,OAAA4E,EAAAiB,OAAA,QAAAjB,EAAAK,IAAAmB,EAAAnB,IAAAL,EAAAkC,SAAA,KAAA5B,EAAA,IAAAwC,EAAAtB,EAAAnB,IAAA,OAAAyC,IAAAL,MAAAzC,EAAAkC,EAAAa,YAAAD,EAAAnN,MAAAqK,EAAAgD,KAAAd,EAAAe,QAAA,WAAAjD,EAAAiB,SAAAjB,EAAAiB,OAAA,OAAAjB,EAAAK,SAAAsC,GAAA3C,EAAAkC,SAAA,KAAA5B,GAAAwC,GAAA9C,EAAAiB,OAAA,QAAAjB,EAAAK,IAAA,IAAAwC,UAAA,oCAAA7C,EAAAkC,SAAA,KAAA5B,GAAA,SAAA4C,EAAAC,GAAA,IAAAhF,EAAA,CAAAiF,OAAAD,EAAA,SAAAA,IAAAhF,EAAAkF,SAAAF,EAAA,SAAAA,IAAAhF,EAAAmF,WAAAH,EAAA,GAAAhF,EAAAoF,SAAAJ,EAAA,SAAAK,WAAAxL,KAAAmG,GAAA,SAAAsF,EAAAtF,GAAA,IAAAqD,EAAArD,EAAAuF,YAAA,GAAAlC,EAAApG,KAAA,gBAAAoG,EAAAnB,IAAAlC,EAAAuF,WAAAlC,EAAA,SAAAvB,EAAAL,GAAA,KAAA4D,WAAA,EAAAJ,OAAA,SAAAxD,EAAAoB,QAAAkC,EAAA,WAAAS,OAAA,YAAA9C,EAAA+C,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAA7E,GAAA,GAAA8E,EAAA,OAAAA,EAAAhP,KAAA+O,GAAA,sBAAAA,EAAAZ,KAAA,OAAAY,EAAA,IAAAE,MAAAF,EAAA7K,QAAA,KAAArE,GAAA,EAAAsO,EAAA,SAAAA,IAAA,OAAAtO,EAAAkP,EAAA7K,QAAA,GAAA6F,EAAA/J,KAAA+O,EAAAlP,GAAA,OAAAsO,EAAArN,MAAAiO,EAAAlP,GAAAsO,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAArN,WAAAgN,EAAAK,EAAAP,MAAA,EAAAO,GAAA,OAAAA,UAAA,OAAAA,KAAAf,GAAA,SAAAA,IAAA,OAAAtM,WAAAgN,EAAAF,MAAA,UAAAlC,EAAAjK,UAAAkK,EAAAnL,EAAAyL,EAAA,eAAAnL,MAAA6K,EAAAnB,cAAA,IAAAhK,EAAAmL,EAAA,eAAA7K,MAAA4K,EAAAlB,cAAA,IAAAkB,EAAAwD,YAAA3E,EAAAoB,EAAArB,EAAA,qBAAA3K,EAAAwP,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,KAAAE,YAAA,QAAAD,QAAA3D,GAAA,uBAAA2D,EAAAH,aAAAG,EAAAjP,QAAAT,EAAA4P,KAAA,SAAAH,GAAA,OAAA7O,OAAAiP,eAAAjP,OAAAiP,eAAAJ,EAAAzD,IAAAyD,EAAAK,UAAA9D,EAAApB,EAAA6E,EAAA9E,EAAA,sBAAA8E,EAAA3N,UAAAlB,OAAAY,OAAA8K,GAAAmD,GAAAzP,EAAA+P,MAAA,SAAAlE,GAAA,OAAAsB,QAAAtB,IAAAU,EAAAI,EAAA7K,WAAA8I,EAAA+B,EAAA7K,UAAA2I,GAAA,0BAAAzK,EAAA2M,gBAAA3M,EAAAgQ,MAAA,SAAA/E,EAAAC,EAAAC,EAAAC,EAAAwB,QAAA,IAAAA,MAAAqD,SAAA,IAAAC,EAAA,IAAAvD,EAAA3B,EAAAC,EAAAC,EAAAC,EAAAC,GAAAwB,GAAA,OAAA5M,EAAAwP,oBAAAtE,GAAAgF,IAAA1B,OAAApB,MAAA,SAAAH,GAAA,OAAAA,EAAAgB,KAAAhB,EAAA9L,MAAA+O,EAAA1B,WAAAjC,EAAAD,GAAA1B,EAAA0B,EAAA3B,EAAA,aAAAC,EAAA0B,EAAA/B,GAAA,0BAAAK,EAAA0B,EAAA,qDAAAtM,EAAAmQ,KAAA,SAAAC,GAAA,IAAAxO,EAAAhB,OAAAwP,GAAAD,EAAA,WAAA1O,KAAAG,EAAAuO,EAAA3M,KAAA/B,GAAA,OAAA0O,EAAAE,UAAA,SAAA7B,IAAA,KAAA2B,EAAA5L,QAAA,KAAA9C,EAAA0O,EAAAG,MAAA,GAAA7O,KAAAG,EAAA,OAAA4M,EAAArN,MAAAM,EAAA+M,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAP,MAAA,EAAAO,IAAAxO,EAAAqM,SAAAZ,EAAA3J,UAAA,CAAA6N,YAAAlE,EAAA0D,MAAA,SAAAoB,GAAA,QAAAC,KAAA,OAAAhC,KAAA,OAAAX,KAAA,KAAAC,WAAAK,EAAA,KAAAF,MAAA,OAAAP,SAAA,UAAAjB,OAAA,YAAAZ,SAAAsC,EAAA,KAAAa,WAAAxC,QAAAyC,IAAAsB,EAAA,QAAA9P,KAAA,WAAAA,EAAAgQ,OAAA,IAAArG,EAAA/J,KAAA,KAAAI,KAAA6O,OAAA7O,EAAAiQ,MAAA,WAAAjQ,QAAA0N,IAAAwC,KAAA,gBAAA1C,MAAA,MAAA2C,EAAA,KAAA5B,WAAA,GAAAE,WAAA,aAAA0B,EAAAhK,KAAA,MAAAgK,EAAA/E,IAAA,YAAAgF,MAAA9C,kBAAA,SAAA+C,GAAA,QAAA7C,KAAA,MAAA6C,EAAA,IAAAtF,EAAA,cAAAuF,EAAAC,EAAAC,GAAA,OAAAjE,EAAApG,KAAA,QAAAoG,EAAAnB,IAAAiF,EAAAtF,EAAAgD,KAAAwC,EAAAC,IAAAzF,EAAAiB,OAAA,OAAAjB,EAAAK,SAAAsC,KAAA8C,EAAA,QAAA/Q,EAAA,KAAA8O,WAAAzK,OAAA,EAAArE,GAAA,IAAAA,EAAA,KAAAyJ,EAAA,KAAAqF,WAAA9O,GAAA8M,EAAArD,EAAAuF,WAAA,YAAAvF,EAAAiF,OAAA,OAAAmC,EAAA,UAAApH,EAAAiF,QAAA,KAAA4B,KAAA,KAAAU,EAAA9G,EAAA/J,KAAAsJ,EAAA,YAAAwH,EAAA/G,EAAA/J,KAAAsJ,EAAA,iBAAAuH,GAAAC,EAAA,SAAAX,KAAA7G,EAAAkF,SAAA,OAAAkC,EAAApH,EAAAkF,UAAA,WAAA2B,KAAA7G,EAAAmF,WAAA,OAAAiC,EAAApH,EAAAmF,iBAAA,GAAAoC,GAAA,QAAAV,KAAA7G,EAAAkF,SAAA,OAAAkC,EAAApH,EAAAkF,UAAA,YAAAsC,EAAA,UAAAnM,MAAA,kDAAAwL,KAAA7G,EAAAmF,WAAA,OAAAiC,EAAApH,EAAAmF,gBAAAd,OAAA,SAAApH,EAAAiF,GAAA,QAAA3L,EAAA,KAAA8O,WAAAzK,OAAA,EAAArE,GAAA,IAAAA,EAAA,KAAAyJ,EAAA,KAAAqF,WAAA9O,GAAA,GAAAyJ,EAAAiF,QAAA,KAAA4B,MAAApG,EAAA/J,KAAAsJ,EAAA,oBAAA6G,KAAA7G,EAAAmF,WAAA,KAAAsC,EAAAzH,EAAA,OAAAyH,IAAA,UAAAxK,GAAA,aAAAA,IAAAwK,EAAAxC,QAAA/C,MAAAuF,EAAAtC,aAAAsC,EAAA,UAAApE,EAAAoE,IAAAlC,WAAA,UAAAlC,EAAApG,OAAAoG,EAAAnB,MAAAuF,GAAA,KAAA3E,OAAA,YAAA+B,KAAA4C,EAAAtC,WAAAhD,GAAA,KAAAuF,SAAArE,IAAAqE,SAAA,SAAArE,EAAA+B,GAAA,aAAA/B,EAAApG,KAAA,MAAAoG,EAAAnB,IAAA,gBAAAmB,EAAApG,MAAA,aAAAoG,EAAApG,KAAA,KAAA4H,KAAAxB,EAAAnB,IAAA,WAAAmB,EAAApG,MAAA,KAAAiK,KAAA,KAAAhF,IAAAmB,EAAAnB,IAAA,KAAAY,OAAA,cAAA+B,KAAA,kBAAAxB,EAAApG,MAAAmI,IAAA,KAAAP,KAAAO,GAAAjD,GAAAwF,OAAA,SAAAxC,GAAA,QAAA5O,EAAA,KAAA8O,WAAAzK,OAAA,EAAArE,GAAA,IAAAA,EAAA,KAAAyJ,EAAA,KAAAqF,WAAA9O,GAAA,GAAAyJ,EAAAmF,eAAA,YAAAuC,SAAA1H,EAAAuF,WAAAvF,EAAAoF,UAAAE,EAAAtF,GAAAmC,IAAAyF,MAAA,SAAA3C,GAAA,QAAA1O,EAAA,KAAA8O,WAAAzK,OAAA,EAAArE,GAAA,IAAAA,EAAA,KAAAyJ,EAAA,KAAAqF,WAAA9O,GAAA,GAAAyJ,EAAAiF,WAAA,KAAA5B,EAAArD,EAAAuF,WAAA,aAAAlC,EAAApG,KAAA,KAAA4K,EAAAxE,EAAAnB,IAAAoD,EAAAtF,GAAA,OAAA6H,GAAA,UAAAxM,MAAA,0BAAAyM,cAAA,SAAArC,EAAAb,EAAAE,GAAA,YAAAf,SAAA,CAAAlD,SAAA6B,EAAA+C,GAAAb,aAAAE,WAAA,cAAAhC,SAAA,KAAAZ,SAAAsC,GAAArC,IAAA9L,EAAA,SAAA0R,EAAAC,EAAA7E,EAAAC,EAAA6E,EAAAC,EAAApQ,EAAAoK,GAAA,QAAAyC,EAAAqD,EAAAlQ,GAAAoK,GAAA1K,EAAAmN,EAAAnN,MAAA,MAAAmM,GAAA,YAAAP,EAAAO,GAAAgB,EAAAL,KAAAnB,EAAA3L,GAAA8O,QAAAnD,QAAA3L,GAAAiM,KAAAwE,EAAAC,GAAA,SAAAC,EAAAlG,GAAA,sBAAAT,EAAA,KAAA4G,EAAAC,UAAA,WAAA/B,SAAA,SAAAnD,EAAAC,GAAA,IAAA4E,EAAA/F,EAAAqG,MAAA9G,EAAA4G,GAAA,SAAAH,EAAAzQ,GAAAuQ,EAAAC,EAAA7E,EAAAC,EAAA6E,EAAAC,EAAA,OAAA1Q,GAAA,SAAA0Q,EAAA9G,GAAA2G,EAAAC,EAAA7E,EAAAC,EAAA6E,EAAAC,EAAA,QAAA9G,GAAA6G,OAAAzD,OAEA,IAAQtF,EAAcvG,SAAduG,UACAqJ,EAAa5P,SAAS6P,KAAtBD,SACAE,EAAc9P,SAAS+P,MAAMzQ,OAA7BwQ,UACAE,EAAYhQ,SAAS+P,MAAME,MAA3BD,QAERzJ,EAAU2J,SAAS,iCAAkC,CACjDzJ,SCTW,mrGDWX0J,OAAQ,CAAC,qBAETC,MAAO,CACHC,iBAAkB,CACd/L,KAAMhG,OACNgS,UAAU,GAGdC,aAAc,CACVjM,KAAMhG,OACNgS,UAAU,GAGdE,QAAS,CACLlM,KAAMhG,OACNgS,UAAU,IAIlBG,KAAI,WACA,MAAO,CACHC,WAAW,EACXC,oBAAoB,EACpBC,0BAA2B,KAC3BC,mBAAoB,GACpBC,2BAA4B,KAIpCjK,SAAU,CACNkK,gBAAe,WACX,IAAMC,EAAUrK,KAAK6J,QAAQS,MAAQtK,KAAK6J,QAAQS,MAAMzP,GAAKmF,KAAK6J,QAAQQ,QAE1E,QAAkC,IAA9BrK,KAAK6J,QAAQpO,MAAMH,QAAgB0E,KAAK4J,aAAaW,gBAAkBF,IAIpErK,KAAK4J,aAAa/O,KAAOwP,GAGpCG,uBAAsB,WAClB,OAAOxK,KAAKyK,kBAAkBlS,OAAO,gCAGzCmS,iBAAgB,WACZ,IAAMC,EAAW,IAAI1B,EAGrB,OAFA0B,EAASC,eAAe,UAEjBD,IAIf5K,QAAO,WACHC,KAAKC,oBAGTM,QAAS,CACLN,iBAAgB,WAAI,IAAD4K,EAAA,KACf,GAAI7K,KAAK0J,iBAAiBoB,OAAQ,CAC9B9K,KAAK+J,WAAY,EACjB,IAAMY,EAAW,IAAI1B,EACrB0B,EAASI,UAAU9B,EAAS+B,OAAO,iBAAkBhL,KAAK4J,aAAa/O,KACvEmF,KAAKwK,uBAAuBS,OAAON,EAAUtR,SAASmJ,QAAQ0I,KAAK/G,MAAK,SAACgH,GACrE,GAAIA,EAAI7P,QAAU,EACd,OAAOuP,EAAKL,uBAAuBY,KAAKP,EAAKnB,kBAAkBvF,MAAK,WAChE0G,EAAKL,uBAAuB1S,IACxB+S,EAAKnB,iBAAiB7O,GACtBxB,SAASmJ,QAAQ0I,KACnB/G,MAAK,SAACkH,GACJR,EAAKd,WAAY,EACjBc,EAAKnB,iBAAmB2B,EACxBR,EAAKZ,0BAA4Bd,EAAU0B,EAAKnB,kBAChDmB,EAAKZ,0BAA0BqB,UAAYT,EAAKnB,iBAAiB4B,UACjET,EAAKU,0BACLV,EAAKb,mBAAqBa,EAAKT,mBAChC9B,OAAM,WACLuC,EAAKd,WAAY,QAEtBzB,OAAM,WACLuC,EAAKd,WAAY,KAGrBc,EAAKd,WAAY,EACjBc,EAAKnB,iBAAmByB,EAAIK,QAC5BX,EAAKZ,0BAA4Bd,EAAU0B,EAAKnB,kBAChDmB,EAAKZ,0BAA0BqB,UAAYT,EAAKnB,iBAAiB4B,UACjET,EAAKU,0BACLV,EAAKb,mBAAqBa,EAAKT,wBAIvCpK,KAAKiK,0BAA4Bd,EAAUnJ,KAAK0J,kBAChD1J,KAAKiK,0BAA0BqB,UAAYtL,KAAK0J,iBAAiB4B,UACjEtL,KAAKuL,0BACLvL,KAAKgK,mBAAqBhK,KAAKoK,iBAIvCmB,wBAAuB,WAAI,IAADE,EAAA,KACjBzL,KAAKiK,0BAA0BqB,WAAiE,IAApDtL,KAAKiK,0BAA0BqB,UAAUhQ,SACtF0E,KAAKiK,0BAA0ByB,2BAA6B,IAG5D1L,KAAKiK,0BAA0ByB,4BAA8B1L,KAAKiK,0BAA0ByB,2BAA2BpQ,OAAS,GAChI0E,KAAKiK,0BAA0ByB,2BAA2BnI,SAAQ,SAAC+G,IAC3C,IAAhBA,EAAMpS,OACNuT,EAAKvB,mBAAmB3P,KAAK,CAACoR,WAAYrB,EAAMqB,WAAYzT,MAAOoS,EAAMpS,YAMzF0T,SAAQ,WACJ5L,KAAKmK,2BAA6B,GAClCnK,KAAKkK,mBAAqB,GAC1BlK,KAAKiK,0BAA4B,KACjCjK,KAAK6L,MAAM,gBAGfC,QAAO,WACHnU,OAAOoU,OAAO/L,KAAK0J,iBAAkB1J,KAAKiK,2BAEtCjK,KAAKgK,oBACLhK,KAAKgM,iBAAiBhM,KAAK4J,cAG3B5J,KAAKmK,4BAA8BnK,KAAKmK,2BAA2B7O,OAAS,GAC5E0E,KAAKiM,6BAGT5S,SAASC,MAAMuH,OAAO,6CAA8Cb,KAAK0J,kBACzE1J,KAAK6L,MAAM,aAAc7L,KAAK0J,mBAGlCuC,2BAA0B,WAAI,IAADC,EAAA,KACrBlM,KAAK6J,SAAW7J,KAAK6J,QAAQpO,OAC7BuE,KAAK6J,QAAQpO,MAAM8H,SAAQ,SAACqG,GACpBA,EAAa/O,KAAOqR,EAAKtC,aAAa/O,IAAM+O,EAAauC,YAAcvC,EAAauC,WAAWC,0BAA4BxC,EAAauC,WAAWC,yBAAyBV,4BAA8BrC,EAAQO,EAAauC,WAAWC,yBAAyBV,6BAA+B9B,EAAauC,WAAWC,yBAAyBV,2BAA2BpQ,OAAS,GACvXsO,EAAauC,WAAWC,yBAAyBV,2BAA2BnI,SAAQ,SAACmI,GAC7EQ,EAAK/B,2BAA2BvP,MAAK,SAAAyR,GAAiC,OAAIA,EAAkCV,aAAeD,EAA2BC,gBACtJD,EAA2BxT,OAAQ,UAQ3DoU,iBAAgB,SAACC,GACb,OAAOvM,KAAKwM,IACR,0DAA2D,EAAG,CAACD,SAAUA,EAAS/U,QAI1FiV,oBAAmB,SAACF,GAChB,OAAOvM,KAAKwM,IACR,6DAA8D,EAAG,CAACD,SAAUA,EAAS/U,QAIvFkV,cAAa,SAACxU,EAAOkC,GAAQ,IAADuS,EAAA,YAAA9D,EAAA5H,IAAA0F,MAAA,SAAAiG,IAAA,OAAA3L,IAAAc,MAAA,SAAA8K,GAAA,cAAAA,EAAAtF,KAAAsF,EAAAtH,MAAA,OAC9BoH,EAAK1C,0BAA0ByB,2BAA2BtR,GAAOlC,MAAQA,GAC3D,IAAVA,IACuC,IAAnCyU,EAAKzC,mBAAmB5O,QAAiBqR,EAAKzC,mBAAmBtP,MAAK,SAAAkS,GAAa,OAAIA,EAAcnB,aAAegB,EAAK1C,0BAA0ByB,2BAA2BtR,GAAOuR,eAAgBgB,EAAKxC,2BAA2BvP,MAAK,SAAAkS,GAAa,OAAIA,EAAcnB,aAAegB,EAAK1C,0BAA0ByB,2BAA2BtR,GAAOuR,eACzVgB,EAAKxC,2BAA2B5P,KAAK,CAACoR,WAAYgB,EAAK1C,0BAA0ByB,2BAA2BtR,GAAOuR,cAE1H,wBAAAkB,EAAAnF,UAAAkF,MAN6B/D,IAS5BkE,sBAAqB,SAAC7U,GAAQ,IAAD8U,EAAA,YAAAnE,EAAA5H,IAAA0F,MAAA,SAAAsG,IAAA,OAAAhM,IAAAc,MAAA,SAAAmL,GAAA,cAAAA,EAAA3F,KAAA2F,EAAA3H,MAAA,OAC/ByH,EAAKhD,mBAAqB9R,EAAM,wBAAAgV,EAAAxF,UAAAuF,MADDpE,IAInCmD,iBAAgB,SAACpC,GACb5J,KAAK6J,QAAQS,MAAQV,EACrB5J,KAAK6J,QAAQQ,QAAUT,EAAa/O,IAGlCsS,wBAAuB,SAACC,GAAmB,IAADC,EAAA,YAAAxE,EAAA5H,IAAA0F,MAAA,SAAA2G,IAAA,IAAA3M,EAAA,OAAAM,IAAAc,MAAA,SAAAwL,GAAA,cAAAA,EAAAhG,KAAAgG,EAAAhI,MAAA,OAC5C8H,EAAKpD,0BAA0BqB,UAAY8B,EAEtCC,EAAKpD,0BAA0ByB,4BAA+BrC,EAAQgE,EAAKpD,0BAA0ByB,8BACtG2B,EAAKpD,0BAA0ByB,2BAA6B,IAG1D/K,EAAQ,GAEVyM,GAAoBA,EAAiB9R,OAAS,GAC9C8R,EAAiB7J,SAAQ,SAACiK,GAKtB,GAJyE,IAArEH,EAAKpD,0BAA0ByB,2BAA2BpQ,QAAiB+R,EAAKpD,0BAA0ByB,2BAA2B9Q,MAAK,SAAAlD,GAAC,OAAIA,EAAEiU,aAAe6B,EAAO3S,OACvKwS,EAAKpD,0BAA0ByB,2BAA2BnR,KAAK,CAACoR,WAAY6B,EAAO3S,GAAIrD,KAAMgW,EAAOhW,KAAMU,OAAO,IAGjHsV,EAAOC,OAAQ,CACf,IAAM1M,EAAOyM,EAAOC,OAAO1M,KAAK2M,MAAM,KAAK,GAAG7Q,cAC9C8D,EAAMpG,KAAKwG,OAKvB1H,SAASC,MAAMuH,OAAO,qDAAsD,CAAClH,QAAS0T,EAAKzD,aAAajQ,QAASgH,MAAOA,IAEpH0M,EAAKpD,0BAA0ByB,2BAA2BpQ,OAAS,GACnE+R,EAAKpD,0BAA0ByB,2BAA2BnI,SAAQ,SAACoK,EAAevT,GACzEgT,EAAiBxS,MAAK,SAAAlD,GAAC,OAAIA,EAAEmD,KAAO8S,EAAchC,eACnD0B,EAAKpD,0BAA0ByB,2BAA2BkC,OAAOxT,EAAO,MAGnF,wBAAAmT,EAAA7F,UAAA4F,MA9B2CzE,OE3LlCxP,SAAduG,UAEEC,SAAS,wBAAyB,CACxCC,SCLW,iiCDOXI,SAAU,CACN2N,WAAU,WACN,IAAMA,EAAa7N,KAAK8N,OAAO,cACzBjU,EAAmB,GACnBC,EAAgB,GA+BtB,OA7BIkG,KAAKpG,aACLoG,KAAKpG,YAAY2J,SAAQ,SAACiK,GACtBK,EAAWtK,SAAQ,SAACqG,GACZA,EAAa/O,KAAO2S,EAAO1S,iBAC3B8O,EAAauC,WAAWC,yBAA2BoB,SAMnEK,EAAWtK,SAAQ,SAACqG,GAChB,IAAMjJ,EAAQ,GACViJ,EAAauC,YAAcvC,EAAauC,WAAWC,0BAA4BxC,EAAauC,WAAWC,yBAAyBd,WAAa1B,EAAauC,WAAWC,yBAAyBd,UAAUhQ,OAAS,IACjNzB,EAAiBU,KAAKqP,EAAajQ,SAEnCiQ,EAAauC,WAAWC,yBAAyBd,UAAU/H,SAAQ,SAACgJ,GAChE,GAAIA,EAASkB,OAAQ,CACjB,IAAM1M,EAAOwL,EAASkB,OAAO1M,KAAK2M,MAAM,KAAK,GAAG7Q,cAChD8D,EAAMpG,KAAKwG,OAInBjH,EAAcS,KAAK,CAACZ,QAASiQ,EAAajQ,QAASgH,MAAOA,QAIlEtH,SAASC,MAAMuH,OAAO,mDAAoDhH,GAC1ER,SAASC,MAAMuH,OAAO,iDAAkD/G,GAEjE+T,GAGXjU,YAAW,WACP,OAAOP,SAASC,MAAMxB,IAAI,iCAAiC8B,aAG/DuG,aAAY,WACR,OAAO9G,SAASC,MAAMxB,IAAI,iCAAiC+B,iBAAiByB,OAAS,GAGzFuO,QAAO,WACH,IAAMpQ,EAAQJ,SAASC,MAAMxB,IAAI,mBAEjC,OAAIkI,KAAK+N,YACEtU,EAAMuU,cAGVvU,EAAMoQ,SAGjBnQ,WAAU,WACN,OAAOL,SAASC,MAAMxB,IAAI,iCAAiC4B,YAG/DuU,oBAAmB,WACf,OAAO5U,SAASC,MAAMxB,IAAI,iCAAiC6B,SAG/D6Q,uBAAsB,WAClB,OAAOxK,KAAKyK,kBAAkBlS,OAAO,gCAGzC2V,kBAAiB,WAAI,IAADrD,EAAA,KACZqD,EAAoBlO,KAAK6N,WAAWjT,MAAK,SAAAlD,GAAC,OAAIA,EAAEiC,UAAYkR,EAAKoD,uBAOrE,OALKC,EAAkB/B,WAAWC,0BAA6B8B,EAAkB/B,WAAWC,yBAAyBvR,KACjHqT,EAAkB/B,WAAWC,yBAA2BpM,KAAKwK,uBAAuBjS,OAAOc,SAASmJ,QAAQ0I,KAC5GgD,EAAkB/B,WAAWC,yBAAyBtR,eAAiBoT,EAAkBrT,IAGtFqT,IAIf3N,QAAS,CACLC,QAAO,WACH,OAAO,GAGX2N,8BAA6B,WACzB9U,SAASC,MAAMuH,OAAO,6CAA6C,GACnExH,SAASC,MAAMuH,OAAO,2CAA4C,OAGtEuN,+BAA8B,WAC1B/U,SAASC,MAAMuH,OAAO,6CAA6C,GACnExH,SAASC,MAAMuH,OAAO,2CAA4C,U,8BEnGxDxH,SAAduG,UAEEC,SAAS,oBAAqB,CACpCK,SAAU,CACNmO,gBAAe,WACX,IAAM1D,EAAW3K,KAAK8N,OAAO,mBAG7B,OAFAnD,EAASC,eAAe,mDACxBD,EAASC,eAAe,mDACjBD,GAGX/Q,YAAW,WACP,OAAOP,SAASC,MAAMxB,IAAI,iCAAiC8B,cAInE2G,QAAS,CACL+N,OAAM,WAAI,IAADzD,EAAA,KACD7K,KAAK6J,SAAW7J,KAAKpG,cACrBoG,KAAKpG,YAAY2J,SAAQ,SAACiK,GACtB3C,EAAKhB,QAAQpO,MAAM8H,SAAQ,SAACqG,GACpBA,EAAa/O,KAAO2S,EAAO1S,iBAC3B8O,EAAauC,WAAWC,yBAA2BoB,MAIvD3C,EAAKhB,QAAQS,OAASO,EAAKhB,QAAQS,MAAMzP,KAAO2S,EAAO1S,iBACvD+P,EAAKhB,QAAQS,MAAM6B,WAAWC,yBAA2BoB,MAGjEnU,SAASC,MAAMuH,OAAO,mDAE1Bb,KAAK8N,OAAO,e,qBC7BxB,IAAI7U,EAAU,EAAQ,QACnBA,EAAQZ,aAAYY,EAAUA,EAAQC,SACnB,iBAAZD,IAAsBA,EAAU,CAAC,CAACjC,EAAOC,EAAIgC,EAAS,MAC7DA,EAAQE,SAAQnC,EAAOD,QAAUkC,EAAQE,SAG/BC,EADH,EAAQ,QAAsJF,SACvJ,WAAYD,GAAS,EAAM", "file": "static/js/acris-product-display-image.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/bundles/acrisproductdisplayimage/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"bQkd\");\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-product-image.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"1dd95ef4\", content, true, {});", "Shopware.State.registerModule('acrisProductDisplayImageState', {\r\n    namespaced: true,\r\n\r\n    state: {\r\n        isEditMode: false,\r\n        mediaId: null,\r\n        newEntities: [],\r\n        configuredImages: [],\r\n        configuredISO: []\r\n    },\r\n\r\n    mutations: {\r\n        setConfiguredItems(state, configuredItems) {\r\n            state.configuredImages = configuredItems;\r\n        },\r\n\r\n        addConfigurationItem(state, configuredItem) {\r\n            const index = state.configuredISO.findIndex(item => item.mediaId === configuredItem.mediaId);\r\n\r\n            if (index !== -1) {\r\n                state.configuredISO[index] = configuredItem;\r\n            } else {\r\n                state.configuredISO.push(configuredItem);\r\n            }\r\n        },\r\n\r\n        setConfiguredISO(state, configuredItems) {\r\n            state.configuredISO = configuredItems;\r\n        },\r\n\r\n        setEditMode(state, isEditMode) {\r\n            state.isEditMode = isEditMode;\r\n        },\r\n\r\n        setMediaId(state, mediaId) {\r\n            state.mediaId = mediaId;\r\n        },\r\n\r\n        addNewEntity(state, addNewEntity) {\r\n            if (!state.newEntities.find(o => o.id === addNewEntity.productMediaId)) {\r\n                state.newEntities.push(addNewEntity);\r\n            }\r\n        },\r\n\r\n        resetNewEntities(state) {\r\n            state.newEntities = [];\r\n        },\r\n\r\n        closeEditMode(state) {\r\n            state.isEditMode = false;\r\n        },\r\n    }\r\n});\r\n", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "import template from './sw-product-image.html.twig';\nimport './sw-product-image.scss';\n\nconst { Component } = Shopware;\n\nComponent.override('sw-product-image', {\n    template,\n\n    created() {\n        this.createdComponent();\n    },\n\n    computed: {\n        isConfigured() {\n            return Shopware.State.get('acrisProductDisplayImageState').configuredImages.length > 0 && Shopware.State.get('acrisProductDisplayImageState').configuredImages.includes(this.mediaId) && Shopware.State.get('acrisProductDisplayImageState').configuredISO.length > 0 && this.getCodesById(Shopware.State.get('acrisProductDisplayImageState').configuredISO, this.mediaId) && this.getCodesById(Shopware.State.get('acrisProductDisplayImageState').configuredISO, this.mediaId).length > 0;\n        },\n\n        ISOCodes () {\n            const configuredISO = Shopware.State.get('acrisProductDisplayImageState').configuredISO;\n            return this.getCodesById(configuredISO, this.mediaId);\n        }\n    },\n\n    methods: {\n        createdComponent() {\n            this.isCover = false;\n        },\n\n        getCodesById(mediaArray, id) {\n            const item = mediaArray.find(entry => entry.mediaId === id);\n            return item ? item.codes : []; // Return codes if found, otherwise an empty array\n        },\n\n\n        onProductMediaEdit(){\n            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', true);\n            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', this.mediaId);\n        },\n\n        getCountryFlag(code) {\n            return \"https://flagcdn.com/w20/{{ countryCode }}.png\".replace('{{ countryCode }}', code);\n        }\n    }\n});\n", "export default \"{% block sw_product_image_context_cover_action %}{% endblock %}\\r\\n\\r\\n{% block sw_product_image_context_delete_action %}\\r\\n    <sw-context-menu-item\\r\\n        @click=\\\"onProductMediaEdit\\\"\\r\\n    >\\r\\n        {{ $tc('acris-product-display-image.product-media.buttonEdit') }}\\r\\n    </sw-context-menu-item>\\r\\n    {% parent %}\\r\\n{% endblock %}\\r\\n\\r\\n{% block sw_product_image_preview %}\\r\\n    {% block acris_product_display_image_langauge_flags %}\\r\\n        <div v-if=\\\"isConfigured\\\" class=\\\"acris-product-display-image-flag-container\\\">\\r\\n            <span v-for=\\\"code in ISOCodes\\\" class=\\\"acris-product-display-image-flag\\\">\\r\\n                <img :src=\\\"getCountryFlag(code)\\\">\\r\\n            </span>\\r\\n        </div>\\r\\n    {% endblock %}\\r\\n\\r\\n    {% parent %}\\r\\n{% endblock %}\\r\\n\";", "import template from './acris-edit-display-image-modal.html.twig';\nimport './acris-edit-display-image-modal.scss';\n\nconst { Component } = Shopware;\nconst { Criteria } = Shopware.Data;\nconst { cloneDeep } = Shopware.Utils.object;\nconst { isArray } = Shopware.Utils.types;\n\nComponent.register('acris-edit-display-image-modal', {\n    template,\n\n    inject: ['repositoryFactory'],\n\n    props: {\n        displayImageItem: {\n            type: Object,\n            required: true\n        },\n\n        productMedia: {\n            type: Object,\n            required: true\n        },\n\n        product: {\n            type: Object,\n            required: true\n        }\n    },\n\n    data() {\n        return {\n            isLoading: false,\n            isStandardCoverSet: false,\n            displayImageConfiguration: null,\n            preConfiguredCover: [],\n            configureCoverForLanguages: []\n        }\n    },\n\n    computed: {\n        isStandardCover() {\n            const coverId = this.product.cover ? this.product.cover.id : this.product.coverId;\n\n            if (this.product.media.length === 0 || this.productMedia.isPlaceholder || !coverId) {\n                return false;\n            }\n\n            return this.productMedia.id === coverId;\n        },\n\n        displayImageRepository() {\n            return this.repositoryFactory.create('acris_product_display_image');\n        },\n\n        languageCriteria() {\n            const criteria = new Criteria();\n            criteria.addAssociation('locale');\n\n            return criteria;\n        },\n    },\n\n    created() {\n        this.createdComponent();\n    },\n\n    methods: {\n        createdComponent() {\n            if (this.displayImageItem._isNew) {\n                this.isLoading = true;\n                const criteria = new Criteria();\n                criteria.addFilter(Criteria.equals('productMediaId', this.productMedia.id));\n                this.displayImageRepository.search(criteria, Shopware.Context.api).then((res) => {\n                    if (res.length <= 0) {\n                        return this.displayImageRepository.save(this.displayImageItem).then(() => {\n                            this.displayImageRepository.get(\n                                this.displayImageItem.id,\n                                Shopware.Context.api\n                            ).then((displayImage) => {\n                                this.isLoading = false;\n                                this.displayImageItem = displayImage;\n                                this.displayImageConfiguration = cloneDeep(this.displayImageItem);\n                                this.displayImageConfiguration.languages = this.displayImageItem.languages;\n                                this.checkPreConfiguredCover();\n                                this.isStandardCoverSet = this.isStandardCover;\n                            }).catch(() => {\n                                this.isLoading = false;\n                            });\n                        }).catch(() => {\n                            this.isLoading = false;\n                        });\n                    } else {\n                        this.isLoading = false;\n                        this.displayImageItem = res.first();\n                        this.displayImageConfiguration = cloneDeep(this.displayImageItem);\n                        this.displayImageConfiguration.languages = this.displayImageItem.languages;\n                        this.checkPreConfiguredCover();\n                        this.isStandardCoverSet = this.isStandardCover;\n                    }\n                });\n            } else {\n                this.displayImageConfiguration = cloneDeep(this.displayImageItem);\n                this.displayImageConfiguration.languages = this.displayImageItem.languages;\n                this.checkPreConfiguredCover();\n                this.isStandardCoverSet = this.isStandardCover;\n            }\n        },\n\n        checkPreConfiguredCover() {\n            if (!this.displayImageConfiguration.languages || this.displayImageConfiguration.languages.length === 0) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage = [];\n            }\n\n            if (this.displayImageConfiguration.useImageAsCoverForLanguage && this.displayImageConfiguration.useImageAsCoverForLanguage.length > 0) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage.forEach((cover) => {\n                    if (cover.value === true) {\n                        this.preConfiguredCover.push({languageId: cover.languageId, value: cover.value})\n                    }\n                });\n            }\n        },\n\n        onCancel() {\n            this.configureCoverForLanguages = [];\n            this.preConfiguredCover = [];\n            this.displayImageConfiguration = null;\n            this.$emit('modal-close');\n        },\n\n        onApply() {\n            Object.assign(this.displayImageItem, this.displayImageConfiguration);\n\n            if (this.isStandardCoverSet) {\n                this.markMediaAsCover(this.productMedia);\n            }\n\n            if (this.configureCoverForLanguages && this.configureCoverForLanguages.length > 0) {\n                this.markMediaAsConfiguredCover();\n            }\n\n            Shopware.State.commit('acrisProductDisplayImageState/addNewEntity', this.displayImageItem);\n            this.$emit('modal-save', this.displayImageItem);\n        },\n\n        markMediaAsConfiguredCover() {\n            if (this.product && this.product.media) {\n                this.product.media.forEach((productMedia) => {\n                    if (productMedia.id !== this.productMedia.id && productMedia.extensions && productMedia.extensions.acrisProductDisplayImage && productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage && isArray(productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage) && productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.length > 0) {\n                        productMedia.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.forEach((useImageAsCoverForLanguage) => {\n                            if (this.configureCoverForLanguages.find(configuredImageAsCoverForLanguage => configuredImageAsCoverForLanguage.languageId === useImageAsCoverForLanguage.languageId)) {\n                                useImageAsCoverForLanguage.value = false;\n                            }\n                        });\n                    }\n                });\n            }\n        },\n\n        useForCoverLabel(language) {\n            return this.$tc(\n                'acris-product-display-image.modal.fieldLabelUseForCover', 0, {language: language.name}\n            );\n        },\n\n        useForCoverHelpText(language) {\n            return this.$tc(\n                'acris-product-display-image.modal.fieldHelpTextUseForCover', 0, {language: language.name}\n            );\n        },\n\n        async onCoverChange(value, index) {\n            this.displayImageConfiguration.useImageAsCoverForLanguage[index].value = value\n            if (value === true) {\n                if (this.preConfiguredCover.length === 0 || !this.preConfiguredCover.find(preConfigured => preConfigured.languageId === this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId) || !this.configureCoverForLanguages.find(preConfigured => preConfigured.languageId === this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId)) {\n                    this.configureCoverForLanguages.push({languageId: this.displayImageConfiguration.useImageAsCoverForLanguage[index].languageId});\n                }\n            }\n        },\n\n        async onStandardCoverChange(value) {\n            this.isStandardCoverSet = value;\n        },\n\n        markMediaAsCover(productMedia) {\n            this.product.cover = productMedia;\n            this.product.coverId = productMedia.id;\n        },\n\n        async onAssignLanguagesChange(entityCollection) {\n            this.displayImageConfiguration.languages = entityCollection;\n\n            if (!this.displayImageConfiguration.useImageAsCoverForLanguage || !isArray(this.displayImageConfiguration.useImageAsCoverForLanguage)) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage = [];\n            }\n\n            const codes = [];\n\n            if (entityCollection && entityCollection.length > 0) {\n                entityCollection.forEach((entity) => {\n                    if (this.displayImageConfiguration.useImageAsCoverForLanguage.length === 0 || !this.displayImageConfiguration.useImageAsCoverForLanguage.find(o => o.languageId === entity.id)) {\n                        this.displayImageConfiguration.useImageAsCoverForLanguage.push({languageId: entity.id, name: entity.name, value: false});\n                    }\n\n                    if (entity.locale) {\n                        const code = entity.locale.code.split('-')[1].toLowerCase();\n                        codes.push(code);\n                    }\n                });\n            }\n\n            Shopware.State.commit('acrisProductDisplayImageState/addConfigurationItem', {mediaId: this.productMedia.mediaId, codes: codes});\n\n            if (this.displayImageConfiguration.useImageAsCoverForLanguage.length > 0) {\n                this.displayImageConfiguration.useImageAsCoverForLanguage.forEach((fallbackCover, index) => {\n                    if (!entityCollection.find(o => o.id === fallbackCover.languageId)) {\n                        this.displayImageConfiguration.useImageAsCoverForLanguage.splice(index, 1);\n                    }\n                });\n            }\n        }\n    }\n});\n", "export default \"{% block acris_product_display_image_modal %}\\n    <sw-modal class=\\\"acris-product-display-image-modal\\\"\\n              v-if=\\\"displayImageConfiguration && !isLoading\\\"\\n              :title=\\\"$tc('acris-product-display-image.modal.title')\\\"\\n              @modal-close=\\\"onCancel\\\">\\n\\n        {% block acris_product_display_image_modal_form %}\\n            {% block acris_product_display_image_field_use_as_default_cover %}\\n                <sw-field type=\\\"switch\\\"\\n                          @change=\\\"onStandardCoverChange\\\"\\n                          :disabled=\\\"isStandardCover\\\"\\n                          :value=\\\"isStandardCoverSet\\\"\\n                          class=\\\"acris-product-display-image-use-for-standard-cover-field\\\"\\n                          :label=\\\"$tc('acris-product-display-image.modal.useAsStandardCoverLabelField')\\\"\\n                          :helpText=\\\"$tc('acris-product-display-image.modal.useAsStandardCoverHelpTextField')\\\">\\n                </sw-field>\\n            {% endblock %}\\n\\n            {% block acris_product_display_image_modal_form_language %}\\n                <sw-entity-many-to-many-select\\n                    :label=\\\"$tc('acris-product-display-image.modal.languageLabelField')\\\"\\n                    :placeholder=\\\"$tc('acris-product-display-image.modal.languagePlaceholderField')\\\"\\n                    :helpText=\\\"$tc('acris-product-display-image.modal.languageHelpTextField')\\\"\\n                    :localMode=\\\"true\\\"\\n                    :criteria=\\\"languageCriteria\\\"\\n                    @change=\\\"onAssignLanguagesChange\\\"\\n                    v-model=\\\"displayImageConfiguration.languages\\\">\\n                </sw-entity-many-to-many-select>\\n            {% endblock %}\\n\\n            {% block acris_product_display_image_field_use_cover %}\\n                <sw-field v-if=\\\"displayImageConfiguration.useImageAsCoverForLanguage.length > 0\\\"\\n                          v-for=\\\"(useCoverForLanguage, index) in displayImageConfiguration.useImageAsCoverForLanguage\\\"\\n                          type=\\\"switch\\\"\\n                          @change=\\\"onCoverChange($event, index)\\\"\\n                          :value=\\\"useCoverForLanguage.value\\\"\\n                          class=\\\"acris-product-display-image-use-for-cover-field\\\"\\n                          :label=\\\"useForCoverLabel(useCoverForLanguage)\\\"\\n                          :helpText=\\\"useForCoverHelpText(useCoverForLanguage)\\\">\\n                </sw-field>\\n            {% endblock %}\\n        {% endblock %}\\n\\n        {% block acris_product_display_image_modal_footer %}\\n            <template slot=\\\"modal-footer\\\">\\n\\n                {% block acris_product_display_image_modal_footer_button_cancel %}\\n                    <sw-button size=\\\"small\\\" @click=\\\"onCancel\\\">\\n                        {{ $tc('global.default.cancel') }}\\n                    </sw-button>\\n                {% endblock %}\\n\\n                {% block acris_product_display_image_modal_footer_button_apply %}\\n                    <sw-button variant=\\\"primary\\\" size=\\\"small\\\" @click=\\\"onApply\\\">\\n                        {{ $tc('global.default.apply') }}\\n                    </sw-button>\\n                {% endblock %}\\n            </template>\\n        {% endblock %}\\n    </sw-modal>\\n{% endblock %}\\n\";", "import template from './sw-product-media-form.html.twig';\n\nconst { Component } = Shopware;\n\nComponent.override('sw-product-media-form', {\n    template,\n\n    computed: {\n        mediaItems() {\n            const mediaItems = this.$super('mediaItems');\n            const configuredImages = [];\n            const configuredISO = [];\n\n            if (this.newEntities) {\n                this.newEntities.forEach((entity) => {\n                    mediaItems.forEach((productMedia) => {\n                        if (productMedia.id === entity.productMediaId) {\n                            productMedia.extensions.acrisProductDisplayImage = entity;\n                        }\n                    });\n                });\n            }\n\n            mediaItems.forEach((productMedia) => {\n                const codes = [];\n                if (productMedia.extensions && productMedia.extensions.acrisProductDisplayImage && productMedia.extensions.acrisProductDisplayImage.languages && productMedia.extensions.acrisProductDisplayImage.languages.length > 0) {\n                    configuredImages.push(productMedia.mediaId);\n\n                    productMedia.extensions.acrisProductDisplayImage.languages.forEach((language) => {\n                        if (language.locale) {\n                            const code = language.locale.code.split('-')[1].toLowerCase();\n                            codes.push(code);\n                        }\n                    });\n\n                    configuredISO.push({mediaId: productMedia.mediaId, codes: codes});\n                }\n            });\n\n            Shopware.State.commit('acrisProductDisplayImageState/setConfiguredItems', configuredImages);\n            Shopware.State.commit('acrisProductDisplayImageState/setConfiguredISO', configuredISO);\n\n            return mediaItems;\n        },\n\n        newEntities() {\n            return Shopware.State.get('acrisProductDisplayImageState').newEntities;\n        },\n\n        isConfigured() {\n            return Shopware.State.get('acrisProductDisplayImageState').configuredImages.length > 0;\n        },\n\n        product() {\n            const state = Shopware.State.get('swProductDetail');\n\n            if (this.isInherited) {\n                return state.parentProduct;\n            }\n\n            return state.product;\n        },\n\n        isEditMode() {\n            return Shopware.State.get('acrisProductDisplayImageState').isEditMode;\n        },\n\n        displayImageMediaId() {\n            return Shopware.State.get('acrisProductDisplayImageState').mediaId;\n        },\n\n        displayImageRepository() {\n            return this.repositoryFactory.create('acris_product_display_image');\n        },\n\n        displayImageMedia() {\n            let displayImageMedia = this.mediaItems.find(o => o.mediaId === this.displayImageMediaId);\n\n            if (!displayImageMedia.extensions.acrisProductDisplayImage || !displayImageMedia.extensions.acrisProductDisplayImage.id) {\n                displayImageMedia.extensions.acrisProductDisplayImage = this.displayImageRepository.create(Shopware.Context.api);\n                displayImageMedia.extensions.acrisProductDisplayImage.productMediaId = displayImageMedia.id;\n            }\n\n            return displayImageMedia;\n        }\n    },\n\n    methods: {\n        isCover() {\n            return false;\n        },\n\n        onEditProductDisplayImageSave() {\n            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', false);\n            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', null);\n        },\n\n        onEditProductDisplayImageClose() {\n            Shopware.State.commit('acrisProductDisplayImageState/setEditMode', false);\n            Shopware.State.commit('acrisProductDisplayImageState/setMediaId', null);\n        }\n    }\n});\n", "export default \"{% block sw_product_media_form_grid %}\\r\\n    {% block acris_product_display_image_form_grid_configured_info %}\\r\\n        <div v-if=\\\"isConfigured\\\">\\r\\n            <sw-alert class=\\\"acris-product-display-image-grid__configured-info\\\" variant=\\\"info\\\">\\r\\n                {{ $tc('acris-product-display-image.product-media.configuredInfo') }}\\r\\n            </sw-alert>\\r\\n        </div>\\r\\n    {% endblock %}\\r\\n\\r\\n    {% parent %}\\r\\n\\r\\n    {% block acris_product_display_image_form_edit_modal %}\\r\\n        <acris-edit-display-image-modal v-if=\\\"isEditMode\\\"\\r\\n                                   :displayImageItem=\\\"displayImageMedia.extensions.acrisProductDisplayImage\\\"\\r\\n                                   :productMedia=\\\"displayImageMedia\\\"\\r\\n                                   :product=\\\"product\\\"\\r\\n                                   @modal-save=\\\"onEditProductDisplayImageSave\\\"\\r\\n                                   @modal-close=\\\"onEditProductDisplayImageClose\\\">\\r\\n        </acris-edit-display-image-modal>\\r\\n    {% endblock %}\\r\\n{% endblock %}\\r\\n\";", "const { Component } = Shopware;\r\n\r\nComponent.override('sw-product-detail', {\r\n    computed: {\r\n        productCriteria() {\r\n            const criteria = this.$super('productCriteria');\r\n            criteria.addAssociation('media.acrisProductDisplayImage.languages.locale');\r\n            criteria.addAssociation('cover.acrisProductDisplayImage.languages.locale');\r\n            return criteria;\r\n        },\r\n\r\n        newEntities() {\r\n            return Shopware.State.get('acrisProductDisplayImageState').newEntities;\r\n        }\r\n    },\r\n\r\n    methods: {\r\n        onSave() {\r\n            if (this.product && this.newEntities) {\r\n                this.newEntities.forEach((entity) => {\r\n                    this.product.media.forEach((productMedia) => {\r\n                        if (productMedia.id === entity.productMediaId) {\r\n                            productMedia.extensions.acrisProductDisplayImage = entity;\r\n                        }\r\n                    });\r\n\r\n                    if (this.product.cover && this.product.cover.id === entity.productMediaId) {\r\n                        this.product.cover.extensions.acrisProductDisplayImage = entity;\r\n                    }\r\n                });\r\n                Shopware.State.commit('acrisProductDisplayImageState/resetNewEntities');\r\n            }\r\n            this.$super('onSave');\r\n        }\r\n    }\r\n});\r\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./acris-edit-display-image-modal.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"433d0445\", content, true, {});"], "sourceRoot": ""}