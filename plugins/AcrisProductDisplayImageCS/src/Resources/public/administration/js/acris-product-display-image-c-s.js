/*! For license information please see acris-product-display-image-c-s.js.LICENSE.txt */
!function(e){var t={};function a(r){if(t[r])return t[r].exports;var n=t[r]={i:r,l:!1,exports:{}};return e[r].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.m=e,a.c=t,a.d=function(e,t,r){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(a.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)a.d(r,n,function(t){return e[t]}.bind(null,n));return r},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p=(window.__sw__.assetPath + '/bundles/acrisproductdisplayimagecs/'),a(a.s="2qTK")}({"2qTK":function(e,t,a){"use strict";a.r(t);a("W/+4"),a("Q35n");Shopware.Component.override("sw-product-image",{template:'{% block sw_product_image_context_cover_action %}{% endblock %}\n\n{% block sw_product_image_context_delete_action %}\n    <sw-context-menu-item\n        @click="onProductMediaEdit"\n    >\n        {{ $tc(\'acris-product-display-image.product-media.buttonEdit\') }}\n    </sw-context-menu-item>\n    {% parent %}\n{% endblock %}\n\n{% block sw_product_image_preview %}\n    {% block acris_product_display_image_langauge_flags %}\n        <div v-if="isConfigured" class="acris-product-display-image-flag-container">\n            <span v-for="code in ISOCodes" class="acris-product-display-image-flag">\n                <img :src="getCountryFlag(code)">\n            </span>\n        </div>\n    {% endblock %}\n\n    {% parent %}\n{% endblock %}\n',created:function(){this.createdComponent()},computed:{isConfigured:function(){return Shopware.State.get("acrisProductDisplayImageState").configuredImages.length>0&&Shopware.State.get("acrisProductDisplayImageState").configuredImages.includes(this.mediaId)&&Shopware.State.get("acrisProductDisplayImageState").configuredISO.length>0&&this.getCodesById(Shopware.State.get("acrisProductDisplayImageState").configuredISO,this.mediaId)&&this.getCodesById(Shopware.State.get("acrisProductDisplayImageState").configuredISO,this.mediaId).length>0},ISOCodes:function(){var e=Shopware.State.get("acrisProductDisplayImageState").configuredISO;return this.getCodesById(e,this.mediaId)}},methods:{createdComponent:function(){this.isCover=!1},getCodesById:function(e,t){var a=e.find((function(e){return e.mediaId===t}));return a?a.codes:[]},onProductMediaEdit:function(){Shopware.State.commit("acrisProductDisplayImageState/setEditMode",!0),Shopware.State.commit("acrisProductDisplayImageState/setMediaId",this.mediaId)},getCountryFlag:function(e){return"https://flagcdn.com/w20/{{ countryCode }}.png".replace("{{ countryCode }}",e)}}});a("YJR3");function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(){n=function(){return e};var e={},t=Object.prototype,a=t.hasOwnProperty,i=Object.defineProperty||function(e,t,a){e[t]=a.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function d(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,a){return e[t]=a}}function l(e,t,a,r){var n=t&&t.prototype instanceof f?t:f,o=Object.create(n.prototype),s=new P(r||[]);return i(o,"_invoke",{value:_(e,a,s)}),o}function g(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var p={};function f(){}function m(){}function h(){}var y={};d(y,s,(function(){return this}));var v=Object.getPrototypeOf,I=v&&v(v(E([])));I&&I!==t&&a.call(I,s)&&(y=I);var C=h.prototype=f.prototype=Object.create(y);function S(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function n(i,o,s,u){var c=g(e[i],e,o);if("throw"!==c.type){var d=c.arg,l=d.value;return l&&"object"==r(l)&&a.call(l,"__await")?t.resolve(l.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):t.resolve(l).then((function(e){d.value=e,s(d)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var o;i(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){n(e,a,t,r)}))}return o=o?o.then(r,r):r()}})}function _(e,t,a){var r="suspendedStart";return function(n,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===n)throw i;return F()}for(a.method=n,a.arg=i;;){var o=a.delegate;if(o){var s=b(o,a);if(s){if(s===p)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var u=g(e,t,a);if("normal"===u.type){if(r=a.done?"completed":"suspendedYield",u.arg===p)continue;return{value:u.arg,done:a.done}}"throw"===u.type&&(r="completed",a.method="throw",a.arg=u.arg)}}}function b(e,t){var a=t.method,r=e.iterator[a];if(void 0===r)return t.delegate=null,"throw"===a&&e.iterator.return&&(t.method="return",t.arg=void 0,b(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),p;var n=g(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,p;var i=n.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function E(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return n.next=n}}return{next:F}}function F(){return{value:void 0,done:!0}}return m.prototype=h,i(C,"constructor",{value:h,configurable:!0}),i(h,"constructor",{value:m,configurable:!0}),m.displayName=d(h,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,d(e,c,"GeneratorFunction")),e.prototype=Object.create(C),e},e.awrap=function(e){return{__await:e}},S(w.prototype),d(w.prototype,u,(function(){return this})),e.AsyncIterator=w,e.async=function(t,a,r,n,i){void 0===i&&(i=Promise);var o=new w(l(t,a,r,n),i);return e.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(C),d(C,c,"Generator"),d(C,s,(function(){return this})),d(C,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=E,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(a,r){return o.type="throw",o.arg=e,t.next=a,r&&(t.method="next",t.arg=void 0),!!r}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],o=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=a.call(i,"catchLoc"),u=a.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),x(a),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var n=r.arg;x(a)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:E(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=void 0),p}},e}function i(e,t,a,r,n,i,o){try{var s=e[i](o),u=s.value}catch(e){return void a(e)}s.done?t(u):Promise.resolve(u).then(r,n)}function o(e){return function(){var t=this,a=arguments;return new Promise((function(r,n){var o=e.apply(t,a);function s(e){i(o,r,n,s,u,"next",e)}function u(e){i(o,r,n,s,u,"throw",e)}s(void 0)}))}}var s=Shopware.Component,u=Shopware.Data.Criteria,c=Shopware.Utils.object.cloneDeep,d=Shopware.Utils.types.isArray;s.register("acris-edit-display-image-modal",{template:'{% block acris_product_display_image_modal %}\n    <sw-modal class="acris-product-display-image-modal"\n              v-if="displayImageConfiguration && !isLoading"\n              :title="$tc(\'acris-product-display-image.modal.title\')"\n              @modal-close="onCancel">\n\n        {% block acris_product_display_image_modal_form %}\n            {% block acris_product_display_image_field_use_as_default_cover %}\n                <sw-field type="switch"\n                          @change="onStandardCoverChange"\n                          :disabled="isStandardCover"\n                          :value="isStandardCoverSet"\n                          class="acris-product-display-image-use-for-standard-cover-field"\n                          :label="$tc(\'acris-product-display-image.modal.useAsStandardCoverLabelField\')"\n                          :helpText="$tc(\'acris-product-display-image.modal.useAsStandardCoverHelpTextField\')">\n                </sw-field>\n            {% endblock %}\n\n            {% block acris_product_display_image_modal_form_language %}\n                <sw-entity-many-to-many-select\n                    :label="$tc(\'acris-product-display-image.modal.languageLabelField\')"\n                    :placeholder="$tc(\'acris-product-display-image.modal.languagePlaceholderField\')"\n                    :helpText="$tc(\'acris-product-display-image.modal.languageHelpTextField\')"\n                    :localMode="true"\n                    :criteria="languageCriteria"\n                    @change="onAssignLanguagesChange"\n                    v-model="displayImageConfiguration.languages">\n                </sw-entity-many-to-many-select>\n            {% endblock %}\n\n            {% block acris_product_display_image_field_use_cover %}\n                <sw-field v-if="displayImageConfiguration.useImageAsCoverForLanguage.length > 0"\n                          v-for="(useCoverForLanguage, index) in displayImageConfiguration.useImageAsCoverForLanguage"\n                          type="switch"\n                          @change="onCoverChange($event, index)"\n                          :value="useCoverForLanguage.value"\n                          class="acris-product-display-image-use-for-cover-field"\n                          :label="useForCoverLabel(useCoverForLanguage)"\n                          :helpText="useForCoverHelpText(useCoverForLanguage)">\n                </sw-field>\n            {% endblock %}\n        {% endblock %}\n\n        {% block acris_product_display_image_modal_footer %}\n            <template slot="modal-footer">\n\n                {% block acris_product_display_image_modal_footer_button_cancel %}\n                    <sw-button size="small" @click="onCancel">\n                        {{ $tc(\'global.default.cancel\') }}\n                    </sw-button>\n                {% endblock %}\n\n                {% block acris_product_display_image_modal_footer_button_apply %}\n                    <sw-button variant="primary" size="small" @click="onApply">\n                        {{ $tc(\'global.default.apply\') }}\n                    </sw-button>\n                {% endblock %}\n            </template>\n        {% endblock %}\n    </sw-modal>\n{% endblock %}\n',inject:["repositoryFactory"],props:{displayImageItem:{type:Object,required:!0},productMedia:{type:Object,required:!0},product:{type:Object,required:!0}},data:function(){return{isLoading:!1,isStandardCoverSet:!1,displayImageConfiguration:null,preConfiguredCover:[],configureCoverForLanguages:[]}},computed:{isStandardCover:function(){var e=this.product.cover?this.product.cover.id:this.product.coverId;return!(0===this.product.media.length||this.productMedia.isPlaceholder||!e)&&this.productMedia.id===e},displayImageRepository:function(){return this.repositoryFactory.create("acris_product_display_image")},languageCriteria:function(){var e=new u;return e.addAssociation("locale"),e}},created:function(){this.createdComponent()},methods:{createdComponent:function(){var e=this;if(this.displayImageItem._isNew){this.isLoading=!0;var t=new u;t.addFilter(u.equals("productMediaId",this.productMedia.id)),this.displayImageRepository.search(t,Shopware.Context.api).then((function(t){if(t.length<=0)return e.displayImageRepository.save(e.displayImageItem).then((function(){e.displayImageRepository.get(e.displayImageItem.id,Shopware.Context.api).then((function(t){e.isLoading=!1,e.displayImageItem=t,e.displayImageConfiguration=c(e.displayImageItem),e.displayImageConfiguration.languages=e.displayImageItem.languages,e.checkPreConfiguredCover(),e.isStandardCoverSet=e.isStandardCover})).catch((function(){e.isLoading=!1}))})).catch((function(){e.isLoading=!1}));e.isLoading=!1,e.displayImageItem=t.first(),e.displayImageConfiguration=c(e.displayImageItem),e.displayImageConfiguration.languages=e.displayImageItem.languages,e.checkPreConfiguredCover(),e.isStandardCoverSet=e.isStandardCover}))}else this.displayImageConfiguration=c(this.displayImageItem),this.displayImageConfiguration.languages=this.displayImageItem.languages,this.checkPreConfiguredCover(),this.isStandardCoverSet=this.isStandardCover},checkPreConfiguredCover:function(){var e=this;this.displayImageConfiguration.languages&&0!==this.displayImageConfiguration.languages.length||(this.displayImageConfiguration.useImageAsCoverForLanguage=[]),this.displayImageConfiguration.useImageAsCoverForLanguage&&this.displayImageConfiguration.useImageAsCoverForLanguage.length>0&&this.displayImageConfiguration.useImageAsCoverForLanguage.forEach((function(t){!0===t.value&&e.preConfiguredCover.push({languageId:t.languageId,value:t.value})}))},onCancel:function(){this.configureCoverForLanguages=[],this.preConfiguredCover=[],this.displayImageConfiguration=null,this.$emit("modal-close")},onApply:function(){Object.assign(this.displayImageItem,this.displayImageConfiguration),this.isStandardCoverSet&&this.markMediaAsCover(this.productMedia),this.configureCoverForLanguages&&this.configureCoverForLanguages.length>0&&this.markMediaAsConfiguredCover(),Shopware.State.commit("acrisProductDisplayImageState/addNewEntity",this.displayImageItem),this.$emit("modal-save",this.displayImageItem)},markMediaAsConfiguredCover:function(){var e=this;this.product&&this.product.media&&this.product.media.forEach((function(t){t.id!==e.productMedia.id&&t.extensions&&t.extensions.acrisProductDisplayImage&&t.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage&&d(t.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage)&&t.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.length>0&&t.extensions.acrisProductDisplayImage.useImageAsCoverForLanguage.forEach((function(t){e.configureCoverForLanguages.find((function(e){return e.languageId===t.languageId}))&&(t.value=!1)}))}))},useForCoverLabel:function(e){return this.$tc("acris-product-display-image.modal.fieldLabelUseForCover",0,{language:e.name})},useForCoverHelpText:function(e){return this.$tc("acris-product-display-image.modal.fieldHelpTextUseForCover",0,{language:e.name})},onCoverChange:function(e,t){var a=this;return o(n().mark((function r(){return n().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:a.displayImageConfiguration.useImageAsCoverForLanguage[t].value=e,!0===e&&(0!==a.preConfiguredCover.length&&a.preConfiguredCover.find((function(e){return e.languageId===a.displayImageConfiguration.useImageAsCoverForLanguage[t].languageId}))&&a.configureCoverForLanguages.find((function(e){return e.languageId===a.displayImageConfiguration.useImageAsCoverForLanguage[t].languageId}))||a.configureCoverForLanguages.push({languageId:a.displayImageConfiguration.useImageAsCoverForLanguage[t].languageId}));case 2:case"end":return r.stop()}}),r)})))()},onStandardCoverChange:function(e){var t=this;return o(n().mark((function a(){return n().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:t.isStandardCoverSet=e;case 1:case"end":return a.stop()}}),a)})))()},markMediaAsCover:function(e){this.product.cover=e,this.product.coverId=e.id},onAssignLanguagesChange:function(e){var t=this;return o(n().mark((function a(){var r;return n().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:t.displayImageConfiguration.languages=e,t.displayImageConfiguration.useImageAsCoverForLanguage&&d(t.displayImageConfiguration.useImageAsCoverForLanguage)||(t.displayImageConfiguration.useImageAsCoverForLanguage=[]),r=[],e&&e.length>0&&e.forEach((function(e){if(0!==t.displayImageConfiguration.useImageAsCoverForLanguage.length&&t.displayImageConfiguration.useImageAsCoverForLanguage.find((function(t){return t.languageId===e.id}))||t.displayImageConfiguration.useImageAsCoverForLanguage.push({languageId:e.id,name:e.name,value:!1}),e.locale){var a=e.locale.code.split("-")[1].toLowerCase();r.push(a)}})),Shopware.State.commit("acrisProductDisplayImageState/addConfigurationItem",{mediaId:t.productMedia.mediaId,codes:r}),t.displayImageConfiguration.useImageAsCoverForLanguage.length>0&&t.displayImageConfiguration.useImageAsCoverForLanguage.forEach((function(a,r){e.find((function(e){return e.id===a.languageId}))||t.displayImageConfiguration.useImageAsCoverForLanguage.splice(r,1)}));case 6:case"end":return a.stop()}}),a)})))()}}});Shopware.Component.override("sw-product-media-form",{template:'{% block sw_product_media_form_grid %}\n    {% block acris_product_display_image_form_grid_configured_info %}\n        <div v-if="isConfigured">\n            <sw-alert class="acris-product-display-image-grid__configured-info" variant="info">\n                {{ $tc(\'acris-product-display-image.product-media.configuredInfo\') }}\n            </sw-alert>\n        </div>\n    {% endblock %}\n\n    {% parent %}\n\n    {% block acris_product_display_image_form_edit_modal %}\n        <acris-edit-display-image-modal v-if="isEditMode"\n                                   :displayImageItem="displayImageMedia.extensions.acrisProductDisplayImage"\n                                   :productMedia="displayImageMedia"\n                                   :product="product"\n                                   @modal-save="onEditProductDisplayImageSave"\n                                   @modal-close="onEditProductDisplayImageClose">\n        </acris-edit-display-image-modal>\n    {% endblock %}\n{% endblock %}\n',computed:{mediaItems:function(){var e=this.$super("mediaItems"),t=[],a=[];return this.newEntities&&this.newEntities.forEach((function(t){e.forEach((function(e){e.id===t.productMediaId&&(e.extensions.acrisProductDisplayImage=t)}))})),e.forEach((function(e){var r=[];e.extensions&&e.extensions.acrisProductDisplayImage&&e.extensions.acrisProductDisplayImage.languages&&e.extensions.acrisProductDisplayImage.languages.length>0&&(t.push(e.mediaId),e.extensions.acrisProductDisplayImage.languages.forEach((function(e){if(e.locale){var t=e.locale.code.split("-")[1].toLowerCase();r.push(t)}})),a.push({mediaId:e.mediaId,codes:r}))})),Shopware.State.commit("acrisProductDisplayImageState/setConfiguredItems",t),Shopware.State.commit("acrisProductDisplayImageState/setConfiguredISO",a),e},newEntities:function(){return Shopware.State.get("acrisProductDisplayImageState").newEntities},isConfigured:function(){return Shopware.State.get("acrisProductDisplayImageState").configuredImages.length>0},product:function(){var e=Shopware.State.get("swProductDetail");return this.isInherited?e.parentProduct:e.product},isEditMode:function(){return Shopware.State.get("acrisProductDisplayImageState").isEditMode},displayImageMediaId:function(){return Shopware.State.get("acrisProductDisplayImageState").mediaId},displayImageRepository:function(){return this.repositoryFactory.create("acris_product_display_image")},displayImageMedia:function(){var e=this,t=this.mediaItems.find((function(t){return t.mediaId===e.displayImageMediaId}));return t.extensions.acrisProductDisplayImage&&t.extensions.acrisProductDisplayImage.id||(t.extensions.acrisProductDisplayImage=this.displayImageRepository.create(Shopware.Context.api),t.extensions.acrisProductDisplayImage.productMediaId=t.id),t}},methods:{isCover:function(){return!1},onEditProductDisplayImageSave:function(){Shopware.State.commit("acrisProductDisplayImageState/setEditMode",!1),Shopware.State.commit("acrisProductDisplayImageState/setMediaId",null)},onEditProductDisplayImageClose:function(){Shopware.State.commit("acrisProductDisplayImageState/setEditMode",!1),Shopware.State.commit("acrisProductDisplayImageState/setMediaId",null)}}});a("UgmO")},"4QAy":function(e,t,a){},P8hj:function(e,t,a){"use strict";function r(e,t){for(var a=[],r={},n=0;n<t.length;n++){var i=t[n],o=i[0],s={id:e+":"+n,css:i[1],media:i[2],sourceMap:i[3]};r[o]?r[o].parts.push(s):a.push(r[o]={id:o,parts:[s]})}return a}a.r(t),a.d(t,"default",(function(){return f}));var n="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!n)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},o=n&&(document.head||document.getElementsByTagName("head")[0]),s=null,u=0,c=!1,d=function(){},l=null,g="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function f(e,t,a,n){c=a,l=n||{};var o=r(e,t);return m(o),function(t){for(var a=[],n=0;n<o.length;n++){var s=o[n];(u=i[s.id]).refs--,a.push(u)}t?m(o=r(e,t)):o=[];for(n=0;n<a.length;n++){var u;if(0===(u=a[n]).refs){for(var c=0;c<u.parts.length;c++)u.parts[c]();delete i[u.id]}}}}function m(e){for(var t=0;t<e.length;t++){var a=e[t],r=i[a.id];if(r){r.refs++;for(var n=0;n<r.parts.length;n++)r.parts[n](a.parts[n]);for(;n<a.parts.length;n++)r.parts.push(y(a.parts[n]));r.parts.length>a.parts.length&&(r.parts.length=a.parts.length)}else{var o=[];for(n=0;n<a.parts.length;n++)o.push(y(a.parts[n]));i[a.id]={id:a.id,refs:1,parts:o}}}}function h(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function y(e){var t,a,r=document.querySelector("style["+g+'~="'+e.id+'"]');if(r){if(c)return d;r.parentNode.removeChild(r)}if(p){var n=u++;r=s||(s=h()),t=C.bind(null,r,n,!1),a=C.bind(null,r,n,!0)}else r=h(),t=S.bind(null,r),a=function(){r.parentNode.removeChild(r)};return t(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap)return;t(e=r)}else a()}}var v,I=(v=[],function(e,t){return v[e]=t,v.filter(Boolean).join("\n")});function C(e,t,a,r){var n=a?"":r.css;if(e.styleSheet)e.styleSheet.cssText=I(t,n);else{var i=document.createTextNode(n),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(i,o[t]):e.appendChild(i)}}function S(e,t){var a=t.css,r=t.media,n=t.sourceMap;if(r&&e.setAttribute("media",r),l.ssrId&&e.setAttribute(g,t.id),n&&(a+="\n/*# sourceURL="+n.sources[0]+" */",a+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */"),e.styleSheet)e.styleSheet.cssText=a;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(a))}}},Q35n:function(e,t,a){var r=a("4QAy");r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,a("P8hj").default)("4f254878",r,!0,{})},Raoe:function(e,t,a){},UgmO:function(e,t){Shopware.Component.override("sw-product-detail",{computed:{productCriteria:function(){var e=this.$super("productCriteria");return e.addAssociation("media.acrisProductDisplayImage.languages.locale"),e.addAssociation("cover.acrisProductDisplayImage.languages.locale"),e},newEntities:function(){return Shopware.State.get("acrisProductDisplayImageState").newEntities}},methods:{onSave:function(){var e=this;this.product&&this.newEntities&&(this.newEntities.forEach((function(t){e.product.media.forEach((function(e){e.id===t.productMediaId&&(e.extensions.acrisProductDisplayImage=t)})),e.product.cover&&e.product.cover.id===t.productMediaId&&(e.product.cover.extensions.acrisProductDisplayImage=t)})),Shopware.State.commit("acrisProductDisplayImageState/resetNewEntities")),this.$super("onSave")}}})},"W/+4":function(e,t){Shopware.State.registerModule("acrisProductDisplayImageState",{namespaced:!0,state:{isEditMode:!1,mediaId:null,newEntities:[],configuredImages:[],configuredISO:[]},mutations:{setConfiguredItems:function(e,t){e.configuredImages=t},addConfigurationItem:function(e,t){var a=e.configuredISO.findIndex((function(e){return e.mediaId===t.mediaId}));-1!==a?e.configuredISO[a]=t:e.configuredISO.push(t)},setConfiguredISO:function(e,t){e.configuredISO=t},setEditMode:function(e,t){e.isEditMode=t},setMediaId:function(e,t){e.mediaId=t},addNewEntity:function(e,t){e.newEntities.find((function(e){return e.id===t.productMediaId}))||e.newEntities.push(t)},resetNewEntities:function(e){e.newEntities=[]},closeEditMode:function(e){e.isEditMode=!1}}})},YJR3:function(e,t,a){var r=a("Raoe");r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,a("P8hj").default)("88d2fb96",r,!0,{})}});
//# sourceMappingURL=acris-product-display-image-c-s.js.map