<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Storefront\Subscriber;

use Acris\ProductDisplayImage\Components\ProductDisplayImageService;
use Shopware\Core\Content\Product\Events\ProductGatewayCriteriaEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class CartSubscriber implements EventSubscriberInterface
{
    private ProductDisplayImageService $productDisplayImageService;

    public function __construct(ProductDisplayImageService $productDisplayImageService)
    {
        $this->productDisplayImageService = $productDisplayImageService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ProductGatewayCriteriaEvent::class => 'onProductGatewayCriteria'
        ];
    }

    public function onProductGatewayCriteria(ProductGatewayCriteriaEvent $event): void
    {
            $this->productDisplayImageService->addProductImageAssociationCriteria($event->getCriteria());
    }
}
