<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Storefront\Subscriber;

use Shopware\Core\Content\ProductExport\Event\ProductExportProductCriteriaEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ProductExportSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            ProductExportProductCriteriaEvent::class => 'onProductExportProductCriteria'
        ];
    }

    public function onProductExportProductCriteria(ProductExportProductCriteriaEvent $event): void
    {
        $event->getCriteria()->addAssociation('media.media');
    }
}
