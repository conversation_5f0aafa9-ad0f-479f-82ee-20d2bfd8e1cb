<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Storefront\Subscriber;

use Acris\ProductDisplayImage\Components\ProductDisplayImageService;
use Shopware\Core\Content\Product\SalesChannel\SalesChannelProductEntity;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelEntityLoadedEvent;
use Shopware\Core\System\SalesChannel\Event\SalesChannelProcessCriteriaEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ProductPageSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly ProductDisplayImageService $productDisplayImageService
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'sales_channel.product.process.criteria' => 'onProductCriteriaLoaded',
            'sales_channel.product.loaded' => 'onSalesChannelProductLoaded',
        ];
    }

    public function onProductCriteriaLoaded(SalesChannelProcessCriteriaEvent $event): void
    {
        $languageId = $event->getContext()->getLanguageId();
        $this->productDisplayImageService->addProductImageAssociationCriteria($languageId, $event->getCriteria());
    }

    public function onSalesChannelProductLoaded(SalesChannelEntityLoadedEvent $event):void
    {
        $languageId = $event->getContext()->getLanguageId();

        /** @var SalesChannelProductEntity $product */
        foreach ($event->getEntities() as $product) {
            $this->productDisplayImageService->checkLanguageForProductImage($product, $languageId, $event->getSalesChannelContext());
        }
    }
}
