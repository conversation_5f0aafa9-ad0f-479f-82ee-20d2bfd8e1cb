<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Custom;

use Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityExtension;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\ApiAware;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\CascadeDelete;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class ProductMediaExtension extends EntityExtension
{
    public const DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION = 'acrisProductDisplayImage';

    public function extendFields(FieldCollection $collection): void
    {
        $collection->add(
            (new OneToOneAssociationField(
                'acrisProductDisplayImage',
                'id',
                'product_media_id',
                ProductDisplayImageDefinition::class,
                true,
            ))->addFlags(new ApiAware(), new CascadeDelete())
        );
    }

    public function getDefinitionClass(): string
    {
        return ProductMediaDefinition::class;
    }
}
