<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Custom;

use Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;
use Shopware\Core\System\Language\LanguageCollection;

class ProductDisplayImageEntity extends Entity
{
    use EntityIdTrait;

    /**
     * @var string|null
     */
    protected $productMediaId;

    /**
     * @var ProductMediaEntity|null
     */
    protected $productMedia;

    /**
     * @var array|null
     */
    protected $languageIds;

    /**
     * @var LanguageCollection|null
     */
    protected $languages;

    /**
     * @var array|null
     */
    protected $useImageAsCoverForLanguage;

    /**
     * @return string|null
     */
    public function getProductMediaId(): ?string
    {
        return $this->productMediaId;
    }

    /**
     * @param string|null $productMediaId
     */
    public function setProductMediaId(?string $productMediaId): void
    {
        $this->productMediaId = $productMediaId;
    }

    /**
     * @return ProductMediaEntity|null
     */
    public function getProductMedia(): ?ProductMediaEntity
    {
        return $this->productMedia;
    }

    /**
     * @param ProductMediaEntity|null $productMedia
     */
    public function setProductMedia(?ProductMediaEntity $productMedia): void
    {
        $this->productMedia = $productMedia;
    }

    /**
     * @return array|null
     */
    public function getLanguageIds(): ?array
    {
        return $this->languageIds;
    }

    /**
     * @param array|null $languageIds
     */
    public function setLanguageIds(?array $languageIds): void
    {
        $this->languageIds = $languageIds;
    }

    /**
     * @return LanguageCollection|null
     */
    public function getLanguages(): ?LanguageCollection
    {
        return $this->languages;
    }

    /**
     * @param LanguageCollection|null $languages
     */
    public function setLanguages(?LanguageCollection $languages): void
    {
        $this->languages = $languages;
    }

    /**
     * @return array|null
     */
    public function getUseImageAsCoverForLanguage(): ?array
    {
        return $this->useImageAsCoverForLanguage;
    }

    /**
     * @param array|null $useImageAsCoverForLanguage
     */
    public function setUseImageAsCoverForLanguage(?array $useImageAsCoverForLanguage): void
    {
        $this->useImageAsCoverForLanguage = $useImageAsCoverForLanguage;
    }
}
