<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Custom;

use Acris\ProductDisplayImage\Custom\Aggregate\ProductDisplayImageLanguage\ProductDisplayImageLanguageDefinition;
use Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\ApiAware;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\JsonField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToManyAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToManyIdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;
use Shopware\Core\System\Language\LanguageDefinition;

class ProductDisplayImageDefinition extends EntityDefinition
{
    public const ENTITY_NAME = 'acris_product_display_image';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getCollectionClass(): string
    {
        return ProductDisplayImageCollection::class;
    }

    public function getEntityClass(): string
    {
        return ProductDisplayImageEntity::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new IdField('id', 'id'))->addFlags(new PrimaryKey(), new Required()),

            new FkField('product_media_id', 'productMediaId', ProductMediaDefinition::class),
            new OneToOneAssociationField('productMedia', 'product_media_id', 'id', ProductMediaDefinition::class, false),

            (new ManyToManyIdField('language_ids', 'languageIds', 'languages'))->addFlags(new ApiAware()),
            (new ManyToManyAssociationField('languages', LanguageDefinition::class, ProductDisplayImageLanguageDefinition::class, "display_image_id", "language_id")),

            (new JsonField('use_image_as_cover_for_language', 'useImageAsCoverForLanguage')),
        ]);
    }
}
