<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Custom;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void              add(ProductDisplayImageEntity $entity)
 * @method void              set(string $key, ProductDisplayImageEntity $entity)
 * @method ProductDisplayImageEntity[]    getIterator()
 * @method ProductDisplayImageEntity[]    getElements()
 * @method ProductDisplayImageEntity|null get(string $key)
 * @method ProductDisplayImageEntity|null first()
 * @method ProductDisplayImageEntity|null last()
 */
class ProductDisplayImageCollection extends EntityCollection
{
    protected function getExpectedClass(): string
    {
        return ProductDisplayImageEntity::class;
    }
}
