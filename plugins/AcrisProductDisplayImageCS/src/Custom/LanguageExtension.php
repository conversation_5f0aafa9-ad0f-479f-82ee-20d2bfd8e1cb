<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Custom;

use Acris\ProductDisplayImage\Custom\Aggregate\ProductDisplayImageLanguage\ProductDisplayImageLanguageDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityExtension;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\CascadeDelete;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Inherited;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToManyAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;
use Shopware\Core\System\Language\LanguageDefinition;

class LanguageExtension extends EntityExtension
{
    public function extendFields(FieldCollection $collection): void
    {
        $collection->add(
            (new ManyToManyAssociationField(
                'acrisProductDisplayImages',
                ProductDisplayImageDefinition::class,
                ProductDisplayImageLanguageDefinition::class,
                'language_id',
                'display_image_id'
            ))->addFlags(new CascadeDelete(), new Inherited())
        );
    }

    public function getDefinitionClass(): string
    {
        return LanguageDefinition::class;
    }
}
