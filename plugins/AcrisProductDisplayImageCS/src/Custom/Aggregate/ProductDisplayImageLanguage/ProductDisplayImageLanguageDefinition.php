<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Custom\Aggregate\ProductDisplayImageLanguage;

use Acris\ProductDisplayImage\Custom\ProductDisplayImageDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\CreatedAtField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;
use Shopware\Core\Framework\DataAbstractionLayer\MappingEntityDefinition;
use Shopware\Core\System\Language\LanguageDefinition;

class ProductDisplayImageLanguageDefinition extends MappingEntityDefinition
{
    public CONST ENTITY_NAME = 'acris_product_display_image_language';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new FkField('display_image_id', 'displayImageId', ProductDisplayImageDefinition::class))->addFlags(new PrimaryKey(), new Required()),
            (new FkField('language_id', 'languageId', LanguageDefinition::class))->addFlags(new PrimaryKey(), new Required()),
            new ManyToOneAssociationField('displayImage', 'display_image_id', ProductDisplayImageDefinition::class),
            new ManyToOneAssociationField('language', 'language_id', LanguageDefinition::class),
            new CreatedAtField()
        ]);
    }
}
