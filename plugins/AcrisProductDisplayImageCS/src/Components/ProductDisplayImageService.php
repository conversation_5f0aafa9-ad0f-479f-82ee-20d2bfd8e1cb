<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Components;

use Acris\ProductDisplayImage\Custom\ProductDisplayImageEntity;
use Acris\ProductDisplayImage\Custom\ProductMediaExtension;
use Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaCollection;
use Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaEntity;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\System\SalesChannel\SalesChannelContext;

class ProductDisplayImageService
{
    public function addProductImageAssociationCriteria(string $languageId, Criteria $criteria): void
    {
        if (!$criteria->hasAssociation('media')) {
            $criteria->addAssociation('media');
        }

        if (!$criteria->hasAssociation('cover')) {
            $criteria->addAssociation('cover');
        }

        $criteria->getAssociation('media')
            ->addSorting(new FieldSorting('position'))
            ->addAssociation('acrisProductDisplayImage.languages')
            ->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, [
                new EqualsFilter('acrisProductDisplayImage.languageIds', $languageId),
                new EqualsFilter('acrisProductDisplayImage.languageIds', null),
            ]));

        $criteria->getAssociation('cover')
            ->addSorting(new FieldSorting('position'))
            ->addAssociation('acrisProductDisplayImage.languages')
            ->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, [
                new EqualsFilter('acrisProductDisplayImage.languageIds', $languageId),
                new EqualsFilter('acrisProductDisplayImage.languageIds', null),
            ]));
    }

    public function checkLanguageForProductImage(ProductEntity $product, string $languageId, SalesChannelContext $context): void
    {
        // replace cover for assigned language
        $this->replaceCover($languageId, $product);

        // check media for assigned language
        $media = $product->getMedia();
        if (!empty($media)) {
            foreach ($media->getElements() as $productMediaEntity) {
                if ($productMediaEntity->hasExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION)) {
                    $displayImageItem = $productMediaEntity->getExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION);
                    if(!empty($displayImageItem->getLanguages()) && $displayImageItem->getLanguages()->count() > 0) {
                        $languageExists = false;
                        foreach ($displayImageItem->getLanguages()->getElements() as $languageEntity) {
                            if($languageEntity->getId() === $languageId) $languageExists = true;
                        }

                        if (!$languageExists) {
                            $media->remove($productMediaEntity->getId());
                        }
                    }
                }
            }

            $cover = $product->getCover();
            $coverId = !empty($cover) ? $cover->getId() : null;

            if (!empty($coverId)) {
                $this->sortMedia($media, $coverId);
            }

            // correct positions
            $i = 0;
            foreach ($media->getElements() as $productMediaEntity) {
                $productMediaEntity->setPosition($i);
                $i++;
                if (!empty($cover)) {
                    if ($productMediaEntity->getId() === $cover->getId()) {
                        try {
                            $cover->setPosition($productMediaEntity->getPosition());
                        } catch (\Throwable $e) {
                            $cover->setPosition(0);
                        }
                    }
                }
            }
        }
    }

    private function replaceCover(string $languageId, ProductEntity $product): void
    {
        $media = $product->getMedia();
        if (empty($media)) return;

        foreach ($media->getElements() as $productMediaEntity) {
            if ($productMediaEntity->hasExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION)) {
                /** @var ProductDisplayImageEntity $displayImageItem */
                $displayImageItem = $productMediaEntity->getExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION);

                if (!empty($displayImageItem->getUseImageAsCoverForLanguage())) {
                    foreach ($displayImageItem->getUseImageAsCoverForLanguage() as $fallbackCover) {
                        if (isset($fallbackCover['languageId']) && $fallbackCover['languageId'] === $languageId && isset($fallbackCover['value']) && $fallbackCover['value']) {
                            $product->setCoverId($productMediaEntity->getId());
                            $product->setCover($productMediaEntity);
                            return;
                        }
                    }
                }
            }
        }

        $cover = $product->getCover();

        if (!empty($cover)) {
            if ($cover->hasExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION) && !empty($cover->getExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION))) {
                $displayImageItem = $cover->getExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION);
                if((empty($displayImageItem->getLanguages()) || $displayImageItem->getLanguages()->count() === 0) || ($displayImageItem->getLanguages()->count() > 0 && $displayImageItem->getLanguages()->has($languageId))) {
                    return;
                }
            } else {
                return;
            }
        }

        foreach ($media->getElements() as $productMediaEntity) {
            if ($productMediaEntity->hasExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION) && !empty($productMediaEntity->getExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION))) {
                $displayImageItem = $productMediaEntity->getExtension(ProductMediaExtension::DEFAULT_PRODUCT_MEDIA_DISPLAY_IMAGE_EXTENSION);
                if((empty($displayImageItem->getLanguages()) || $displayImageItem->getLanguages()->count() === 0) || ($displayImageItem->getLanguages()->count() > 0 && $displayImageItem->getLanguages()->has($languageId))) {
                    $product->setCoverId($productMediaEntity->getId());
                    $product->setCover($productMediaEntity);
                    return;
                }
            } else {
                $product->setCoverId($productMediaEntity->getId());
                $product->setCover($productMediaEntity);
                return;
            }
        }
    }

    private function sortMedia(ProductMediaCollection $media, string $sortMediaId): void
    {
        $media->sort(static function(ProductMediaEntity $a, ProductMediaEntity $b) use ($sortMediaId) {
            if ($a->getId() === $sortMediaId ) {
                return -1;
            }

            if ($b->getId() === $sortMediaId) {
                return 1;
            }

            try {
                $aPos = $a->getPosition();
            } catch (\Throwable $e) {
                $aPos = 0;
            }

            try {
                $bPos = $b->getPosition();
            } catch (\Throwable $e) {
                $bPos = 0;
            }

            return $aPos - $bPos;
        });
    }
}
