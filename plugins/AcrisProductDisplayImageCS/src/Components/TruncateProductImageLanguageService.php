<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Components;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SystemConfig\SystemConfigService;

class TruncateProductImageLanguageService
{
    public function __construct
    (
        private readonly SystemConfigService $systemConfigService,
        private readonly Connection $connection,
    ) {
    }

    public function allowTruncate(): bool
    {
        return $this->systemConfigService->get('AcrisProductDisplayImageCS.config.truncateProductDisplayImage') === true;
    }

    public function truncateDisplayImageLanguages(string $productMediaId, $key, array $rawData): array
    {
        try {
            $productMediaId = Uuid::fromHexToBytes($productMediaId);
            $displayImageId = $this->connection->fetchOne('SELECT id FROM acris_product_display_image WHERE product_media_id = ?;', [$productMediaId]);

            if (empty($displayImageId)) {
                return $rawData;
            }

            $displayImageIdHex = Uuid::fromBytesToHex($displayImageId);

            $rawData['media'][$key]['acrisProductDisplayImage']['id'] = $displayImageIdHex;

            foreach ($rawData['media'][$key]['acrisProductDisplayImage']['languages'] as $languageKey => $language) {
                if (empty($language) || !is_array($language) || !array_key_exists('displayImageId', $language)) {
                    continue;
                }

                $rawData['media'][$key]['acrisProductDisplayImage']['languages'][$languageKey]['displayImageId'] = $displayImageIdHex;
            }

            if (!array_key_exists('languages', $rawData['media'][$key]['acrisProductDisplayImage'])) {
                return $rawData;
            }

            $this->connection->executeStatement('DELETE FROM acris_product_display_image_language WHERE display_image_id = ?;', [$displayImageId]);
        } catch (\Throwable $e) {}

        return $rawData;
    }
}
