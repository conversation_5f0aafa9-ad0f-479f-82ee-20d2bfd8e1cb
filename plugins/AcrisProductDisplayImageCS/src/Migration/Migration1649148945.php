<?php declare(strict_types=1);

namespace Acris\ProductDisplayImage\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\InheritanceUpdaterTrait;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1649148945 extends MigrationStep
{
    use InheritanceUpdaterTrait;

    public function getCreationTimestamp(): int
    {
        return 1649148945;
    }

    public function update(Connection $connection): void
    {
        // implement update
        $query = <<<SQL
            CREATE TABLE IF NOT EXISTS `acris_product_display_image` (
                `id` BINARY(16) NOT NULL,
                `product_media_id` BINARY(16) NULL,
                `language_ids` JSON NULL,
                `use_image_as_cover_for_language` JSON NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3) NULL,
                PRIMARY KEY (`id`),
                KEY `fk.acris_product_display_image.product_media_id` (`product_media_id`),
                CONSTRAINT `fk.acris_product_display_image.product_media_id` FOREIGN KEY (`product_media_id`) REFERENCES `product_media` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `json.acris_product_display_image.language_ids` CHECK (JSON_VALID(`language_ids`)),
                CONSTRAINT `json.acris_product_display_image.use_image_as_cover_for_language` CHECK (JSON_VALID(`use_image_as_cover_for_language`))
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;

        $connection->executeStatement($query);

        $query = <<<SQL
            CREATE TABLE IF NOT EXISTS `acris_product_display_image_language` (
                `display_image_id` BINARY(16) NOT NULL,
                `language_id` BINARY(16) NOT NULL,
                `created_at` DATETIME(3) NOT NULL,
                PRIMARY KEY (`display_image_id`,`language_id`),
                KEY `fk.acris_product_display_image_language.display_image_id` (`display_image_id`),
                KEY `fk.acris_product_display_image_language.language_id` (`language_id`),
                CONSTRAINT `fk.acris_product_display_image_language.display_image_id` FOREIGN KEY (`display_image_id`) REFERENCES `acris_product_display_image` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `fk.acris_product_display_image_language.language_id` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;

        $connection->executeStatement($query);

        $this->updateInheritance($connection, 'product_media', 'acrisProductDisplayImage');
        $this->updateInheritance($connection, 'language', 'acrisProductDisplayImages');
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}





