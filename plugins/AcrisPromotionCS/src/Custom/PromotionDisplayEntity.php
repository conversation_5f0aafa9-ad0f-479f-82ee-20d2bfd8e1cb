<?php declare(strict_types=1);

namespace Acris\Promotion\Custom;

use Shopware\Core\Checkout\Promotion\PromotionEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;

class PromotionDisplayEntity extends Entity
{
    use EntityIdTrait;

    /**
     * @var PromotionEntity
     */
    protected $promotion;
    /**
     * @var string|null
     */
    protected $discountDisplay;

    /**
     * @var bool|null
     */
    protected $discountInfo;

    /**
     * @var string|null
     */
    protected $displayPosition;

    /**
     * @return PromotionEntity
     */
    public function getPromotion(): PromotionEntity
    {
        return $this->promotion;
    }

    /**
     * @param PromotionEntity $promotion
     */
    public function setPromotion(PromotionEntity $promotion): void
    {
        $this->promotion = $promotion;
    }

    /**
     * @return string|null
     */
    public function getDiscountDisplay(): ?string
    {
        return $this->discountDisplay;
    }

    /**
     * @param string|null $discountDisplay
     */
    public function setDiscountDisplay(?string $discountDisplay): void
    {
        $this->discountDisplay = $discountDisplay;
    }

    /**
     * @return bool|null
     */
    public function getDiscountInfo(): ?bool
    {
        return $this->discountInfo;
    }

    /**
     * @param bool|null $discountInfo
     */
    public function setDiscountInfo(?bool $discountInfo): void
    {
        $this->discountInfo = $discountInfo;
    }

    /**
     * @return string|null
     */
    public function getDisplayPosition(): ?string
    {
        return $this->displayPosition;
    }

    /**
     * @param string|null $displayPosition
     */
    public function setDisplayPosition(?string $displayPosition): void
    {
        $this->displayPosition = $displayPosition;
    }
}
