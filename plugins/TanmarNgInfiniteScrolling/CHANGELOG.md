# 1.5.3
- Compatibility with other plugins has been improved

# 1.5.2
- Added link to improve seo optimization

# 1.5.1
- Compatibility with other plugins has been improved

# 1.5.0
- adjustment for shopware version 6.5

# 1.4.7
- Added link to improve seo optimization

# 1.4.6
- Compatibility with other plugins has been improved

# 1.4.5
- Compatibility with other plugins has been improved

# 1.4.4
- Compatibility with other plugins has been improved

# 1.4.3
- Compatibility with other plugins has been improved

# 1.4.2
- Moved style element to html header

# 1.4.1
- Bugfix: account orders

# 1.4.0
- Improved compatibility with shopware 6.4.17
- Bugfix: observer is null on filter reset
- Bugfix: Products are not loaded after browser back
- Added custom selector for the product box

# 1.3.1
- Added configuration to only search for products within the listing.

# 1.3.0
- Added trigger for afterRenderResponse event

# 1.2.0
- Compatibility with other plugins has been improved
- Fixed a bug where a configuration caused an error in the storefront.

# 1.1.2
- Added loading class on filter change
- Added custom selectors

# 1.1.1
- Bugfix: Uncaught Error: Call to a member function get() on null

# 1.1.0
- added shopware 6.4 compatibility

# 1.0.11
- Removed loading class, causing product boxes to change their height

# 1.0.10
- Fixed a bug where the navigation box to appear multiple times in mobile

# 1.0.9
- Fixed a bug where the browser scrolles to the top of the listing as soon as a new page is loaded
- Fixed a bug where the product boxes append to the listing after sorting
- Fixed a bug where listing ajax was called twice after sorting

# 1.0.8
- Fixed a bug in the plugin configuration for each sales channel

# 1.0.7
- Fixed a bug in the plugin configuration for each sales channel
- Fixed a bug where the the next box does not show up after sorting
- HTML structure improved

# 1.0.6
- Fixed a bug in the HTML structure

# 1.0.5
- Improved shopware 6.3 compatibility

# 1.0.4
- Fixed a bug where images were not displayed

# 1.0.3
- Fixed a bug in the sorting action

# 1.0.2
- fixed a bug in 1.0.1 update

# 1.0.1
- added config for number of pages to be loaded

# 1.0.0
- first version for shopware 6
