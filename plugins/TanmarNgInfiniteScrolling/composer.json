{"name": "tanmar/infinite-scrolling", "description": "Infinite Scrolling", "version": "1.5.3", "type": "shopware-platform-plugin", "license": "GPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>"}], "require": {"shopware/core": "~6.5", "shopware/storefront": "*"}, "extra": {"shopware-plugin-class": "Tanmar\\NgInfiniteScrolling\\TanmarNgInfiniteScrolling", "copyright": "(c) by <PERSON><PERSON>", "label": {"de-DE": "Infinite Scrolling für Shopware 6", "en-GB": "Infinite Scrolling for Shopware 6"}, "description": {"de-DE": "<PERSON>t diesem Plugin rüsten Sie eine Infinite Scrolling Funktion für die Produktlisten in Ihrem Shopware 6 Shop nach, um Kunden das Stöbern in großen Kategorien zu erleichtern.", "en-GB": "The plugin implements an Infinite Scrolling feature for all product lists in your shop. Thereby browsing large categories becomes more customer-friendly."}, "manufacturerLink": {"de-DE": "https://www.tanmar.de", "en-GB": "https://www.tanmar.de"}, "supportLink": {"de-DE": "https://www.tanmar.de/kontakt/", "en-GB": "https://www.tanmar.de/kontakt/"}}, "autoload": {"psr-4": {"Tanmar\\NgInfiniteScrolling\\": "src/"}}, "autoload-dev": {"psr-4": {"Tanmar\\NgInfiniteScrolling\\Test\\": "tests/"}}}