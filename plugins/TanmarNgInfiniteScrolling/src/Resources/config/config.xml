<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>Basic Configuration</title>
        <title lang="de-DE">Allgemeine Konfiguration</title>
        <input-field type="bool">
            <name>active</name>
            <label>Active</label>
            <label lang="de-DE">Aktiv</label>
        </input-field>

        <input-field type="int">
            <name>pages</name>
            <defaultValue>2</defaultValue>
            <label>Number of pages to be loaded</label>
            <label lang="de-DE">Anzahl der Seiten die nachgeladen werden sollen</label>
        </input-field>
        
        <input-field type="text">
            <name>customProductSelector</name>
            <defaultValue></defaultValue>
            <label>Custom selector for the product boxes</label>
            <label lang="de-DE">Benutzerdefinierter Selektor für die Produktboxen</label>
        </input-field>

        <input-field type="text">
            <name>customPrepend</name>
            <defaultValue></defaultValue>
            <label>Custom selector for the prev box</label>
            <label lang="de-DE">Benutzerdefinierter Selektor für die Zurück Box</label>
        </input-field>

        <input-field type="text">
            <name>customAppend</name>
            <defaultValue></defaultValue>
            <label>Custom selector for the next box</label>
            <label lang="de-DE">Benutzerdefinierter Selektor für die Weiter Box</label>
        </input-field>
        
        <input-field type="text">
            <name>customPaginationSelector</name>
            <defaultValue></defaultValue>
            <label>Custom selector for the pagination</label>
            <label lang="de-DE">Benutzerdefinierter Selektor für die Paginierung</label>
        </input-field>
    </card>
    <card>
        <title>Advanced setting</title>
        <title lang="de-DE">Erweiterte Einstellung</title>

        <input-field type="text">
            <name>rootMargin</name>
            <defaultValue>0px</defaultValue>
            <label>Intersection Observer API: rootMargin</label>
            <label lang="de-DE">Intersection Observer API: rootMargin</label>
            <helpText>RootMargin property of the Intersection Observer API, it defines how much distance to the edge of the page should be kept, from when a new page should be loaded.</helpText>
            <helpText lang="de-DE">RootMargin Eigenschaft der Intersection Observer API, sie definiert wie viel Abstand zu Rand der Seite eingehalten werden soll, ab wann eine neue Seite geladen werden soll.</helpText>
        </input-field>

        <input-field type="text">
            <name>threshold</name>
            <defaultValue>0.5</defaultValue>
            <label>Intersection Observer API: threshold</label>
            <label lang="de-DE">Intersection Observer API: threshold</label>
            <helpText>Threshold property of the Intersection Observer API, it defines how many pixels visibility of the last products a new page should be loaded.</helpText>
            <helpText lang="de-DE">Threshold Eigenschaft der Intersection Observer API, sie legt fest ab wie viel Pixel Sichtbarkeit der letzen Produkte eine neue Seite geladen werden soll.</helpText>
        </input-field>

        <input-field type="bool">
            <name>debug</name>
            <defaultValue>false</defaultValue>
            <label>Debug</label>
            <label lang="de-DE">Debug</label>
            <helpText>Displays information in the browser console.</helpText>
            <helpText lang="de-DE">Zeigt informationen in der Browserkonsole an.</helpText>
        </input-field>

        <input-field type="bool">
            <name>triggerAfterRenderResponseEvent</name>
            <defaultValue>false</defaultValue>
            <label>Trigger Shopware afterRenderResponse Event</label>
            <label lang="de-DE">Löse Shopwares afterRenderResponse Event aus</label>
            <helpText>Triggers Shopware's afterRenderResponse javascript event after a new page is loaded.</helpText>
            <helpText lang="de-DE">Löst Shopwares afterRenderResponse javascript Event aus, nachdem eine neue Seite geladen wurde.</helpText>
        </input-field>

        <input-field type="bool">
            <name>onlyObserveWithinListingWrapper</name>
            <defaultValue>false</defaultValue>
            <label>Only search for products within "cms-element-product-listing-wrapper"</label>
            <label lang="de-DE">Nur innerhalb des Blockes "cms-element-product-listing-wrapper" nach Produkten suchen</label>
            <helpText>Switch this option on if you have other elements in your layout that contain products in addition to the listing</helpText>
            <helpText lang="de-DE">Schalten Sie diese Option ein, wenn Sie neben dem Listing noch andere Elemente in Ihrem Layout haben, die Produkte enthalten</helpText>
        </input-field>

    </card>
</config>
