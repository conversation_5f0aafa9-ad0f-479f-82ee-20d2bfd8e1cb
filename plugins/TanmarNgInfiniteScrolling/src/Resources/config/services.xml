<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="Tanmar\NgInfiniteScrolling\Service\ConfigService">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="request_stack"/>
        </service>
        <service id="Tanmar\NgInfiniteScrolling\Storefront\Page\StorefrontRenderSubscriber">
            <argument type="service" id="Tanmar\NgInfiniteScrolling\Service\ConfigService"/>
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container> 