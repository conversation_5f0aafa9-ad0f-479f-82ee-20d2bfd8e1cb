"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["tanmar-ng-infinite-scrolling"],{5886:(t,e,i)=>{i(8089);class n extends(PluginManager.getPlugin("FilterBoolean").get("class")){_onChangeCheckbox(){var t=this.listing;t._tmisActive?(t._tmisListingOption="override",t._visitedPagesClear(),t._tmisNewPageRequestCounter=0,t._tmisIsLoading=!0,t._tmisLog("  reset _onChangeCheckbox"),t.changeListing()):super._onChangeCheckbox()}}i(5410);class s extends(PluginManager.getPlugin("FilterMultiSelect").get("class")){_onChangeFilter(){var t=this.listing;t._tmisActive?(t._tmisListingOption="override",t._visitedPagesClear(),t._tmisNewPageRequestCounter=0,t._tmisIsLoading=!0,t._tmisLog("  reset _onChangeFilter"),t.changeListing()):super._onChangeFilter()}}i(4699);class r extends(PluginManager.getPlugin("FilterRange").get("class")){_onChangeInput(){var t=this;t.listing._tmisActive?(clearTimeout(t._timeout),t._timeout=setTimeout(function(){var t=this,e=t.listing;t._isInputInvalid()?t._setError():t._removeError(),e._tmisListingOption="override",e._visitedPagesClear(),e._tmisNewPageRequestCounter=0,e._tmisIsLoading=!0,e._tmisLog("  reset _onChangeInput"),e.changeListing()}.bind(t),t.options.inputTimeout)):super._onChangeInput()}}i(158);class o extends(PluginManager.getPlugin("FilterRatingSelect").get("class")){_onChangeFilter(){var t=this.listing;t._tmisActive?(t._tmisListingOption="override",t._visitedPagesClear(),t._tmisNewPageRequestCounter=0,t._tmisIsLoading=!0,t._tmisLog("  reset property _onChangeRating"),t.changeListing()):super._onChangeFilter()}}i(3629);class a extends(PluginManager.getPlugin("FilterPropertySelect").get("class")){_onChangeFilter(){var t=this.listing;t._tmisActive?(t._tmisListingOption="override",t._visitedPagesClear(),t._tmisNewPageRequestCounter=0,t._tmisIsLoading=!0,t._tmisLog("  reset property _onChangeFilter"),t.changeListing()):super._onChangeFilter()}}i(9737);class l extends(PluginManager.getPlugin("ListingSorting").get("class")){onChangeSorting(t){var e=this.listing;e._tmisActive?(this.options.sorting=t.target.value,e._tmisListingOption="override",e._visitedPagesClear(),e._tmisNewPageRequestCounter=0,e._tmisIsLoading=!0,e._tmisLog("  reset onChangeSorting"),e.changeListing()):super.onChangeSorting(t)}afterContentChange(){this.listing._tmisActive||super.afterContentChange(event)}}i(3139);var c=i(5362),u=i(9068),h=i(7906);class g extends(u.Z.getPlugin("Listing").get("class")){init(){var t=this;t._tmisVersion="1.5.3",t._tmisDebug=!!window._tanmarInfiniteScrolling&&!!window._tanmarInfiniteScrolling.debug,t._tmisActive=!1,t._tmisNewPageRequestCounter=0,t._tmisIsLoading=!1,t._tmisListingElement=null,t._tmisListingOption="",t._tmisSnippets={},t._tmisNewPageRequestMax=1,t._listingRowSelector=".cms-element-product-listing-wrapper .cms-listing-row",t._paginationResponseSelector=".cms-element-product-listing-wrapper .pagination-nav",t._paginationSelector=".pagination-nav",t._triggerAfterRenderResponseEvent=!1,t._onlyObserveWithinListingWrapper=!1,t._customProductSelector="",t.iObserver=!1,t.options.scrollTopListingWrapper=!1,t._tmisIsB2B=!1,super.init(),t._tmisInit()}_tmisInit(){var t=this;if(t._tmisLog("TanmarInfiniteScrolling v"+t._tmisVersion),!document.querySelector("body.is-tanmar-infinite-scrolling"))return t._tmisLog('  error: css class "is-tanmar-infinite-scrolling" in body not found'),void this._tmisDestroy();if(!window._tanmarInfiniteScrolling)return t._tmisLog("  error: cant find window._tanmarInfiniteScrolling"),void this._tmisDestroy();t._tmisLog("  config ",window._tanmarInfiniteScrolling),t._tmisNewPageRequestMax=parseInt(window._tanmarInfiniteScrolling.pages,10),t._tmisSnippets=window._tanmarInfiniteScrolling.snippets,t._tmisLog("  snippets success ",t._tmisSnippets),t._triggerAfterRenderResponseEvent=window._tanmarInfiniteScrolling.triggerAfterRenderResponseEvent,t._onlyObserveWithinListingWrapper=window._tanmarInfiniteScrolling.onlyObserveWithinListingWrapper,t._customProductSelector=window._tanmarInfiniteScrolling.customProduct;let e=window._tanmarInfiniteScrolling.customPaginationSelector+"";""!=e.trim()&&(t._paginationSelector=e),window._tanmarInfiniteScrolling.visitedPages||(window._tanmarInfiniteScrolling.visitedPages=[]),t._tmisActive=!0;var i=document.querySelectorAll(t._paginationSelector);return i.length<=0?(t._tmisLog('  error - no pagination found: "'+t._paginationSelector+'" not found'),void this._tmisDestroy()):(i.forEach((t=>{t.classList.add("tmis-d-none")})),t._tmisIsB2B=document.querySelectorAll(".b2b-table .b2b-listing-name").length>0,t._tmisIsB2B?(t._listingRowSelector=".cms-element-product-listing-wrapper .b2b-table > tbody",this._tmisDestroy()):(t._tmisRegisterIntersectionObserver(),t._tmisSetCurrentAndLastPage(i),t.lastPage>t.currentPage&&(t._tmisLog("  register oberver on init"),t._tmisObserveLastProductBox()),t._visitedPagesAdd(t.currentPage),t._tmisListingElement=document.querySelector(this.options.cmsProductListingSelector),t._tmisLog("  currentPage = "+t.currentPage+" - lastPage = "+t.lastPage+" - visitedPages:"),t._tmisLog(window._tanmarInfiniteScrolling.visitedPages),t.currentPage>1&&t._tmisBuildPrevInfoBox(),void(0==t._tmisNewPageRequestMax&&t._tmisBuildNextInfoBox())))}_tmisSetCurrentAndLastPage(t){var e=this;let i=1,n=1;if(t&&t[0]){let s=t[0].querySelector(".page-item.active input");s?i=parseInt(s.value,10):e._tmisLog("  can't find 'page-item.active input'");let r=t[0].querySelector(".page-item.page-last input");r?n=parseInt(r.value,10):e._tmisLog("  can't find 'page-item.page-last input'"),e.currentPage=i,e.lastPage=n}}_visitedPagesAdd(t){this._visitedPagesIndexOf(t)<0&&window._tanmarInfiniteScrolling.visitedPages.push(t)}_visitedPagesIndexOf(t){return window._tanmarInfiniteScrolling.visitedPages.indexOf(t)}_visitedPagesClear(){window._tanmarInfiniteScrolling.visitedPages=[]}_tmisLog(){this._tmisDebug&&console.log(...arguments)}_tmisRegisterIntersectionObserver(){var t={root:null,rootMargin:window._tanmarInfiniteScrolling.rootMargin||"0px",threshold:this._tmisSafeThreshold(window._tanmarInfiniteScrolling.threshold||.5)};this.iObserver=new IntersectionObserver(this._tmisOnIntersection.bind(this),t)}_tmisSafeThreshold(t){var e;try{e=JSON.parse(t)}catch(i){e=t}if(Array.isArray(e))return e;var i=parseFloat(e,10);return isNaN(i)?.5:i}_getAllProductBoxes(){let t=".card.product-box";return this._tmisIsB2B&&(t=".b2b-table .b2b-listing-name"),this._customProductSelector&&""!=this._customProductSelector&&(t=this._customProductSelector),this._onlyObserveWithinListingWrapper&&(t=".cms-element-product-listing-wrapper "+t),document.querySelectorAll(t)}_tmisObserveLastProductBox(){var t=[];try{t=this._getAllProductBoxes()}catch(t){}if(t.length<=0)return this._tmisLog('  error "last product" not found'),void this._tmisDestroy();var e=t[t.length-1];return this._tmisLog("  observe following element"),this._tmisLog(e),this.iObserver||this._tmisRegisterIntersectionObserver(),this.iObserver.observe(e),e}_tmisOnIntersection(t,e){var i=this;t.forEach((t=>{if(t.intersectionRatio>0&&!i._tmisIsLoading){const n=document.querySelector(".pagination .page-next");n&&(i._tmisLog("  on intersection"),n.classList.contains("disabled")?(i._tmisLog("  no new page, unobserve element"),i._tmisLog(t.target),e.unobserve(t.target)):i._tmisNewPageRequestCounter<i._tmisNewPageRequestMax&&(i._tmisRequestNewPage(n,"append"),i._tmisLog("  request new page, unobserve element"),i._tmisLog(t.target),e.unobserve(t.target)))}}))}_tmisRequestNewPage(t,e){var i,n=this;switch(n._tmisIsLoading=!0,n._tmisLog("  Request new page"),e){case"append":n._tmisListingOption="append";break;case"prepend":n._tmisListingOption="prepend";break;default:n._tmisListingOption="override"}i=isNaN(t)?parseInt(t.querySelector("input").value,10):parseInt(t,10);u.Z.getPluginInstanceFromElement(document.querySelector("[data-listing-pagination]"),"ListingPagination").onChangePage({target:{value:i}}),n._tmisNewPageRequestCounter++,n._tmisBuildLoading()}_tmisBuildLoading(){const t=document.createElement("div");t.classList.add("text-center"),t.classList.add("infinite-scrolling-loading");const e=new h.Z(t);var i=document.querySelectorAll(this._listingRowSelector);const n=i[i.length-1];if("append"===this._tmisListingOption)n.parentNode.insertBefore(t,n.nextSibling);else i[0].parentNode.insertBefore(t,i[0]);e.create()}_tmisGetNewPageParam(t,e){var i=t.split("?"),n=[];void 0!==i[1]&&(n=(n=i[1].split("&").map((t=>t.split("=")))).filter((t=>""!=t)));var s=!1;return n=n.map((t=>("p"==t[0]&&(s=!0,t[1]=parseInt(t[1])+e),t))),s||n.push(["p",1]),i[0]+"?"+n.map((t=>t.join("="))).join("&")}_tmisBuildPrevInfoBox(){var t=this;if(null!==document.querySelector(".infinite-scrolling-button-prev"))return;const e=document.createElement("div");e.classList.add("text-center"),e.classList.add("infinite-scrolling-button-prev");var i=t.currentPage-1;if(i>0){const s=i/(t.lastPage>0?t.lastPage:1)*100;let r=t._tmisSnippets.prev.navi.split("{x}").join(i).split("{y}").join(t.lastPage),o=this._tmisGetNewPageParam(document.location.href,-1);if(e.innerHTML=`<a class="btn btn-block btn-buy" href="${o}">${t._tmisSnippets.prev.btn}</a>\n                            <span class="tanmar-infinity-scrolling-button-text">\n                                <span>${r}</span>\n                                <span class="tanmar-infinity-scrolling-button-bar">\n                                    <span style="width: ${s}%"></span>\n                                </span>\n                            </span>`,""!=window._tanmarInfiniteScrolling.customPrepend){let t=document.querySelector(window._tanmarInfiniteScrolling.customPrepend);null!==t&&t.appendChild(e)}else t._tmisListingElement.insertBefore(e,t._tmisListingElement.firstChild);var n=document.querySelector(".infinite-scrolling-button-prev a");n&&n.addEventListener("click",function(t){t.preventDefault();var e=this[0],i=this[1];if(!e._tmisIsLoading){e._tmisRequestNewPage(i,"prepend");const t=document.querySelector(".infinite-scrolling-button-prev");t.parentNode.removeChild(t)}return!1}.bind([t,i]))}}_tmisBuildNextInfoBox(){var t=this;if(null===document.querySelector(".infinite-scrolling-button-more")){var e=parseInt(t.currentPage,10)+1;if(t._tmisLog("  nextPage = "+e+" lastPage = "+t.lastPage),t._visitedPagesIndexOf(e)<0&&e<=t.lastPage){const n=document.createElement("div");n.classList.add("text-center"),n.classList.add("infinite-scrolling-button-more");const s=t.currentPage/(t.lastPage>0?t.lastPage:1)*100;let r=t._tmisSnippets.next.navi.split("{x}").join(e).split("{y}").join(t.lastPage),o=this._tmisGetNewPageParam(document.location.href,1);if(n.innerHTML=`<a class="btn btn-block btn-buy" href="${o}">${t._tmisSnippets.next.btn}</a>\n                            <span class="tanmar-infinity-scrolling-button-text">\n                                <span>${r}</span>\n                                <span class="tanmar-infinity-scrolling-button-bar">\n                                    <span style="width: ${s}%"></span>\n                                </span>\n                            </span>`,""!=window._tanmarInfiniteScrolling.customAppend){let t=document.querySelector(window._tanmarInfiniteScrolling.customAppend);null!==t&&t.appendChild(n)}else t._tmisListingElement.appendChild(n);var i=document.querySelector(".infinite-scrolling-button-more a");i&&i.addEventListener("click",function(t){t.preventDefault();var e=this[0],i=this[1];if(!e._tmisIsLoading){e._tmisRequestNewPage(i,"append");const t=document.querySelector(".infinite-scrolling-button-more");t.parentNode.removeChild(t)}return!1}.bind([t,e]))}}}renderResponse(t){var e=this;if(!e._tmisActive)return super.renderResponse(t);e._tmisIsLoading=!1,document.querySelectorAll(".infinite-scrolling-loading").forEach((t=>{t.parentNode.removeChild(t)}));var i=(new DOMParser).parseFromString(t,"text/html"),n=i.querySelector(e._listingRowSelector);if(n||(e._tmisLog("  content is null, responseHtml:"),e._tmisLog(i)),e._tmisHandleResponsePagination(i),e._visitedPagesIndexOf(e.currentPage)<0){e._visitedPagesAdd(e.currentPage),e._tmisLog("  currentPage = "+e.currentPage+" - lastPage = "+e.lastPage+" - visitedPages:"),e._tmisLog(window._tanmarInfiniteScrolling.visitedPages);var s=document.querySelectorAll(e._listingRowSelector);if(s.length>0){var r=s[s.length-1];switch(e._tmisLog('  renderResponse "'+e._tmisListingOption+'"'),e._tmisListingOption){case"append":e._tmisListingAppend(r,n);break;case"prepend":e._tmisListingPrepend(r,n);break;default:e._tmisListingOverride(s,n)}this._registry.forEach((t=>{"function"==typeof t.afterContentChange&&t.afterContentChange()})),window.PluginManager.initializePlugins(),e._triggerAfterRenderResponseEvent&&this.$emitter.publish("Listing/afterRenderResponse",{response:t}),e.lastPage>e.currentPage&&(e._tmisLog("  register new oberver"),e._tmisObserveLastProductBox()),e._tmisAfterContentChange(n),this.$emitter.publish("TanmarInfiniteScrolling/afterRenderResponse",{response:t})}}else e._tmisLog("  page "+e.currentPage+" already loaded - "+e._tmisListingOption)}_tmisListingAppend(t,e){var i=this;t.innerHTML+=e.innerHTML,i._tmisNewPageRequestCounter>=i._tmisNewPageRequestMax&&i._tmisBuildNextInfoBox()}_tmisListingPrepend(t,e){t.innerHTML=e.innerHTML+t.innerHTML,this.currentPage>1&&this._tmisBuildPrevInfoBox()}_tmisListingOverride(t,e){var i=this;Array.from(t).forEach(((t,i)=>{0==i?t.innerHTML=e.innerHTML:t&&t.parentNode&&t.parentNode.removeChild(t)}));let n=document.querySelector(".infinite-scrolling-button-prev");n&&n.parentNode.removeChild(n),n=document.querySelector(".infinite-scrolling-button-more"),n&&n.parentNode.removeChild(n),i.lastPage>i.currentPage&&(i._tmisNewPageRequestCounter=0),i._tmisNewPageRequestCounter>=i._tmisNewPageRequestMax&&i._tmisBuildNextInfoBox()}_tmisHandleResponsePagination(t){var e=this,i=t.querySelector(e._paginationResponseSelector);i?(document.querySelectorAll(".pagination-nav").length<=0?document.querySelector("body").append(i):c.Z.replaceElement(i,document.querySelectorAll(this._paginationSelector)),e.currentPage=parseInt(i.querySelector(".page-item.active input").value,10),e.lastPage=parseInt(i.querySelector(".page-item.page-last input").value,10)):(document.querySelectorAll(".pagination-nav").forEach((t=>{t.innerHTML=""})),e._tmisLog("  remove pagination-nav"),e.currentPage=1,e.lastPage=1)}_tmisAfterContentChange(t){if(t){var e=t.querySelectorAll("img"),i=t.querySelectorAll("img");e.forEach(((t,e)=>{t.outerHTML=i[e].outerHTML}))}}resetAllFilter(){var t=this;t._tmisActive&&(t._tmisListingOption="override",t._visitedPagesClear(),t._tmisNewPageRequestCounter=0,t._tmisIsLoading=!0,t._tmisLog("  reset resetAllFilter")),super.resetAllFilter()}resetFilter(t){var e=this;e._tmisActive&&(e._tmisListingOption="override",e._visitedPagesClear(),e._tmisNewPageRequestCounter=0,e._tmisIsLoading=!0,e._tmisLog("  reset resetFilter")),super.resetFilter(t)}addLoadingElementLoaderClass(){if(!this._tmisActive)return super.addLoadingElementLoaderClass();"override"==this._tmisListingOption&&super.addLoadingElementLoaderClass()}removeLoadingElementLoaderClass(){if(!this._tmisActive)return super.removeLoadingElementLoaderClass();"override"==this._tmisListingOption&&super.removeLoadingElementLoaderClass()}refreshRegistry(){if(!this._tmisActive)return super.refreshRegistry();const t=this._registry.filter((t=>document.body.contains(t.el)));super.init(),this._registry=t,window.PluginManager.initializePlugins()}_onWindowPopstate(){var t=this;t._tmisActive&&(t._tmisListingOption="override",t._visitedPagesClear(),t._tmisNewPageRequestCounter=0,t._tmisIsLoading=!0,t._tmisLog("  reset _onWindowPopstate")),super._onWindowPopstate()}_tmisDestroy(){this._tmisActive=!1,this._tmisLog("  _tmisDestroy"),document.querySelectorAll(".tmis-d-none").forEach((t=>{t.classList.remove("tmis-d-none")})),document.querySelectorAll("body").forEach((t=>{t.classList.remove("is-tanmar-infinite-scrolling")}))}}var d=i(6285);class p extends d.Z{init(){this.debug=!1,window._tanmarInfiniteScrolling?(window._tanmarInfiniteScrolling&&window._tanmarInfiniteScrolling.debug&&(this.debug=!0),setTimeout((t=>{this.testListingPlugin()}),100)):this.log("Plugin not active or template error: cant find window._tanmarInfiniteScrolling")}testListingPlugin(){try{if(!PluginManager.getPluginInstances("Listing")[0]._tmisInit){this.error("TanmarInfiniteScrolling selftest: Listing override failed"),PluginManager.getPluginList().SwagCmsExtensionsQuickview&&this.error("  Shopware CMS Extensions detected, Infinite Scrolling is not compatible with CMS Extensions")}}catch(t){}}log(t){this.debug&&console.log(t)}error(t){this.debug&&console.error(t)}}if(PluginManager.register("TanmarInfiniteScrollingHelper",p),document.querySelector(".is-tanmar-infinite-scrolling")){!function(t,e){if("IntersectionObserver"in t&&"IntersectionObserverEntry"in t&&"intersectionRatio"in t.IntersectionObserverEntry.prototype)"isIntersecting"in t.IntersectionObserverEntry.prototype||Object.defineProperty(t.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var i=[];s.prototype.THROTTLE_TIMEOUT=100,s.prototype.POLL_INTERVAL=null,s.prototype.USE_MUTATION_OBSERVER=!0,s.prototype.observe=function(t){if(!this._observationTargets.some((function(e){return e.element==t}))){if(!t||1!=t.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:t,entry:null}),this._monitorIntersections(),this._checkForIntersections()}},s.prototype.unobserve=function(t){this._observationTargets=this._observationTargets.filter((function(e){return e.element!=t})),this._observationTargets.length||(this._unmonitorIntersections(),this._unregisterInstance())},s.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorIntersections(),this._unregisterInstance()},s.prototype.takeRecords=function(){var t=this._queuedEntries.slice();return this._queuedEntries=[],t},s.prototype._initThresholds=function(t){var e=t||[0];return Array.isArray(e)||(e=[e]),e.sort().filter((function(t,e,i){if("number"!=typeof t||isNaN(t)||t<0||t>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return t!==i[e-1]}))},s.prototype._parseRootMargin=function(t){var e=(t||"0px").split(/\s+/).map((function(t){var e=/^(-?\d*\.?\d+)(px|%)$/.exec(t);if(!e)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(e[1]),unit:e[2]}}));return e[1]=e[1]||e[0],e[2]=e[2]||e[0],e[3]=e[3]||e[1],e},s.prototype._monitorIntersections=function(){this._monitoringIntersections||(this._monitoringIntersections=!0,this.POLL_INTERVAL?this._monitoringInterval=setInterval(this._checkForIntersections,this.POLL_INTERVAL):(r(t,"resize",this._checkForIntersections,!0),r(e,"scroll",this._checkForIntersections,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in t&&(this._domObserver=new MutationObserver(this._checkForIntersections),this._domObserver.observe(e,{attributes:!0,childList:!0,characterData:!0,subtree:!0}))))},s.prototype._unmonitorIntersections=function(){this._monitoringIntersections&&(this._monitoringIntersections=!1,clearInterval(this._monitoringInterval),this._monitoringInterval=null,o(t,"resize",this._checkForIntersections,!0),o(e,"scroll",this._checkForIntersections,!0),this._domObserver&&(this._domObserver.disconnect(),this._domObserver=null))},s.prototype._checkForIntersections=function(){var e=this._rootIsInDom(),i=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(s){var r=s.element,o=a(r),l=this._rootContainsTarget(r),c=s.entry,u=e&&l&&this._computeTargetAndRootIntersection(r,i),h=s.entry=new n({time:t.performance&&performance.now&&performance.now(),target:r,boundingClientRect:o,rootBounds:i,intersectionRect:u});c?e&&l?this._hasCrossedThreshold(c,h)&&this._queuedEntries.push(h):c&&c.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)},s.prototype._computeTargetAndRootIntersection=function(i,n){if("none"!=t.getComputedStyle(i).display){for(var s,r,o,l,u,h,g,d,p=a(i),m=c(i),_=!1;!_;){var v=null,f=1==m.nodeType?t.getComputedStyle(m):{};if("none"==f.display)return;if(m==this.root||m==e?(_=!0,v=n):m!=e.body&&m!=e.documentElement&&"visible"!=f.overflow&&(v=a(m)),v&&(s=v,r=p,o=void 0,l=void 0,u=void 0,h=void 0,g=void 0,d=void 0,o=Math.max(s.top,r.top),l=Math.min(s.bottom,r.bottom),u=Math.max(s.left,r.left),d=l-o,!(p=(g=(h=Math.min(s.right,r.right))-u)>=0&&d>=0&&{top:o,bottom:l,left:u,right:h,width:g,height:d})))break;m=c(m)}return p}},s.prototype._getRootRect=function(){var t;if(this.root)t=a(this.root);else{var i=e.documentElement,n=e.body;t={top:0,left:0,right:i.clientWidth||n.clientWidth,width:i.clientWidth||n.clientWidth,bottom:i.clientHeight||n.clientHeight,height:i.clientHeight||n.clientHeight}}return this._expandRectByRootMargin(t)},s.prototype._expandRectByRootMargin=function(t){var e=this._rootMarginValues.map((function(e,i){return"px"==e.unit?e.value:e.value*(i%2?t.width:t.height)/100})),i={top:t.top-e[0],right:t.right+e[1],bottom:t.bottom+e[2],left:t.left-e[3]};return i.width=i.right-i.left,i.height=i.bottom-i.top,i},s.prototype._hasCrossedThreshold=function(t,e){var i=t&&t.isIntersecting?t.intersectionRatio||0:-1,n=e.isIntersecting?e.intersectionRatio||0:-1;if(i!==n)for(var s=0;s<this.thresholds.length;s++){var r=this.thresholds[s];if(r==i||r==n||r<i!=r<n)return!0}},s.prototype._rootIsInDom=function(){return!this.root||l(e,this.root)},s.prototype._rootContainsTarget=function(t){return l(this.root||e,t)},s.prototype._registerInstance=function(){i.indexOf(this)<0&&i.push(this)},s.prototype._unregisterInstance=function(){var t=i.indexOf(this);-1!=t&&i.splice(t,1)},t.IntersectionObserver=s,t.IntersectionObserverEntry=n}function n(t){this.time=t.time,this.target=t.target,this.rootBounds=t.rootBounds,this.boundingClientRect=t.boundingClientRect,this.intersectionRect=t.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0},this.isIntersecting=!!t.intersectionRect;var e=this.boundingClientRect,i=e.width*e.height,n=this.intersectionRect,s=n.width*n.height;this.intersectionRatio=i?s/i:this.isIntersecting?1:0}function s(t,e){var i,n,s,r=e||{};if("function"!=typeof t)throw new Error("callback must be a function");if(r.root&&1!=r.root.nodeType)throw new Error("root must be an Element");this._checkForIntersections=(i=this._checkForIntersections.bind(this),n=this.THROTTLE_TIMEOUT,s=null,function(){s||(s=setTimeout((function(){i(),s=null}),n))}),this._callback=t,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(r.rootMargin),this.thresholds=this._initThresholds(r.threshold),this.root=r.root||null,this.rootMargin=this._rootMarginValues.map((function(t){return t.value+t.unit})).join(" ")}function r(t,e,i,n){"function"==typeof t.addEventListener?t.addEventListener(e,i,n||!1):"function"==typeof t.attachEvent&&t.attachEvent("on"+e,i)}function o(t,e,i,n){"function"==typeof t.removeEventListener?t.removeEventListener(e,i,n||!1):"function"==typeof t.detatchEvent&&t.detatchEvent("on"+e,i)}function a(t){var e;try{e=t.getBoundingClientRect()}catch(t){}return e?(e.width&&e.height||(e={top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:e.right-e.left,height:e.bottom-e.top}),e):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function l(t,e){for(var i=e;i;){if(i==t)return!0;i=c(i)}return!1}function c(t){var e=t.parentNode;return e&&11==e.nodeType&&e.host?e.host:e}}(window,document);const t=window.PluginManager;t.override("FilterBoolean",n,"[data-filter-boolean]"),t.override("FilterMultiSelect",s,"[data-filter-multi-select]"),t.override("FilterPropertySelect",a,"[data-filter-property-select]"),t.override("FilterRange",r,"[data-filter-range]"),t.override("FilterRatingSelect",o,"[data-filter-rating-select]"),t.override("ListingSorting",l,"[data-listing-sorting]"),t.override("Listing",g,"[data-listing]")}},6536:(t,e,i)=>{i.d(e,{Z:()=>l});var n,s,r,o=i(6285),a=i(3206);class l extends o.Z{_init(){super._init(),this._validateMethods();const t=a.Z.querySelector(document,this.options.parentFilterPanelSelector);this.listing=window.PluginManager.getPluginInstanceFromElement(t,"Listing"),this.listing.registerFilter(this),this._preventDropdownClose()}_preventDropdownClose(){const t=a.Z.querySelector(this.el,this.options.dropdownSelector,!1);t&&t.addEventListener("click",(t=>{t.stopPropagation()}))}_validateMethods(){if("function"!=typeof this.getValues)throw new Error(`[${this._pluginName}] Needs the method "getValues"'`);if("function"!=typeof this.getLabels)throw new Error(`[${this._pluginName}] Needs the method "getLabels"'`);if("function"!=typeof this.reset)throw new Error(`[${this._pluginName}] Needs the method "reset"'`);if("function"!=typeof this.resetAll)throw new Error(`[${this._pluginName}] Needs the method "resetAll"'`)}}n=l,r={parentFilterPanelSelector:".cms-element-product-listing-wrapper",dropdownSelector:".filter-panel-item-dropdown"},(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s="options"))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r},8089:(t,e,i)=>{i.d(e,{Z:()=>u});var n,s,r,o=i(3206),a=i(6536),l=i(1857),c=i.n(l);class u extends a.Z{init(){this.checkbox=o.Z.querySelector(this.el,this.options.checkboxSelector),this._registerEvents()}_registerEvents(){this.checkbox.addEventListener("change",this._onChangeCheckbox.bind(this))}reset(t){t===this.options.name&&(this.checkbox.checked=!1)}resetAll(){this.checkbox.checked=!1}getValues(){const t={};return t[this.options.name]=this.checkbox.checked?"1":"",t}getLabels(){let t=[];return this.checkbox.checked?t.push({label:this.options.displayName,id:this.options.name}):t=[],t}setValuesFromUrl(t){let e=!1;return Object.keys(t).forEach((i=>{i===this.options.name&&t[i]&&(this.checkbox.checked=1,e=!0)})),e}refreshDisabledState(t){const e=t[this.options.name];e.max&&e.max>0?this.enableFilter():this.disableFilter()}enableFilter(){this.el.classList.remove("disabled"),this.el.removeAttribute("title"),this.checkbox.removeAttribute("disabled")}disableFilter(){this.el.classList.add("disabled"),this.el.setAttribute("title",this.options.snippets.disabledFilterText),this.checkbox.disabled=!0}_onChangeCheckbox(){this.listing.changeListing()}}n=u,s="options",r=c()(a.Z.options,{checkboxSelector:".filter-boolean-input",activeClass:"is-active",snippets:{disabledFilterText:"Filter not active"}}),(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r},5410:(t,e,i)=>{i.d(e,{Z:()=>h});var n,s,r,o=i(3206),a=i(1966),l=i(6536),c=i(1857),u=i.n(c);class h extends l.Z{init(){this.selection=[],this.counter=o.Z.querySelector(this.el,this.options.countSelector),this._registerEvents()}_registerEvents(){const t=o.Z.querySelectorAll(this.el,this.options.checkboxSelector);a.Z.iterate(t,(t=>{t.addEventListener("change",this._onChangeFilter.bind(this))}))}getValues(){const t=o.Z.querySelectorAll(this.el,`${this.options.checkboxSelector}:checked`,!1);let e=[];t?a.Z.iterate(t,(t=>{e.push(t.id)})):e=[],this.selection=e,this._updateCount();const i={};return i[this.options.name]=e,i}getLabels(){const t=o.Z.querySelectorAll(this.el,`${this.options.checkboxSelector}:checked`,!1);let e=[];return t?a.Z.iterate(t,(t=>{e.push({label:t.dataset.label,id:t.id})})):e=[],e}setValuesFromUrl(t={}){let e=!1;const i=t[this.options.name],n=i?i.split("|"):[],s=this.selection.filter((t=>!n.includes(t))),r=n.filter((t=>!this.selection.includes(t)));return(s.length>0||r.length>0)&&(e=!0),r.forEach((t=>{const e=o.Z.querySelector(this.el,`[id="${t}"]`,!1);e&&(e.checked=!0,this.selection.push(e.id))})),s.forEach((t=>{this.reset(t),this.selection=this.selection.filter((e=>e!==t))})),this._updateCount(),e}_onChangeFilter(){this.listing.changeListing(!0,{p:1})}reset(t){const e=o.Z.querySelector(this.el,`[id="${t}"]`,!1);e&&(e.checked=!1)}resetAll(){this.selection.filter=[];const t=o.Z.querySelectorAll(this.el,`${this.options.checkboxSelector}:checked`,!1);t&&a.Z.iterate(t,(t=>{t.checked=!1}))}refreshDisabledState(t){const e=t[this.options.name];!e.entities||e.entities.length<1?this.disableFilter():(this.enableFilter(),this._disableInactiveFilterOptions(e.entities.map((t=>t.id))))}_disableInactiveFilterOptions(t){const e=o.Z.querySelectorAll(this.el,this.options.checkboxSelector);a.Z.iterate(e,(e=>{!0!==e.checked&&(t.includes(e.id)?this.enableOption(e):this.disableOption(e))}))}disableOption(t){const e=t.closest(this.options.listItemSelector);e.classList.add("disabled"),e.setAttribute("title",this.options.snippets.disabledFilterText),t.disabled=!0}enableOption(t){const e=t.closest(this.options.listItemSelector);e.removeAttribute("title"),e.classList.remove("disabled"),t.disabled=!1}enableAllOptions(){const t=o.Z.querySelectorAll(this.el,this.options.checkboxSelector);a.Z.iterate(t,(t=>{this.enableOption(t)}))}disableFilter(){const t=o.Z.querySelector(this.el,this.options.mainFilterButtonSelector);t.classList.add("disabled"),t.setAttribute("disabled","disabled"),t.setAttribute("title",this.options.snippets.disabledFilterText)}enableFilter(){const t=o.Z.querySelector(this.el,this.options.mainFilterButtonSelector);t.classList.remove("disabled"),t.removeAttribute("disabled"),t.removeAttribute("title")}_updateCount(){this.counter.innerText=this.selection.length?`(${this.selection.length})`:""}}n=h,s="options",r=u()(l.Z.options,{checkboxSelector:".filter-multi-select-checkbox",countSelector:".filter-multi-select-count",listItemSelector:".filter-multi-select-list-item",snippets:{disabledFilterText:"Filter not active"},mainFilterButtonSelector:".filter-panel-item-toggle"}),(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r},3629:(t,e,i)=>{i.d(e,{Z:()=>h});var n,s,r,o=i(5410),a=i(1966),l=i(3206),c=i(1857),u=i.n(c);class h extends o.Z{getLabels(){const t=l.Z.querySelectorAll(this.el,`${this.options.checkboxSelector}:checked`,!1);let e=[];return t?a.Z.iterate(t,(t=>{e.push({label:t.dataset.label,id:t.id,previewHex:t.dataset.previewHex,previewImageUrl:t.dataset.previewImageUrl})})):e=[],e}refreshDisabledState(t){if(""===this.options.propertyName)return;const e=[],i=t[this.options.name].entities;if(!i)return void this.disableFilter();const n=i.find((t=>t.translated.name===this.options.propertyName));if(!n)return void this.disableFilter();e.push(...n.options);const s=this.getValues();e.length<1&&0===s.properties.length?this.disableFilter():(this.enableFilter(),s.properties.length>0||this._disableInactiveFilterOptions(e.map((t=>t.id))))}}n=h,s="options",r=u()(o.Z.options,{propertyName:""}),(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r},4699:(t,e,i)=>{i.d(e,{Z:()=>u});var n,s,r,o=i(6536),a=i(3206),l=i(1857),c=i.n(l);class u extends o.Z{init(){this._container=a.Z.querySelector(this.el,this.options.containerSelector),this._inputMin=a.Z.querySelector(this.el,this.options.inputMinSelector),this._inputMax=a.Z.querySelector(this.el,this.options.inputMaxSelector),this._timeout=null,this._hasError=!1,this._registerEvents()}_registerEvents(){this._inputMin.addEventListener("input",this._onChangeInput.bind(this)),this._inputMax.addEventListener("input",this._onChangeInput.bind(this))}_onChangeInput(){clearTimeout(this._timeout),this._timeout=setTimeout((()=>{this._isInputInvalid()?this._setError(this._getErrorMessageTemplate("filterRangeErrorMessage")):this._isInputLowerBoundInvalid()?this._setError(this._getErrorMessageTemplate("filterRangeLowerBoundErrorMessage")):this._removeError(),this.listing.changeListing()}),this.options.inputTimeout)}getValues(){const t={};return t[this.options.minKey]=this._inputMin.value,t[this.options.maxKey]=this._inputMax.value,t}_isInputInvalid(){return parseFloat(this._inputMin.value)>parseFloat(this._inputMax.value)}_isInputLowerBoundInvalid(){return parseFloat(this._inputMin.value)<this.options.lowerBound||parseFloat(this._inputMax.value)<this.options.lowerBound}_getErrorMessageTemplate(t){return`<div class="${this.options.errorContainerClass}">${this.options.snippets[t]}</div>`}_setError(t){this._hasError||(this._inputMin.classList.add(this.options.inputInvalidCLass),this._inputMax.classList.add(this.options.inputInvalidCLass),this._container.insertAdjacentHTML("afterend",t),this._hasError=!0)}_removeError(){this._inputMin.classList.remove(this.options.inputInvalidCLass),this._inputMax.classList.remove(this.options.inputInvalidCLass);const t=a.Z.querySelector(this.el,`.${this.options.errorContainerClass}`,!1);t&&t.remove(),this._hasError=!1}setValuesFromUrl(t){let e=!1;return Object.keys(t).forEach((i=>{i===this.options.minKey&&(this._inputMin.value=t[i],e=!0),i===this.options.maxKey&&(this._inputMax.value=t[i],e=!0)})),e}getLabels(){let t=[];return this._inputMin.value.length||this._inputMax.value.length?(this._inputMin.value.length&&t.push({label:`${this.options.snippets.filterRangeActiveMinLabel} ${this._inputMin.value} ${this.options.unit}`,id:this.options.minKey}),this._inputMax.value.length&&t.push({label:`${this.options.snippets.filterRangeActiveMaxLabel} ${this._inputMax.value} ${this.options.unit}`,id:this.options.maxKey})):t=[],t}reset(t){t===this.options.minKey&&(this._inputMin.value=""),t===this.options.maxKey&&(this._inputMax.value=""),this._removeError()}resetAll(){this._inputMin.value="",this._inputMax.value="",this._removeError()}}n=u,s="options",r=c()(o.Z.options,{inputMinSelector:".min-input",inputMaxSelector:".max-input",inputInvalidCLass:"is-invalid",inputTimeout:500,minKey:"min-price",maxKey:"max-price",lowerBound:0,unit:"€",errorContainerClass:"filter-range-error",containerSelector:".filter-range-container",snippets:{filterRangeActiveMinLabel:"",filterRangeActiveMaxLabel:"",filterRangeErrorMessage:"",filterRangeLowerBoundErrorMessage:""}}),(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r},158:(t,e,i)=>{i.d(e,{Z:()=>h});var n,s,r,o=i(5410),a=i(1966),l=i(3206),c=i(1857),u=i.n(c);class h extends o.Z{getValues(){const t={},e=l.Z.querySelector(this.el,`${this.options.checkboxSelector}:checked`,!1);return this.currentRating=e.value,this._updateCount(),t[this.options.name]=this.currentRating?this.currentRating.toString():"",t}setValuesFromUrl(t){let e=!1;return Object.keys(t).forEach((i=>{if(i===this.options.name){this.currentRating=t[i],this._updateCount();const n=l.Z.querySelectorAll(this.el,this.options.checkboxSelector,!1);n&&a.Z.iterate(n,(t=>{t.value===this.currentRating&&(t.checked=!0)})),e=!0}})),e}getLabels(){const t=l.Z.querySelector(this.el,this.options.checkboxSelector+":checked",!1).value;let e=[];if(t){let i=this.options.snippets.filterRatingActiveLabelEnd;1===parseInt(t)&&(i=this.options.snippets.filterRatingActiveLabelEndSingular),e.push({label:`${this.options.snippets.filterRatingActiveLabelStart}\n                        ${t}/${this.options.maxPoints}\n                        ${i}`,id:"rating"})}else e=[];return e}refreshDisabledState(t){const e=t[this.options.name].max;if(e&&e>0)return this.enableFilter(),void this._disableInactiveFilterOptions(e);this.disableFilter()}_disableInactiveFilterOptions(t){const e=l.Z.querySelectorAll(this.el,this.options.checkboxSelector);a.Z.iterate(e,(e=>{!0!==e.checked&&(t>=e.value?this.enableOption(e):this.disableOption(e))}))}reset(){this.resetAll()}_updateCount(){this.counter.innerText=this.currentRating?`(${this.currentRating}/${this.options.maxPoints})`:""}}n=h,s="options",r=u()(o.Z.options,{maxPoints:5,snippets:{filterRatingActiveLabelStart:"Minimum",filterRatingActiveLabelEndSingular:"star",filterRatingActiveLabelEnd:"stars",disabledFilterText:"Filter not active"}}),(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r},9737:(t,e,i)=>{i.d(e,{Z:()=>c});var n,s,r,o=i(6536),a=i(1857),l=i.n(a);class c extends o.Z{init(){this.select=this.el.querySelector("select"),this._registerEvents()}_registerEvents(){this.select.addEventListener("change",this.onChangeSorting.bind(this))}onChangeSorting(t){this.options.sorting=t.target.value,this.listing.changeListing()}reset(){}resetAll(){}getValues(){return null===this.options.sorting?{}:{order:this.options.sorting}}afterContentChange(){this.listing.deregisterFilter(this)}getLabels(){return[]}}n=c,s="options",r=l()(o.Z.options,{sorting:null}),(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r},3139:(t,e,i)=>{i.d(e,{Z:()=>p});var n,s,r,o=i(6285),a=i(8254),l=i(1966),c=i(3206),u=i(5944),h=i(5362),g=i(6510),d=i(46);class p extends o.Z{init(){this._registry=[],this.httpClient=new a.Z,this._urlFilterParams=u.parse(g.Z.getSearch()),this._filterPanel=c.Z.querySelector(document,this.options.filterPanelSelector,!1),this._filterPanelActive=!!this._filterPanel,this._filterPanelActive&&(this._showResetAll=!1,this.activeFilterContainer=c.Z.querySelector(document,this.options.activeFilterContainerSelector)),this._cmsProductListingWrapper=c.Z.querySelector(document,this.options.cmsProductListingWrapperSelector,!1),this._cmsProductListingWrapperActive=!!this._cmsProductListingWrapper,this._allFiltersInitializedDebounce=d.Z.debounce(this.sendDisabledFiltersRequest.bind(this),100),this._registerEvents()}refreshRegistry(){const t=this._registry.filter((t=>document.body.contains(t.el)));this.init(),this._registry=t,window.PluginManager.initializePlugins()}changeListing(t=!0,e={}){this._buildRequest(t,e),this._filterPanelActive&&this._buildLabels()}registerFilter(t){this._registry.push(t),this._setFilterState(t),this.options.disableEmptyFilter&&this._allFiltersInitializedDebounce()}_setFilterState(t){if(Object.keys(this._urlFilterParams).length>0&&"function"==typeof t.setValuesFromUrl){if(!t.setValuesFromUrl(this._urlFilterParams)||!this._filterPanelActive)return;this._showResetAll=!0,this._buildLabels()}}deregisterFilter(t){this._registry=this._registry.filter((e=>e!==t))}_fetchValuesOfRegisteredFilters(){const t={};return this._registry.forEach((e=>{const i=e.getValues();Object.keys(i).forEach((e=>{Object.prototype.hasOwnProperty.call(t,e)?Object.values(i[e]).forEach((i=>{t[e].push(i)})):t[e]=i[e]}))})),t}_mapFilters(t){const e={};return Object.keys(t).forEach((i=>{let n=t[i];Array.isArray(n)&&(n=n.join("|"));`${n}`.length&&(e[i]=n)})),e}_buildRequest(t=!0,e={}){const i=this._fetchValuesOfRegisteredFilters(),n=this._mapFilters(i);this._filterPanelActive&&(this._showResetAll=!!Object.keys(n).length),this.options.params&&Object.keys(this.options.params).forEach((t=>{n[t]=this.options.params[t]})),Object.entries(e).forEach((([t,e])=>{n[t]=e}));let s=u.stringify(n);this.sendDataRequest(s),delete n.slots,delete n["no-aggregations"],delete n["reduce-aggregations"],delete n["only-aggregations"],s=u.stringify(n),t&&this._updateHistory(s),this.options.scrollTopListingWrapper&&this._scrollTopOfListing()}_scrollTopOfListing(){const t=this._cmsProductListingWrapper.getBoundingClientRect();if(t.top>=0)return;const e=t.top+window.scrollY-this.options.scrollOffset;window.scrollTo({top:e,behavior:"smooth"})}_getDisabledFiltersParamsFromParams(t){const e=Object.assign({},{"only-aggregations":1,"reduce-aggregations":1},t);return delete e.p,delete e.order,delete e["no-aggregations"],e}_updateHistory(t){g.Z.push(g.Z.getLocation().pathname,t,{})}_buildLabels(){let t="";this._registry.forEach((e=>{const i=e.getLabels();i.length&&i.forEach((e=>{t+=this.getLabelTemplate(e)}))})),this.activeFilterContainer.innerHTML=t;const e=c.Z.querySelectorAll(this.activeFilterContainer,`.${this.options.activeFilterLabelRemoveClass}`,!1);t.length&&(this._registerLabelEvents(e),this.createResetAllButton())}_registerLabelEvents(t){l.Z.iterate(t,(t=>{t.addEventListener("click",(()=>this.resetFilter(t)))}))}createResetAllButton(){this.activeFilterContainer.insertAdjacentHTML("beforeend",this.getResetAllButtonTemplate());const t=c.Z.querySelector(this.activeFilterContainer,this.options.resetAllFilterButtonSelector);t.removeEventListener("click",this.resetAllFilter.bind(this)),t.addEventListener("click",this.resetAllFilter.bind(this)),this._showResetAll||t.remove()}resetFilter(t){this._registry.forEach((e=>{e.reset(t.dataset.id)})),this._buildRequest(),this._buildLabels()}resetAllFilter(){this._registry.forEach((t=>{t.resetAll()})),this._buildRequest(),this._buildLabels()}getLabelTemplate(t){return`\n        <span class="${this.options.activeFilterLabelClass}">\n            ${this.getLabelPreviewTemplate(t)}\n            ${t.label}\n            <button class="${this.options.activeFilterLabelRemoveClass}"\n                    data-id="${t.id}">\n                &times;\n            </button>\n        </span>\n        `}getLabelPreviewTemplate(t){const e=this.options.activeFilterLabelPreviewClass;return t.previewHex?`\n                <span class="${e}" style="background-color: ${t.previewHex};"></span>\n            `:t.previewImageUrl?`\n                <span class="${e}" style="background-image: url('${t.previewImageUrl}');"></span>\n            `:""}getResetAllButtonTemplate(){return`\n        <button class="${this.options.resetAllFilterButtonClasses}">\n            ${this.options.snippets.resetAllButtonText}\n        </button>\n        `}addLoadingIndicatorClass(){this._filterPanel.classList.add(this.options.loadingIndicatorClass)}removeLoadingIndicatorClass(){this._filterPanel.classList.remove(this.options.loadingIndicatorClass)}addLoadingElementLoaderClass(){this._cmsProductListingWrapper.classList.add(this.options.loadingElementLoaderClass)}removeLoadingElementLoaderClass(){this._cmsProductListingWrapper.classList.remove(this.options.loadingElementLoaderClass)}sendDataRequest(t){this._filterPanelActive&&this.addLoadingIndicatorClass(),this._cmsProductListingWrapperActive&&this.addLoadingElementLoaderClass(),this.options.disableEmptyFilter&&this.sendDisabledFiltersRequest(),this.httpClient.get(`${this.options.dataUrl}?${t}`,(t=>{this.renderResponse(t),this._filterPanelActive&&this.removeLoadingIndicatorClass(),this._cmsProductListingWrapperActive&&this.removeLoadingElementLoaderClass()}))}sendDisabledFiltersRequest(){const t=this._fetchValuesOfRegisteredFilters(),e=this._mapFilters(t);this.options.params&&Object.keys(this.options.params).forEach((t=>{e[t]=this.options.params[t]})),this._allFiltersInitializedDebounce=()=>{};const i=this._getDisabledFiltersParamsFromParams(e);this.httpClient.get(`${this.options.filterUrl}?${u.stringify(i)}`,(t=>{const e=JSON.parse(t);this._registry.forEach((t=>{"function"==typeof t.refreshDisabledState&&t.refreshDisabledState(e,i)}))}))}renderResponse(t){h.Z.replaceFromMarkup(t,this.options.cmsProductListingSelector,!1),this._registry.forEach((t=>{"function"==typeof t.afterContentChange&&t.afterContentChange()})),window.PluginManager.initializePlugins(),this.$emitter.publish("Listing/afterRenderResponse",{response:t})}_registerEvents(){window.onpopstate=this._onWindowPopstate.bind(this)}_onWindowPopstate(){this.refreshRegistry(),this._registry.forEach((t=>{0===Object.keys(this._urlFilterParams).length&&(this._urlFilterParams.p=1),this._setFilterState(t)})),this.options.disableEmptyFilter&&this._allFiltersInitializedDebounce(),this.changeListing(!1)}}n=p,r={dataUrl:"",filterUrl:"",params:{},filterPanelSelector:".filter-panel",cmsProductListingSelector:".cms-element-product-listing",cmsProductListingWrapperSelector:".cms-element-product-listing-wrapper",activeFilterContainerSelector:".filter-panel-active-container",activeFilterLabelClass:"filter-active",activeFilterLabelRemoveClass:"filter-active-remove",activeFilterLabelPreviewClass:"filter-active-preview",resetAllFilterButtonClasses:"filter-reset-all btn btn-sm btn-outline-danger",resetAllFilterButtonSelector:".filter-reset-all",loadingIndicatorClass:"is-loading",loadingElementLoaderClass:"has-element-loader",disableEmptyFilter:!1,snippets:{resetAllButtonText:"Reset all"},scrollTopListingWrapper:!0,scrollOffset:15},(s=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(s="options"))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r}},t=>{t.O(0,["vendor-node","vendor-shared"],(()=>{return e=5886,t(t.s=e);var e}));t.O()}]);