
import ListingSortingPlugin from 'src/plugin/listing/listing-sorting.plugin';

export default class TanmarInfiniteScrollingListingSortingPlugin extends PluginManager.getPlugin('ListingSorting').get('class') {

    onChangeSorting(event) {
        var me = this, l = me.listing;
        
        if (l._tmisActive) {
            me.options.sorting = event.target.value;
            l._tmisListingOption = 'override';
            l._visitedPagesClear();
            l._tmisNewPageRequestCounter = 0;
            l._tmisIsLoading = true;
            l._tmisLog('  reset onChangeSorting');
            l.changeListing();
        }else{
            super.onChangeSorting(event);
        }
    }

    afterContentChange() {
        if (this.listing._tmisActive) {
            // dont remove filter
        }else{
            super.afterContentChange(event);
        }
    }
}
