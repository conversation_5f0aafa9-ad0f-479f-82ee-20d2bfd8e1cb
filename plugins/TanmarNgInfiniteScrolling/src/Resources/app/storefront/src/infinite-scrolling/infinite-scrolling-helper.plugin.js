import Plugin from 'src/plugin-system/plugin.class';

export default class TanmarInfiniteScrollingHelper extends Plugin{

    init() {
        this.debug = false;
        
        if(!window._tanmarInfiniteScrolling){
            this.log('Plugin not active or template error: cant find window._tanmarInfiniteScrolling');
            return;
        }
        
        if(!!window._tanmarInfiniteScrolling){
            if(window._tanmarInfiniteScrolling.debug){
                this.debug = true;
            }
        }
        
        setTimeout(i => {
            this.testListingPlugin();
        }, 100);
    }
    
    testListingPlugin(){
        try {
            if(!PluginManager.getPluginInstances('Listing')[0]._tmisInit){

                this.error("TanmarInfiniteScrolling selftest: Listing override failed");

                let PluginList = PluginManager.getPluginList();
                if(!!PluginList.SwagCmsExtensionsQuickview){
                    this.error("  Shopware CMS Extensions detected, Infinite Scrolling is not compatible with CMS Extensions");
                }

            }
        } catch (e) {
            
        }
    }
    
    log(msg){
        if(this.debug){
            console.log(msg);
        }
    }
    
    error(msg){
        if(this.debug){
            console.error(msg);
        }
    }
}
