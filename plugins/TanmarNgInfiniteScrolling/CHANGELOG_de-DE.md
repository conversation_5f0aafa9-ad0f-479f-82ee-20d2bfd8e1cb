# 1.5.3
- Kompatibilität mit anderen Plugins wurde verbessert

# 1.5.2
- Link hinzugefügt um seo optimierung zu verbessern

# 1.5.1
- Kompatibilität mit anderen Plugins wurde verbessert

# 1.5.0
- Anpassung für Shopware Version 6.5

# 1.4.7
- Link hinzugefügt um seo optimierung zu verbessern

# 1.4.6
- Kompatibilität mit anderen Plugins wurde verbessert

# 1.4.5
- Kompatibilität mit anderen Plugins wurde verbessert

# 1.4.4
- Kompatibilität mit anderen Plugins wurde verbessert

# 1.4.3
- Kompatibilität mit anderen Plugins wurde verbessert

# 1.4.2
- Style Element in den HTML-Header verschoben

# 1.4.1
- Bugfix: Account Bestellungen

# 1.4.0
- Verbesserte kompatibilität mit shopware 6.4.17
- Bugfix: Observer ist beim Zurücksetzen des Filters null
- Bugfix: Produkte werden nach Browser-Zurück nicht geladen
- Benutzerdefinierter Selektor für Produkte hinzugefügt

# 1.3.1
- Konfiguration hinzugefügt, mit der Produkte nur innerhalb des Listings gesucht wird.

# 1.3.0
- Auslöser für afterRenderResponse-Ereignis hinzugefügt

# 1.2.0
- Kompatibilität mit anderen Plugins wurde verbessert
- Es wurde ein Fehler behoben, wodurch eine konfiguration einen fehler in der storefront verursacht hat.

# 1.1.2
- Loading Klasse für das Ändern der Filter wieder hinzugefügt
- Benutzerdefinierte Selektoren hinzugefügt

# 1.1.1
- Bugfix: Uncaught Error: Call to a member function get() on null

# 1.1.0
- Kompatibilität mit Shopware 6.4 hinzugefügt 

# 1.0.11
- Loading Klasse entfernt, wodurch Produktboxen ihre höhe verändern

# 1.0.10
- Es wurde ein Fehler behoben, wodurch das Navigationsfeld im Mobile mehrmals angezeigt wurde

# 1.0.9
- Es wurde ein Fehler behoben, wodurch der Browser an die oberste Stelle des Listings scrollt sobald eine neue Seite geladen wird
- Es wurde ein Fehler behoben, wodurch die Produktboxen nach dem Sortieren an das Listing angehängt wurden
- Es wurde ein Fehler behoben, wodurch das Listing-Ajax nach dem Sortieren doppelt aufgerufen wurde

# 1.0.8
- Fehler in der Plugin Konfiguration pro Vertriebskanal behoben

# 1.0.7
- Fehler in der Plugin Konfiguration pro Vertriebskanal behoben
- Fehler behoben, wodurch die NextBox nach dem Sortieren nicht angezeigt wurde
- HTML Struktur verbessert

# 1.0.6
- Fehler in der HTML-Struktur behoben

# 1.0.5
- Verbesserte Kompatibilität mit Shopware 6.3

# 1.0.4
- Fehler behoben, wodurch Produktbilder nicht angezeigt wurden

# 1.0.3
- Fehler in der Sortierung behoben

# 1.0.2
- Fehler im Update auf 1.0.1 behoben

# 1.0.1
- Konfiguration für die Anzahl der Seiten die nachgeladen werden sollen hinzugefügt

# 1.0.0
- Erste Version für Shopware 6
