/*! For license information please see c108-hotspots.js.LICENSE.txt */
!function(e){var t={};function i(l){if(t[l])return t[l].exports;var a=t[l]={i:l,l:!1,exports:{}};return e[l].call(a.exports,a,a.exports,i),a.l=!0,a.exports}i.m=e,i.c=t,i.d=function(e,t,l){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:l})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var l=Object.create(null);if(i.r(l),Object.defineProperty(l,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)i.d(l,a,function(t){return e[t]}.bind(null,a));return l},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p=(window.__sw__.assetPath + '/bundles/c108hotspots/'),i(i.s="cdUW")}({"+jUR":function(e,t,i){var l=i("klZQ");l.__esModule&&(l=l.default),"string"==typeof l&&(l=[[e.i,l,""]]),l.locals&&(e.exports=l.locals);(0,i("P8hj").default)("b46adae6",l,!0,{})},"2Tqa":function(e,t,i){},"6t9y":function(e,t,i){var l=i("OdnZ");l.__esModule&&(l=l.default),"string"==typeof l&&(l=[[e.i,l,""]]),l.locals&&(e.exports=l.locals);(0,i("P8hj").default)("d04220d4",l,!0,{})},AhVI:function(e,t,i){},G8Ty:function(e){e.exports=JSON.parse('{"sw-cms":{"blocks":{"textImage":{"hotspots":{"label":"Image with markers"}}},"elements":{"hotspots":{"label":"Image with markers","config":{"tabnames":{"image":"Image","settings":"Settings","marker":"Marker"},"tabsettings":{"imagelabel":"Image (recommended resolution: 1920 x 960 pixels)","imagehelptext":"For an optimal representation, we recommend a resolution of 1920 x 960 pixels of the image. Preferably, you should also upload the image compressed.","markingnr":"Number","markingsnumber":"marker | markers","markingsnr1":"1 marker","design":"Design","designopt1":"Simple","designopt2":"With numbering","designopt3":"With plus sign","color":"Color","mobileimage":"Alternative image for mobile","mobileimagehelptext":"Enable this switch if an alternative image should be used for screen widths < 768 pixels (recommended resolution: 1000 x 500 pixels).","mobileimageurl":"URL for mobile image","tabletimage":"Alternative image for tablet","tabletimagehelptext":"Enable this switch if an alternative image should be used for screen widths > 767 pixels and < 1280 pixels (recommended resolution: 1400 x 700 pixels).","tabletimageurl":"URL for tablet image","lazyloading":"Add the lazy loading attribute to the image","lazyloadinghelptext":"Enable this switch if the lazy loading attribute should be added to the image. Please take into account the current browser support for this attribute. Also note that in some situations the lazy loading attribute may lead to performance issues.","backendlabels":"Labels in preview","backendlabelshelptext":"Enable this switch if additional labels should be displayed in the preview in the backend for an easier assignment of the markers.","frontendlabels":"Always show titles","frontendlabelshelptext":"Enable this switch if the marker titles should always be displayed.","individualcolors":"Individual colors","individualcolorshelptext":"Enable this switch if different colors should be used for the markers.","useeditor":"Use text editor for the content","useeditorhelptext":"Enable this switch if you want to use a text editor to set the content for the markers.","usemaxheight":"Maximum height and y-scroll","usemaxheighthelptext":"Enable this switch if you want to activate maximum height and y-scroll for the content.","useindividualimagesize":"Consider individual image size","useindividualimagesizehelptext":"Enable this switch if the individual image size (width and height) should be considered."},"tabmarker":{"position":"Position","positionhelptext":"Click on the image to determine the position of the marking. You should first set the desired image in the settings.","title":"Title","description":"Description","link":"Link","url":"URL","text":"Text","linkinternal":"Internal link","linkadvanced":"Advanced URL field","content":"Content","frontendlabel":"Label"}}}}}}')},OdnZ:function(e,t,i){},P2IY:function(e,t,i){var l=i("2Tqa");l.__esModule&&(l=l.default),"string"==typeof l&&(l=[[e.i,l,""]]),l.locals&&(e.exports=l.locals);(0,i("P8hj").default)("3182fa32",l,!0,{})},P8hj:function(e,t,i){"use strict";function l(e,t){for(var i=[],l={},a=0;a<t.length;a++){var c=t[a],n=c[0],s={id:e+":"+a,css:c[1],media:c[2],sourceMap:c[3]};l[n]?l[n].parts.push(s):i.push(l[n]={id:n,parts:[s]})}return i}i.r(t),i.d(t,"default",(function(){return d}));var a="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!a)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var c={},n=a&&(document.head||document.getElementsByTagName("head")[0]),s=null,o=0,r=!1,h=function(){},u=null,v="data-vue-ssr-id",m="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function d(e,t,i,a){r=i,u=a||{};var n=l(e,t);return g(n),function(t){for(var i=[],a=0;a<n.length;a++){var s=n[a];(o=c[s.id]).refs--,i.push(o)}t?g(n=l(e,t)):n=[];for(a=0;a<i.length;a++){var o;if(0===(o=i[a]).refs){for(var r=0;r<o.parts.length;r++)o.parts[r]();delete c[o.id]}}}}function g(e){for(var t=0;t<e.length;t++){var i=e[t],l=c[i.id];if(l){l.refs++;for(var a=0;a<l.parts.length;a++)l.parts[a](i.parts[a]);for(;a<i.parts.length;a++)l.parts.push(k(i.parts[a]));l.parts.length>i.parts.length&&(l.parts.length=i.parts.length)}else{var n=[];for(a=0;a<i.parts.length;a++)n.push(k(i.parts[a]));c[i.id]={id:i.id,refs:1,parts:n}}}}function f(){var e=document.createElement("style");return e.type="text/css",n.appendChild(e),e}function k(e){var t,i,l=document.querySelector("style["+v+'~="'+e.id+'"]');if(l){if(r)return h;l.parentNode.removeChild(l)}if(m){var a=o++;l=s||(s=f()),t=x.bind(null,l,a,!1),i=x.bind(null,l,a,!0)}else l=f(),t=w.bind(null,l),i=function(){l.parentNode.removeChild(l)};return t(e),function(l){if(l){if(l.css===e.css&&l.media===e.media&&l.sourceMap===e.sourceMap)return;t(e=l)}else i()}}var p,b=(p=[],function(e,t){return p[e]=t,p.filter(Boolean).join("\n")});function x(e,t,i,l){var a=i?"":l.css;if(e.styleSheet)e.styleSheet.cssText=b(t,a);else{var c=document.createTextNode(a),n=e.childNodes;n[t]&&e.removeChild(n[t]),n.length?e.insertBefore(c,n[t]):e.appendChild(c)}}function w(e,t){var i=t.css,l=t.media,a=t.sourceMap;if(l&&e.setAttribute("media",l),u.ssrId&&e.setAttribute(v,t.id),a&&(i+="\n/*# sourceURL="+a.sources[0]+" */",i+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),e.styleSheet)e.styleSheet.cssText=i;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(i))}}},cdUW:function(e,t,i){"use strict";i.r(t);i("pp4b");Shopware.Component.register("sw-cms-block-hotspots",{template:'{% block sw_cms_block_hotspots %}\n<div class="sw-cms-block-hotspots">\n<slot name="c108himageslot">{% block sw_cms_block_hotspots_slot_c108t %}{% endblock %}</slot>\n</div>\n{% endblock %}\n'});i("6t9y");Shopware.Component.register("sw-cms-preview-hotspots",{template:'{% block sw_cms_block_hotspots_preview %}\n\t<div class="sw-cms-preview-hotspots">\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t</div>\n{% endblock %}\n'}),Shopware.Service("cmsService").registerCmsBlock({name:"hotspots",label:"sw-cms.blocks.textImage.hotspots.label",category:"text-image",component:"sw-cms-block-hotspots",previewComponent:"sw-cms-preview-hotspots",defaultConfig:{marginBottom:"20px",marginTop:"20px",marginLeft:"20px",marginRight:"20px",sizingMode:"boxed"},slots:{c108himageslot:{type:"hotspots",default:{config:{displayMode:{source:"static",value:"cover"}}}}}});i("+jUR");Shopware.Component.register("sw-cms-el-hotspots",{template:'{% block sw_cms_element_hotspots %}\n<div class="c108-hfi-wrapper" :class="`${element.config.c108hdesign.value}`" :style="passStyles">\n<div class="c108-hfi-inner">\n<img class="c108-hfi-bgimg c108-h-mobile-image" alt="Preview Mobile Image for Hotspots" v-if="element.config.c108hmobileimagebool.value && element.config.c108hmobileimageurl.value" :src="element.config.c108hmobileimageurl.value">\n<img class="c108-hfi-bgimg c108-h-mobile-image" alt="Preview Mobile Image for Hotspots" v-else :src="mediaUrl">\n<img class="c108-hfi-bgimg c108-h-tablet-image" alt="Preview Tablet Image for Hotspots" v-if="element.config.c108htabletimagebool.value && element.config.c108htabletimageurl.value" :src="element.config.c108htabletimageurl.value">\n<img class="c108-hfi-bgimg c108-h-tablet-image" alt="Preview Tablet Image for Hotspots" v-else :src="mediaUrl">\n<img class="c108-hfi-bgimg c108-h-desktop-image" alt="Preview Desktop Image for Hotspots" :src="mediaUrl">\n\n<div v-for="i in 50" :key="i">\n  <div v-if="element.config.c108hnrhotspots.value >= i" class="c108-hfi-element" :style="`top: ${element.config[\'c108hmarker\' + (\'0\' + i).slice(-2) + \'ypos\'].value}%; left: ${element.config[\'c108hmarker\' + (\'0\' + i).slice(-2) + \'xpos\'].value}%`">\n    <div class="c108-hfi-marker" :style="element.config.c108hindividualcolorsbool.value ? `background-color: ${element.config[\'c108hmarkercolor\' + (\'0\' + i).slice(-2)].value}` : `background-color: ${element.config.c108hhighlightcol.value}`">\n      <span v-show="element.config.c108hbackendlabelsbool.value" class="hfi-backend-label">Marker {{ i }}</span>\n      <span v-show="element.config.c108hfrontendlabelsbool.value" class="hfi-frontend-label">{{ element.config[\'c108htitle\' + (\'0\' + i).slice(-2)].value }}</span>\n      <span class="hfi-num">{{ i }}</span>\n      <span class="hfi-plus">+</span>\n    </div>\n  </div>\n</div>\n    \n</div>\n</div>\n{% endblock %}\n',mixins:[Shopware.Mixin.getByName("cms-element")],computed:{passStyles:function(){var e="unset",t="none";return void 0!==this.element.config.c108husemaxheightbool.value&&null!=this.element.config.c108husemaxheightbool.value&&this.element.config.c108husemaxheightbool.value&&(e="180px",t="scroll"),{"--c108hmaxheight":e,"--c108hoverflowY":t}},mediaUrl:function(){var e=Shopware.Context.api,t=this.element.data.media;return t&&t.id?this.element.data.media.url:t&&t.url?"".concat(e.assetsPath).concat(t.url):void 0}},created:function(){this.createdComponent()},methods:{createdComponent:function(){this.initElementConfig("hotspots"),this.initElementData("hotspots")}}});i("P2IY");function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(){a=function(){return e};var e={},t=Object.prototype,i=t.hasOwnProperty,c=Object.defineProperty||function(e,t,i){e[t]=i.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",r=n.toStringTag||"@@toStringTag";function h(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch(e){h=function(e,t,i){return e[t]=i}}function u(e,t,i,l){var a=t&&t.prototype instanceof d?t:d,n=Object.create(a.prototype),s=new H(l||[]);return c(n,"_invoke",{value:y(e,i,s)}),n}function v(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var m={};function d(){}function g(){}function f(){}var k={};h(k,s,(function(){return this}));var p=Object.getPrototypeOf,b=p&&p(p(T([])));b&&b!==t&&i.call(b,s)&&(k=b);var x=f.prototype=d.prototype=Object.create(k);function w(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function a(c,n,s,o){var r=v(e[c],e,n);if("throw"!==r.type){var h=r.arg,u=h.value;return u&&"object"==l(u)&&i.call(u,"__await")?t.resolve(u.__await).then((function(e){a("next",e,s,o)}),(function(e){a("throw",e,s,o)})):t.resolve(u).then((function(e){h.value=e,s(h)}),(function(e){return a("throw",e,s,o)}))}o(r.arg)}var n;c(this,"_invoke",{value:function(e,i){function l(){return new t((function(t,l){a(e,i,t,l)}))}return n=n?n.then(l,l):l()}})}function y(e,t,i){var l="suspendedStart";return function(a,c){if("executing"===l)throw new Error("Generator is already running");if("completed"===l){if("throw"===a)throw c;return I()}for(i.method=a,i.arg=c;;){var n=i.delegate;if(n){var s=E(n,i);if(s){if(s===m)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===l)throw l="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);l="executing";var o=v(e,t,i);if("normal"===o.type){if(l=i.done?"completed":"suspendedYield",o.arg===m)continue;return{value:o.arg,done:i.done}}"throw"===o.type&&(l="completed",i.method="throw",i.arg=o.arg)}}}function E(e,t){var i=t.method,l=e.iterator[i];if(void 0===l)return t.delegate=null,"throw"===i&&e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method)||"return"!==i&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+i+"' method")),m;var a=v(l,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,m;var c=a.arg;return c?c.done?(t[e.resultName]=c.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,m):c:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function M(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function H(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(M,this),this.reset(!0)}function T(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var l=-1,a=function t(){for(;++l<e.length;)if(i.call(e,l))return t.value=e[l],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:I}}function I(){return{value:void 0,done:!0}}return g.prototype=f,c(x,"constructor",{value:f,configurable:!0}),c(f,"constructor",{value:g,configurable:!0}),g.displayName=h(f,r,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,h(e,r,"GeneratorFunction")),e.prototype=Object.create(x),e},e.awrap=function(e){return{__await:e}},w(C.prototype),h(C.prototype,o,(function(){return this})),e.AsyncIterator=C,e.async=function(t,i,l,a,c){void 0===c&&(c=Promise);var n=new C(u(t,i,l,a),c);return e.isGeneratorFunction(i)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},w(x),h(x,r,"Generator"),h(x,s,(function(){return this})),h(x,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),i=[];for(var l in t)i.push(l);return i.reverse(),function e(){for(;i.length;){var l=i.pop();if(l in t)return e.value=l,e.done=!1,e}return e.done=!0,e}},e.values=T,H.prototype={constructor:H,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function l(i,l){return n.type="throw",n.arg=e,t.next=i,l&&(t.method="next",t.arg=void 0),!!l}for(var a=this.tryEntries.length-1;a>=0;--a){var c=this.tryEntries[a],n=c.completion;if("root"===c.tryLoc)return l("end");if(c.tryLoc<=this.prev){var s=i.call(c,"catchLoc"),o=i.call(c,"finallyLoc");if(s&&o){if(this.prev<c.catchLoc)return l(c.catchLoc,!0);if(this.prev<c.finallyLoc)return l(c.finallyLoc)}else if(s){if(this.prev<c.catchLoc)return l(c.catchLoc,!0)}else{if(!o)throw new Error("try statement without catch or finally");if(this.prev<c.finallyLoc)return l(c.finallyLoc)}}}},abrupt:function(e,t){for(var l=this.tryEntries.length-1;l>=0;--l){var a=this.tryEntries[l];if(a.tryLoc<=this.prev&&i.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var c=a;break}}c&&("break"===e||"continue"===e)&&c.tryLoc<=t&&t<=c.finallyLoc&&(c=null);var n=c?c.completion:{};return n.type=e,n.arg=t,c?(this.method="next",this.next=c.finallyLoc,m):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),L(i),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var l=i.completion;if("throw"===l.type){var a=l.arg;L(i)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:T(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=void 0),m}},e}function c(e,t,i,l,a,c,n){try{var s=e[c](n),o=s.value}catch(e){return void i(e)}s.done?t(o):Promise.resolve(o).then(l,a)}Shopware.Component.register("sw-cms-el-config-hotspots",{template:'{% block sw_cms_element_c108_t_config %}\n<div class="sw-cms-el-config-hotspots">\n<sw-tabs defaultItem="image">\n<template slot-scope="{ active }">\n<sw-tabs-item name="image" :activeTab="active">\n{{ $tc(\'sw-cms.elements.hotspots.config.tabnames.image\') }}\n</sw-tabs-item>\n<sw-tabs-item name="settings" :activeTab="active">\n{{ $tc(\'sw-cms.elements.hotspots.config.tabnames.settings\') }}\n</sw-tabs-item>\n<sw-tabs-item v-for="i in parseInt(element.config.c108hnrhotspots.value)" :name="\'marker\'+i" :value="i" :activeTab="active" @click="setBgImage(i, element.config.c108hhighlightcol.value, element.config.c108hindividualcolorsbool.value, allIndividualColors[i])">\n{{ $tc(\'sw-cms.elements.hotspots.config.tabnames.marker\', i ) }} {{ i }} \n</sw-tabs-item>\n</template>\n\n<template slot="content" slot-scope="{ active }">\n\n{# tab: settings #}\n<sw-container v-if="active === \'image\'">\n\n{# media upload V2 #}\n<div class="c108-config-img">\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.imagehelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n<sw-cms-mapping-field :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.imagelabel\')"\nvalueTypes="entity" entity="media" v-model="element.config.media">\n<sw-media-upload-v2 variant="regular"\n:uploadTag="uploadTag"\n:source="previewSource"\n:allowMultiSelect="false"\n:defaultFolder="cmsPageState.pageEntityName"\n:caption="$tc(\'sw-cms.elements.general.config.caption.mediaUpload\')"\n@media-upload-sidebar-open="onOpenMediaModal"\n@media-upload-remove-image="onImageRemove">\n</sw-media-upload-v2>\n<div class="sw-cms-el-config-image__mapping-preview" slot="preview" slot-scope="{ demoValue }">\n<img :src="demoValue.url" alt="Demo Image" v-if="demoValue.url">\n<sw-alert class="sw-cms-el-config-image__preview-info" variant="info" v-else>\n{{ $tc(\'sw-cms.detail.label.mappingEmptyPreview\') }}\n</sw-alert>\n</div>\n</sw-cms-mapping-field>\n<sw-upload-listener\n:uploadTag="uploadTag"\nautoUpload\n@media-upload-finish="onImageUpload">\n</sw-upload-listener>\n<sw-media-modal-v2\nvariant="regular"\nv-if="mediaModalIsOpen"\n:caption="$tc(\'sw-cms.elements.general.config.caption.mediaUpload\')"\n:entityContext="cmsPageState.entityName"\n:allowMultiSelect="false"\n:initialFolderId="cmsPageState.defaultMediaFolderId"\n@media-upload-remove-image="onImageRemove"\n@media-modal-selection-change="onSelectionChanges"\n@modal-close="onCloseModal">\n</sw-media-modal-v2>\n</div>\n\n{# responsive settings #}\n{# mobile image #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.mobileimage\')" v-model="element.config.c108hmobileimagebool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.mobileimagehelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# mobile image settings #}\n<sw-card :hero="false" :isLoading="false" :large="false" v-show="element.config.c108hmobileimagebool.value">\n\n{# mobile image advanced url field  #}\n<div v-if="element.config.c108hmobileimageurladvancedlink.value">\n<span class="c108-h-custom-label no-margin">{{ $tc(\'sw-cms.elements.hotspots.config.tabsettings.mobileimageurl\') }}</span>\n<input \nclass="c108-h-custom-input"\ntype="url" \nname="c108hadvancedurlmobileimage"\nid="c108hadvancedurlmobileimage" \nsize="30" \nv-model="element.config.c108hmobileimageurl.value">\n</div>\n\n{# mobile image standard url field #}\n<sw-url-field v-else :error="null" :omitUrlHash="false" :omitUrlSearch="false" v-model="element.config.c108hmobileimageurl.value" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.mobileimageurl\')">\n</sw-url-field>\n\n{# mobile image advanced link switch #}\n<sw-switch-field \n:label="$t(\'sw-cms.elements.hotspots.config.tabmarker.linkadvanced\')" \nv-model="element.config.c108hmobileimageurladvancedlink.value">\n</sw-switch-field>\n\n</sw-card>\n\n{# tablet image #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.tabletimage\')" v-model="element.config.c108htabletimagebool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.tabletimagehelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# tablet image settings #}\n<sw-card :hero="false" :isLoading="false" :large="false" v-show="element.config.c108htabletimagebool.value">\n\n{# tablet image advanced url field  #}\n<div v-if="element.config.c108htabletimageurladvancedlink.value">\n<span class="c108-h-custom-label no-margin">{{ $tc(\'sw-cms.elements.hotspots.config.tabsettings.tabletimageurl\') }}</span>\n<input \nclass="c108-h-custom-input"\ntype="url" \nname="c108hadvancedurltabletimage"\nid="c108hadvancedurltabletimage" \nsize="30" \nv-model="element.config.c108htabletimageurl.value">\n</div>\n\n{# tablet image standard url field #}\n<sw-url-field v-else :error="null" :omitUrlHash="false" :omitUrlSearch="false" v-model="element.config.c108htabletimageurl.value" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.tabletimageurl\')">\n</sw-url-field>\n\n{# tablet image advanced link switch #}\n<sw-switch-field \n:label="$t(\'sw-cms.elements.hotspots.config.tabmarker.linkadvanced\')" \nv-model="element.config.c108htabletimageurladvancedlink.value">\n</sw-switch-field>\n\n</sw-card>\n\n</sw-container>\n\n{# tab: settings #}\n<sw-container v-if="active === \'settings\'">\n\n{# number of markers #}\n<div class="c108-config-nrhotspots">\n<sw-select-field v-model="element.config.c108hnrhotspots.value" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.markingnr\')">\n<option v-for="n in 50" :value="n"> {{ n }} {{ $tc(\'sw-cms.elements.hotspots.config.tabsettings.markingsnumber\', n ) }}</option>\n</sw-select-field>\n</div>\n\n{# design #}\n<div class="c108-config-hdesign">\n<sw-select-field v-model="element.config.c108hdesign.value" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.design\')">\n<option value="einfach">{{ $tc(\'sw-cms.elements.hotspots.config.tabsettings.designopt1\') }}</option>\n<option value="nummerierung">{{ $tc(\'sw-cms.elements.hotspots.config.tabsettings.designopt2\') }}</option>\n<option value="plus">{{ $tc(\'sw-cms.elements.hotspots.config.tabsettings.designopt3\') }}</option>\n</sw-select-field>\n</div>\n\n{# highlight color #}\n<div class="c108-config-highlightcol">\n<sw-colorpicker v-model="element.config.c108hhighlightcol.value"\n:label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.color\')"\nvalue="#77cc33"\ncolorOutput="auto"\n:alpha="true"\n:disabled="false"\n:colorLabels="true"\nzIndex="1001">\n</sw-colorpicker>\n</div>\n\n{# backend labels #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.backendlabels\')" v-model="element.config.c108hbackendlabelsbool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.backendlabelshelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# lazy loading #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.lazyloading\')" v-model="element.config.c108hlazyloadingattributebool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.lazyloadinghelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# use texteditor #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$t(\'sw-cms.elements.hotspots.config.tabsettings.useeditor\')" v-model="element.config.c108huseeditorbool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.useeditorhelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# maximum height and y scroll #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$t(\'sw-cms.elements.hotspots.config.tabsettings.usemaxheight\')" v-model="element.config.c108husemaxheightbool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.usemaxheighthelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# use individual image size #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$t(\'sw-cms.elements.hotspots.config.tabsettings.useindividualimagesize\')" v-model="element.config.c108huseindividualimagesizebool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.useindividualimagesizehelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# frontend labels #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.frontendlabels\')" v-model="element.config.c108hfrontendlabelsbool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.frontendlabelshelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n{# individual colors #}\n<div class="c108-h-config-switch-wrapper">\n<sw-switch-field class="c108-h-config-small-switch" :label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.individualcolors\')" v-model="element.config.c108hindividualcolorsbool.value"></sw-switch-field>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabsettings.individualcolorshelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n</sw-container>\n\n\n{# tab: marker #}\n<div v-for="number in parseInt(element.config.c108hnrhotspots.value)">\n<sw-container v-show="active === \'marker\'+ number">\n<span class="c108-h-custom-label">{{ $tc(\'sw-cms.elements.hotspots.config.tabmarker.position\') }}</span>\n<div class="c108-canvas-row">\n<div class="c108-hs-canvas-wrapper">\n<canvas :id="\'canvas-hs-\'+ number" :width="fixedCanvasWidth" :height="fixedCanvasHeight" style="border:1px solid #d1d9e0;" @click="setHotspot($event, element.config.c108hhighlightcol.value, number, element.config.c108hindividualcolorsbool.value, allIndividualColors[number])"></canvas>\n</div>\n<sw-help-text :text="$tc(\'sw-cms.elements.hotspots.config.tabmarker.positionhelptext\')" :width="200" tooltipPosition="left">\n</sw-help-text>\n</div>\n\n<div class="c108-text-row">\n\n{# variant 1: use texteditor for the content #}\n<div v-show="element.config.c108huseeditorbool.value" class="c108-h-text-editor-wrapper">\n<sw-text-editor\n:label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.content\')"\nvalue="allTextEditorContents[number]"\nv-model="allTextEditorContents[number]"\n@input="setTextEditorContent(number)"\n:is-inline-edit="false">\n</sw-text-editor>\n\n<div v-show="element.config.c108hfrontendlabelsbool.value == true" class="c108-h-title-wrapper">\n<span class="c108-h-custom-label">{{ $tc(\'sw-cms.elements.hotspots.config.tabmarker.frontendlabel\') }}</span>\n<sw-text-field \nv-model="allTitles[number]"\n@change="setTitle(event, number)">\n</sw-text-field>\n</div>\n\n</div>\n\n{# variant 1: use text fields for the content #}\n<div v-show="!element.config.c108huseeditorbool.value">\n<div class="c108-h-title-wrapper">\n<span class="c108-h-custom-label">{{ $tc(\'sw-cms.elements.hotspots.config.tabmarker.title\') }}</span>\n<sw-text-field \nv-model="allTitles[number]"\n@change="setTitle(event, number)">\n</sw-text-field>\n</div>\n<div class="sw-field sw-block-field sw-field--textarea sw-field--default">\n<span class="c108-h-custom-label">{{ $tc(\'sw-cms.elements.hotspots.config.tabmarker.description\') }}</span>\n<div class="sw-block-field__block">\n<textarea \nv-model="allDescriptions[number]"\n@change="setDescription(event, number)" \nmaxlength="100"></textarea>\n</div>\n</div>\n\n{# link #}\n<sw-switch-field \n:label="$t(\'sw-cms.elements.hotspots.config.tabmarker.link\')" \nv-model="allLinkBools[number]"\n@change="setLinkBool(event, number)">\n</sw-switch-field>\n\n{# link internal #}\n<sw-switch-field class="c108-h-small-spacing" \n:label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.linkinternal\')" \nv-model="allLinkInternals[number]"\n@change="setLinkInternal(event, number)">\n</sw-switch-field>\n\n{# advanced link switch #}\n<sw-switch-field \n:label="$t(\'sw-cms.elements.hotspots.config.tabmarker.linkadvanced\')" \nv-model="allLinkAdvancedBools[number]"\n@change="setLinkAdvancedBool(event, number)">\n</sw-switch-field>\n\n{# advanced url field  #}\n<div v-if="allLinkAdvancedBools[number]">\n<span class="c108-h-custom-label no-margin">{{ $tc(\'sw-cms.elements.hotspots.config.tabmarker.url\') }}</span>\n<input \nclass="c108-h-custom-input"\ntype="url" \nname="c108hadvancedurl01"\nid="c108hadvancedurl01" \nsize="30" \nv-model="allLinkUrls[number]"\n@change="setLinkUrl(event, number)">\n</div>\n\n{# standard url field #}\n<div v-else>\n\n<sw-url-field v-if="number === 1"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink01url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 2"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink02url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 3"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink03url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 4"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink04url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 5"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink05url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 6"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink06url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 7"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink07url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 8"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink08url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 9"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink09url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 10"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink10url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 11"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink11url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 12"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink12url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 13"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink13url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 14"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink14url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 15"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink15url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 16"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink16url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 17"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink17url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 18"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink18url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 19"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink19url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 20"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink20url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 21"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink21url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 22"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink22url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 23"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink23url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 24"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink24url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 25"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink25url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 26"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink26url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 27"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink27url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 28"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink28url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 29"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink29url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 30"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink30url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 31"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink31url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 32"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink32url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 33"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink33url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 34"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink34url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 35"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink35url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 36"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink36url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 37"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink37url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 38"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink38url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 39"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink39url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 40"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink40url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 41"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink41url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 42"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink42url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 43"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink43url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 44"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink44url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 45"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink45url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 46"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink46url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 47"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink47url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 48"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink48url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 49"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink49url.value"\n></sw-url-field>\n\n<sw-url-field v-if="number === 50"\n  :label="$tc(\'sw-cms.elements.hotspots.config.tabmarker.url\')"\n  v-model="element.config.c108hlink50url.value"\n></sw-url-field>\n\n</div>\n\n{# link text #}\n<sw-text-field \n:label="$t(\'sw-cms.elements.hotspots.config.tabmarker.text\')" \nv-model="allLinkTitles[number]"\n@change="setLinkTitle(event, number)">\n</sw-text-field>\n</div>\n\n{# individual color #}\n<div v-if="element.config.c108hindividualcolorsbool.value" class="c108-config-highlightcol">\n<sw-colorpicker v-model="allIndividualColors[number]"\n:label="$tc(\'sw-cms.elements.hotspots.config.tabsettings.color\')"\nvalue="#77cc33"\ncolorOutput="auto"\n:alpha="true"\n:disabled="false"\n:colorLabels="true"\nzIndex="1001"\n@input="setIndividualColor(number)">\n</sw-colorpicker>\n</div>\n\n</div>\n\n</sw-container>\n</div>\n\n</template>\n</sw-tabs>\n</div>\n{% endblock %}\n',mixins:[Shopware.Mixin.getByName("cms-element")],inject:["repositoryFactory"],data:function(){return{mediaModalIsOpen:!1,initialFolderId:null,fixedCanvasWidth:300,fixedCanvasHeight:150,allTitles:[0,this.element.config.c108htitle01.value,this.element.config.c108htitle02.value,this.element.config.c108htitle03.value,this.element.config.c108htitle04.value,this.element.config.c108htitle05.value,this.element.config.c108htitle06.value,this.element.config.c108htitle07.value,this.element.config.c108htitle08.value,this.element.config.c108htitle09.value,this.element.config.c108htitle10.value,this.element.config.c108htitle11.value,this.element.config.c108htitle12.value,this.element.config.c108htitle13.value,this.element.config.c108htitle14.value,this.element.config.c108htitle15.value,this.element.config.c108htitle16.value,this.element.config.c108htitle17.value,this.element.config.c108htitle18.value,this.element.config.c108htitle19.value,this.element.config.c108htitle20.value,this.element.config.c108htitle21.value,this.element.config.c108htitle22.value,this.element.config.c108htitle23.value,this.element.config.c108htitle24.value,this.element.config.c108htitle25.value,this.element.config.c108htitle26.value,this.element.config.c108htitle27.value,this.element.config.c108htitle28.value,this.element.config.c108htitle29.value,this.element.config.c108htitle30.value,this.element.config.c108htitle31.value,this.element.config.c108htitle32.value,this.element.config.c108htitle33.value,this.element.config.c108htitle34.value,this.element.config.c108htitle35.value,this.element.config.c108htitle36.value,this.element.config.c108htitle37.value,this.element.config.c108htitle38.value,this.element.config.c108htitle39.value,this.element.config.c108htitle40.value,this.element.config.c108htitle41.value,this.element.config.c108htitle42.value,this.element.config.c108htitle43.value,this.element.config.c108htitle44.value,this.element.config.c108htitle45.value,this.element.config.c108htitle46.value,this.element.config.c108htitle47.value,this.element.config.c108htitle48.value,this.element.config.c108htitle49.value,this.element.config.c108htitle50.value],allDescriptions:[0,this.element.config.c108hdescr01.value,this.element.config.c108hdescr02.value,this.element.config.c108hdescr03.value,this.element.config.c108hdescr04.value,this.element.config.c108hdescr05.value,this.element.config.c108hdescr06.value,this.element.config.c108hdescr07.value,this.element.config.c108hdescr08.value,this.element.config.c108hdescr09.value,this.element.config.c108hdescr10.value,this.element.config.c108hdescr11.value,this.element.config.c108hdescr12.value,this.element.config.c108hdescr13.value,this.element.config.c108hdescr14.value,this.element.config.c108hdescr15.value,this.element.config.c108hdescr16.value,this.element.config.c108hdescr17.value,this.element.config.c108hdescr18.value,this.element.config.c108hdescr19.value,this.element.config.c108hdescr20.value,this.element.config.c108hdescr21.value,this.element.config.c108hdescr22.value,this.element.config.c108hdescr23.value,this.element.config.c108hdescr24.value,this.element.config.c108hdescr25.value,this.element.config.c108hdescr26.value,this.element.config.c108hdescr27.value,this.element.config.c108hdescr28.value,this.element.config.c108hdescr29.value,this.element.config.c108hdescr30.value,this.element.config.c108hdescr31.value,this.element.config.c108hdescr32.value,this.element.config.c108hdescr33.value,this.element.config.c108hdescr34.value,this.element.config.c108hdescr35.value,this.element.config.c108hdescr36.value,this.element.config.c108hdescr37.value,this.element.config.c108hdescr38.value,this.element.config.c108hdescr39.value,this.element.config.c108hdescr40.value,this.element.config.c108hdescr41.value,this.element.config.c108hdescr42.value,this.element.config.c108hdescr43.value,this.element.config.c108hdescr44.value,this.element.config.c108hdescr45.value,this.element.config.c108hdescr46.value,this.element.config.c108hdescr47.value,this.element.config.c108hdescr48.value,this.element.config.c108hdescr49.value,this.element.config.c108hdescr50.value],allDescriptionsCharCounter:[0,this.element.config.c108hdescr01charcounter.value,this.element.config.c108hdescr02charcounter.value,this.element.config.c108hdescr03charcounter.value,this.element.config.c108hdescr04charcounter.value,this.element.config.c108hdescr05charcounter.value,this.element.config.c108hdescr06charcounter.value,this.element.config.c108hdescr07charcounter.value,this.element.config.c108hdescr08charcounter.value,this.element.config.c108hdescr09charcounter.value,this.element.config.c108hdescr10charcounter.value,this.element.config.c108hdescr11charcounter.value,this.element.config.c108hdescr12charcounter.value,this.element.config.c108hdescr13charcounter.value,this.element.config.c108hdescr14charcounter.value,this.element.config.c108hdescr15charcounter.value,this.element.config.c108hdescr16charcounter.value,this.element.config.c108hdescr17charcounter.value,this.element.config.c108hdescr18charcounter.value,this.element.config.c108hdescr19charcounter.value,this.element.config.c108hdescr20charcounter.value,this.element.config.c108hdescr21charcounter.value,this.element.config.c108hdescr22charcounter.value,this.element.config.c108hdescr23charcounter.value,this.element.config.c108hdescr24charcounter.value,this.element.config.c108hdescr25charcounter.value,this.element.config.c108hdescr26charcounter.value,this.element.config.c108hdescr27charcounter.value,this.element.config.c108hdescr28charcounter.value,this.element.config.c108hdescr29charcounter.value,this.element.config.c108hdescr30charcounter.value,this.element.config.c108hdescr31charcounter.value,this.element.config.c108hdescr32charcounter.value,this.element.config.c108hdescr33charcounter.value,this.element.config.c108hdescr34charcounter.value,this.element.config.c108hdescr35charcounter.value,this.element.config.c108hdescr36charcounter.value,this.element.config.c108hdescr37charcounter.value,this.element.config.c108hdescr38charcounter.value,this.element.config.c108hdescr39charcounter.value,this.element.config.c108hdescr40charcounter.value,this.element.config.c108hdescr41charcounter.value,this.element.config.c108hdescr42charcounter.value,this.element.config.c108hdescr43charcounter.value,this.element.config.c108hdescr44charcounter.value,this.element.config.c108hdescr45charcounter.value,this.element.config.c108hdescr46charcounter.value,this.element.config.c108hdescr47charcounter.value,this.element.config.c108hdescr48charcounter.value,this.element.config.c108hdescr49charcounter.value,this.element.config.c108hdescr50charcounter.value],allLinkBools:[0,this.element.config.c108hlink01bool.value,this.element.config.c108hlink02bool.value,this.element.config.c108hlink03bool.value,this.element.config.c108hlink04bool.value,this.element.config.c108hlink05bool.value,this.element.config.c108hlink06bool.value,this.element.config.c108hlink07bool.value,this.element.config.c108hlink08bool.value,this.element.config.c108hlink09bool.value,this.element.config.c108hlink10bool.value,this.element.config.c108hlink11bool.value,this.element.config.c108hlink12bool.value,this.element.config.c108hlink13bool.value,this.element.config.c108hlink14bool.value,this.element.config.c108hlink15bool.value,this.element.config.c108hlink16bool.value,this.element.config.c108hlink17bool.value,this.element.config.c108hlink18bool.value,this.element.config.c108hlink19bool.value,this.element.config.c108hlink20bool.value,this.element.config.c108hlink21bool.value,this.element.config.c108hlink22bool.value,this.element.config.c108hlink23bool.value,this.element.config.c108hlink24bool.value,this.element.config.c108hlink25bool.value,this.element.config.c108hlink26bool.value,this.element.config.c108hlink27bool.value,this.element.config.c108hlink28bool.value,this.element.config.c108hlink29bool.value,this.element.config.c108hlink30bool.value,this.element.config.c108hlink31bool.value,this.element.config.c108hlink32bool.value,this.element.config.c108hlink33bool.value,this.element.config.c108hlink34bool.value,this.element.config.c108hlink35bool.value,this.element.config.c108hlink36bool.value,this.element.config.c108hlink37bool.value,this.element.config.c108hlink38bool.value,this.element.config.c108hlink39bool.value,this.element.config.c108hlink40bool.value,this.element.config.c108hlink41bool.value,this.element.config.c108hlink42bool.value,this.element.config.c108hlink43bool.value,this.element.config.c108hlink44bool.value,this.element.config.c108hlink45bool.value,this.element.config.c108hlink46bool.value,this.element.config.c108hlink47bool.value,this.element.config.c108hlink48bool.value,this.element.config.c108hlink49bool.value,this.element.config.c108hlink50bool.value],allLinkInternals:[0,this.element.config.c108hlink01internal.value,this.element.config.c108hlink02internal.value,this.element.config.c108hlink03internal.value,this.element.config.c108hlink04internal.value,this.element.config.c108hlink05internal.value,this.element.config.c108hlink06internal.value,this.element.config.c108hlink07internal.value,this.element.config.c108hlink08internal.value,this.element.config.c108hlink09internal.value,this.element.config.c108hlink10internal.value,this.element.config.c108hlink11internal.value,this.element.config.c108hlink12internal.value,this.element.config.c108hlink13internal.value,this.element.config.c108hlink14internal.value,this.element.config.c108hlink15internal.value,this.element.config.c108hlink16internal.value,this.element.config.c108hlink17internal.value,this.element.config.c108hlink18internal.value,this.element.config.c108hlink19internal.value,this.element.config.c108hlink20internal.value,this.element.config.c108hlink21internal.value,this.element.config.c108hlink22internal.value,this.element.config.c108hlink23internal.value,this.element.config.c108hlink24internal.value,this.element.config.c108hlink25internal.value,this.element.config.c108hlink26internal.value,this.element.config.c108hlink27internal.value,this.element.config.c108hlink28internal.value,this.element.config.c108hlink29internal.value,this.element.config.c108hlink30internal.value,this.element.config.c108hlink31internal.value,this.element.config.c108hlink32internal.value,this.element.config.c108hlink33internal.value,this.element.config.c108hlink34internal.value,this.element.config.c108hlink35internal.value,this.element.config.c108hlink36internal.value,this.element.config.c108hlink37internal.value,this.element.config.c108hlink38internal.value,this.element.config.c108hlink39internal.value,this.element.config.c108hlink40internal.value,this.element.config.c108hlink41internal.value,this.element.config.c108hlink42internal.value,this.element.config.c108hlink43internal.value,this.element.config.c108hlink44internal.value,this.element.config.c108hlink45internal.value,this.element.config.c108hlink46internal.value,this.element.config.c108hlink47internal.value,this.element.config.c108hlink48internal.value,this.element.config.c108hlink49internal.value,this.element.config.c108hlink50internal.value],allLinkUrls:[0,this.element.config.c108hlink01url.value,this.element.config.c108hlink02url.value,this.element.config.c108hlink03url.value,this.element.config.c108hlink04url.value,this.element.config.c108hlink05url.value,this.element.config.c108hlink06url.value,this.element.config.c108hlink07url.value,this.element.config.c108hlink08url.value,this.element.config.c108hlink09url.value,this.element.config.c108hlink10url.value,this.element.config.c108hlink11url.value,this.element.config.c108hlink12url.value,this.element.config.c108hlink13url.value,this.element.config.c108hlink14url.value,this.element.config.c108hlink15url.value,this.element.config.c108hlink16url.value,this.element.config.c108hlink17url.value,this.element.config.c108hlink18url.value,this.element.config.c108hlink19url.value,this.element.config.c108hlink20url.value,this.element.config.c108hlink21url.value,this.element.config.c108hlink22url.value,this.element.config.c108hlink23url.value,this.element.config.c108hlink24url.value,this.element.config.c108hlink25url.value,this.element.config.c108hlink26url.value,this.element.config.c108hlink27url.value,this.element.config.c108hlink28url.value,this.element.config.c108hlink29url.value,this.element.config.c108hlink30url.value,this.element.config.c108hlink31url.value,this.element.config.c108hlink32url.value,this.element.config.c108hlink33url.value,this.element.config.c108hlink34url.value,this.element.config.c108hlink35url.value,this.element.config.c108hlink36url.value,this.element.config.c108hlink37url.value,this.element.config.c108hlink38url.value,this.element.config.c108hlink39url.value,this.element.config.c108hlink40url.value,this.element.config.c108hlink41url.value,this.element.config.c108hlink42url.value,this.element.config.c108hlink43url.value,this.element.config.c108hlink44url.value,this.element.config.c108hlink45url.value,this.element.config.c108hlink46url.value,this.element.config.c108hlink47url.value,this.element.config.c108hlink48url.value,this.element.config.c108hlink49url.value,this.element.config.c108hlink50url.value],allLinkTitles:[0,this.element.config.c108hlink01title.value,this.element.config.c108hlink02title.value,this.element.config.c108hlink03title.value,this.element.config.c108hlink04title.value,this.element.config.c108hlink05title.value,this.element.config.c108hlink06title.value,this.element.config.c108hlink07title.value,this.element.config.c108hlink08title.value,this.element.config.c108hlink09title.value,this.element.config.c108hlink10title.value,this.element.config.c108hlink11title.value,this.element.config.c108hlink12title.value,this.element.config.c108hlink13title.value,this.element.config.c108hlink14title.value,this.element.config.c108hlink15title.value,this.element.config.c108hlink16title.value,this.element.config.c108hlink17title.value,this.element.config.c108hlink18title.value,this.element.config.c108hlink19title.value,this.element.config.c108hlink20title.value,this.element.config.c108hlink21title.value,this.element.config.c108hlink22title.value,this.element.config.c108hlink23title.value,this.element.config.c108hlink24title.value,this.element.config.c108hlink25title.value,this.element.config.c108hlink26title.value,this.element.config.c108hlink27title.value,this.element.config.c108hlink28title.value,this.element.config.c108hlink29title.value,this.element.config.c108hlink30title.value,this.element.config.c108hlink31title.value,this.element.config.c108hlink32title.value,this.element.config.c108hlink33title.value,this.element.config.c108hlink34title.value,this.element.config.c108hlink35title.value,this.element.config.c108hlink36title.value,this.element.config.c108hlink37title.value,this.element.config.c108hlink38title.value,this.element.config.c108hlink39title.value,this.element.config.c108hlink40title.value,this.element.config.c108hlink41title.value,this.element.config.c108hlink42title.value,this.element.config.c108hlink43title.value,this.element.config.c108hlink44title.value,this.element.config.c108hlink45title.value,this.element.config.c108hlink46title.value,this.element.config.c108hlink47title.value,this.element.config.c108hlink48title.value,this.element.config.c108hlink49title.value,this.element.config.c108hlink50title.value],allLinkAdvancedBools:[0,this.element.config.c108hlink01advancedbool.value,this.element.config.c108hlink02advancedbool.value,this.element.config.c108hlink03advancedbool.value,this.element.config.c108hlink04advancedbool.value,this.element.config.c108hlink05advancedbool.value,this.element.config.c108hlink06advancedbool.value,this.element.config.c108hlink07advancedbool.value,this.element.config.c108hlink08advancedbool.value,this.element.config.c108hlink09advancedbool.value,this.element.config.c108hlink10advancedbool.value,this.element.config.c108hlink11advancedbool.value,this.element.config.c108hlink12advancedbool.value,this.element.config.c108hlink13advancedbool.value,this.element.config.c108hlink14advancedbool.value,this.element.config.c108hlink15advancedbool.value,this.element.config.c108hlink16advancedbool.value,this.element.config.c108hlink17advancedbool.value,this.element.config.c108hlink18advancedbool.value,this.element.config.c108hlink19advancedbool.value,this.element.config.c108hlink20advancedbool.value,this.element.config.c108hlink21advancedbool.value,this.element.config.c108hlink22advancedbool.value,this.element.config.c108hlink23advancedbool.value,this.element.config.c108hlink24advancedbool.value,this.element.config.c108hlink25advancedbool.value,this.element.config.c108hlink26advancedbool.value,this.element.config.c108hlink27advancedbool.value,this.element.config.c108hlink28advancedbool.value,this.element.config.c108hlink29advancedbool.value,this.element.config.c108hlink30advancedbool.value,this.element.config.c108hlink31advancedbool.value,this.element.config.c108hlink32advancedbool.value,this.element.config.c108hlink33advancedbool.value,this.element.config.c108hlink34advancedbool.value,this.element.config.c108hlink35advancedbool.value,this.element.config.c108hlink36advancedbool.value,this.element.config.c108hlink37advancedbool.value,this.element.config.c108hlink38advancedbool.value,this.element.config.c108hlink39advancedbool.value,this.element.config.c108hlink40advancedbool.value,this.element.config.c108hlink41advancedbool.value,this.element.config.c108hlink42advancedbool.value,this.element.config.c108hlink43advancedbool.value,this.element.config.c108hlink44advancedbool.value,this.element.config.c108hlink45advancedbool.value,this.element.config.c108hlink46advancedbool.value,this.element.config.c108hlink47advancedbool.value,this.element.config.c108hlink48advancedbool.value,this.element.config.c108hlink49advancedbool.value,this.element.config.c108hlink50advancedbool.value],allTextEditorContents:[0,this.element.config.c108htexteditorcontent01.value,this.element.config.c108htexteditorcontent02.value,this.element.config.c108htexteditorcontent03.value,this.element.config.c108htexteditorcontent04.value,this.element.config.c108htexteditorcontent05.value,this.element.config.c108htexteditorcontent06.value,this.element.config.c108htexteditorcontent07.value,this.element.config.c108htexteditorcontent08.value,this.element.config.c108htexteditorcontent09.value,this.element.config.c108htexteditorcontent10.value,this.element.config.c108htexteditorcontent11.value,this.element.config.c108htexteditorcontent12.value,this.element.config.c108htexteditorcontent13.value,this.element.config.c108htexteditorcontent14.value,this.element.config.c108htexteditorcontent15.value,this.element.config.c108htexteditorcontent16.value,this.element.config.c108htexteditorcontent17.value,this.element.config.c108htexteditorcontent18.value,this.element.config.c108htexteditorcontent19.value,this.element.config.c108htexteditorcontent20.value,this.element.config.c108htexteditorcontent21.value,this.element.config.c108htexteditorcontent22.value,this.element.config.c108htexteditorcontent23.value,this.element.config.c108htexteditorcontent24.value,this.element.config.c108htexteditorcontent25.value,this.element.config.c108htexteditorcontent26.value,this.element.config.c108htexteditorcontent27.value,this.element.config.c108htexteditorcontent28.value,this.element.config.c108htexteditorcontent29.value,this.element.config.c108htexteditorcontent30.value,this.element.config.c108htexteditorcontent31.value,this.element.config.c108htexteditorcontent32.value,this.element.config.c108htexteditorcontent33.value,this.element.config.c108htexteditorcontent34.value,this.element.config.c108htexteditorcontent35.value,this.element.config.c108htexteditorcontent36.value,this.element.config.c108htexteditorcontent37.value,this.element.config.c108htexteditorcontent38.value,this.element.config.c108htexteditorcontent39.value,this.element.config.c108htexteditorcontent40.value,this.element.config.c108htexteditorcontent41.value,this.element.config.c108htexteditorcontent42.value,this.element.config.c108htexteditorcontent43.value,this.element.config.c108htexteditorcontent44.value,this.element.config.c108htexteditorcontent45.value,this.element.config.c108htexteditorcontent46.value,this.element.config.c108htexteditorcontent47.value,this.element.config.c108htexteditorcontent48.value,this.element.config.c108htexteditorcontent49.value,this.element.config.c108htexteditorcontent50.value],allIndividualColors:[0,this.element.config.c108hmarkercolor01.value,this.element.config.c108hmarkercolor02.value,this.element.config.c108hmarkercolor03.value,this.element.config.c108hmarkercolor04.value,this.element.config.c108hmarkercolor05.value,this.element.config.c108hmarkercolor06.value,this.element.config.c108hmarkercolor07.value,this.element.config.c108hmarkercolor08.value,this.element.config.c108hmarkercolor09.value,this.element.config.c108hmarkercolor10.value,this.element.config.c108hmarkercolor11.value,this.element.config.c108hmarkercolor12.value,this.element.config.c108hmarkercolor13.value,this.element.config.c108hmarkercolor14.value,this.element.config.c108hmarkercolor15.value,this.element.config.c108hmarkercolor16.value,this.element.config.c108hmarkercolor17.value,this.element.config.c108hmarkercolor18.value,this.element.config.c108hmarkercolor19.value,this.element.config.c108hmarkercolor20.value,this.element.config.c108hmarkercolor21.value,this.element.config.c108hmarkercolor22.value,this.element.config.c108hmarkercolor23.value,this.element.config.c108hmarkercolor24.value,this.element.config.c108hmarkercolor25.value,this.element.config.c108hmarkercolor26.value,this.element.config.c108hmarkercolor27.value,this.element.config.c108hmarkercolor28.value,this.element.config.c108hmarkercolor29.value,this.element.config.c108hmarkercolor30.value,this.element.config.c108hmarkercolor31.value,this.element.config.c108hmarkercolor32.value,this.element.config.c108hmarkercolor33.value,this.element.config.c108hmarkercolor34.value,this.element.config.c108hmarkercolor35.value,this.element.config.c108hmarkercolor36.value,this.element.config.c108hmarkercolor37.value,this.element.config.c108hmarkercolor38.value,this.element.config.c108hmarkercolor39.value,this.element.config.c108hmarkercolor40.value,this.element.config.c108hmarkercolor41.value,this.element.config.c108hmarkercolor42.value,this.element.config.c108hmarkercolor43.value,this.element.config.c108hmarkercolor44.value,this.element.config.c108hmarkercolor45.value,this.element.config.c108hmarkercolor46.value,this.element.config.c108hmarkercolor47.value,this.element.config.c108hmarkercolor48.value,this.element.config.c108hmarkercolor49.value,this.element.config.c108hmarkercolor50.value]}},computed:{mediaUrl:function(){var e=Shopware.Context.api,t=this.element.data.media;return t&&t.id?this.element.data.media.url:t&&t.url?"".concat(e.assetsPath).concat(t.url):void 0},mediaRepository:function(){return this.repositoryFactory.create("media")},uploadTag:function(){return"cms-element-media-config-".concat(this.element.id)},previewSource:function(){return this.element.data&&this.element.data.media&&this.element.data.media.id?this.element.data.media:this.element.config.media.value}},created:function(){this.createdComponent()},methods:{setTitle:function(e,t){switch(t){case 1:this.element.config.c108htitle01.value=e.target.value;break;case 2:this.element.config.c108htitle02.value=e.target.value;break;case 3:this.element.config.c108htitle03.value=e.target.value;break;case 4:this.element.config.c108htitle04.value=e.target.value;break;case 5:this.element.config.c108htitle05.value=e.target.value;break;case 6:this.element.config.c108htitle06.value=e.target.value;break;case 7:this.element.config.c108htitle07.value=e.target.value;break;case 8:this.element.config.c108htitle08.value=e.target.value;break;case 9:this.element.config.c108htitle09.value=e.target.value;break;case 10:this.element.config.c108htitle10.value=e.target.value;break;case 11:this.element.config.c108htitle11.value=e.target.value;break;case 12:this.element.config.c108htitle12.value=e.target.value;break;case 13:this.element.config.c108htitle13.value=e.target.value;break;case 14:this.element.config.c108htitle14.value=e.target.value;break;case 15:this.element.config.c108htitle15.value=e.target.value;break;case 16:this.element.config.c108htitle16.value=e.target.value;break;case 17:this.element.config.c108htitle17.value=e.target.value;break;case 18:this.element.config.c108htitle18.value=e.target.value;break;case 19:this.element.config.c108htitle19.value=e.target.value;break;case 20:this.element.config.c108htitle20.value=e.target.value;break;case 21:this.element.config.c108htitle21.value=e.target.value;break;case 22:this.element.config.c108htitle22.value=e.target.value;break;case 23:this.element.config.c108htitle23.value=e.target.value;break;case 24:this.element.config.c108htitle24.value=e.target.value;break;case 25:this.element.config.c108htitle25.value=e.target.value;break;case 26:this.element.config.c108htitle26.value=e.target.value;break;case 27:this.element.config.c108htitle27.value=e.target.value;break;case 28:this.element.config.c108htitle28.value=e.target.value;break;case 29:this.element.config.c108htitle29.value=e.target.value;break;case 30:this.element.config.c108htitle30.value=e.target.value;break;case 31:this.element.config.c108htitle31.value=e.target.value;break;case 32:this.element.config.c108htitle32.value=e.target.value;break;case 33:this.element.config.c108htitle33.value=e.target.value;break;case 34:this.element.config.c108htitle34.value=e.target.value;break;case 35:this.element.config.c108htitle35.value=e.target.value;break;case 36:this.element.config.c108htitle36.value=e.target.value;break;case 37:this.element.config.c108htitle37.value=e.target.value;break;case 38:this.element.config.c108htitle38.value=e.target.value;break;case 39:this.element.config.c108htitle39.value=e.target.value;break;case 40:this.element.config.c108htitle40.value=e.target.value;break;case 41:this.element.config.c108htitle41.value=e.target.value;break;case 42:this.element.config.c108htitle42.value=e.target.value;break;case 43:this.element.config.c108htitle43.value=e.target.value;break;case 44:this.element.config.c108htitle44.value=e.target.value;break;case 45:this.element.config.c108htitle45.value=e.target.value;break;case 46:this.element.config.c108htitle46.value=e.target.value;break;case 47:this.element.config.c108htitle47.value=e.target.value;break;case 48:this.element.config.c108htitle48.value=e.target.value;break;case 49:this.element.config.c108htitle49.value=e.target.value;break;case 50:this.element.config.c108htitle50.value=e.target.value}},setDescription:function(e,t){switch(t){case 1:this.element.config.c108hdescr01.value=e.target.value;break;case 2:this.element.config.c108hdescr02.value=e.target.value;break;case 3:this.element.config.c108hdescr03.value=e.target.value;break;case 4:this.element.config.c108hdescr04.value=e.target.value;break;case 5:this.element.config.c108hdescr05.value=e.target.value;break;case 6:this.element.config.c108hdescr06.value=e.target.value;break;case 7:this.element.config.c108hdescr07.value=e.target.value;break;case 8:this.element.config.c108hdescr08.value=e.target.value;break;case 9:this.element.config.c108hdescr09.value=e.target.value;break;case 10:this.element.config.c108hdescr10.value=e.target.value;break;case 11:this.element.config.c108hdescr11.value=e.target.value;break;case 12:this.element.config.c108hdescr12.value=e.target.value;break;case 13:this.element.config.c108hdescr13.value=e.target.value;break;case 14:this.element.config.c108hdescr14.value=e.target.value;break;case 15:this.element.config.c108hdescr15.value=e.target.value;break;case 16:this.element.config.c108hdescr16.value=e.target.value;break;case 17:this.element.config.c108hdescr17.value=e.target.value;break;case 18:this.element.config.c108hdescr18.value=e.target.value;break;case 19:this.element.config.c108hdescr19.value=e.target.value;break;case 20:this.element.config.c108hdescr20.value=e.target.value;break;case 21:this.element.config.c108hdescr21.value=e.target.value;break;case 22:this.element.config.c108hdescr22.value=e.target.value;break;case 23:this.element.config.c108hdescr23.value=e.target.value;break;case 24:this.element.config.c108hdescr24.value=e.target.value;break;case 25:this.element.config.c108hdescr25.value=e.target.value;break;case 26:this.element.config.c108hdescr26.value=e.target.value;break;case 27:this.element.config.c108hdescr27.value=e.target.value;break;case 28:this.element.config.c108hdescr28.value=e.target.value;break;case 29:this.element.config.c108hdescr29.value=e.target.value;break;case 30:this.element.config.c108hdescr30.value=e.target.value;break;case 31:this.element.config.c108hdescr31.value=e.target.value;break;case 32:this.element.config.c108hdescr32.value=e.target.value;break;case 33:this.element.config.c108hdescr33.value=e.target.value;break;case 34:this.element.config.c108hdescr34.value=e.target.value;break;case 35:this.element.config.c108hdescr35.value=e.target.value;break;case 36:this.element.config.c108hdescr36.value=e.target.value;break;case 37:this.element.config.c108hdescr37.value=e.target.value;break;case 38:this.element.config.c108hdescr38.value=e.target.value;break;case 39:this.element.config.c108hdescr39.value=e.target.value;break;case 40:this.element.config.c108hdescr40.value=e.target.value;break;case 41:this.element.config.c108hdescr41.value=e.target.value;break;case 42:this.element.config.c108hdescr42.value=e.target.value;break;case 43:this.element.config.c108hdescr43.value=e.target.value;break;case 44:this.element.config.c108hdescr44.value=e.target.value;break;case 45:this.element.config.c108hdescr45.value=e.target.value;break;case 46:this.element.config.c108hdescr46.value=e.target.value;break;case 47:this.element.config.c108hdescr47.value=e.target.value;break;case 48:this.element.config.c108hdescr48.value=e.target.value;break;case 49:this.element.config.c108hdescr49.value=e.target.value;break;case 50:this.element.config.c108hdescr50.value=e.target.value}},setLinkBool:function(e,t){switch(t){case 1:this.element.config.c108hlink01bool.value=e.target.checked;break;case 2:this.element.config.c108hlink02bool.value=e.target.checked;break;case 3:this.element.config.c108hlink03bool.value=e.target.checked;break;case 4:this.element.config.c108hlink04bool.value=e.target.checked;break;case 5:this.element.config.c108hlink05bool.value=e.target.checked;break;case 6:this.element.config.c108hlink06bool.value=e.target.checked;break;case 7:this.element.config.c108hlink07bool.value=e.target.checked;break;case 8:this.element.config.c108hlink08bool.value=e.target.checked;break;case 9:this.element.config.c108hlink09bool.value=e.target.checked;break;case 10:this.element.config.c108hlink10bool.value=e.target.checked;break;case 11:this.element.config.c108hlink11bool.value=e.target.checked;break;case 12:this.element.config.c108hlink12bool.value=e.target.checked;break;case 13:this.element.config.c108hlink13bool.value=e.target.checked;break;case 14:this.element.config.c108hlink14bool.value=e.target.checked;break;case 15:this.element.config.c108hlink15bool.value=e.target.checked;break;case 16:this.element.config.c108hlink16bool.value=e.target.checked;break;case 17:this.element.config.c108hlink17bool.value=e.target.checked;break;case 18:this.element.config.c108hlink18bool.value=e.target.checked;break;case 19:this.element.config.c108hlink19bool.value=e.target.checked;break;case 20:this.element.config.c108hlink20bool.value=e.target.checked;break;case 21:this.element.config.c108hlink21bool.value=e.target.checked;break;case 22:this.element.config.c108hlink22bool.value=e.target.checked;break;case 23:this.element.config.c108hlink23bool.value=e.target.checked;break;case 24:this.element.config.c108hlink24bool.value=e.target.checked;break;case 25:this.element.config.c108hlink25bool.value=e.target.checked;break;case 26:this.element.config.c108hlink26bool.value=e.target.checked;break;case 27:this.element.config.c108hlink27bool.value=e.target.checked;break;case 28:this.element.config.c108hlink28bool.value=e.target.checked;break;case 29:this.element.config.c108hlink29bool.value=e.target.checked;break;case 30:this.element.config.c108hlink30bool.value=e.target.checked;break;case 31:this.element.config.c108hlink31bool.value=e.target.checked;break;case 32:this.element.config.c108hlink32bool.value=e.target.checked;break;case 33:this.element.config.c108hlink33bool.value=e.target.checked;break;case 34:this.element.config.c108hlink34bool.value=e.target.checked;break;case 35:this.element.config.c108hlink35bool.value=e.target.checked;break;case 36:this.element.config.c108hlink36bool.value=e.target.checked;break;case 37:this.element.config.c108hlink37bool.value=e.target.checked;break;case 38:this.element.config.c108hlink38bool.value=e.target.checked;break;case 39:this.element.config.c108hlink39bool.value=e.target.checked;break;case 40:this.element.config.c108hlink40bool.value=e.target.checked;break;case 41:this.element.config.c108hlink41bool.value=e.target.checked;break;case 42:this.element.config.c108hlink42bool.value=e.target.checked;break;case 43:this.element.config.c108hlink43bool.value=e.target.checked;break;case 44:this.element.config.c108hlink44bool.value=e.target.checked;break;case 45:this.element.config.c108hlink45bool.value=e.target.checked;break;case 46:this.element.config.c108hlink46bool.value=e.target.checked;break;case 47:this.element.config.c108hlink47bool.value=e.target.checked;break;case 48:this.element.config.c108hlink48bool.value=e.target.checked;break;case 49:this.element.config.c108hlink49bool.value=e.target.checked;break;case 50:this.element.config.c108hlink50bool.value=e.target.checked}},setLinkInternal:function(e,t){switch(t){case 1:this.element.config.c108hlink01internal.value=e.target.checked;break;case 2:this.element.config.c108hlink02internal.value=e.target.checked;break;case 3:this.element.config.c108hlink03internal.value=e.target.checked;break;case 4:this.element.config.c108hlink04internal.value=e.target.checked;break;case 5:this.element.config.c108hlink05internal.value=e.target.checked;break;case 6:this.element.config.c108hlink06internal.value=e.target.checked;break;case 7:this.element.config.c108hlink07internal.value=e.target.checked;break;case 8:this.element.config.c108hlink08internal.value=e.target.checked;break;case 9:this.element.config.c108hlink09internal.value=e.target.checked;break;case 10:this.element.config.c108hlink10internal.value=e.target.checked;break;case 11:this.element.config.c108hlink11internal.value=e.target.checked;break;case 12:this.element.config.c108hlink12internal.value=e.target.checked;break;case 13:this.element.config.c108hlink13internal.value=e.target.checked;break;case 14:this.element.config.c108hlink14internal.value=e.target.checked;break;case 15:this.element.config.c108hlink15internal.value=e.target.checked;break;case 16:this.element.config.c108hlink16internal.value=e.target.checked;break;case 17:this.element.config.c108hlink17internal.value=e.target.checked;break;case 18:this.element.config.c108hlink18internal.value=e.target.checked;break;case 19:this.element.config.c108hlink19internal.value=e.target.checked;break;case 20:this.element.config.c108hlink20internal.value=e.target.checked;break;case 21:this.element.config.c108hlink21internal.value=e.target.checked;break;case 22:this.element.config.c108hlink22internal.value=e.target.checked;break;case 23:this.element.config.c108hlink23internal.value=e.target.checked;break;case 24:this.element.config.c108hlink24internal.value=e.target.checked;break;case 25:this.element.config.c108hlink25internal.value=e.target.checked;break;case 26:this.element.config.c108hlink26internal.value=e.target.checked;break;case 27:this.element.config.c108hlink27internal.value=e.target.checked;break;case 28:this.element.config.c108hlink28internal.value=e.target.checked;break;case 29:this.element.config.c108hlink29internal.value=e.target.checked;break;case 30:this.element.config.c108hlink30internal.value=e.target.checked;break;case 31:this.element.config.c108hlink31internal.value=e.target.checked;break;case 32:this.element.config.c108hlink32internal.value=e.target.checked;break;case 33:this.element.config.c108hlink33internal.value=e.target.checked;break;case 34:this.element.config.c108hlink34internal.value=e.target.checked;break;case 35:this.element.config.c108hlink35internal.value=e.target.checked;break;case 36:this.element.config.c108hlink36internal.value=e.target.checked;break;case 37:this.element.config.c108hlink37internal.value=e.target.checked;break;case 38:this.element.config.c108hlink38internal.value=e.target.checked;break;case 39:this.element.config.c108hlink39internal.value=e.target.checked;break;case 40:this.element.config.c108hlink40internal.value=e.target.checked;break;case 41:this.element.config.c108hlink41internal.value=e.target.checked;break;case 42:this.element.config.c108hlink42internal.value=e.target.checked;break;case 43:this.element.config.c108hlink43internal.value=e.target.checked;break;case 44:this.element.config.c108hlink44internal.value=e.target.checked;break;case 45:this.element.config.c108hlink45internal.value=e.target.checked;break;case 46:this.element.config.c108hlink46internal.value=e.target.checked;break;case 47:this.element.config.c108hlink47internal.value=e.target.checked;break;case 48:this.element.config.c108hlink48internal.value=e.target.checked;break;case 49:this.element.config.c108hlink49internal.value=e.target.checked;break;case 50:this.element.config.c108hlink50internal.value=e.target.checked}},setLinkUrl:function(e,t){switch(t){case 1:this.element.config.c108hlink01url.value=e.target.value;break;case 2:this.element.config.c108hlink02url.value=e.target.value;break;case 3:this.element.config.c108hlink03url.value=e.target.value;break;case 4:this.element.config.c108hlink04url.value=e.target.value;break;case 5:this.element.config.c108hlink05url.value=e.target.value;break;case 6:this.element.config.c108hlink06url.value=e.target.value;break;case 7:this.element.config.c108hlink07url.value=e.target.value;break;case 8:this.element.config.c108hlink08url.value=e.target.value;break;case 9:this.element.config.c108hlink09url.value=e.target.value;break;case 10:this.element.config.c108hlink10url.value=e.target.value;break;case 11:this.element.config.c108hlink11url.value=e.target.value;break;case 12:this.element.config.c108hlink12url.value=e.target.value;break;case 13:this.element.config.c108hlink13url.value=e.target.value;break;case 14:this.element.config.c108hlink14url.value=e.target.value;break;case 15:this.element.config.c108hlink15url.value=e.target.value;break;case 16:this.element.config.c108hlink16url.value=e.target.value;break;case 17:this.element.config.c108hlink17url.value=e.target.value;break;case 18:this.element.config.c108hlink18url.value=e.target.value;break;case 19:this.element.config.c108hlink19url.value=e.target.value;break;case 20:this.element.config.c108hlink20url.value=e.target.value;break;case 21:this.element.config.c108hlink21url.value=e.target.value;break;case 22:this.element.config.c108hlink22url.value=e.target.value;break;case 23:this.element.config.c108hlink23url.value=e.target.value;break;case 24:this.element.config.c108hlink24url.value=e.target.value;break;case 25:this.element.config.c108hlink25url.value=e.target.value;break;case 26:this.element.config.c108hlink26url.value=e.target.value;break;case 27:this.element.config.c108hlink27url.value=e.target.value;break;case 28:this.element.config.c108hlink28url.value=e.target.value;break;case 29:this.element.config.c108hlink29url.value=e.target.value;break;case 30:this.element.config.c108hlink30url.value=e.target.value;break;case 31:this.element.config.c108hlink31url.value=e.target.value;break;case 32:this.element.config.c108hlink32url.value=e.target.value;break;case 33:this.element.config.c108hlink33url.value=e.target.value;break;case 34:this.element.config.c108hlink34url.value=e.target.value;break;case 35:this.element.config.c108hlink35url.value=e.target.value;break;case 36:this.element.config.c108hlink36url.value=e.target.value;break;case 37:this.element.config.c108hlink37url.value=e.target.value;break;case 38:this.element.config.c108hlink38url.value=e.target.value;break;case 39:this.element.config.c108hlink39url.value=e.target.value;break;case 40:this.element.config.c108hlink40url.value=e.target.value;break;case 41:this.element.config.c108hlink41url.value=e.target.value;break;case 42:this.element.config.c108hlink42url.value=e.target.value;break;case 43:this.element.config.c108hlink43url.value=e.target.value;break;case 44:this.element.config.c108hlink44url.value=e.target.value;break;case 45:this.element.config.c108hlink45url.value=e.target.value;break;case 46:this.element.config.c108hlink46url.value=e.target.value;break;case 47:this.element.config.c108hlink47url.value=e.target.value;break;case 48:this.element.config.c108hlink48url.value=e.target.value;break;case 49:this.element.config.c108hlink49url.value=e.target.value;break;case 50:this.element.config.c108hlink50url.value=e.target.value}},setLinkTitle:function(e,t){switch(t){case 1:this.element.config.c108hlink01title.value=e.target.value;break;case 2:this.element.config.c108hlink02title.value=e.target.value;break;case 3:this.element.config.c108hlink03title.value=e.target.value;break;case 4:this.element.config.c108hlink04title.value=e.target.value;break;case 5:this.element.config.c108hlink05title.value=e.target.value;break;case 6:this.element.config.c108hlink06title.value=e.target.value;break;case 7:this.element.config.c108hlink07title.value=e.target.value;break;case 8:this.element.config.c108hlink08title.value=e.target.value;break;case 9:this.element.config.c108hlink09title.value=e.target.value;break;case 10:this.element.config.c108hlink10title.value=e.target.value;break;case 11:this.element.config.c108hlink11title.value=e.target.value;break;case 12:this.element.config.c108hlink12title.value=e.target.value;break;case 13:this.element.config.c108hlink13title.value=e.target.value;break;case 14:this.element.config.c108hlink14title.value=e.target.value;break;case 15:this.element.config.c108hlink15title.value=e.target.value;break;case 16:this.element.config.c108hlink16title.value=e.target.value;break;case 17:this.element.config.c108hlink17title.value=e.target.value;break;case 18:this.element.config.c108hlink18title.value=e.target.value;break;case 19:this.element.config.c108hlink19title.value=e.target.value;break;case 20:this.element.config.c108hlink20title.value=e.target.value;break;case 21:this.element.config.c108hlink21title.value=e.target.value;break;case 22:this.element.config.c108hlink22title.value=e.target.value;break;case 23:this.element.config.c108hlink23title.value=e.target.value;break;case 24:this.element.config.c108hlink24title.value=e.target.value;break;case 25:this.element.config.c108hlink25title.value=e.target.value;break;case 26:this.element.config.c108hlink26title.value=e.target.value;break;case 27:this.element.config.c108hlink27title.value=e.target.value;break;case 28:this.element.config.c108hlink28title.value=e.target.value;break;case 29:this.element.config.c108hlink29title.value=e.target.value;break;case 30:this.element.config.c108hlink30title.value=e.target.value;break;case 31:this.element.config.c108hlink31title.value=e.target.value;break;case 32:this.element.config.c108hlink32title.value=e.target.value;break;case 33:this.element.config.c108hlink33title.value=e.target.value;break;case 34:this.element.config.c108hlink34title.value=e.target.value;break;case 35:this.element.config.c108hlink35title.value=e.target.value;break;case 36:this.element.config.c108hlink36title.value=e.target.value;break;case 37:this.element.config.c108hlink37title.value=e.target.value;break;case 38:this.element.config.c108hlink38title.value=e.target.value;break;case 39:this.element.config.c108hlink39title.value=e.target.value;break;case 40:this.element.config.c108hlink40title.value=e.target.value;break;case 41:this.element.config.c108hlink41title.value=e.target.value;break;case 42:this.element.config.c108hlink42title.value=e.target.value;break;case 43:this.element.config.c108hlink43title.value=e.target.value;break;case 44:this.element.config.c108hlink44title.value=e.target.value;break;case 45:this.element.config.c108hlink45title.value=e.target.value;break;case 46:this.element.config.c108hlink46title.value=e.target.value;break;case 47:this.element.config.c108hlink47title.value=e.target.value;break;case 48:this.element.config.c108hlink48title.value=e.target.value;break;case 49:this.element.config.c108hlink49title.value=e.target.value;break;case 50:this.element.config.c108hlink50title.value=e.target.value}},setLinkAdvancedBool:function(e,t){switch(t){case 1:this.element.config.c108hlink01advancedbool.value=e.target.checked;break;case 2:this.element.config.c108hlink02advancedbool.value=e.target.checked;break;case 3:this.element.config.c108hlink03advancedbool.value=e.target.checked;break;case 4:this.element.config.c108hlink04advancedbool.value=e.target.checked;break;case 5:this.element.config.c108hlink05advancedbool.value=e.target.checked;break;case 6:this.element.config.c108hlink06advancedbool.value=e.target.checked;break;case 7:this.element.config.c108hlink07advancedbool.value=e.target.checked;break;case 8:this.element.config.c108hlink08advancedbool.value=e.target.checked;break;case 9:this.element.config.c108hlink09advancedbool.value=e.target.checked;break;case 10:this.element.config.c108hlink10advancedbool.value=e.target.checked;break;case 11:this.element.config.c108hlink11advancedbool.value=e.target.checked;break;case 12:this.element.config.c108hlink12advancedbool.value=e.target.checked;break;case 13:this.element.config.c108hlink13advancedbool.value=e.target.checked;break;case 14:this.element.config.c108hlink14advancedbool.value=e.target.checked;break;case 15:this.element.config.c108hlink15advancedbool.value=e.target.checked;break;case 16:this.element.config.c108hlink16advancedbool.value=e.target.checked;break;case 17:this.element.config.c108hlink17advancedbool.value=e.target.checked;break;case 18:this.element.config.c108hlink18advancedbool.value=e.target.checked;break;case 19:this.element.config.c108hlink19advancedbool.value=e.target.checked;break;case 20:this.element.config.c108hlink20advancedbool.value=e.target.checked;break;case 21:this.element.config.c108hlink21advancedbool.value=e.target.checked;break;case 22:this.element.config.c108hlink22advancedbool.value=e.target.checked;break;case 23:this.element.config.c108hlink23advancedbool.value=e.target.checked;break;case 24:this.element.config.c108hlink24advancedbool.value=e.target.checked;break;case 25:this.element.config.c108hlink25advancedbool.value=e.target.checked;break;case 26:this.element.config.c108hlink26advancedbool.value=e.target.checked;break;case 27:this.element.config.c108hlink27advancedbool.value=e.target.checked;break;case 28:this.element.config.c108hlink28advancedbool.value=e.target.checked;break;case 29:this.element.config.c108hlink29advancedbool.value=e.target.checked;break;case 30:this.element.config.c108hlink30advancedbool.value=e.target.checked;break;case 31:this.element.config.c108hlink31advancedbool.value=e.target.checked;break;case 32:this.element.config.c108hlink32advancedbool.value=e.target.checked;break;case 33:this.element.config.c108hlink33advancedbool.value=e.target.checked;break;case 34:this.element.config.c108hlink34advancedbool.value=e.target.checked;break;case 35:this.element.config.c108hlink35advancedbool.value=e.target.checked;break;case 36:this.element.config.c108hlink36advancedbool.value=e.target.checked;break;case 37:this.element.config.c108hlink37advancedbool.value=e.target.checked;break;case 38:this.element.config.c108hlink38advancedbool.value=e.target.checked;break;case 39:this.element.config.c108hlink39advancedbool.value=e.target.checked;break;case 40:this.element.config.c108hlink40advancedbool.value=e.target.checked;break;case 41:this.element.config.c108hlink41advancedbool.value=e.target.checked;break;case 42:this.element.config.c108hlink42advancedbool.value=e.target.checked;break;case 43:this.element.config.c108hlink43advancedbool.value=e.target.checked;break;case 44:this.element.config.c108hlink44advancedbool.value=e.target.checked;break;case 45:this.element.config.c108hlink45advancedbool.value=e.target.checked;break;case 46:this.element.config.c108hlink46advancedbool.value=e.target.checked;break;case 47:this.element.config.c108hlink47advancedbool.value=e.target.checked;break;case 48:this.element.config.c108hlink48advancedbool.value=e.target.checked;break;case 49:this.element.config.c108hlink49advancedbool.value=e.target.checked;break;case 50:this.element.config.c108hlink50advancedbool.value=e.target.checked}},setTextEditorContent:function(e){switch(e){case 1:this.element.config.c108htexteditorcontent01.value=this.allTextEditorContents[e];break;case 2:this.element.config.c108htexteditorcontent02.value=this.allTextEditorContents[e];break;case 3:this.element.config.c108htexteditorcontent03.value=this.allTextEditorContents[e];break;case 4:this.element.config.c108htexteditorcontent04.value=this.allTextEditorContents[e];break;case 5:this.element.config.c108htexteditorcontent05.value=this.allTextEditorContents[e];break;case 6:this.element.config.c108htexteditorcontent06.value=this.allTextEditorContents[e];break;case 7:this.element.config.c108htexteditorcontent07.value=this.allTextEditorContents[e];break;case 8:this.element.config.c108htexteditorcontent08.value=this.allTextEditorContents[e];break;case 9:this.element.config.c108htexteditorcontent09.value=this.allTextEditorContents[e];break;case 10:this.element.config.c108htexteditorcontent10.value=this.allTextEditorContents[e];break;case 11:this.element.config.c108htexteditorcontent11.value=this.allTextEditorContents[e];break;case 12:this.element.config.c108htexteditorcontent12.value=this.allTextEditorContents[e];break;case 13:this.element.config.c108htexteditorcontent13.value=this.allTextEditorContents[e];break;case 14:this.element.config.c108htexteditorcontent14.value=this.allTextEditorContents[e];break;case 15:this.element.config.c108htexteditorcontent15.value=this.allTextEditorContents[e];break;case 16:this.element.config.c108htexteditorcontent16.value=this.allTextEditorContents[e];break;case 17:this.element.config.c108htexteditorcontent17.value=this.allTextEditorContents[e];break;case 18:this.element.config.c108htexteditorcontent18.value=this.allTextEditorContents[e];break;case 19:this.element.config.c108htexteditorcontent19.value=this.allTextEditorContents[e];break;case 20:this.element.config.c108htexteditorcontent20.value=this.allTextEditorContents[e];break;case 21:this.element.config.c108htexteditorcontent21.value=this.allTextEditorContents[e];break;case 22:this.element.config.c108htexteditorcontent22.value=this.allTextEditorContents[e];break;case 23:this.element.config.c108htexteditorcontent23.value=this.allTextEditorContents[e];break;case 24:this.element.config.c108htexteditorcontent24.value=this.allTextEditorContents[e];break;case 25:this.element.config.c108htexteditorcontent25.value=this.allTextEditorContents[e];break;case 26:this.element.config.c108htexteditorcontent26.value=this.allTextEditorContents[e];break;case 27:this.element.config.c108htexteditorcontent27.value=this.allTextEditorContents[e];break;case 28:this.element.config.c108htexteditorcontent28.value=this.allTextEditorContents[e];break;case 29:this.element.config.c108htexteditorcontent29.value=this.allTextEditorContents[e];break;case 30:this.element.config.c108htexteditorcontent30.value=this.allTextEditorContents[e];break;case 31:this.element.config.c108htexteditorcontent31.value=this.allTextEditorContents[e];break;case 32:this.element.config.c108htexteditorcontent32.value=this.allTextEditorContents[e];break;case 33:this.element.config.c108htexteditorcontent33.value=this.allTextEditorContents[e];break;case 34:this.element.config.c108htexteditorcontent34.value=this.allTextEditorContents[e];break;case 35:this.element.config.c108htexteditorcontent35.value=this.allTextEditorContents[e];break;case 36:this.element.config.c108htexteditorcontent36.value=this.allTextEditorContents[e];break;case 37:this.element.config.c108htexteditorcontent37.value=this.allTextEditorContents[e];break;case 38:this.element.config.c108htexteditorcontent38.value=this.allTextEditorContents[e];break;case 39:this.element.config.c108htexteditorcontent39.value=this.allTextEditorContents[e];break;case 40:this.element.config.c108htexteditorcontent40.value=this.allTextEditorContents[e];break;case 41:this.element.config.c108htexteditorcontent41.value=this.allTextEditorContents[e];break;case 42:this.element.config.c108htexteditorcontent42.value=this.allTextEditorContents[e];break;case 43:this.element.config.c108htexteditorcontent43.value=this.allTextEditorContents[e];break;case 44:this.element.config.c108htexteditorcontent44.value=this.allTextEditorContents[e];break;case 45:this.element.config.c108htexteditorcontent45.value=this.allTextEditorContents[e];break;case 46:this.element.config.c108htexteditorcontent46.value=this.allTextEditorContents[e];break;case 47:this.element.config.c108htexteditorcontent47.value=this.allTextEditorContents[e];break;case 48:this.element.config.c108htexteditorcontent48.value=this.allTextEditorContents[e];break;case 49:this.element.config.c108htexteditorcontent49.value=this.allTextEditorContents[e];break;case 50:this.element.config.c108htexteditorcontent50.value=this.allTextEditorContents[e]}},setBgImage:function(e,t,i,l){var a=this,c=new Image,n=this.element.config.media.value;this.mediaRepository.get(n,Shopware.Context.api).then((function(n){c.src=n.url,a.element.config.c108huseindividualimagesizebool.value&&(a.fixedCanvasWidth=Math.round(n.metaData.width/6),a.fixedCanvasHeight=Math.round(n.metaData.height/6)),a.element.config.c108huseindividualimagesizebool.value||(a.fixedCanvasWidth=300,a.fixedCanvasHeight=150);var s=document.getElementById("canvas-hs-"+e),o=s.getContext("2d");switch(o.clearRect(0,0,s.width,s.height),c.src.includes("undefined")&&(o.font="13px Source Sans Pro",o.textAlign="center",o.fillStyle="#52667a",o.fillText("No image set",s.width/2,s.height/2)),c.onload=function(){o.drawImage(c,0,0,c.width,c.height,0,0,s.width,s.height);o.beginPath(),o.arc(r,h,10,0,2*Math.PI),o.fillStyle=t,i&&(o.fillStyle=l),o.fill(),o.lineWidth=3,o.strokeStyle="white",o.stroke()},e){case 1:var r=a.element.config.c108hmarker01xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker01ypos.value/100*a.fixedCanvasHeight;break;case 2:r=a.element.config.c108hmarker02xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker02ypos.value/100*a.fixedCanvasHeight;break;case 3:r=a.element.config.c108hmarker03xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker03ypos.value/100*a.fixedCanvasHeight;break;case 4:r=a.element.config.c108hmarker04xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker04ypos.value/100*a.fixedCanvasHeight;break;case 5:r=a.element.config.c108hmarker05xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker05ypos.value/100*a.fixedCanvasHeight;break;case 6:r=a.element.config.c108hmarker06xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker06ypos.value/100*a.fixedCanvasHeight;break;case 7:r=a.element.config.c108hmarker07xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker07ypos.value/100*a.fixedCanvasHeight;break;case 8:r=a.element.config.c108hmarker08xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker08ypos.value/100*a.fixedCanvasHeight;break;case 9:r=a.element.config.c108hmarker09xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker09ypos.value/100*a.fixedCanvasHeight;break;case 10:r=a.element.config.c108hmarker10xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker10ypos.value/100*a.fixedCanvasHeight;break;case 11:r=a.element.config.c108hmarker11xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker11ypos.value/100*a.fixedCanvasHeight;break;case 12:r=a.element.config.c108hmarker12xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker12ypos.value/100*a.fixedCanvasHeight;break;case 13:r=a.element.config.c108hmarker13xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker13ypos.value/100*a.fixedCanvasHeight;break;case 14:r=a.element.config.c108hmarker14xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker14ypos.value/100*a.fixedCanvasHeight;break;case 15:r=a.element.config.c108hmarker15xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker15ypos.value/100*a.fixedCanvasHeight;break;case 16:r=a.element.config.c108hmarker16xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker16ypos.value/100*a.fixedCanvasHeight;break;case 17:r=a.element.config.c108hmarker17xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker17ypos.value/100*a.fixedCanvasHeight;break;case 18:r=a.element.config.c108hmarker18xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker18ypos.value/100*a.fixedCanvasHeight;break;case 19:r=a.element.config.c108hmarker19xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker19ypos.value/100*a.fixedCanvasHeight;break;case 20:r=a.element.config.c108hmarker20xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker20ypos.value/100*a.fixedCanvasHeight;break;case 21:r=a.element.config.c108hmarker21xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker21ypos.value/100*a.fixedCanvasHeight;break;case 22:r=a.element.config.c108hmarker22xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker22ypos.value/100*a.fixedCanvasHeight;break;case 23:r=a.element.config.c108hmarker23xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker23ypos.value/100*a.fixedCanvasHeight;break;case 24:r=a.element.config.c108hmarker24xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker24ypos.value/100*a.fixedCanvasHeight;break;case 25:r=a.element.config.c108hmarker25xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker25ypos.value/100*a.fixedCanvasHeight;break;case 26:r=a.element.config.c108hmarker26xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker26ypos.value/100*a.fixedCanvasHeight;break;case 27:r=a.element.config.c108hmarker27xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker27ypos.value/100*a.fixedCanvasHeight;break;case 28:r=a.element.config.c108hmarker28xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker28ypos.value/100*a.fixedCanvasHeight;break;case 29:r=a.element.config.c108hmarker29xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker29ypos.value/100*a.fixedCanvasHeight;break;case 30:r=a.element.config.c108hmarker30xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker30ypos.value/100*a.fixedCanvasHeight;break;case 31:r=a.element.config.c108hmarker31xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker31ypos.value/100*a.fixedCanvasHeight;break;case 32:r=a.element.config.c108hmarker32xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker32ypos.value/100*a.fixedCanvasHeight;break;case 33:r=a.element.config.c108hmarker33xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker33ypos.value/100*a.fixedCanvasHeight;break;case 34:r=a.element.config.c108hmarker34xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker34ypos.value/100*a.fixedCanvasHeight;break;case 35:r=a.element.config.c108hmarker35xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker35ypos.value/100*a.fixedCanvasHeight;break;case 36:r=a.element.config.c108hmarker36xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker36ypos.value/100*a.fixedCanvasHeight;break;case 37:r=a.element.config.c108hmarker37xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker37ypos.value/100*a.fixedCanvasHeight;break;case 38:r=a.element.config.c108hmarker38xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker38ypos.value/100*a.fixedCanvasHeight;break;case 39:r=a.element.config.c108hmarker39xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker39ypos.value/100*a.fixedCanvasHeight;break;case 40:r=a.element.config.c108hmarker40xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker40ypos.value/100*a.fixedCanvasHeight;break;case 41:r=a.element.config.c108hmarker41xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker41ypos.value/100*a.fixedCanvasHeight;break;case 42:r=a.element.config.c108hmarker42xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker42ypos.value/100*a.fixedCanvasHeight;break;case 43:r=a.element.config.c108hmarker43xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker43ypos.value/100*a.fixedCanvasHeight;break;case 44:r=a.element.config.c108hmarker44xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker44ypos.value/100*a.fixedCanvasHeight;break;case 45:r=a.element.config.c108hmarker45xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker45ypos.value/100*a.fixedCanvasHeight;break;case 46:r=a.element.config.c108hmarker46xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker46ypos.value/100*a.fixedCanvasHeight;break;case 47:r=a.element.config.c108hmarker47xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker47ypos.value/100*a.fixedCanvasHeight;break;case 48:r=a.element.config.c108hmarker48xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker48ypos.value/100*a.fixedCanvasHeight;break;case 49:r=a.element.config.c108hmarker49xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker49ypos.value/100*a.fixedCanvasHeight;break;case 50:r=a.element.config.c108hmarker50xpos.value/100*a.fixedCanvasWidth,h=a.element.config.c108hmarker50ypos.value/100*a.fixedCanvasHeight}}))},setIndividualColor:function(e){switch(e){case 1:this.element.config.c108hmarkercolor01.value=this.allIndividualColors[e];break;case 2:this.element.config.c108hmarkercolor02.value=this.allIndividualColors[e];break;case 3:this.element.config.c108hmarkercolor03.value=this.allIndividualColors[e];break;case 4:this.element.config.c108hmarkercolor04.value=this.allIndividualColors[e];break;case 5:this.element.config.c108hmarkercolor05.value=this.allIndividualColors[e];break;case 6:this.element.config.c108hmarkercolor06.value=this.allIndividualColors[e];break;case 7:this.element.config.c108hmarkercolor07.value=this.allIndividualColors[e];break;case 8:this.element.config.c108hmarkercolor08.value=this.allIndividualColors[e];break;case 9:this.element.config.c108hmarkercolor09.value=this.allIndividualColors[e];break;case 10:this.element.config.c108hmarkercolor10.value=this.allIndividualColors[e];break;case 11:this.element.config.c108hmarkercolor11.value=this.allIndividualColors[e];break;case 12:this.element.config.c108hmarkercolor12.value=this.allIndividualColors[e];break;case 13:this.element.config.c108hmarkercolor13.value=this.allIndividualColors[e];break;case 14:this.element.config.c108hmarkercolor14.value=this.allIndividualColors[e];break;case 15:this.element.config.c108hmarkercolor15.value=this.allIndividualColors[e];break;case 16:this.element.config.c108hmarkercolor16.value=this.allIndividualColors[e];break;case 17:this.element.config.c108hmarkercolor17.value=this.allIndividualColors[e];break;case 18:this.element.config.c108hmarkercolor18.value=this.allIndividualColors[e];break;case 19:this.element.config.c108hmarkercolor19.value=this.allIndividualColors[e];break;case 20:this.element.config.c108hmarkercolor20.value=this.allIndividualColors[e];break;case 21:this.element.config.c108hmarkercolor21.value=this.allIndividualColors[e];break;case 22:this.element.config.c108hmarkercolor22.value=this.allIndividualColors[e];break;case 23:this.element.config.c108hmarkercolor23.value=this.allIndividualColors[e];break;case 24:this.element.config.c108hmarkercolor24.value=this.allIndividualColors[e];break;case 25:this.element.config.c108hmarkercolor25.value=this.allIndividualColors[e];break;case 26:this.element.config.c108hmarkercolor26.value=this.allIndividualColors[e];break;case 27:this.element.config.c108hmarkercolor27.value=this.allIndividualColors[e];break;case 28:this.element.config.c108hmarkercolor28.value=this.allIndividualColors[e];break;case 29:this.element.config.c108hmarkercolor29.value=this.allIndividualColors[e];break;case 30:this.element.config.c108hmarkercolor30.value=this.allIndividualColors[e];break;case 31:this.element.config.c108hmarkercolor31.value=this.allIndividualColors[e];break;case 32:this.element.config.c108hmarkercolor32.value=this.allIndividualColors[e];break;case 33:this.element.config.c108hmarkercolor33.value=this.allIndividualColors[e];break;case 34:this.element.config.c108hmarkercolor34.value=this.allIndividualColors[e];break;case 35:this.element.config.c108hmarkercolor35.value=this.allIndividualColors[e];break;case 36:this.element.config.c108hmarkercolor36.value=this.allIndividualColors[e];break;case 37:this.element.config.c108hmarkercolor37.value=this.allIndividualColors[e];break;case 38:this.element.config.c108hmarkercolor38.value=this.allIndividualColors[e];break;case 39:this.element.config.c108hmarkercolor39.value=this.allIndividualColors[e];break;case 40:this.element.config.c108hmarkercolor40.value=this.allIndividualColors[e];break;case 41:this.element.config.c108hmarkercolor41.value=this.allIndividualColors[e];break;case 42:this.element.config.c108hmarkercolor42.value=this.allIndividualColors[e];break;case 43:this.element.config.c108hmarkercolor43.value=this.allIndividualColors[e];break;case 44:this.element.config.c108hmarkercolor44.value=this.allIndividualColors[e];break;case 45:this.element.config.c108hmarkercolor45.value=this.allIndividualColors[e];break;case 46:this.element.config.c108hmarkercolor46.value=this.allIndividualColors[e];break;case 47:this.element.config.c108hmarkercolor47.value=this.allIndividualColors[e];break;case 48:this.element.config.c108hmarkercolor48.value=this.allIndividualColors[e];break;case 49:this.element.config.c108hmarkercolor49.value=this.allIndividualColors[e];break;case 50:this.element.config.c108hmarkercolor50.value=this.allIndividualColors[e]}},getHoverPos:function(e,t){return e<=50&&t<=50?"c108-hfi-open-bottom-right":e>=50&&t<=50?"c108-hfi-open-bottom-left":e<=50&&t>=50?"c108-hfi-open-top-right":"c108-hfi-open-top-left"},setHotspot:function(e,t,i,l,a){var c=e.target,n=c.getContext("2d"),s=e.target.getBoundingClientRect(),o=e.clientX-s.left,r=e.clientY-s.top;switch(i){case 1:this.element.config.c108hmarker01xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker01ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos01.value=this.getHoverPos(o,r);break;case 2:this.element.config.c108hmarker02xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker02ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos02.value=this.getHoverPos(o,r);break;case 3:this.element.config.c108hmarker03xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker03ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos03.value=this.getHoverPos(o,r);break;case 4:this.element.config.c108hmarker04xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker04ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos04.value=this.getHoverPos(o,r);break;case 5:this.element.config.c108hmarker05xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker05ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos05.value=this.getHoverPos(o,r);break;case 6:this.element.config.c108hmarker06xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker06ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos06.value=this.getHoverPos(o,r);break;case 7:this.element.config.c108hmarker07xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker07ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos07.value=this.getHoverPos(o,r);break;case 8:this.element.config.c108hmarker08xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker08ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos08.value=this.getHoverPos(o,r);break;case 9:this.element.config.c108hmarker09xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker09ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos09.value=this.getHoverPos(o,r);break;case 10:this.element.config.c108hmarker10xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker10ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos10.value=this.getHoverPos(o,r);break;case 11:this.element.config.c108hmarker11xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker11ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos11.value=this.getHoverPos(o,r);break;case 12:this.element.config.c108hmarker12xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker12ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos12.value=this.getHoverPos(o,r);break;case 13:this.element.config.c108hmarker13xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker13ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos13.value=this.getHoverPos(o,r);break;case 14:this.element.config.c108hmarker14xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker14ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos14.value=this.getHoverPos(o,r);break;case 15:this.element.config.c108hmarker15xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker15ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos15.value=this.getHoverPos(o,r);break;case 16:this.element.config.c108hmarker16xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker16ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos16.value=this.getHoverPos(o,r);break;case 17:this.element.config.c108hmarker17xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker17ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos17.value=this.getHoverPos(o,r);break;case 18:this.element.config.c108hmarker18xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker18ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos18.value=this.getHoverPos(o,r);break;case 19:this.element.config.c108hmarker19xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker19ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos19.value=this.getHoverPos(o,r);break;case 20:this.element.config.c108hmarker20xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker20ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos20.value=this.getHoverPos(o,r);break;case 21:this.element.config.c108hmarker21xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker21ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos21.value=this.getHoverPos(o,r);break;case 22:this.element.config.c108hmarker22xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker22ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos22.value=this.getHoverPos(o,r);break;case 23:this.element.config.c108hmarker23xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker23ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos23.value=this.getHoverPos(o,r);break;case 24:this.element.config.c108hmarker24xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker24ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos24.value=this.getHoverPos(o,r);break;case 25:this.element.config.c108hmarker25xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker25ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos25.value=this.getHoverPos(o,r);break;case 26:this.element.config.c108hmarker26xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker26ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos26.value=this.getHoverPos(o,r);break;case 27:this.element.config.c108hmarker27xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker27ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos27.value=this.getHoverPos(o,r);break;case 28:this.element.config.c108hmarker28xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker28ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos28.value=this.getHoverPos(o,r);break;case 29:this.element.config.c108hmarker29xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker29ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos29.value=this.getHoverPos(o,r);break;case 30:this.element.config.c108hmarker30xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker30ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos30.value=this.getHoverPos(o,r);break;case 31:this.element.config.c108hmarker31xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker31ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos31.value=this.getHoverPos(o,r);break;case 32:this.element.config.c108hmarker32xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker32ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos32.value=this.getHoverPos(o,r);break;case 33:this.element.config.c108hmarker33xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker33ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos33.value=this.getHoverPos(o,r);break;case 34:this.element.config.c108hmarker34xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker34ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos34.value=this.getHoverPos(o,r);break;case 35:this.element.config.c108hmarker35xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker35ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos35.value=this.getHoverPos(o,r);break;case 36:this.element.config.c108hmarker36xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker36ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos36.value=this.getHoverPos(o,r);break;case 37:this.element.config.c108hmarker37xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker37ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos37.value=this.getHoverPos(o,r);break;case 38:this.element.config.c108hmarker38xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker38ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos38.value=this.getHoverPos(o,r);break;case 39:this.element.config.c108hmarker39xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker39ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos39.value=this.getHoverPos(o,r);break;case 40:this.element.config.c108hmarker40xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker40ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos40.value=this.getHoverPos(o,r);break;case 41:this.element.config.c108hmarker41xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker41ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos41.value=this.getHoverPos(o,r);break;case 42:this.element.config.c108hmarker42xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker42ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos42.value=this.getHoverPos(o,r);break;case 43:this.element.config.c108hmarker43xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker43ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos43.value=this.getHoverPos(o,r);break;case 44:this.element.config.c108hmarker44xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker44ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos44.value=this.getHoverPos(o,r);break;case 45:this.element.config.c108hmarker45xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker45ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos45.value=this.getHoverPos(o,r);break;case 46:this.element.config.c108hmarker46xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker46ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos46.value=this.getHoverPos(o,r);break;case 47:this.element.config.c108hmarker47xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker47ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos47.value=this.getHoverPos(o,r);break;case 48:this.element.config.c108hmarker48xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker48ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos48.value=this.getHoverPos(o,r);break;case 49:this.element.config.c108hmarker49xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker49ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos49.value=this.getHoverPos(o,r);break;case 50:this.element.config.c108hmarker50xpos.value=Math.round(o/this.fixedCanvasWidth*100),this.element.config.c108hmarker50ypos.value=Math.round(r/this.fixedCanvasHeight*100),this.element.config.c108hhoverpos50.value=this.getHoverPos(o,r)}n.clearRect(0,0,c.width,c.height);var h=new Image,u=this.element.config.media.value;this.mediaRepository.get(u,Shopware.Context.api).then((function(e){h.src=e.url,h.onload=function(){n.drawImage(h,0,0,h.width,h.height,0,0,c.width,c.height);n.beginPath(),n.arc(o,r,10,0,2*Math.PI),n.fillStyle=t,l&&(n.fillStyle=a),n.fill(),n.lineWidth=3,n.strokeStyle="white",n.stroke()}}))},createdComponent:function(){this.initElementConfig("hotspots")},onImageUpload:function(e){var t,i=this;return(t=a().mark((function t(){var l,c;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return l=e.targetId,t.next=3,i.mediaRepository.get(l,Shopware.Context.api);case 3:c=t.sent,i.element.config.media.value=c.id,i.updateElementData(c),i.$emit("element-update",i.element);case 7:case"end":return t.stop()}}),t)})),function(){var e=this,i=arguments;return new Promise((function(l,a){var n=t.apply(e,i);function s(e){c(n,l,a,s,o,"next",e)}function o(e){c(n,l,a,s,o,"throw",e)}s(void 0)}))})()},onImageRemove:function(){this.element.config.media.value=null,this.updateElementData(),this.$emit("element-update",this.element)},onCloseModal:function(){this.mediaModalIsOpen=!1},onSelectionChanges:function(e){var t=e[0];this.element.config.media.value=t.id,this.updateElementData(t),this.$emit("element-update",this.element)},updateElementData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=null===e?null:e.id;this.element.data?(this.$set(this.element.data,"mediaId",t),this.$set(this.element.data,"media",e)):(this.$set(this.element,"data",{mediaId:t}),this.$set(this.element,"data",{media:e}))},onOpenMediaModal:function(){this.mediaModalIsOpen=!0}}});i("wzcS");Shopware.Component.register("sw-cms-el-preview-hotspots",{template:'{% block sw_cms_element_hotspots_preview %}\n\t<div class="sw-cms-el-preview-hotspots">\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t\t<span class="hotspots-preview-marker">\n\t\t\t<span>+</span>\n\t\t</span>\n\t</div>\n{% endblock %}\n'}),Shopware.Service("cmsService").registerCmsElement({name:"hotspots",label:"sw-cms.elements.hotspots.label",component:"sw-cms-el-hotspots",configComponent:"sw-cms-el-config-hotspots",previewComponent:"sw-cms-el-preview-hotspots",defaultConfig:{media:{source:"static",value:null,required:!0,entity:{name:"media"}},c108hnrhotspots:{source:"static",value:"2"},c108hhighlightcol:{source:"static",value:"#188EEE"},c108hdesign:{source:"static",value:"einfach"},c108hmarker01xpos:{source:"static",value:"20"},c108hmarker01ypos:{source:"static",value:"40"},c108htitle01:{source:"static",value:"Titel"},c108hdescr01:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink01bool:{source:"static",value:!0},c108hlink01internal:{source:"static",value:!1},c108hlink01url:{source:"static",value:"example.com"},c108hlink01title:{source:"static",value:"Mehr"},c108hdescr01charcounter:{source:"static",value:56},c108hmarker02xpos:{source:"static",value:"60"},c108hmarker02ypos:{source:"static",value:"30"},c108htitle02:{source:"static",value:"Titel"},c108hdescr02:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink02bool:{source:"static",value:!0},c108hlink02internal:{source:"static",value:!1},c108hlink02url:{source:"static",value:"example.com"},c108hlink02title:{source:"static",value:"Mehr"},c108hdescr02charcounter:{source:"static",value:56},c108hmarker03xpos:{source:"static",value:"80"},c108hmarker03ypos:{source:"static",value:"60"},c108htitle03:{source:"static",value:"Titel"},c108hdescr03:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink03bool:{source:"static",value:!0},c108hlink03internal:{source:"static",value:!1},c108hlink03url:{source:"static",value:"example.com"},c108hlink03title:{source:"static",value:"Mehr"},c108hdescr03charcounter:{source:"static",value:56},c108hmarker04xpos:{source:"static",value:"60"},c108hmarker04ypos:{source:"static",value:"50"},c108htitle04:{source:"static",value:"Titel"},c108hdescr04:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink04bool:{source:"static",value:!0},c108hlink04internal:{source:"static",value:!1},c108hlink04url:{source:"static",value:"example.com"},c108hlink04title:{source:"static",value:"Mehr"},c108hdescr04charcounter:{source:"static",value:56},c108hmarker05xpos:{source:"static",value:"80"},c108hmarker05ypos:{source:"static",value:"20"},c108htitle05:{source:"static",value:"Titel"},c108hdescr05:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink05bool:{source:"static",value:!0},c108hlink05internal:{source:"static",value:!1},c108hlink05url:{source:"static",value:"example.com"},c108hlink05title:{source:"static",value:"Mehr"},c108hdescr05charcounter:{source:"static",value:56},c108hmarker06xpos:{source:"static",value:"40"},c108hmarker06ypos:{source:"static",value:"70"},c108hmarker07xpos:{source:"static",value:"48"},c108hmarker07ypos:{source:"static",value:"78"},c108hmarker08xpos:{source:"static",value:"58"},c108hmarker08ypos:{source:"static",value:"89"},c108hmarker09xpos:{source:"static",value:"68"},c108hmarker09ypos:{source:"static",value:"49"},c108hmarker10xpos:{source:"static",value:"38"},c108hmarker10ypos:{source:"static",value:"29"},c108hmarker11xpos:{source:"static",value:"18"},c108hmarker11ypos:{source:"static",value:"89"},c108hmarker12xpos:{source:"static",value:"11"},c108hmarker12ypos:{source:"static",value:"26"},c108hmarker13xpos:{source:"static",value:"68"},c108hmarker13ypos:{source:"static",value:"19"},c108hmarker14xpos:{source:"static",value:"28"},c108hmarker14ypos:{source:"static",value:"72"},c108hmarker15xpos:{source:"static",value:"18"},c108hmarker15ypos:{source:"static",value:"9"},c108hmarker16xpos:{source:"static",value:"22"},c108hmarker16ypos:{source:"static",value:"24"},c108hmarker17xpos:{source:"static",value:"88"},c108hmarker17ypos:{source:"static",value:"33"},c108hmarker18xpos:{source:"static",value:"10"},c108hmarker18ypos:{source:"static",value:"90"},c108hmarker19xpos:{source:"static",value:"64"},c108hmarker19ypos:{source:"static",value:"92"},c108hmarker20xpos:{source:"static",value:"95"},c108hmarker20ypos:{source:"static",value:"12"},c108hmarker21xpos:{source:"static",value:"5"},c108hmarker21ypos:{source:"static",value:"77"},c108hmarker22xpos:{source:"static",value:"33"},c108hmarker22ypos:{source:"static",value:"44"},c108hmarker23xpos:{source:"static",value:"45"},c108hmarker23ypos:{source:"static",value:"8"},c108hmarker24xpos:{source:"static",value:"14"},c108hmarker24ypos:{source:"static",value:"51"},c108hmarker25xpos:{source:"static",value:"10"},c108hmarker25ypos:{source:"static",value:"8"},c108hmarker26xpos:{source:"static",value:"85"},c108hmarker26ypos:{source:"static",value:"90"},c108hmarker27xpos:{source:"static",value:"27"},c108hmarker27ypos:{source:"static",value:"32"},c108hmarker28xpos:{source:"static",value:"93"},c108hmarker28ypos:{source:"static",value:"61"},c108hmarker29xpos:{source:"static",value:"68"},c108hmarker29ypos:{source:"static",value:"79"},c108hmarker30xpos:{source:"static",value:"40"},c108hmarker30ypos:{source:"static",value:"89"},c108hmarker31xpos:{source:"static",value:"42"},c108hmarker31ypos:{source:"static",value:"61"},c108hmarker32xpos:{source:"static",value:"73"},c108hmarker32ypos:{source:"static",value:"18"},c108hmarker33xpos:{source:"static",value:"55"},c108hmarker33ypos:{source:"static",value:"82"},c108hmarker34xpos:{source:"static",value:"66"},c108hmarker34ypos:{source:"static",value:"20"},c108hmarker35xpos:{source:"static",value:"49"},c108hmarker35ypos:{source:"static",value:"78"},c108hmarker36xpos:{source:"static",value:"67"},c108hmarker36ypos:{source:"static",value:"26"},c108hmarker37xpos:{source:"static",value:"54"},c108hmarker37ypos:{source:"static",value:"87"},c108hmarker38xpos:{source:"static",value:"43"},c108hmarker38ypos:{source:"static",value:"62"},c108hmarker39xpos:{source:"static",value:"70"},c108hmarker39ypos:{source:"static",value:"15"},c108hmarker40xpos:{source:"static",value:"38"},c108hmarker40ypos:{source:"static",value:"59"},c108hmarker41xpos:{source:"static",value:"80"},c108hmarker41ypos:{source:"static",value:"11"},c108hmarker42xpos:{source:"static",value:"44"},c108hmarker42ypos:{source:"static",value:"75"},c108hmarker43xpos:{source:"static",value:"68"},c108hmarker43ypos:{source:"static",value:"28"},c108hmarker44xpos:{source:"static",value:"53"},c108hmarker44ypos:{source:"static",value:"83"},c108hmarker45xpos:{source:"static",value:"74"},c108hmarker45ypos:{source:"static",value:"16"},c108hmarker46xpos:{source:"static",value:"47"},c108hmarker46ypos:{source:"static",value:"79"},c108hmarker47xpos:{source:"static",value:"69"},c108hmarker47ypos:{source:"static",value:"24"},c108hmarker48xpos:{source:"static",value:"50"},c108hmarker48ypos:{source:"static",value:"86"},c108hmarker49xpos:{source:"static",value:"75"},c108hmarker49ypos:{source:"static",value:"13"},c108hmarker50xpos:{source:"static",value:"48"},c108hmarker50ypos:{source:"static",value:"70"},c108htitle06:{source:"static",value:"Titel"},c108hdescr06:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink06bool:{source:"static",value:!0},c108hlink06internal:{source:"static",value:!1},c108hlink06url:{source:"static",value:"example.com"},c108hlink06title:{source:"static",value:"Mehr"},c108hdescr06charcounter:{source:"static",value:56},c108htitle07:{source:"static",value:"Titel"},c108hdescr07:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink07bool:{source:"static",value:!0},c108hlink07internal:{source:"static",value:!1},c108hlink07url:{source:"static",value:"example.com"},c108hlink07title:{source:"static",value:"Mehr"},c108hdescr07charcounter:{source:"static",value:56},c108htitle08:{source:"static",value:"Titel"},c108hdescr08:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink08bool:{source:"static",value:!0},c108hlink08internal:{source:"static",value:!1},c108hlink08url:{source:"static",value:"example.com"},c108hlink08title:{source:"static",value:"Mehr"},c108hdescr08charcounter:{source:"static",value:56},c108htitle09:{source:"static",value:"Titel"},c108hdescr09:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink09bool:{source:"static",value:!0},c108hlink09internal:{source:"static",value:!1},c108hlink09url:{source:"static",value:"example.com"},c108hlink09title:{source:"static",value:"Mehr"},c108hdescr09charcounter:{source:"static",value:56},c108htitle10:{source:"static",value:"Titel"},c108hdescr10:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink10bool:{source:"static",value:!0},c108hlink10internal:{source:"static",value:!1},c108hlink10url:{source:"static",value:"example.com"},c108hlink10title:{source:"static",value:"Mehr"},c108hdescr10charcounter:{source:"static",value:56},c108htitle11:{source:"static",value:"Titel"},c108hdescr11:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink11bool:{source:"static",value:!0},c108hlink11internal:{source:"static",value:!1},c108hlink11url:{source:"static",value:"example.com"},c108hlink11title:{source:"static",value:"Mehr"},c108hdescr11charcounter:{source:"static",value:56},c108htitle12:{source:"static",value:"Titel"},c108hdescr12:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink12bool:{source:"static",value:!0},c108hlink12internal:{source:"static",value:!1},c108hlink12url:{source:"static",value:"example.com"},c108hlink12title:{source:"static",value:"Mehr"},c108hdescr12charcounter:{source:"static",value:56},c108htitle13:{source:"static",value:"Titel"},c108hdescr13:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink13bool:{source:"static",value:!0},c108hlink13internal:{source:"static",value:!1},c108hlink13url:{source:"static",value:"example.com"},c108hlink13title:{source:"static",value:"Mehr"},c108hdescr13charcounter:{source:"static",value:56},c108htitle14:{source:"static",value:"Titel"},c108hdescr14:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink14bool:{source:"static",value:!0},c108hlink14internal:{source:"static",value:!1},c108hlink14url:{source:"static",value:"example.com"},c108hlink14title:{source:"static",value:"Mehr"},c108hdescr14charcounter:{source:"static",value:56},c108htitle15:{source:"static",value:"Titel"},c108hdescr15:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink15bool:{source:"static",value:!0},c108hlink15internal:{source:"static",value:!1},c108hlink15url:{source:"static",value:"example.com"},c108hlink15title:{source:"static",value:"Mehr"},c108hdescr15charcounter:{source:"static",value:56},c108htitle16:{source:"static",value:"Titel"},c108hdescr16:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink16bool:{source:"static",value:!0},c108hlink16internal:{source:"static",value:!1},c108hlink16url:{source:"static",value:"example.com"},c108hlink16title:{source:"static",value:"Mehr"},c108hdescr16charcounter:{source:"static",value:56},c108htitle17:{source:"static",value:"Titel"},c108hdescr17:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink17bool:{source:"static",value:!0},c108hlink17internal:{source:"static",value:!1},c108hlink17url:{source:"static",value:"example.com"},c108hlink17title:{source:"static",value:"Mehr"},c108hdescr17charcounter:{source:"static",value:56},c108htitle18:{source:"static",value:"Titel"},c108hdescr18:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink18bool:{source:"static",value:!0},c108hlink18internal:{source:"static",value:!1},c108hlink18url:{source:"static",value:"example.com"},c108hlink18title:{source:"static",value:"Mehr"},c108hdescr18charcounter:{source:"static",value:56},c108htitle19:{source:"static",value:"Titel"},c108hdescr19:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink19bool:{source:"static",value:!0},c108hlink19internal:{source:"static",value:!1},c108hlink19url:{source:"static",value:"example.com"},c108hlink19title:{source:"static",value:"Mehr"},c108hdescr19charcounter:{source:"static",value:56},c108htitle20:{source:"static",value:"Titel"},c108hdescr20:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink20bool:{source:"static",value:!0},c108hlink20internal:{source:"static",value:!1},c108hlink20url:{source:"static",value:"example.com"},c108hlink20title:{source:"static",value:"Mehr"},c108hdescr20charcounter:{source:"static",value:56},c108htitle21:{source:"static",value:"Titel"},c108hdescr21:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink21bool:{source:"static",value:!0},c108hlink21internal:{source:"static",value:!1},c108hlink21url:{source:"static",value:"example.com"},c108hlink21title:{source:"static",value:"Mehr"},c108hdescr21charcounter:{source:"static",value:56},c108htitle22:{source:"static",value:"Titel"},c108hdescr22:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink22bool:{source:"static",value:!0},c108hlink22internal:{source:"static",value:!1},c108hlink22url:{source:"static",value:"example.com"},c108hlink22title:{source:"static",value:"Mehr"},c108hdescr22charcounter:{source:"static",value:56},c108htitle23:{source:"static",value:"Titel"},c108hdescr23:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink23bool:{source:"static",value:!0},c108hlink23internal:{source:"static",value:!1},c108hlink23url:{source:"static",value:"example.com"},c108hlink23title:{source:"static",value:"Mehr"},c108hdescr23charcounter:{source:"static",value:56},c108htitle24:{source:"static",value:"Titel"},c108hdescr24:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink24bool:{source:"static",value:!0},c108hlink24internal:{source:"static",value:!1},c108hlink24url:{source:"static",value:"example.com"},c108hlink24title:{source:"static",value:"Mehr"},c108hdescr24charcounter:{source:"static",value:56},c108htitle25:{source:"static",value:"Titel"},c108hdescr25:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink25bool:{source:"static",value:!0},c108hlink25internal:{source:"static",value:!1},c108hlink25url:{source:"static",value:"example.com"},c108hlink25title:{source:"static",value:"Mehr"},c108hdescr25charcounter:{source:"static",value:56},c108htitle26:{source:"static",value:"Titel"},c108hdescr26:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink26bool:{source:"static",value:!0},c108hlink26internal:{source:"static",value:!1},c108hlink26url:{source:"static",value:"example.com"},c108hlink26title:{source:"static",value:"Mehr"},c108hdescr26charcounter:{source:"static",value:56},c108htitle27:{source:"static",value:"Titel"},c108hdescr27:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink27bool:{source:"static",value:!0},c108hlink27internal:{source:"static",value:!1},c108hlink27url:{source:"static",value:"example.com"},c108hlink27title:{source:"static",value:"Mehr"},c108hdescr27charcounter:{source:"static",value:56},c108htitle28:{source:"static",value:"Titel"},c108hdescr28:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink28bool:{source:"static",value:!0},c108hlink28internal:{source:"static",value:!1},c108hlink28url:{source:"static",value:"example.com"},c108hlink28title:{source:"static",value:"Mehr"},c108hdescr28charcounter:{source:"static",value:56},c108htitle29:{source:"static",value:"Titel"},c108hdescr29:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink29bool:{source:"static",value:!0},c108hlink29internal:{source:"static",value:!1},c108hlink29url:{source:"static",value:"example.com"},c108hlink29title:{source:"static",value:"Mehr"},c108hdescr29charcounter:{source:"static",value:56},c108htitle30:{source:"static",value:"Titel"},c108hdescr30:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink30bool:{source:"static",value:!0},c108hlink30internal:{source:"static",value:!1},c108hlink30url:{source:"static",value:"example.com"},c108hlink30title:{source:"static",value:"Mehr"},c108hdescr30charcounter:{source:"static",value:56},c108htitle31:{source:"static",value:"Titel"},c108hdescr31:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink31bool:{source:"static",value:!0},c108hlink31internal:{source:"static",value:!1},c108hlink31url:{source:"static",value:"example.com"},c108hlink31title:{source:"static",value:"Mehr"},c108hdescr31charcounter:{source:"static",value:56},c108htitle32:{source:"static",value:"Titel"},c108hdescr32:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink32bool:{source:"static",value:!0},c108hlink32internal:{source:"static",value:!1},c108hlink32url:{source:"static",value:"example.com"},c108hlink32title:{source:"static",value:"Mehr"},c108hdescr32charcounter:{source:"static",value:56},c108htitle33:{source:"static",value:"Titel"},c108hdescr33:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink33bool:{source:"static",value:!0},c108hlink33internal:{source:"static",value:!1},c108hlink33url:{source:"static",value:"example.com"},c108hlink33title:{source:"static",value:"Mehr"},c108hdescr33charcounter:{source:"static",value:56},c108htitle34:{source:"static",value:"Titel"},c108hdescr34:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink34bool:{source:"static",value:!0},c108hlink34internal:{source:"static",value:!1},c108hlink34url:{source:"static",value:"example.com"},c108hlink34title:{source:"static",value:"Mehr"},c108hdescr34charcounter:{source:"static",value:56},c108htitle35:{source:"static",value:"Titel"},c108hdescr35:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink35bool:{source:"static",value:!0},c108hlink35internal:{source:"static",value:!1},c108hlink35url:{source:"static",value:"example.com"},c108hlink35title:{source:"static",value:"Mehr"},c108hdescr35charcounter:{source:"static",value:56},c108htitle36:{source:"static",value:"Titel"},c108hdescr36:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink36bool:{source:"static",value:!0},c108hlink36internal:{source:"static",value:!1},c108hlink36url:{source:"static",value:"example.com"},c108hlink36title:{source:"static",value:"Mehr"},c108hdescr36charcounter:{source:"static",value:56},c108htitle37:{source:"static",value:"Titel"},c108hdescr37:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink37bool:{source:"static",value:!0},c108hlink37internal:{source:"static",value:!1},c108hlink37url:{source:"static",value:"example.com"},c108hlink37title:{source:"static",value:"Mehr"},c108hdescr37charcounter:{source:"static",value:56},c108htitle38:{source:"static",value:"Titel"},c108hdescr38:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink38bool:{source:"static",value:!0},c108hlink38internal:{source:"static",value:!1},c108hlink38url:{source:"static",value:"example.com"},c108hlink38title:{source:"static",value:"Mehr"},c108hdescr38charcounter:{source:"static",value:56},c108htitle39:{source:"static",value:"Titel"},c108hdescr39:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink39bool:{source:"static",value:!0},c108hlink39internal:{source:"static",value:!1},c108hlink39url:{source:"static",value:"example.com"},c108hlink39title:{source:"static",value:"Mehr"},c108hdescr39charcounter:{source:"static",value:56},c108htitle40:{source:"static",value:"Titel"},c108hdescr40:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink40bool:{source:"static",value:!0},c108hlink40internal:{source:"static",value:!1},c108hlink40url:{source:"static",value:"example.com"},c108hlink40title:{source:"static",value:"Mehr"},c108hdescr40charcounter:{source:"static",value:56},c108htitle41:{source:"static",value:"Titel"},c108hdescr41:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink41bool:{source:"static",value:!0},c108hlink41internal:{source:"static",value:!1},c108hlink41url:{source:"static",value:"example.com"},c108hlink41title:{source:"static",value:"Mehr"},c108hdescr41charcounter:{source:"static",value:56},c108htitle42:{source:"static",value:"Titel"},c108hdescr42:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink42bool:{source:"static",value:!0},c108hlink42internal:{source:"static",value:!1},c108hlink42url:{source:"static",value:"example.com"},c108hlink42title:{source:"static",value:"Mehr"},c108hdescr42charcounter:{source:"static",value:56},c108htitle43:{source:"static",value:"Titel"},c108hdescr43:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink43bool:{source:"static",value:!0},c108hlink43internal:{source:"static",value:!1},c108hlink43url:{source:"static",value:"example.com"},c108hlink43title:{source:"static",value:"Mehr"},c108hdescr43charcounter:{source:"static",value:56},c108htitle44:{source:"static",value:"Titel"},c108hdescr44:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink44bool:{source:"static",value:!0},c108hlink44internal:{source:"static",value:!1},c108hlink44url:{source:"static",value:"example.com"},c108hlink44title:{source:"static",value:"Mehr"},c108hdescr44charcounter:{source:"static",value:56},c108htitle45:{source:"static",value:"Titel"},c108hdescr45:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink45bool:{source:"static",value:!0},c108hlink45internal:{source:"static",value:!1},c108hlink45url:{source:"static",value:"example.com"},c108hlink45title:{source:"static",value:"Mehr"},c108hdescr45charcounter:{source:"static",value:56},c108htitle46:{source:"static",value:"Titel"},c108hdescr46:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink46bool:{source:"static",value:!0},c108hlink46internal:{source:"static",value:!1},c108hlink46url:{source:"static",value:"example.com"},c108hlink46title:{source:"static",value:"Mehr"},c108hdescr46charcounter:{source:"static",value:56},c108htitle47:{source:"static",value:"Titel"},c108hdescr47:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink47bool:{source:"static",value:!0},c108hlink47internal:{source:"static",value:!1},c108hlink47url:{source:"static",value:"example.com"},c108hlink47title:{source:"static",value:"Mehr"},c108hdescr47charcounter:{source:"static",value:56},c108htitle48:{source:"static",value:"Titel"},c108hdescr48:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink48bool:{source:"static",value:!0},c108hlink48internal:{source:"static",value:!1},c108hlink48url:{source:"static",value:"example.com"},c108hlink48title:{source:"static",value:"Mehr"},c108hdescr48charcounter:{source:"static",value:56},c108htitle49:{source:"static",value:"Titel"},c108hdescr49:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink49bool:{source:"static",value:!0},c108hlink49internal:{source:"static",value:!1},c108hlink49url:{source:"static",value:"example.com"},c108hlink49title:{source:"static",value:"Mehr"},c108hdescr49charcounter:{source:"static",value:56},c108htitle50:{source:"static",value:"Titel"},c108hdescr50:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hlink50bool:{source:"static",value:!0},c108hlink50internal:{source:"static",value:!1},c108hlink50url:{source:"static",value:"example.com"},c108hlink50title:{source:"static",value:"Mehr"},c108hdescr50charcounter:{source:"static",value:56},c108hhoverpos01:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos02:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos03:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos04:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos05:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos06:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos07:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos08:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos09:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos10:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos11:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos12:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos13:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos14:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos15:{source:"static",value:"c108-hfi-open-bottom-right"},c108hhoverpos16:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos17:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos18:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos19:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos20:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos21:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos22:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos23:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos24:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos25:{source:"static",value:"c108-hfi-open-bottom-right"},c108hhoverpos26:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos27:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos28:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos29:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos30:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos31:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos32:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos33:{source:"static",value:"c108-hfi-open-top-left"},c108hhoverpos34:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos35:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos36:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos37:{source:"static",value:"c108-hfi-open-top-left"},c108hhoverpos38:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos39:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos40:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos41:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos42:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos43:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos44:{source:"static",value:"c108-hfi-open-top-left"},c108hhoverpos45:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos46:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos47:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos48:{source:"static",value:"c108-hfi-open-top-right"},c108hhoverpos49:{source:"static",value:"c108-hfi-open-bottom-left"},c108hhoverpos50:{source:"static",value:"c108-hfi-open-top-right"},c108htabletimagebool:{source:"static",value:!1},c108htabletimageurl:{source:"static",value:""},c108hmobileimagebool:{source:"static",value:!1},c108hmobileimageurl:{source:"static",value:""},c108hlazyloadingattributebool:{source:"static",value:!1},c108hlink01advancedbool:{source:"static",value:!1},c108hlink02advancedbool:{source:"static",value:!1},c108hlink03advancedbool:{source:"static",value:!1},c108hlink04advancedbool:{source:"static",value:!1},c108hlink05advancedbool:{source:"static",value:!1},c108hlink06advancedbool:{source:"static",value:!1},c108hlink07advancedbool:{source:"static",value:!1},c108hlink08advancedbool:{source:"static",value:!1},c108hlink09advancedbool:{source:"static",value:!1},c108hlink10advancedbool:{source:"static",value:!1},c108hlink11advancedbool:{source:"static",value:!1},c108hlink12advancedbool:{source:"static",value:!1},c108hlink13advancedbool:{source:"static",value:!1},c108hlink14advancedbool:{source:"static",value:!1},c108hlink15advancedbool:{source:"static",value:!1},c108hlink16advancedbool:{source:"static",value:!1},c108hlink17advancedbool:{source:"static",value:!1},c108hlink18advancedbool:{source:"static",value:!1},c108hlink19advancedbool:{source:"static",value:!1},c108hlink20advancedbool:{source:"static",value:!1},c108hlink21advancedbool:{source:"static",value:!1},c108hlink22advancedbool:{source:"static",value:!1},c108hlink23advancedbool:{source:"static",value:!1},c108hlink24advancedbool:{source:"static",value:!1},c108hlink25advancedbool:{source:"static",value:!1},c108hlink26advancedbool:{source:"static",value:!1},c108hlink27advancedbool:{source:"static",value:!1},c108hlink28advancedbool:{source:"static",value:!1},c108hlink29advancedbool:{source:"static",value:!1},c108hlink30advancedbool:{source:"static",value:!1},c108hlink31advancedbool:{source:"static",value:!1},c108hlink32advancedbool:{source:"static",value:!1},c108hlink33advancedbool:{source:"static",value:!1},c108hlink34advancedbool:{source:"static",value:!1},c108hlink35advancedbool:{source:"static",value:!1},c108hlink36advancedbool:{source:"static",value:!1},c108hlink37advancedbool:{source:"static",value:!1},c108hlink38advancedbool:{source:"static",value:!1},c108hlink39advancedbool:{source:"static",value:!1},c108hlink40advancedbool:{source:"static",value:!1},c108hlink41advancedbool:{source:"static",value:!1},c108hlink42advancedbool:{source:"static",value:!1},c108hlink43advancedbool:{source:"static",value:!1},c108hlink44advancedbool:{source:"static",value:!1},c108hlink45advancedbool:{source:"static",value:!1},c108hlink46advancedbool:{source:"static",value:!1},c108hlink47advancedbool:{source:"static",value:!1},c108hlink48advancedbool:{source:"static",value:!1},c108hlink49advancedbool:{source:"static",value:!1},c108hlink50advancedbool:{source:"static",value:!1},c108hbackendlabelsbool:{source:"static",value:!1},c108huseeditorbool:{source:"static",value:!1},c108husemaxheightbool:{source:"static",value:!1},c108huseindividualimagesizebool:{source:"static",value:!1},c108htexteditorcontent01:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent02:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent03:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent04:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent05:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent06:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent07:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent08:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent09:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent10:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent11:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent12:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent13:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent14:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent15:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent16:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent17:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent18:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent19:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent20:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent21:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent22:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent23:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent24:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent25:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent26:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent27:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent28:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent29:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent30:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent31:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent32:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent33:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent34:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent35:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent36:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent37:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent38:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent39:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent40:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent41:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent42:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent43:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent44:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent45:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent46:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent47:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent48:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent49:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108htexteditorcontent50:{source:"static",value:"Lorem ipsum dolor sit amet, consetetur sadipscing elitr."},c108hfrontendlabelsbool:{source:"static",value:!1},c108hindividualcolorsbool:{source:"static",value:!1},c108hmarkercolor01:{source:"static",value:"#188EEE"},c108hmarkercolor02:{source:"static",value:"#188EEE"},c108hmarkercolor03:{source:"static",value:"#188EEE"},c108hmarkercolor04:{source:"static",value:"#188EEE"},c108hmarkercolor05:{source:"static",value:"#188EEE"},c108hmarkercolor06:{source:"static",value:"#188EEE"},c108hmarkercolor07:{source:"static",value:"#188EEE"},c108hmarkercolor08:{source:"static",value:"#188EEE"},c108hmarkercolor09:{source:"static",value:"#188EEE"},c108hmarkercolor10:{source:"static",value:"#188EEE"},c108hmarkercolor11:{source:"static",value:"#188EEE"},c108hmarkercolor12:{source:"static",value:"#188EEE"},c108hmarkercolor13:{source:"static",value:"#188EEE"},c108hmarkercolor14:{source:"static",value:"#188EEE"},c108hmarkercolor15:{source:"static",value:"#188EEE"},c108hmarkercolor16:{source:"static",value:"#188EEE"},c108hmarkercolor17:{source:"static",value:"#188EEE"},c108hmarkercolor18:{source:"static",value:"#188EEE"},c108hmarkercolor19:{source:"static",value:"#188EEE"},c108hmarkercolor20:{source:"static",value:"#188EEE"},c108hmarkercolor21:{source:"static",value:"#188EEE"},c108hmarkercolor22:{source:"static",value:"#188EEE"},c108hmarkercolor23:{source:"static",value:"#188EEE"},c108hmarkercolor24:{source:"static",value:"#188EEE"},c108hmarkercolor25:{source:"static",value:"#188EEE"},c108hmarkercolor26:{source:"static",value:"#188EEE"},c108hmarkercolor27:{source:"static",value:"#188EEE"},c108hmarkercolor28:{source:"static",value:"#188EEE"},c108hmarkercolor29:{source:"static",value:"#188EEE"},c108hmarkercolor30:{source:"static",value:"#188EEE"},c108hmarkercolor31:{source:"static",value:"#188EEE"},c108hmarkercolor32:{source:"static",value:"#188EEE"},c108hmarkercolor33:{source:"static",value:"#188EEE"},c108hmarkercolor34:{source:"static",value:"#188EEE"},c108hmarkercolor35:{source:"static",value:"#188EEE"},c108hmarkercolor36:{source:"static",value:"#188EEE"},c108hmarkercolor37:{source:"static",value:"#188EEE"},c108hmarkercolor38:{source:"static",value:"#188EEE"},c108hmarkercolor39:{source:"static",value:"#188EEE"},c108hmarkercolor40:{source:"static",value:"#188EEE"},c108hmarkercolor41:{source:"static",value:"#188EEE"},c108hmarkercolor42:{source:"static",value:"#188EEE"},c108hmarkercolor43:{source:"static",value:"#188EEE"},c108hmarkercolor44:{source:"static",value:"#188EEE"},c108hmarkercolor45:{source:"static",value:"#188EEE"},c108hmarkercolor46:{source:"static",value:"#188EEE"},c108hmarkercolor47:{source:"static",value:"#188EEE"},c108hmarkercolor48:{source:"static",value:"#188EEE"},c108hmarkercolor49:{source:"static",value:"#188EEE"},c108hmarkercolor50:{source:"static",value:"#188EEE"},c108hmobileimageurladvancedlink:{source:"static",value:!1},c108htabletimageurladvancedlink:{source:"static",value:!1}}});var n=i("zjXs"),s=i("G8Ty");Shopware.Locale.extend("de-DE",n),Shopware.Locale.extend("en-GB",s)},klZQ:function(e,t,i){},pp4b:function(e,t,i){var l=i("AhVI");l.__esModule&&(l=l.default),"string"==typeof l&&(l=[[e.i,l,""]]),l.locals&&(e.exports=l.locals);(0,i("P8hj").default)("67928c3e",l,!0,{})},w0Vy:function(e,t,i){},wzcS:function(e,t,i){var l=i("w0Vy");l.__esModule&&(l=l.default),"string"==typeof l&&(l=[[e.i,l,""]]),l.locals&&(e.exports=l.locals);(0,i("P8hj").default)("39880738",l,!0,{})},zjXs:function(e){e.exports=JSON.parse('{"sw-cms":{"blocks":{"textImage":{"hotspots":{"label":"Bild mit Markierungen"}}},"elements":{"hotspots":{"label":"Bild mit Markierungen","config":{"tabnames":{"image":"Bild","settings":"Einstellungen","marker":"Marker"},"tabsettings":{"imagelabel":"Bild (empfohlene Auflösung: 1920 x 960 Pixel)","imagehelptext":"Für eine optimale Darstellung empfehlen wir eine Auflösung von 1920 x 960 Pixeln für das Bild. Bestenfalls sollten Sie das Bild zudem komprimiert hochladen.","markingnr":"Anzahl","markingsnumber":"Markierung | Markierungen","design":"Design","designopt1":"Einfach","designopt2":"Mit Nummerierung","designopt3":"Mit Pluszeichen","color":"Farbe","mobileimage":"Alternatives Bild für die Mobile-Darstellung","mobileimagehelptext":"Aktivieren Sie diesen Regler, wenn ein alternatives Bild auf Bildschirmbreiten < 768 Pixel genutzt werden soll (empfohlene Auflösung: 1000 x 500 Pixel).","mobileimageurl":"URL zum Bild für die Mobile-Darstellung","tabletimage":"Alternatives Bild für die Tablet-Darstellung","tabletimagehelptext":"Aktivieren Sie diesen Regler, wenn ein alternatives Bild auf Bildschirmbreiten > 767 Pixeln und < 1280 Pixeln genutzt werden soll (empfohlene Auflösung: 1400 x 700 Pixel).","tabletimageurl":"URL zum Bild für die Tablet-Darstellung","lazyloading":"Lazy Loading Attribut zum Bild hinzufügen","lazyloadinghelptext":"Aktivieren Sie den Regler, wenn das Loading Lazy Attribut zum Bild hinzugefügt werden soll. Bitte berücksichtigen Sie hierbei den aktuellen Browser-Support für das Attribut. Zudem kann das Lazy Loading-Attribut je nach Anwendungsfall zu Performance Issues führen.","backendlabels":"Labels in Vorschau","backendlabelshelptext":"Aktivieren Sie diesen Regler, wenn zusätzliche Labels für die einfachere Zurodnung der Marker in der Vorschau im Backend angezeigt werden sollen.","frontendlabels":"Titel immer anzeigen","frontendlabelshelptext":"Aktivieren Sie diesen Regler, wenn die Titel der Marker immer angezeigt werden sollen.","individualcolors":"Individuelle Farben","individualcolorshelptext":"Aktivieren Sie diesen Regler, wenn für die Marker verschiedene Farben genutzt werden sollen.","useeditor":"Texteditor für Inhalte nutzen","useeditorhelptext":"Aktivieren Sie diesen Regler, wenn Sie einen Texteditor für das Setzen der Inhalte für die Marker nutzen möchten.","usemaxheight":"Maximale Höhe und Y-Scroll","usemaxheighthelptext":"Aktivieren Sie diesen Regler, wenn Sie eine maximale Höhe sowie Y-Scroll für die Inhalte aktivieren möchten.","useindividualimagesize":"Individuelle Bildgröße berücksichtigen","useindividualimagesizehelptext":"Aktivieren Sie diesen Regler, wenn die individuelle Größe des Bildes (Breite und Höhe) berücksichtigt werden soll."},"tabmarker":{"position":"Position","positionhelptext":"Klicken Sie auf das Bild um die Position der Markierung festzulegen. Das gewünschte Bild sollten Sie zuvor in den Einstellungen setzen.","title":"Titel","description":"Beschreibung","link":"Link","url":"URL","text":"Text","linkinternal":"Interner Link","linkadvanced":"Advanced URL Feld","content":"Inhalt","frontendlabel":"Label"}}}}}}')}});
//# sourceMappingURL=c108-hotspots.js.map