{% block element_hotspots %}
{% set id = element.id %}
{% set allMarkers = element.data.allMarkers %}
{% set c108hdesign = element.config.c108hdesign.value %}
{% set c108hnrhotspots = element.config.c108hnrhotspots.value %}
{% set currMediaId = element.config.media.value %}
{% set currMediaCollection = searchMedia([currMediaId], context.context) %}
{% set currMediaObject = currMediaCollection.get(currMediaId) %}
{% set currImgPath = currMediaObject.url %}
{% set currImgAlt = currMediaObject.translated.alt %}
{% set currImgTitle = currMediaObject.translated.title  %}
{% set c108hhighlightcol = element.config.c108hhighlightcol.value %}
{% set c108hmobileimagebool = element.config.c108hmobileimagebool.value|default(false) %}
{% set c108htabletimagebool = element.config.c108htabletimagebool.value|default(false) %}
{% set c108hmobileimageurl = element.config.c108hmobileimageurl.value|default('') %}
{% set c108htabletimageurl = element.config.c108htabletimageurl.value|default('') %}
{% set c108hlazyloadingattributebool = element.config.c108hlazyloadingattributebool.value|default(false) %}
{% set c108huseeditorbool = element.config.c108huseeditorbool.value|default(false) %}
{% set c108husemaxheightbool = element.config.c108husemaxheightbool.value|default(false) %}
{% set c108hfrontendlabelsbool = element.config.c108hfrontendlabelsbool.value|default(false) %}
{% set c108hindividualcolorsbool = element.config.c108hindividualcolorsbool.value|default(false) %}

{% if c108hfrontendlabelsbool %}
<style>
div[data-cms-element-id="{{ id }}"] .hfi-frontend-label {
  background-color: rgba(255, 255, 355, 0.85);
  width: 120px;
  display: inline-block;
  text-align: center;
  font-size: 11px;
  padding: 4px;
  font-weight: 600;
  box-shadow: 0 4px 4px rgb(0 0 0 / 30%);
  pointer-events: none;
  position: absolute;
  left: -40px;
  right: 0;
  top: 50px;
  word-break: break-all;
}

div[data-cms-element-id="{{ id }}"] .einfach .hfi-frontend-label {
  left: -50px;
  top: 30px;
}

@media screen and (max-width: 767px) {
  div[data-cms-element-id="{{ id }}"] .hfi-frontend-label {
    display:none;
  }
}
</style>
{% endif %}

<div id="hfi-wrapper-{{ id }}" data-cms-element-id="{{ id }}" class="c108-hfi-wrapper {{ c108hdesign }}">
<div class="c108-hfi-inner">

{# image #}
<picture>
<source media="(min-width: 1280px)" srcset="{{ currImgPath }}">
{% if c108htabletimagebool %}
<source media="(min-width: 768px) and (max-width: 1279px)" srcset="{{ c108htabletimageurl }}">
{% endif %}
{% if c108hmobileimagebool %}
<source media="(max-width: 767px)" srcset="{{ c108hmobileimageurl }}">
{% endif %}
<img class="c108-hfi-bgimg" src="{{ currImgPath }}" alt={% if currImgAlt %}"{{ currImgAlt }}"{% else %}"Animated markings image"{% endif %} {% if currImgTitle %}title="{{ currImgTitle }}"{% else %}title="Animated markings image"{% endif %}{% if c108hlazyloadingattributebool %} loading="lazy"{% endif %}>
</picture>

{# marker #}
{% for marker in allMarkers %}
<div id="{{ id }}-c108-hfi-element-{{ loop.index }}" style="top: {{ marker.ypos.value }}%; left: {{ marker.xpos.value }}%;" class="c108-hfi-element {{ marker.hoverpos.value }}">
<div class="c108-hfi-element-inner" style="position:relative;">
<div data-element-id="{{ id }}" data-explanation-state="closed" class="c108-hfi-marker c108-hfi-marker-{{ id }}" onmouseover="showExplanation('{{ id }}', '{{ loop.index }}')" {% if c108hindividualcolorsbool and marker.color.value %}style="background-color:{{ marker.color.value }}"{% else %}style="background-color:{{ c108hhighlightcol }}"{% endif %}>
{% if c108hfrontendlabelsbool %}<span class="hfi-frontend-label" data-label-state="visible">{{ marker.title.value|u.truncate(50, '...') }}</span>{% endif %}
<span class="hfi-num">{{ loop.index }}</span>
<span class="hfi-plus">+</span>
</div>

{# desktop #}
<div id="{{ id }}-explanation-{{ loop.index }}" class="c108-hfi-explanation c108-hfi-explanation-d-{{ id }} desktop{% if c108husemaxheightbool %} max-height-enabled{% endif %}">
<span class="c108-hfi-explanation-close" onclick="closeExplanation()">×</span>
{% if c108huseeditorbool %}
<div>{% autoescape %}{{ marker.texteditorcontent.value|default('Lorem ipsum dolor sit amet, consetetur sadipscing elitr.')|raw }}{% endautoescape %}</div>
{% else %}
<span class="expl-title">{{ marker.title.value }}</span>
<p>{{ marker.description.value }}</p>
{% if marker.linkbool.value %}
{% if marker.linkinternal.value == true %}
<a style="color:{{ c108hhighlightcol }};" href="{{ marker.linkurl.value }}" title="{{ marker.linktitle.value }}">{{ marker.linktitle.value }}</a>
{% else %}
<a style="color:{{ c108hhighlightcol }};" href="{{ marker.linkurl.value }}" rel="noopener" target="_blank" title="{{ marker.linktitle.value }}">{{marker.linktitle.value }}</a>
{% endif %}
{% endif %}
{% endif %}
</div>
</div>
</div>
{% endfor %}

{# mobile explanation box #}
<div class="hfi-mobile-explanation-box">
{% for marker in allMarkers %}
<div id="explanation-mobile-{{ loop.index }}-{{ id }}" class="c108-hfi-explanation c108-hfi-explanation-m-{{ id }} mobile{% if c108husemaxheightbool %} max-height-enabled{% endif %}">
<span class="c108-hfi-explanation-close" onclick="closeExplanation()">×</span>
{% if c108huseeditorbool %}
<div>{% autoescape %}{{ marker.texteditorcontent.value|default('Lorem ipsum dolor sit amet, consetetur sadipscing elitr.')|raw }}{% endautoescape %}</div>
{% else %}
<span class="expl-title">{{ marker.title.value }}</span>
<p>{{ marker.description.value }}</p>
{% if marker.linkbool.value %}
{% if marker.linkinternal.value == true %}
<a style="color:{{ c108hhighlightcol }};" href="{{ marker.linkurl.value }}" title="{{ marker.linktitle.value }}">{{ marker.linktitle.value }}</a>
{% else %}
<a style="color:{{ c108hhighlightcol }};" href="{{ marker.linkurl.value }}" rel="noopener" target="_blank" title="{{ marker.linktitle.value }}">{{ marker.linktitle.value }}</a>
{% endif %}
{% endif %}
{% endif %}
</div>
{% endfor %}
</div>

</div>
</div>

<script>
function showExplanation(elementid, markernr){
  var currentHotspotsElement = document.getElementById("hfi-wrapper-" + elementid);
  var openExplanations = currentHotspotsElement.querySelectorAll(`[data-explanation-state="opened"]`);
  var hiddenFrontendLabels = currentHotspotsElement.querySelectorAll(`[data-label-state="hidden"]`);

  // hide opened explanations
  for (var i = 0; i < openExplanations.length; i++) {
    var currExplanation = openExplanations[i];
    if (currExplanation !== '' && currExplanation){
      currExplanation.setAttribute("data-explanation-state", "closed");
      currExplanation.style.opacity = "0";
      currExplanation.style.zIndex = "0";
      currExplanation.style.pointerEvents = "none";
      currExplanation.parentElement.style.zIndex = "1";
      currExplanation.parentElement.parentElement.style.zIndex = "1";
    }
  }

  // show hidden frontend labels
  for (var k = 0; k < hiddenFrontendLabels.length; k++) {
    var currLabel = hiddenFrontendLabels[k];
    if (currLabel !== '' && currLabel){
      currLabel.style.visibility = "visible";
      currLabel.setAttribute("data-label-state", "visible");
    }
  }

  // show explanation
  var explanationDesktop = event.target.nextElementSibling;
  var hotspotFrontendLabel = event.target.firstElementChild;

  // hide label
  if(hotspotFrontendLabel && hotspotFrontendLabel.classList.contains("hfi-frontend-label")){
    hotspotFrontendLabel.style.visibility = "hidden";
    hotspotFrontendLabel.setAttribute("data-label-state", "hidden");
  }

  explanationDesktop.setAttribute("data-explanation-state", "opened");
  explanationDesktop.style.opacity = "1";
  explanationDesktop.style.zIndex = "1000";
  explanationDesktop.style.pointerEvents = "visible";
  explanationDesktop.parentElement.style.zIndex = "1000";
  explanationDesktop.parentElement.parentElement.style.zIndex = "1000";

  var explanationMobile = currentHotspotsElement.querySelector("#explanation-mobile-" + markernr + "-" + elementid);
  explanationMobile.setAttribute("data-explanation-state", "opened");
  explanationMobile.style.opacity = "1";
  explanationMobile.style.zIndex = "1000";
  explanationMobile.style.pointerEvents = "visible";
  explanationMobile.parentElement.style.zIndex = "1000";
}

function closeExplanation() {
  var explanation = event.target.parentElement;
  
  if(event.target.parentElement.previousElementSibling && event.target.parentElement.previousElementSibling.firstElementChild){
    var hotspotFrontendLabel = event.target.parentElement.previousElementSibling.firstElementChild;
  }
  
  if(hotspotFrontendLabel && hotspotFrontendLabel.classList.contains("hfi-frontend-label")){
    hotspotFrontendLabel.style.visibility = "visible";
    hotspotFrontendLabel.setAttribute("data-label-state", "visible");
  }

  explanation.style.opacity = "0";
  explanation.style.zIndex = "0";
  explanation.style.pointerEvents = "none";
  explanation.parentElement.style.zIndex = "1";
  explanation.parentElement.parentElement.style.zIndex = "1";
  explanation.setAttribute("data-explanation-state", "closed");
}
</script>

{% endblock %}
