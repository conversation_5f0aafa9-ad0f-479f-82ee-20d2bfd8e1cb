{"sw-cms": {"blocks": {"textImage": {"hotspots": {"label": "Image with markers"}}}, "elements": {"hotspots": {"label": "Image with markers", "config": {"tabnames": {"image": "Image", "settings": "Settings", "marker": "<PERSON><PERSON>"}, "tabsettings": {"imagelabel": "Image (recommended resolution: 1920 x 960 pixels)", "imagehelptext": "For an optimal representation, we recommend a resolution of 1920 x 960 pixels of the image. Preferably, you should also upload the image compressed.", "markingnr": "Number", "markingsnumber": "marker | markers", "markingsnr1": "1 marker", "design": "Design", "designopt1": "Simple", "designopt2": "With numbering", "designopt3": "With plus sign", "color": "Color", "mobileimage": "Alternative image for mobile", "mobileimagehelptext": "Enable this switch if an alternative image should be used for screen widths < 768 pixels (recommended resolution: 1000 x 500 pixels).", "mobileimageurl": "URL for mobile image", "tabletimage": "Alternative image for tablet", "tabletimagehelptext": "Enable this switch if an alternative image should be used for screen widths > 767 pixels and < 1280 pixels (recommended resolution: 1400 x 700 pixels).", "tabletimageurl": "URL for tablet image", "lazyloading": "Add the lazy loading attribute to the image", "lazyloadinghelptext": "Enable this switch if the lazy loading attribute should be added to the image. Please take into account the current browser support for this attribute. Also note that in some situations the lazy loading attribute may lead to performance issues.", "backendlabels": "Labels in preview", "backendlabelshelptext": "Enable this switch if additional labels should be displayed in the preview in the backend for an easier assignment of the markers.", "frontendlabels": "Always show titles", "frontendlabelshelptext": "Enable this switch if the marker titles should always be displayed.", "individualcolors": "Individual colors", "individualcolorshelptext": "Enable this switch if different colors should be used for the markers.", "useeditor": "Use text editor for the content", "useeditorhelptext": "Enable this switch if you want to use a text editor to set the content for the markers.", "usemaxheight": "Maximum height and y-scroll", "usemaxheighthelptext": "Enable this switch if you want to activate maximum height and y-scroll for the content.", "useindividualimagesize": "Consider individual image size", "useindividualimagesizehelptext": "Enable this switch if the individual image size (width and height) should be considered."}, "tabmarker": {"position": "Position", "positionhelptext": "Click on the image to determine the position of the marking. You should first set the desired image in the settings.", "title": "Title", "description": "Description", "link": "Link", "url": "URL", "text": "Text", "linkinternal": "Internal link", "linkadvanced": "Advanced URL field", "content": "Content", "frontendlabel": "Label"}}}}}}