<?php declare(strict_types=1);

namespace BsCodeScanner\Controller\Api;

use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Shopware\Core\Content\Product\SalesChannel\ProductAvailableFilter;
use Shopware\Core\Content\Product\SalesChannel\SalesChannelProductEntity;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Shopware\Storefront\Controller\StorefrontController;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ProductLookupController extends StorefrontController
{
    public const DEFAULT_CURRENCYID = 'b7d2554b0ce847cd82f3ac9bd1c0dfca';

    public function __construct(
        private readonly SalesChannelRepository $salesChannelProductRepository
    ) {
    }

    #[Route(path: '/bs-code-scanner/api/product-lookup', name: 'frontend.bs_code_scanner.product_lookup', defaults: ['XmlHttpRequest' => true], methods: ['POST'])]
    public function lookupProductStorefront(Request $request, SalesChannelContext $salesChannelContext): JsonResponse
    {
        // Get EAN from JSON body
        $data = json_decode($request->getContent(), true);
        $ean = $data['ean'] ?? ltrim($request->get('ean'),0);

        if (empty($ean)) {
            return new JsonResponse([
                'success' => false,
                'message' => 'EAN code is required'
            ], 400);
        }

        try {
            $criteria = new Criteria();
            $criteria->addFilter(new ContainsFilter('ean', $ean));
            $criteria->addFilter(new ProductAvailableFilter($salesChannelContext->getSalesChannel()->getId()));

            $criteria->addAssociation('calculatedPrices');
            $criteria->setLimit(1);

            $product = $this->salesChannelProductRepository->search($criteria, $salesChannelContext)->first();

            if (!$product instanceof ProductEntity) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Product not found or not available'
                ], 404);
            }

            $coverUrl = null;
            if ($product->getCover() && $product->getCover()->getMedia()) {
                $coverUrl = $product->getCover()->getMedia()->getUrl();
            }

            $productData = [
                'name' => $product->getTranslated()['name'] ?? $product->getName(),
                'productNumber' => $product->getProductNumber(),
                'ean' => $product->getEan(),
                'price' => null,
                'stock' => $product->getAvailableStock(),
                'coverUrl' => $coverUrl,
                'url' => $coverUrl
            ];

            $productData['price'] = $this->getPrice($product, $salesChannelContext);

            return new JsonResponse([
                'success' => true,
                'product' => $productData
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while searching for the product: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getPrice(SalesChannelProductEntity $product, SalesChannelContext $salesChannelContext)
    {
        /** @var ?CheapestPrice $cheapestPrice */
        $cheapestPrice = $product->getCheapestPrice();

        $currencyId = $salesChannelContext->getCurrencyId();
        if(isset($cheapestPrice->getPrice()->getElements()[$currencyId])){
            $netPrice = $cheapestPrice?->getPrice()?->getElements()[$currencyId]?->getNet();
            $grossPrice = $cheapestPrice?->getPrice()?->getElements()[$currencyId]?->getGross();
        }
        
        $factor = $salesChannelContext->getCurrency()->getFactor();
        $defaultCurrencyId = self::DEFAULT_CURRENCYID;

        if(empty($netPrice) || empty($grossPrice)) {
            $net = $cheapestPrice->getPrice()?->getElements()[$defaultCurrencyId]?->getNet();
            $netPrice = $net * $factor;

            $gross = $cheapestPrice->getPrice()?->getElements()[$defaultCurrencyId]?->getGross();
            $grossPrice = $gross * $factor;
        }


        $netPrice = ($netPrice)? round($netPrice): null;
        $grossPrice = ($grossPrice)? round($grossPrice): null;

        return [
                'net' => $netPrice,
                'gross' => $grossPrice,
                'currency' => $salesChannelContext->getCurrency()->getSymbol()
            ];

    }
}
