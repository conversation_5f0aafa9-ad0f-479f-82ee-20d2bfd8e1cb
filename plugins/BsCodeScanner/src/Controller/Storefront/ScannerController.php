<?php declare(strict_types=1);

namespace BsCodeScanner\Controller\Storefront;

use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ScannerController extends StorefrontController
{
    private SystemConfigService $systemConfigService;

    public function __construct(SystemConfigService $systemConfigService)
    {
        $this->systemConfigService = $systemConfigService;
    }

    #[Route(path: '/scan', name: 'frontend.bs_code_scanner.scanner', methods: ['GET'])]
    public function scanner(Request $request, SalesChannelContext $context): Response
    {
        // Get configuration values
        $isActive = $this->systemConfigService->get('BsCodeScanner.config.active', $context->getSalesChannel()->getId());
        $enableCameraScanner = $this->systemConfigService->get('BsCodeScanner.config.enableCameraScanner', $context->getSalesChannel()->getId());
        $showProductImage = $this->systemConfigService->get('BsCodeScanner.config.showProductImage', $context->getSalesChannel()->getId());
        $showStock = $this->systemConfigService->get('BsCodeScanner.config.showStock', $context->getSalesChannel()->getId());
        $showPrice = $this->systemConfigService->get('BsCodeScanner.config.showPrice', $context->getSalesChannel()->getId());
        $showDescription = $this->systemConfigService->get('BsCodeScanner.config.showDescription', $context->getSalesChannel()->getId());
        
        if (!$isActive) {
            return $this->redirectToRoute('frontend.home.page');
        }

        return $this->renderStorefront('@BsCodeScanner/storefront/page/scanner/index.html.twig', [
            'config' => [
                'active' => $isActive,
                'enableCameraScanner' => $enableCameraScanner,
                'showProductImage' => $showProductImage,
                'showStock' => $showStock,
                'showPrice' => $showPrice,
                'showDescription' => $showDescription
            ]
        ]);
    }
}
