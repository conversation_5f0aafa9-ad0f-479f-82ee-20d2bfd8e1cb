
$sw-color-text: #000000;
$sw-color-gray-200: #e9ecef;
$sw-color-danger-light: #f8d7da;
$sw-color-danger: #dc3545;
$sw-color-danger-text: #721c24;
$sw-color-warning-light: #fff3cd;
$sw-color-warning: #ffc107;
$sw-color-warning-text: #856404;
$sw-color-success-light: #d4edda;
$sw-pc-label-color: #80868c;

.scanner-video-container {
    .card-body {
        position: relative;
        overflow: hidden;
    }
    
    video {
        border-radius: 0.375rem;
    }
}

@keyframes scannerLine {
    0% {
        top: 0;
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        top: 100%;
        opacity: 1;
    }
}

#productName {
    font-size: 1.2rem;
    font-weight: 600;
}
#productPrice {
    font-size: 1.6rem;
    font-weight: 600;
}
.pc-label {
    color: $sw-pc-label-color;
}
.product-result {
    .product-image img {
        border: 1px solid $sw-border-color;
        background: #FFFFFF;
        padding: 10px;
    }
}

.loading-indicator {
    .spinner-border {
        width: 3rem;
        height: 3rem;
    }
}

.scanner-controls {
    .btn {
        font-weight: 600;
        border-radius: 0.5rem;
        
        i {
            font-size: 1.1em;
        }
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .scanner-header {
        .container {
            padding: 1rem;
        }
        
        h1 {
            font-size: 1.5rem;
        }
    }
    
    .product-result {
        .product-image {
            margin-bottom: 1rem;
        }
    }
}

.scanner-container {
    background-color: $sw-color-brand-primary;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-width: 800px;
    margin: 0 auto;
}
#reader {
    width: 100%;
    height: 300px;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: $sw-background-color;
    margin-bottom: 1.5rem;
}
.product-info-card {
    background-color: #FFFFFF;
    padding: 1rem;
}
.product-info-card p {
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    color: $sw-color-text;
}
.product-info-card strong {
    color: $sw-color-text;
}
.message-box {
    background-color: $sw-color-warning-light;
    border: 1px solid $sw-color-warning;
    color: $sw-color-warning-text;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}
.message-box.error {
    background-color: $sw-color-danger-light;
    border-color: $sw-color-danger;
    color: $sw-color-danger-text;
}
.d-none {
    display: none !important;
}
.btn-custom-primary {
    background-color: $sw-color-brand-primary;
    border-color: $sw-color-brand-primary;
    color: #FFFFFF;
    transition: all 0.2s ease-in-out;
}
.btn-custom-primary:hover {
    background-color: darken($sw-color-brand-primary, 10%);
    border-color: darken($sw-color-brand-primary, 10%);
    color: #FFFFFF;
    transform: translateY(-1px);
}
.btn-custom-secondary {
    background-color: $sw-color-brand-secondary;
    border-color: $sw-color-brand-secondary;
    color: #FFFFFF;
    transition: all 0.2s ease-in-out;
}
.btn-custom-secondary:hover {
    background-color: darken($sw-color-brand-secondary, 10%);
    border-color: darken($sw-color-brand-secondary, 10%);
    color: #FFFFFF;
    transform: translateY(-1px);
}
.btn:active {
    transform: translateY(0);
}

.scanner-view {
    position: relative;
    background: #000;
    height: 60vh;
    min-height: 400px;
}
.scanner-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}
#reader {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
    video {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover;
    }
}
.scanner-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #6c757d;
    z-index: 5;
    i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    p {
        font-size: 1.1rem;
        margin: 0;
        opacity: 0.7;
    }
}
.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    z-index: 50;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease-out;
    @keyframes slideUp {
        from {
            transform: translateY(100%);
        }
        to {
            transform: translateY(0);
        }
    }
}
.product-info-card {
    background: #fff;
    padding: 1.5rem;
    h2 {
        color: $sw-color-success;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.3rem;
        &:before {
            content: '\2713';
            background: $sw-color-success;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }
    }
}
.message-box {
    position: absolute;
    bottom: 80px;
    left: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 0.75rem;
    padding: 1rem;
    font-size: 0.9rem;
    text-align: center;
    z-index: 25;
    backdrop-filter: blur(10px);
    &.error {
        background: rgba(220, 53, 69, 0.9);
    }
}
.scan-again-btn {
    background: $sw-color-brand-primary;
    color: #FFFFFF;
    border: none;
    border-radius: 0;
    padding: 1.25rem;
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
    margin-top: auto;
    &:hover, &:active {
        background: darken($sw-color-brand-primary, 10%);
        color: #FFFFFF;
    }
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}
.scanner-disabled {
    background: #fff;
    border-radius: 1rem;
    padding: 3rem 2rem;
    margin: 2rem;
    text-align: center;
    .btn {
        min-width: 150px;
    }
}

/* --- Loading Spinner --- */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255,255,255,0.85);
    z-index: 100;
}
.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid $sw-color-gray-200;
    border-top: 4px solid $sw-color-success;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.scanner-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.65);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInOverlay 0.25s ease;
}
@keyframes fadeInOverlay {
  from { opacity: 0; }
  to { opacity: 1; }
}
.scanner-overlay-card {
  background: #fff;
  border-radius: 0;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  padding: 2rem 1.5rem;
  max-width: 600px;
  width: 100%;
  position: relative;
  animation: fadeInCard 0.3s cubic-bezier(.4,0,.2,1);
}
@keyframes fadeInCard {
  from { transform: translateY(40px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
