!function(e){var r={};function n(t){if(r[t])return r[t].exports;var o=r[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=r,n.d=function(e,r,t){n.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:t})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,r){if(1&r&&(e=n(e)),8&r)return e;if(4&r&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(n.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&r&&"string"!=typeof e)for(var o in e)n.d(t,o,function(r){return e[r]}.bind(null,o));return t},n.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(r,"a",r),r},n.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},n.p=(window.__sw__.assetPath + '/bundles/bscodescanner/'),n(n.s="69Mf")}({"69Mf":function(e,r,n){"use strict";n.r(r);Shopware.Component.register("sw-cms-el-barcode-scanner",{template:'<div class="sw-cms-el-barcode-scanner">\n    <p>Barcode Scanner will be rendered here on the storefront.</p>\n</div> '});n("kPok");Shopware.Component.register("sw-cms-el-preview-barcode-scanner",{template:'<div class="sw-cms-el-preview-barcode-scanner">\n    <div style="text-align:center; padding: 1rem;">\n        <i class="fas fa-barcode" style="font-size: 2rem;"></i>\n        <p>Barcode Scanner</p>\n    </div>\n</div> '}),Shopware.Service("cmsService").registerCmsElement({name:"barcode-scanner",label:"Barcode Scanner",component:"sw-cms-el-barcode-scanner",previewComponent:"sw-cms-el-preview-barcode-scanner",defaultConfig:{}})},"JE/Z":function(e,r,n){},P8hj:function(e,r,n){"use strict";function t(e,r){for(var n=[],t={},o=0;o<r.length;o++){var a=r[o],s=a[0],i={id:e+":"+o,css:a[1],media:a[2],sourceMap:a[3]};t[s]?t[s].parts.push(i):n.push(t[s]={id:s,parts:[i]})}return n}n.r(r),n.d(r,"default",(function(){return v}));var o="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var a={},s=o&&(document.head||document.getElementsByTagName("head")[0]),i=null,c=0,d=!1,l=function(){},u=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function v(e,r,n,o){d=n,u=o||{};var s=t(e,r);return m(s),function(r){for(var n=[],o=0;o<s.length;o++){var i=s[o];(c=a[i.id]).refs--,n.push(c)}r?m(s=t(e,r)):s=[];for(o=0;o<n.length;o++){var c;if(0===(c=n[o]).refs){for(var d=0;d<c.parts.length;d++)c.parts[d]();delete a[c.id]}}}}function m(e){for(var r=0;r<e.length;r++){var n=e[r],t=a[n.id];if(t){t.refs++;for(var o=0;o<t.parts.length;o++)t.parts[o](n.parts[o]);for(;o<n.parts.length;o++)t.parts.push(b(n.parts[o]));t.parts.length>n.parts.length&&(t.parts.length=n.parts.length)}else{var s=[];for(o=0;o<n.parts.length;o++)s.push(b(n.parts[o]));a[n.id]={id:n.id,refs:1,parts:s}}}}function h(){var e=document.createElement("style");return e.type="text/css",s.appendChild(e),e}function b(e){var r,n,t=document.querySelector("style["+f+'~="'+e.id+'"]');if(t){if(d)return l;t.parentNode.removeChild(t)}if(p){var o=c++;t=i||(i=h()),r=w.bind(null,t,o,!1),n=w.bind(null,t,o,!0)}else t=h(),r=S.bind(null,t),n=function(){t.parentNode.removeChild(t)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else n()}}var g,y=(g=[],function(e,r){return g[e]=r,g.filter(Boolean).join("\n")});function w(e,r,n,t){var o=n?"":t.css;if(e.styleSheet)e.styleSheet.cssText=y(r,o);else{var a=document.createTextNode(o),s=e.childNodes;s[r]&&e.removeChild(s[r]),s.length?e.insertBefore(a,s[r]):e.appendChild(a)}}function S(e,r){var n=r.css,t=r.media,o=r.sourceMap;if(t&&e.setAttribute("media",t),u.ssrId&&e.setAttribute(f,r.id),o&&(n+="\n/*# sourceURL="+o.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},kPok:function(e,r,n){var t=n("JE/Z");t.__esModule&&(t=t.default),"string"==typeof t&&(t=[[e.i,t,""]]),t.locals&&(e.exports=t.locals);(0,n("P8hj").default)("3a6a83b5",t,!0,{})}});
//# sourceMappingURL=bs-code-scanner.js.map