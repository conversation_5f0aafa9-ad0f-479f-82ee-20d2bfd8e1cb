<?php declare(strict_types=1);

namespace Ngs\AchieveFreeShipping\Components\PluginConfig;

use Ngs\AchieveFreeShipping\Struct\PluginConfig;
use Shopware\Core\System\SystemConfig\SystemConfigService;

/**
 * Class PluginConfigService
 */
class PluginConfigService
{
    /** @var SystemConfigService */
    private $systemConfigService;

    /**
     * PluginConfigService constructor.
     *
     * @param SystemConfigService $systemConfigService
     */
    public function __construct(SystemConfigService $systemConfigService)
    {
        $this->systemConfigService = $systemConfigService;
    }

    /**
     * Returns plugin config struct
     *
     * @param string|null $salesChannelId
     *
     * @return PluginConfig
     */
    public function getPluginConfig(?string $salesChannelId): PluginConfig
    {
        $config = $this->getAll($salesChannelId);

        $pluginConfigStruct = new PluginConfig();
        $pluginConfigStruct->active = !empty($config['ngsAchieveFreeShippingActive']);

        if (!$pluginConfigStruct->active) {
            return $pluginConfigStruct;
        }

        $pluginConfigStruct->onlyAuthorizedUsers = !empty($config['ngsAchieveFreeShippingOnlyAuthorizedUsers']);
        $pluginConfigStruct->showInHeader = !empty($config['ngsAchieveFreeShippingShowInHeader']);
        $pluginConfigStruct->showInHeaderMobile = !empty($config['ngsAchieveFreeShippingShowInHeaderMobile']);
        $pluginConfigStruct->showInBasket = !empty($config['ngsAchieveFreeShippingShowInBasket']);
        $pluginConfigStruct->showInCheckoutCart = !empty($config['ngsAchieveFreeShippingShowInCheckoutCart']);
        $pluginConfigStruct->showInCheckoutConfirm = !empty($config['ngsAchieveFreeShippingShowInCheckoutConfirm']);

        return $pluginConfigStruct;
    }

    /**
     * Get all configs
     *
     * @param string|null $salesChannelId
     *
     * @return array|null
     */
    private function getAll(?string $salesChannelId = null): ?array
    {
        return $this->systemConfigService->get('NgsAchieveFreeShippingSw6.config', $salesChannelId);
    }

}