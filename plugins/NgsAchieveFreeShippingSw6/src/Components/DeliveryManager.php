<?php

namespace Ngs\AchieveFreeShipping\Components;

use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\Delivery\DeliveryCalculator;
use Shopware\Core\Checkout\Cart\Delivery\Struct\Delivery;
use Shopware\Core\Checkout\Cart\Price\Struct\CartPrice;
use Shopware\Core\Checkout\Cart\Tax\TaxDetector;
use Shopware\Core\Checkout\Shipping\Aggregate\ShippingMethodPrice\ShippingMethodPriceCollection;
use Shopware\Core\Checkout\Shipping\Aggregate\ShippingMethodPrice\ShippingMethodPriceEntity;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\DataAbstractionLayer\Pricing\Price;
use Shopware\Core\Framework\DataAbstractionLayer\Pricing\PriceCollection;
use Shopware\Core\Framework\Util\FloatComparator;
use Shopware\Core\System\SalesChannel\SalesChannelContext;

/**
 * Class DeliveryManager
 */
class DeliveryManager
{
    /**
     * @var TaxDetector
     */
    private $taxDetector;

    public function __construct(
        TaxDetector $taxDetector
    )
    {
        $this->taxDetector = $taxDetector;
    }

    /**
     * Get free deliveries
     *
     * @param Cart $cart
     * @param SalesChannelContext $salesChannelContext
     *
     * @return array|null
     */
    public function getFreeDeliveries(Cart $cart, SalesChannelContext $salesChannelContext): ?array
    {
    	
        if (!$this->validateDelivery($cart, $salesChannelContext)) {
            return null;
        }

        $freeDeliveryPrices = [];

        foreach ($cart->getDeliveries() as $delivery) {
            if (FloatComparator::equals($delivery->getShippingCosts()->getUnitPrice(), 0)) {
                continue;
            }

            $value = $delivery->getPositions()->getPrices()->sum()->getTotalPrice();
            $quantityStartPrice = $this->getQuantityStartPrice($salesChannelContext, $delivery);

            if ($quantityStartPrice === null) {
                continue;
            }

            if (FloatComparator::greaterThan($quantityStartPrice, $value)) {
                $freeDeliveryPrices[$delivery->getShippingMethod()->getId()]['value'] = $quantityStartPrice - $value;
                $freeDeliveryPrices[$delivery->getShippingMethod()->getId()]['percent'] = round(100 * $value / $quantityStartPrice);
            }
        }

        return $freeDeliveryPrices;
    }


    /**
     * indicates if delivery price matches to rule of sales channel
     *
     * @param ShippingMethodPriceEntity $shippingMethodPrice
     * @param SalesChannelContext $context
     *
     * @return bool
     */
    private function shippingPriceMatches(ShippingMethodPriceEntity $shippingMethodPrice, SalesChannelContext $context): bool
    {
        if (in_array($shippingMethodPrice->getRuleId(), $context->getRuleIds())) {
            return true;
        }
        return false;
    }

    /**
     * @param SalesChannelContext $salesChannelContext
     * @param Delivery $delivery
     *
     * @return float|null
     */
    private function getQuantityStartPrice(SalesChannelContext $salesChannelContext, Delivery $delivery): ?float
    {
        $shippingMethodPriceCollection = $delivery->getShippingMethod()->getPrices();
        $this->sortShippingMethodPrices($shippingMethodPriceCollection, $salesChannelContext);

        $filteredShippingPrice = $this->getFilteredShippingMethodPrice($shippingMethodPriceCollection, $salesChannelContext);

        if ($filteredShippingPrice === null) {
            return null;
        }

        return $filteredShippingPrice->getQuantityStart();
    }

    /**
     * @param PriceCollection $priceCollection
     * @param SalesChannelContext $context
     *
     * @return float
     */
    private function getCurrencyPrice(PriceCollection $priceCollection, SalesChannelContext $context): float
    {
        $price = $priceCollection->getCurrencyPrice($context->getCurrency()->getId());

        if ($price === null) {
            return 0;
        }

        $value = $this->getPriceForTaxState($price, $context);

        if ($price->getCurrencyId() === Defaults::CURRENCY) {
            $value *= $context->getContext()->getCurrencyFactor();
        }

        return $value;
    }

    /**
     * @param Price $price
     * @param SalesChannelContext $context
     *
     * @return float
     */
    private function getPriceForTaxState(Price $price, SalesChannelContext $context): float
    {
        $taxState = $this->taxDetector->getTaxState($context);

        if ($taxState === CartPrice::TAX_STATE_GROSS) {
            return $price->getGross();
        }

        return $price->getNet();
    }

    /**
     * Sort shipping method prices by currency price
     *
     * @param ShippingMethodPriceCollection $shippingPrices
     * @param SalesChannelContext $salesChannelContext
     *
     * Copied from
     *
     * @see \Shopware\Core\Checkout\Cart\Delivery\DeliveryCalculator::getMatchingPriceOfRule
     */
    private function sortShippingMethodPrices(ShippingMethodPriceCollection $shippingPrices, SalesChannelContext $salesChannelContext): void
    {
        $shippingPrices->sort(
            function (ShippingMethodPriceEntity $priceEntityA, ShippingMethodPriceEntity $priceEntityB) use ($salesChannelContext) {
                $priceA = null;
                $priceB = null;

                if ($priceEntityA->getCurrencyPrice()) {
                    $priceA = $this->getCurrencyPrice($priceEntityA->getCurrencyPrice(), $salesChannelContext);
                }

                if ($priceEntityB->getCurrencyPrice()) {
                    $priceB = $this->getCurrencyPrice($priceEntityB->getCurrencyPrice(), $salesChannelContext);
                }

                return $priceA <=> $priceB;
            }
        );
    }

    /**
     * Filter shipping method prices by getting that shipping price which currency price is 0 and quantityStart price is small
     *
     * @param ShippingMethodPriceCollection $shippingMethodPriceCollection
     * @param SalesChannelContext $salesChannelContext
     *
     * @return ShippingMethodPriceEntity|null
     */
    private function getFilteredShippingMethodPrice(ShippingMethodPriceCollection $shippingMethodPriceCollection, SalesChannelContext $salesChannelContext): ?ShippingMethodPriceEntity
    {
        $filteredShippingPrices = new ShippingMethodPriceCollection();

        foreach ($shippingMethodPriceCollection as $shippingMethodPriceEntity) {
            if ($this->shippingPriceMatches($shippingMethodPriceEntity, $salesChannelContext) &&
                FloatComparator::equals($this->getCurrencyPrice($shippingMethodPriceEntity->getCurrencyPrice(), $salesChannelContext), 0)) {
                $filteredShippingPrices->add($shippingMethodPriceEntity);
            }
        }
        $filteredShippingPrices->sort(
            static function (ShippingMethodPriceEntity $priceEntityA, ShippingMethodPriceEntity $priceEntityB) {
                return $priceEntityA->getQuantityStart() <=> $priceEntityB->getQuantityStart();
            }
        );

        return $filteredShippingPrices->first();
    }

    /**
     * Validate delivery
     * indicates if cart contains only shipping method price rules calculating by price
     *
     * @param Cart $cart
     * @param SalesChannelContext $salesChannelContext
     *
     * @return bool
     */
    private function validateDelivery(Cart $cart, SalesChannelContext $salesChannelContext): bool
    {
        if (FloatComparator::equals($cart->getShippingCosts()->getUnitPrice(), 0)) {
            return false;
        }


        foreach($cart->getDeliveries() as $delivery) {
            $shippingMethodPriceCollection = $delivery->getShippingMethod()->getPrices();
            foreach ($shippingMethodPriceCollection as $shippingMethodPriceEntity) {
                if ($this->shippingPriceMatches($shippingMethodPriceEntity, $salesChannelContext) && $shippingMethodPriceEntity->getCalculation() !== DeliveryCalculator::CALCULATION_BY_PRICE) {
                    return false;
                }
            }
        }
        
        return true;
    }

}