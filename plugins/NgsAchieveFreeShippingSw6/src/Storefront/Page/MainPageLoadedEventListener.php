<?php declare(strict_types=1);

namespace Ngs\AchieveFreeShipping\Storefront\Page;

use Ngs\AchieveFreeShipping\Components\DeliveryManager;
use Ngs\AchieveFreeShipping\Components\PluginConfig\PluginConfigService;
use Ngs\AchieveFreeShipping\Struct\PluginConfig;
use Shopware\Core\Checkout\Customer\CustomerEntity;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Page\Page;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Class MainPageLoadedEventListener is main class for all page load event listeners
 */
abstract class MainPageLoadedEventListener implements EventSubscriberInterface
{
    private const CHECKOUT_CART_ROUTE = 'frontend.checkout.cart.page';
    private const CHECKOUT_CONFIRM_ROUTE = 'frontend.checkout.confirm.page';
    private const CHECKOUT_INFO_ROUTE = 'frontend.checkout.info';
    private const CART_OFFCANVAS_ROUTE = 'frontend.cart.offcanvas';

    /**
     * @var PluginConfigService
     */
    protected $pluginConfigService;

    /**
     * @var DeliveryManager
     */
    protected $deliveryManager;

    public function __construct(
        PluginConfigService $pluginConfigService,
        DeliveryManager     $deliveryManager
    )
    {
        $this->pluginConfigService = $pluginConfigService;
        $this->deliveryManager = $deliveryManager;
    }

    /**
     * @param Page $page
     * @param SalesChannelContext $salesChannelContext
     * @param string $route
     */
    protected function initFreeDeliveries(Page $page, SalesChannelContext $salesChannelContext, string $route): void
    {
        $pluginConfig = $this->pluginConfigService->getPluginConfig($salesChannelContext->getSalesChannel()->getId());

        if (!$pluginConfig->active) {
            return;
        }

        if ($pluginConfig->onlyAuthorizedUsers && !$this->isLoggedIn($salesChannelContext)) {
            return;
        }

        if (!$this->isRouteValid($route, $pluginConfig)) {
            return;
        }

        if ($route === self::CHECKOUT_CART_ROUTE || $route === self::CHECKOUT_CONFIRM_ROUTE) {
            $pluginConfig->isCheckoutPage = true;
        }

        $freeDeliveryPrices = $this->deliveryManager->getFreeDeliveries($page->getCart(), $salesChannelContext);

        if (!$freeDeliveryPrices) {
            return;
        }

        $pluginConfig->freeDeliveryPrices = $freeDeliveryPrices;

        $this->assignPluginConfig($page, $pluginConfig);
    }

    /**
     * Returns true in case if user logged in.
     *
     * @param SalesChannelContext $context
     *
     * @return bool
     */
    public function isLoggedIn(SalesChannelContext $context): bool
    {
        return $context->getCustomer() instanceof CustomerEntity;
    }

    /**
     * Assign validation config
     *
     * @param Page $page
     * @param PluginConfig $pluginConfig
     */
    protected function assignPluginConfig(Page $page, PluginConfig $pluginConfig): void
    {
        $page->addExtension(PluginConfig::PLUGIN_CONFIG_EXTENSION_NAME, $pluginConfig);
    }

    /**
     * Returns true in case if current route is valid.
     *
     * @param string $route
     * @param PluginConfig $pluginConfig
     *
     * @return bool
     */
    private function isRouteValid(string $route, PluginConfig $pluginConfig): bool
    {
        if ($route === self::CHECKOUT_INFO_ROUTE && !$pluginConfig->showInHeader && !$pluginConfig->showInHeaderMobile) {
            return false;
        }
        if ($route === self::CART_OFFCANVAS_ROUTE && !$pluginConfig->showInBasket) {
            return false;
        }
        if ($route === self::CHECKOUT_CART_ROUTE && !$pluginConfig->showInCheckoutCart) {
            return false;
        }
        if ($route === self::CHECKOUT_CONFIRM_ROUTE && !$pluginConfig->showInCheckoutConfirm) {
            return false;
        }

        return true;
    }
}