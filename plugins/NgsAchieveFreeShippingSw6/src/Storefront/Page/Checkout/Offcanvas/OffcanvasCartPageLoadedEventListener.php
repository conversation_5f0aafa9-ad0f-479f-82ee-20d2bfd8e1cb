<?php

namespace Ngs\AchieveFreeShipping\Storefront\Page\Checkout\Offcanvas;

use Ng<PERSON>\AchieveFreeShipping\Storefront\Page\MainPageLoadedEventListener;
use Shopware\Storefront\Page\Checkout\Offcanvas\OffcanvasCartPageLoadedEvent;

/**
 * Class OffcanvasCartPageLoadedEventListener
 */
class OffcanvasCartPageLoadedEventListener extends MainPageLoadedEventListener
{
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            OffcanvasCartPageLoadedEvent::class => ['onOffcanvasCartPageLoadedEvent']
        ];
    }

    /**
     * Event is dispatched when off canvas is opened
     *
     * @param OffcanvasCartPageLoadedEvent $event
     */
    public function onOffcanvasCartPageLoadedEvent(OffcanvasCartPageLoadedEvent $event): void
    {
        $this->initFreeDeliveries($event->getPage(), $event->getSalesChannelContext(), $event->getRequest()->get('_route'));
    }
}