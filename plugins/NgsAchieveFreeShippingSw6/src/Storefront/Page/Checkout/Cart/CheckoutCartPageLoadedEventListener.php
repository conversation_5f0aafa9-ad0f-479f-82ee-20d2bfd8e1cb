<?php

namespace Ngs\AchieveFreeShipping\Storefront\Page\Checkout\Cart;

use Ngs\AchieveFreeShipping\Storefront\Page\MainPageLoadedEventListener;
use Shopware\Storefront\Page\Checkout\Cart\CheckoutCartPageLoadedEvent;

/**
 * Class CheckoutCartPageLoadedEventListener
 */
class CheckoutCartPageLoadedEventListener extends MainPageLoadedEventListener
{
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CheckoutCartPageLoadedEvent::class => ['onCheckoutCartPageLoadedEvent']
        ];
    }

    /**
     * Event is dispatched when checkout cart page is opened
     *
     * @param CheckoutCartPageLoadedEvent $event
     */
    public function onCheckoutCartPageLoadedEvent(CheckoutCartPageLoadedEvent $event): void
    {
        $this->initFreeDeliveries($event->getPage(), $event->getSalesChannelContext(), $event->getRequest()->get('_route'));
    }

}