<?php

namespace Ngs\AchieveFreeShipping\Storefront\Page\Checkout\Confirm;

use Ng<PERSON>\AchieveFreeShipping\Storefront\Page\MainPageLoadedEventListener;
use Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoadedEvent;

/**
 * Class CheckoutConfirmPageLoadedEventListener
 */
class CheckoutConfirmPageLoadedEventListener extends MainPageLoadedEventListener
{
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CheckoutConfirmPageLoadedEvent::class => ['onCheckoutConfirmPageLoadedEvent']
        ];
    }

    /**
     * Event is dispatched when checkout confirm page is opened
     *
     * @param CheckoutConfirmPageLoadedEvent $event
     */
    public function onCheckoutConfirmPageLoadedEvent(CheckoutConfirmPageLoadedEvent $event): void
    {
        $this->initFreeDeliveries($event->getPage(), $event->getSalesChannelContext(), $event->getRequest()->get('_route'));
    }

}