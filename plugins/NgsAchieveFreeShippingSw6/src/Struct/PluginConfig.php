<?php declare(strict_types=1);

namespace Ngs\AchieveFreeShipping\Struct;

use Shopware\Core\Framework\Struct\Struct;

/**
 * Class PluginConfig
 */
class PluginConfig extends Struct
{
    public const PLUGIN_CONFIG_EXTENSION_NAME = 'NgsAchieveFreeShippingPluginConfig';

    /**
     * @var bool
     */
    public $active = false;

    /**
     * @var bool
     */
    public $onlyAuthorizedUsers = false;

    /**
     * @var array
     */
    public $freeDeliveryPrices = [];

    /**
     * @var bool
     */
    public $showInHeader = false;

    /**
     * @var bool
     */
    public $showInHeaderMobile = false;

    /**
     * @var bool
     */
    public $showInBasket = false;

    /**
     * @var bool
     */
    public $showInCheckoutCart = false;

    /**
     * @var bool
     */
    public $showInCheckoutConfirm = false;

    /**
     * @var bool
     */
    public $isCheckoutPage = false;

}