"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["ngs-achieve-free-shipping-sw6"],{9509:(e,i,t)=>{var n=t(6285);class s extends n.Z{init(){this.fillPercentage()}fillPercentage(){const e=this.el,i=this.el.getAttribute("data-fill");setTimeout((function(){e.animate([{width:i}],{duration:500,fill:"forwards"})}),100)}}var r=t(9658);class a extends n.Z{init(){this.initOpenMobileBox(),this.initCloseMobileBox(),this.moveMobileBox()}initOpenMobileBox(){const e=this,i=document.getElementById("js--ngs-free-delivery-remaining-amount-box-mobile"),t=r.Z.isTouchDevice()?"touchstart":"click";i.addEventListener(t,(function(t){return t.stopPropagation(),i.classList.contains("is--active")?(i.classList.remove("is--active"),e.emptyPercentage()):(i.classList.add("is--active"),e.fillPercentage()),!1}))}initCloseMobileBox(){const e=this,i=document.getElementById("js--ngs-free-delivery-remaining-amount-box-mobile");document.body.addEventListener("click",(function(t){let n=t.target;n.classList.contains("js--ngs-navigation-free-shipping")||n.closest(".js--ngs-navigation-free-shipping")||(i.classList.remove("is--active"),e.emptyPercentage())}))}fillPercentage(){const e=this.el.getAttribute("data-fill");this.el.animate([{width:e}],{duration:500,fill:"forwards"})}emptyPercentage(){this.el.animate([{width:"0"}],{duration:0,fill:"forwards"})}moveMobileBox(){let e=document.getElementById("js--ngs-free-shipping-mobile-box");if(!e)return;e.innerHTML="";let i=document.getElementById("js--ngs-navigation-free-shipping");i&&e.appendChild(i)}}var o=t(1733);class l extends o.Z{init(){super.init(),this.subscribeToCartWidgetEvents()}subscribeToCartWidgetEvents(){const e=window.PluginManager;this.$emitter.subscribe("fetch",(function(){e.initializePlugin("NgsAchieveFreeShippingFillPlugin","[data-ngs-achieve-free-shipping-fill]"),e.initializePlugin("NgsAchieveFreeShippingFillMobilePlugin","[data-ngs-achieve-free-shipping-fill-mobile]")})),this.$emitter.subscribe("insertStoredContent",(function(){e.initializePlugin("NgsAchieveFreeShippingFillPlugin","[data-ngs-achieve-free-shipping-fill]"),e.initializePlugin("NgsAchieveFreeShippingFillMobilePlugin","[data-ngs-achieve-free-shipping-fill-mobile]")}))}}const g=window.PluginManager;g.register("NgsAchieveFreeShippingFillPlugin",s,"[data-ngs-achieve-free-shipping-fill]"),g.register("NgsAchieveFreeShippingFillMobilePlugin",a,"[data-ngs-achieve-free-shipping-fill-mobile]"),g.override("CartWidget",l,"[data-cart-widget]")},1733:(e,i,t)=>{t.d(i,{Z:()=>g});var n,s,r,a=t(6285),o=t(8254),l=t(6656);class g extends a.Z{init(){this._client=new o.Z,this.insertStoredContent(),this.fetch()}insertStoredContent(){l.Z.setItem(this.options.emptyCartWidgetStorageKey,this.el.innerHTML);const e=l.Z.getItem(this.options.cartWidgetStorageKey);e&&(this.el.innerHTML=e),this.$emitter.publish("insertStoredContent")}fetch(){this._client.get(window.router["frontend.checkout.info"],((e,i)=>{if(!(i.status>=500))if(204!==i.status)l.Z.setItem(this.options.cartWidgetStorageKey,e),this.el.innerHTML=e,this.$emitter.publish("fetch",{content:e});else{l.Z.removeItem(this.options.cartWidgetStorageKey);const e=l.Z.getItem(this.options.emptyCartWidgetStorageKey);e&&(this.el.innerHTML=e)}}))}}n=g,r={cartWidgetStorageKey:"cart-widget-template",emptyCartWidgetStorageKey:"empty-cart-widget"},(s=function(e){var i=function(e,i){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,i||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(e)}(e,"string");return"symbol"==typeof i?i:String(i)}(s="options"))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r}},e=>{e.O(0,["vendor-node","vendor-shared"],(()=>{return i=9509,e(e.s=i);var i}));e.O()}]);