import Plugin from 'src/plugin-system/plugin.class';
import DeviceDetection from 'src/helper/device-detection.helper';

export default class NgsAchieveFreeShippingFillMobilePlugin extends Plugin {

    init() {
        this.initOpenMobileBox();
        this.initCloseMobileBox();
        this.moveMobileBox();
    }

    initOpenMobileBox() {
        const self = this;
        const ngsFreeDeliveryRemainingAmountBoxMobile = document.getElementById('js--ngs-free-delivery-remaining-amount-box-mobile');
        const event = (DeviceDetection.isTouchDevice()) ? 'touchstart' : 'click';

        ngsFreeDeliveryRemainingAmountBoxMobile.addEventListener(event, function (event) {
            event.stopPropagation();

            if (ngsFreeDeliveryRemainingAmountBoxMobile.classList.contains('is--active')) {
                ngsFreeDeliveryRemainingAmountBoxMobile.classList.remove('is--active');
                self.emptyPercentage();
            } else {
                ngsFreeDeliveryRemainingAmountBoxMobile.classList.add('is--active');
                self.fillPercentage();
            }

            return false;
        });
    }

    initCloseMobileBox() {
        const self = this;
        const ngsFreeDeliveryRemainingAmountBoxMobile = document.getElementById('js--ngs-free-delivery-remaining-amount-box-mobile');

        document.body.addEventListener('click', function (event) {
            let targetEl = event.target;

            if (!targetEl.classList.contains('js--ngs-navigation-free-shipping') && !targetEl.closest('.js--ngs-navigation-free-shipping')) {
                ngsFreeDeliveryRemainingAmountBoxMobile.classList.remove('is--active');
                self.emptyPercentage();
            }
        })
    }

    fillPercentage() {
        const fillPercent = this.el.getAttribute('data-fill');

        this.el.animate([
            {width: fillPercent}
        ], {
            duration: 500,
            fill: 'forwards'
        });
    }

    emptyPercentage() {
        this.el.animate([
            {width: '0'}
        ], {
            duration: 0,
            fill: 'forwards'
        });
    }

    moveMobileBox() {
        let mobileBoxWrapperElem = document.getElementById('js--ngs-free-shipping-mobile-box');
        if (!mobileBoxWrapperElem) {
            return;
        }
        mobileBoxWrapperElem.innerHTML = '';

        let mobileBox = document.getElementById('js--ngs-navigation-free-shipping');

        if (!mobileBox) {
            return;
        }
        mobileBoxWrapperElem.appendChild(mobileBox);
    }

}
