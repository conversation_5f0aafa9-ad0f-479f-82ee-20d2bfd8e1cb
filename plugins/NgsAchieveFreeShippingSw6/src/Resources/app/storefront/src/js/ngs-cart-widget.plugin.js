import CartWidgetPlugin from 'src/plugin/header/cart-widget.plugin';

/**
 * NgsCartWidgetPlugin
 */
export default class NgsCartWidgetPlugin extends CartWidgetPlugin {

    init() {
        super.init();
        this.subscribeToCartWidgetEvents();
    }

    subscribeToCartWidgetEvents() {
        const PluginManager = window.PluginManager;

        this.$emitter.subscribe('fetch', function () {
            PluginManager.initializePlugin('NgsAchieveFreeShippingFillPlugin', '[data-ngs-achieve-free-shipping-fill]');
            PluginManager.initializePlugin('NgsAchieveFreeShippingFillMobilePlugin', '[data-ngs-achieve-free-shipping-fill-mobile]');
        });
        this.$emitter.subscribe('insertStoredContent', function () {
            PluginManager.initializePlugin('NgsAchieveFreeShippingFillPlugin', '[data-ngs-achieve-free-shipping-fill]');
            PluginManager.initializePlugin('NgsAchieveFreeShippingFillMobilePlugin', '[data-ngs-achieve-free-shipping-fill-mobile]');
        });
    }

}
