import NgsAchieveFreeShippingFillPlugin from './js/ngs-achieve-free-shipping-fill.plugin';
import NgsAchieveFreeShippingFillMobilePlugin from './js/ngs-achieve-free-shipping-fill-mobile.plugin';
import NgsCartWidgetPlugin from './js/ngs-cart-widget.plugin';

// Register them via the existing PluginManager
const PluginManager = window.PluginManager;

PluginManager.register('NgsAchieveFreeShippingFillPlugin', NgsAchieveFreeShippingFillPlugin, '[data-ngs-achieve-free-shipping-fill]');
PluginManager.register('NgsAchieveFreeShippingFillMobilePlugin', NgsAchieveFreeShippingFillMobilePlugin, '[data-ngs-achieve-free-shipping-fill-mobile]');
PluginManager.override('CartWidget', NgsCartWidgetPlugin, '[data-cart-widget]');

// Necessary for the webpack hot module reloading server
if (module.hot) {
    module.hot.accept();
}

