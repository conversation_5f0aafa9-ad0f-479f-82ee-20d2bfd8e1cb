.navigation--entry {
  &.entry--cart {
    position: relative;
  }
}

.ngs-entry--free-shipping {
  list-style: none;
}

.is-ctl-checkout {
  .header-cart {
    .header-cart-btn {
      .ngs-entry--free-shipping {
        display: none;
      }
    }
  }
}


/** WIDGET STYLES **/

@include media-breakpoint-down(md) {
  .ngs--free-delivery-remaining-amount-container-desktop {
    display: none;
  }

  .shop--navigation {
    .navigation--list {
      font-size: 0;

      .navigation--entry {
        font-size: initial;
      }
    }

    .navigation--entry {
      margin-right: 10px;

      &.entry--cart {
        margin-right: 0;

        ul {
          .navigation--entry {
            &.entry--cart {
              margin: 0;
              padding: 0;
              position: relative;
            }
          }
        }
      }
    }
  }

  .header-cart {
    .header-cart-btn {
      .navigation--entry.entry--cart.is--free-delivery-remaining {
        .ngs--sw-cart-button-container {
          margin-right: 36px;
        }
      }
    }
  }

  .header-main {
    .navigation--entry {
      &.ngs-entry--free-shipping {
        height: 100%;
        text-align: right;
      }
    }
  }

  /*           max-width: 575.98px      */
  @include media-breakpoint-down(xs) {
    .header-cart {
      .header-cart-btn {
        .navigation--entry.entry--cart.is--free-delivery-remaining {
          .ngs--sw-cart-button-container {
            margin-right: 15px;
          }
        }
      }
    }
    .header-main {
      .header-actions-btn {
        display: flex;
      }

      .navigation--entry {
        &.ngs-entry--free-shipping {
          width: 36px;

          .ngs--free-delivery-remaining-amount-truck {
            font-size: 26px;
          }

          .btn.is--icon-left {
            position: static;
            width: 35px;
            height: 35px;
            border: 0;
          }
        }
      }
    }
  }

  .navigation--entry {
    &.ngs-entry--free-shipping {
      margin-left: 10px;
      height: 100%;
      padding: 0;

      .btn {
        width: 100%;
        height: 100%;
        line-height: 0;

        &.is--icon-left {
          padding: 0;
          width: 50px;
          height: 50px;
          border-radius: 50px;
          position: absolute;
          right: 0;
          top: 20px;
          overflow: visible;

          .ngs--free-delivery-remaining-amount-container {
            &.ngs--free-delivery-remaining-amount-container-widget {
              width: 100%;
              height: 100%;
              position: relative;

              .ngs--free-delivery-remaining-amount-free-box {
                width: 100%;
                height: 100%;
                border: none;
                background: none;
                box-shadow: none;
                padding: 6px;

                display: flex;
                align-items: center;
                justify-content: center;
              }

              .ngs--free-delivery-remaining-amount-free-text {
                position: absolute;
                color: #fff;
                z-index: 100;
                font-size: 8px;
                left: 4px;
                top: -2px;
              }

              .ngs--free-delivery-remaining-amount-truck {
                text-align: center;
                top: 0;
                left: 0;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;

                svg {
                  width: 100%;
                  height: 100%;
                  left: 0;
                  top: 0;
                  position: absolute;
                }
              }

              .ngs--free-delivery-remaining-amount-info-box {
                display: none;
                width: 200px;
                position: absolute;
                left: auto;
                top: 100%;
                transform: translatey(12px);
                right: 0;
                z-index: 4000;
                border: 1px solid;
                border-radius: 5px;
                box-shadow: 0px 0px 20px 3px;

                min-width: 260px;
                padding: 0 20px;
                text-align: center;

                .ngs--free-delivery-remaining-amount-fill-container {
                  display: block;
                  width: auto;
                  height: 10px;
                  margin: 5px 10px;
                  position: relative;
                  border-radius: 50px;
                  overflow: hidden;

                  .ngs--free-delivery-remaining-amount-fill {
                    width: 0;
                    height: 100%;
                    position: absolute;
                    left: 0;
                    border-radius: 50px 0 0 50px;
                  }
                }

                .ngs--free-delivery-remaining-amount-mobile-head {
                  font-size: 16px;
                  font-weight: bold;
                  margin: 28px 0 10px 0;
                  line-height: 20px;
                }

                .ngs--free-delivery-remaining-amount-remaining-text {
                  text-align: center;
                  font-weight: normal;
                  line-height: 30px;
                }

                .ngs--free-delivery-remaining-amount-info-text {
                  font-weight: normal;
                  font-size: 12px;
                  margin: 0 0 20px 0;
                  line-height: 14px;
                  white-space: normal;

                  strong {
                    font-weight: bolder;
                  }
                }

                &:before {
                  content: "";
                  width: 0;
                  height: 0;
                  border-left: 10px solid transparent;
                  border-right: 10px solid transparent;
                  border-bottom: 10px solid;
                  position: absolute;
                  top: -10px;
                  right: 8px;

                }
              }

              &.is--active {
                .ngs--free-delivery-remaining-amount-info-box {
                  display: inline-block;
                }
              }
            }
          }
        }
      }
    }
  }
}

@include media-breakpoint-down(1400px) {
  .navigation--entry {
    &.entry--cart {
      .ngs--free-delivery-remaining-amount-container {
        &.ngs--free-delivery-remaining-amount-container-widget {
          .ngs--free-delivery-remaining-amount-info-box {
            padding: 10px 0;
            width: 200px;
            position: absolute;
            right: 0;
            top: 100%;
            left: auto;
            transform: none;
            margin-top: 5px;

            &:before {
              display: none;
            }

            &:after {
              display: none;
            }

            .ngs--free-delivery-remaining-amount-remaining-text {
              font-size: 14px;
              line-height: 20px;
            }
          }
        }
      }
    }
  }
}

@include media-breakpoint-up(lg) {
  .ngs--free-delivery-remaining-amount-container-desktop {
    display: block;
  }

  .ngs-entry--free-shipping {
    display: none !important;
  }

  .ngs--free-delivery-remaining-amount-container {
    &.ngs--free-delivery-remaining-amount-container-widget {
      position: absolute;
      z-index: 1000;
      border-radius: 5px;
      width: auto;
      height: auto;
      top: 0;
      transition: width 0.5s, height 0.5s;

      .ngs--free-delivery-remaining-amount-info-box {
        border-radius: 5px;
        box-shadow: 0px 0px 0px 1px;

        &:before {
          content: "";
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-bottom: 10px solid;
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translate(-50%, 0);
        }

        &:after {
          content: "";
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-bottom: 10px solid;
          position: absolute;
          top: -11px;
          left: 50%;
          transform: translate(-50%, 0);
          z-index: -1;
        }
      }

      &.is--minimized {
        .ngs--free-delivery-remaining-amount-info-box {
          display: none;
        }

        &:hover {
          .ngs--free-delivery-remaining-amount-info-box {
            display: inline-block;
          }
        }
      }

      .ngs--free-delivery-remaining-amount-free-box {
        .ngs--free-delivery-remaining-amount-info-box {
          display: inline-block;
        }
      }

      .ngs--free-delivery-remaining-amount-free-box {
        transition: width 0.5s, height 0.5s;
        -webkit-transition: width 0.5s, height 0.5s;
        -moz-transition: width 0.5s, height 0.5s;
        -o-transition: width 0.5s, height 0.5s;
        width: auto;
        border-radius: 50%;
        height: auto;
        border: 3px solid;
        vertical-align: middle;
        text-align: center;
        box-shadow: 0px 0px 0px 1px;
        line-height: 0;
        padding: 6px;

        .ngs--free-delivery-remaining-amount-truck {
          transition: width 0.5s, height 0.5s;
          -webkit-transition: width 0.5s, height 0.5s;
          -moz-transition: width 0.5s, height 0.5s;
          -o-transition: width 0.5s, height 0.5s;
          position: relative;
          width: 20px;
          height: 20px;


          svg {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
          }
        }
      }

      .ngs--free-delivery-remaining-amount-info-box {
        display: inline-block;
        width: 200px;
        position: absolute;
        left: 50%;
        top: 100%;
        transform: translate(-50%, 12px);
        padding: 10px 0;

        .ngs--free-delivery-remaining-amount-fill-container {
          display: block;
          width: auto;
          height: 10px;
          margin: 5px 10px;
          position: relative;
          border-radius: 50px;
          overflow: hidden;

          .ngs--free-delivery-remaining-amount-fill {
            width: 0;
            height: 100%;
            position: absolute;
            left: 0;
            border-radius: 50px 0 0 50px;
          }
        }

        .ngs--free-delivery-remaining-amount-remaining-text {
          text-align: left;
          padding: 0 10px;
        }
      }
    }
  }
}

@include media-breakpoint-down(xs) {
  .shop--navigation {
    .navigation--entry {
      margin-right: 5px;

      &.entry--cart {
        margin-right: 5px;

        ul {
          .navigation--entry {
            &.entry--cart {
              margin: 0;
              position: relative;
            }
          }
        }
      }
    }
  }

  .navigation--entry {
    &.ngs-entry--free-shipping {
      margin-left: 5px;
    }
  }
}