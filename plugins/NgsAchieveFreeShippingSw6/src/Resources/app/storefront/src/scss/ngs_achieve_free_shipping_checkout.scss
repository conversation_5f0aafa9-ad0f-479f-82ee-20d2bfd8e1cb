/** CHECKOUT STYLES **/

.free-delivery--checkout-box {
  margin: 10px 0;

  .alert {
    .alert--icon, .icon--element {
      width: 30%;
      display: table-cell;
      margin: 0;
      padding: 10px 20px;
      float: none;
      line-height: initial;
      vertical-align: middle;
    }

    .alert--content {
      width: auto;
      display: table-cell;
      margin: 0;
      padding: 10px 20px;
      float: none;
      text-align: center;
    }
  }
}

.free-delivery--checkout-box {
  .ngs--free-delivery-remaining-amount-container {
    display: table;
    width: 100%;

    .ngs--free-delivery-remaining-amount-free-box {
      display: inline-block;
      position: relative;
      border-radius: 50%;
      border: 2px solid #4A545B;
      vertical-align: middle;
      text-align: center;
      width: auto;
      height: auto;
      box-sizing: border-box;
      background: #ffffff;

      .ngs--free-delivery-remaining-amount-free-label {
        left: 4px;
        position: absolute;
        top: 6px;
        font-size: 10px;
        z-index: 10;
        pointer-events: none;
        transition: font-size 0.5s;
      }

      .ngs--free-delivery-remaining-amount-truck {
        vertical-align: middle;
        margin: 6px;
        font-size: 28px;

        svg {
          width: 100%;
          vertical-align: top;
          left: 0;
          top: 0;
          height: 100%;
        }
      }

    }

    .ngs--free-delivery-remaining-amount-free-remaining-container {
      display: inline-block;
      text-align: center;
      min-width: 200px;
      vertical-align: middle;
      font-weight: normal;

      .ngs--free-delivery-remaining-amount-fill-container {

      }
    }

    .ngs--free-delivery-remaining-amount-info-box {
      display: inline-block;
      max-width: 500px;
      vertical-align: middle;
      text-align: center;
      font-size: 12px;
      font-weight: normal;
    }
  }
}

@include media-breakpoint-up(md) {
  .free-delivery--checkout-box {
    .alert {
      .alert--icon, .icon--element {
        width: 25%;
        display: table-cell;
        margin: 0;
        padding: 10px 20px;
        float: none;
        line-height: initial;
        vertical-align: middle;
      }

      .alert--content {
        width: auto;
        display: table-cell;
        margin: 0;
        padding: 10px 20px;
        float: none;
        text-align: center;
      }
    }
  }
}

@include media-breakpoint-down(xs) {
  .free-delivery--checkout-box {
    .alert {
      .alert--icon {
        font-size: 12px;
      }
    }

    .ngs--free-delivery-remaining-amount-container {
      .ngs--free-delivery-remaining-amount-free-remaining-container {
        min-width: initial;
      }
    }

  }
}
