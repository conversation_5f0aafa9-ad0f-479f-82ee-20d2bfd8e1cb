<?php declare(strict_types=1);

namespace Ngs\AchieveFreeShipping\Resources\snippet;

use Shopware\Core\System\Snippet\Files\SnippetFileInterface;

/**
 * Trait BaseSnippetFile
 */
abstract class BaseSnippetFile implements SnippetFileInterface
{
    private const SNIPPET_AUTHOR = 'NgsAchieveFreeShipping';
    private const NAME_WITHOUT_ISO = 'ngs_achieve_free_shipping';

    abstract protected function getCurrentDir(): string;

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return self::NAME_WITHOUT_ISO . '.' . $this->getIso();
    }

    /**
     * @inheritDoc
     */
    public function getPath(): string
    {
        return $this->getCurrentDir() . $this->getPathName();
    }

    /**
     * @inheritDoc
     */
    public function getAuthor(): string
    {
        return self::SNIPPET_AUTHOR;
    }

    /**
     * @inheritDoc
     */
    public function isBase(): bool
    {
        return false;
    }

    protected function getPathName(): string
    {
        return '/' . $this->getName() . '.json';
    }

}