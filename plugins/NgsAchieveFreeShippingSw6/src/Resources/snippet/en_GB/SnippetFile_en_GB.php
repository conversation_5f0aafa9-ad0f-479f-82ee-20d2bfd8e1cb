<?php declare(strict_types=1);

namespace Ngs\AchieveFreeShipping\Resources\snippet\en_GB;

use Ngs\AchieveFreeShipping\Resources\snippet\BaseSnippetFile;

/**
 * Class SnippetFile_en_GB
 */
class SnippetFile_en_GB extends BaseSnippetFile
{
    /**
     * Get current dir
     *
     * @return string
     */
    protected function getCurrentDir(): string
    {
        return __DIR__;
    }

    /**
     * @inheritDoc
     */
    public function getIso(): string
    {
        return 'en-GB';
    }
}
