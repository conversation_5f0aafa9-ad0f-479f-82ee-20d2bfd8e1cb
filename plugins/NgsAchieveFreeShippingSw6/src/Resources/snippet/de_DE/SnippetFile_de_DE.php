<?php declare(strict_types=1);

namespace Ngs\AchieveFreeShipping\Resources\snippet\de_DE;

use Ngs\AchieveFreeShipping\Resources\snippet\BaseSnippetFile;

/**
 * Class SnippetFile_de_DE
 */
class SnippetFile_de_DE extends BaseSnippetFile
{
    /**
     * Get current dir
     *
     * @return string
     */
    protected function getCurrentDir(): string
    {
        return __DIR__;
    }

    /**
     * @inheritDoc
     */
    public function getIso(): string
    {
        return 'de-DE';
    }
}
