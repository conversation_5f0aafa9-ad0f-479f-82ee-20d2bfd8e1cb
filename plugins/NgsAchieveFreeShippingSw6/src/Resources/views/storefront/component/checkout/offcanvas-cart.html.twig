{% sw_extends '@Storefront/storefront/component/checkout/offcanvas-cart.html.twig' %}

{% block utilities_offcanvas_content %}
    {% if page.hasExtension(constant('Ng<PERSON>\\AchieveFreeShipping\\Struct\\PluginConfig::PLUGIN_CONFIG_EXTENSION_NAME')) %}
        {% set NgsAchieveFreeShippingPluginConfig = page.getExtension(constant('Ngs\\AchieveFreeShipping\\Struct\\PluginConfig::PLUGIN_CONFIG_EXTENSION_NAME')) %}
        {% sw_include '@Storefront/storefront/component/achieve-free-shipping/free-delivery-box-offcanvas.html.twig' with {
            freeDeliveryPrices: NgsAchieveFreeShippingPluginConfig.freeDeliveryPrices,
            shippingMethod: page.cart.deliveries.elements[0].shippingMethod
        } %}
    {% endif %}

    {{ parent() }}
{% endblock %}