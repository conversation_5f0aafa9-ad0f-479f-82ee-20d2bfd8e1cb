{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_stylesheet %}
    {{ parent() }}

    {% if config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingActive') %}
        <style type="text/css">

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-truck {
                width: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeMobile') }}px;
                height: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeMobile') }}px;
            }

            .ngs-entry--free-shipping .btn {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBorderColorMobile') }};
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorMobile') }};
                background-image: none;
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-free-text {
                display: none;
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBorderColorMobile') }};
                box-shadow: 0 0 20px 3px{{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBorderColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box:before {
                border-bottom-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-mobile-head {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-fill-container {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorMobile') }};
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-fill {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetTextColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-text strong {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetTextColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-info-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetTextColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .btn.is--icon-left .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-info-text strong {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetTextColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-truck .ngs--free-delivery-remaining-amount-truck-svg {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorMobile') }};
            }

            .navigation--entry.ngs-entry--free-shipping .ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-truck .ngs--free-delivery-remaining-amount-truck-svg-text {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorMobile') }};
            }


            .ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-truck .ngs--free-delivery-remaining-amount-truck-svg {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-truck .ngs--free-delivery-remaining-amount-truck-svg-text {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget:hover .ngs--free-delivery-remaining-amount-free-box .ngs--free-delivery-remaining-amount-truck {
                width: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeDesktop') }}px;
                height: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeDesktop') }}px;
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-free-box {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBorderColor') }};
                background: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBackgroundColor') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-free-box {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBorderColor') }};
                background: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBackgroundColor') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget:hover .ngs--free-delivery-remaining-amount-free-box {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketIconColor') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-free-box .ngs--free-delivery-remaining-amount-free-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorDesktop') }};
                border-radius: 5px;
                box-shadow: 0 0 0 1px{{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBorderColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box:before {
                border-bottom-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box:after {
                border-bottom-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBorderColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-fill {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-fill-container {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconColorDesktop') }};
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetBackgroundColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetTextColorDesktop') }};
            }

            .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-text strong {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetTextColorDesktop') }};
            }

            .navigation--entry.entry--cart .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget {
                top: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeDesktop')/4 }}px;
                left: calc(100% - {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeDesktop') + config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeDesktop')/4 }}px);
            }

            .navigation--entry.entry--cart.is--free-delivery-remaining .ngs--sw-cart-button-container {
                margin-right: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingWidgetIconSizeDesktop') }}px;
            }

            @media screen and (max-width: 1024px) {
                .navigation--entry.entry--cart.is--free-delivery-remaining .ngs--sw-cart-button-container {
                    margin-right: 0px;
                }
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketBorderColor') }};
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketBackgroundColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-truck-svg {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketIconColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-free-box .ngs--free-delivery-remaining-amount-free-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketIconColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketTextColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-text strong {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketTextColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-info-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketTextColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-info-box .ngs--free-delivery-remaining-amount-remaining-info-text strong {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketTextColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-fill-container {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketIconColor') }};
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketBackgroundColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-fill {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketIconColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-free-box .ngs--free-delivery-remaining-amount-truck {
                width: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketIconSize') }}px;
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketTextColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-free-box .ngs--free-delivery-remaining-amount-truck .ngs--free-delivery-remaining-amount-free-label {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketBorderColor') }};
            }

            .free-delivery--ajax-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-truck-svg-text {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingBasketBackgroundColor') }};
                font-size: 26px;
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-free-box .ngs--free-delivery-remaining-amount-truck {
                width: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutIconSize') }}px;
                height: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutIconSize') }}px;
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-container.alert.is--info .alert--icon {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutIconColor') }};
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBackgroundColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-container.alert.is--info {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutTextBackgroundColor') }};
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutIconColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-fill-container {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutIconColor') }};
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutTextBackgroundColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-fill {
                background-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutIconColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-info-box {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutTextColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-remaining-info-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutTextColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-remaining-info-text strong {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutTextColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-remaining-text {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutTextColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-fill-container .ngs--free-delivery-remaining-amount-remaining-text strong {
                color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutTextColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-free-box {
                border-color: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBorderColor') }};
                background: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBackgroundColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-truck-svg {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutIconColor') }};
            }

            .free-delivery--checkout-box .ngs--free-delivery-remaining-amount-container .ngs--free-delivery-remaining-amount-truck-svg-text {
                fill: {{ config('NgsAchieveFreeShippingSw6.config.ngsAchieveFreeShippingCheckoutBackgroundColor') }};
            }

        </style>
    {% endif %}
{% endblock %}