<?xml version="1.0" encoding="UTF-8" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="Ngs\AchieveFreeShipping\Components\PluginConfig\PluginConfigService">
            <argument id="Shopware\Core\System\SystemConfig\SystemConfigService" type="service"/>
        </service>

        <service id="Ngs\AchieveFreeShipping\Components\DeliveryManager">
            <argument id="Shopware\Core\Checkout\Cart\Tax\TaxDetector" type="service"/>
        </service>

    </services>
</container>