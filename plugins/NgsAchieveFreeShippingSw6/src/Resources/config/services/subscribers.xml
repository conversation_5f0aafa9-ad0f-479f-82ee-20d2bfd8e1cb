<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="Ngs\AchieveFreeShipping\Storefront\Page\Checkout\Offcanvas\OffcanvasCartPageLoadedEventListener">
            <argument type="service" id="Ngs\AchieveFreeShipping\Components\PluginConfig\PluginConfigService"/>
            <argument type="service" id="Ngs\AchieveFreeShipping\Components\DeliveryManager"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ngs\AchieveFreeShipping\Storefront\Page\Checkout\Cart\CheckoutCartPageLoadedEventListener">
            <argument type="service" id="Ngs\AchieveFreeShipping\Components\PluginConfig\PluginConfigService"/>
            <argument type="service" id="Ngs\AchieveFreeShipping\Components\DeliveryManager"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ngs\AchieveFreeShipping\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoadedEventListener">
            <argument type="service" id="Ngs\AchieveFreeShipping\Components\PluginConfig\PluginConfigService"/>
            <argument type="service" id="Ngs\AchieveFreeShipping\Components\DeliveryManager"/>

            <tag name="kernel.event_subscriber"/>
        </service>

    </services>
</container>