<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>General configurations</title>
        <title lang="de-DE">Allgemeine Konfiguration</title>

        <!--Activate plugin for subshop-->
        <input-field type="bool">
            <name>ngsAchieveFreeShippingActive</name>
            <label>Activate plugin for this sales channel</label>
            <label lang="de-DE">Aktivieren Sie das Plugin für diesen Vertriebskanal</label>
            <defaultValue>false</defaultValue>
        </input-field>
        <input-field type="bool">
            <name>ngsAchieveFreeShippingOnlyAuthorizedUsers</name>
            <label>Show for logged in user</label>
            <label lang="de-DE">Zeigen Sie für angemeldete Benutzer</label>
            <defaultValue>false</defaultValue>
            <helpText>Should the plugin be active only for logged in user?</helpText>
            <helpText lang="de-DE">Soll das Plugin nur für angemeldete Benutzer aktiv sein?</helpText>
        </input-field>
    </card>
    <card>
        <title>Desktop configurations</title>
        <title lang="de-DE">Desktop Konfiguration</title>

        <input-field type="bool">
            <name>ngsAchieveFreeShippingShowInHeader</name>
            <label>Display progress bar on Basket button</label>
            <label lang="de-DE">Fortschrittsbalken auf den Knopf "Warenkorb" anzeigen</label>
            <defaultValue>true</defaultValue>
            <helpText>A pop-up with the loader and message will open from the "Shopping Cart" button either on hover  or any page redirection/refresh</helpText>
            <helpText lang="de-DE">Ein Popup-Fenster mit dem Ladeprogramm und mit der Nachricht wird von dem Knopf "Warenkorb" entweder beim Schweben oder bei jeder Seitenumleitung / Aktualisierung geöffnet</helpText>
        </input-field>
        <input-field type="int">
            <name>ngsAchieveFreeShippingWidgetIconSizeDesktop</name>
            <label>Basket Truck Icon Size (px)</label>
            <label lang="de-DE">Lastwagen-Symbol Größe im Korb (px)</label>
            <defaultValue>36</defaultValue>
            <min>20</min>
            <max>48</max>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetIconColorDesktop</name>
            <label>Basket Truck Icon Color</label>
            <label lang="de-DE">Lastwagen-Symbol Farbe im Korb</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the color of truck which is displayed on navigation basket</helpText>
            <helpText lang="de-DE">Dies ist die Farbe des Lastwagens, der im Navigationskorb angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetBackgroundColorDesktop</name>
            <label>Basket Info tool-tip Background Color</label>
            <label lang="de-DE">Tooltip Hintergrundfarbe der Information im Korb</label>
            <defaultValue>#FFFFFF</defaultValue>
            <helpText>This is the background color of tool-tip which appears on hover on the truck icon (on navigation basket)</helpText>
            <helpText lang="de-DE">Dies ist die Hintergrundfarbe des Tooltips, der beim Schweben auf dem Lastwagen-Symbol (im Navigationskorb) erscheint</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetBorderColorDesktop</name>
            <label>Basket Info tool-tip Border Color</label>
            <label lang="de-DE">Tooltip Rahmenfarbe der Information im Korb</label>
            <defaultValue>#DADAE5</defaultValue>
            <helpText>This is the border color of tool-tip which appears on hover on the truck icon (on navigation basket)</helpText>
            <helpText lang="de-DE">Dies ist die Rahmenfarbe des Tooltips, der beim Schweben auf dem Lastwagen-Symbol (im Navigationskorb) erscheint</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetTextColorDesktop</name>
            <label>Basket Info tool-tip Text Color</label>
            <label lang="de-DE">Die Textfarbe des Tooltips im Korb</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the text color of tool-tip which appears on hover on the truck icon (on navigation basket)</helpText>
            <helpText lang="de-DE">Dies ist die Textfarbe des Tooltips, der beim Schweben auf dem Lastwagen-Symbol (im Navigationskorb) erscheint</helpText>
        </input-field>
    </card>

    <card>
        <title>Mobile configurations</title>
        <title lang="de-DE">Mobile Konfiguration</title>

        <input-field type="bool">
            <name>ngsAchieveFreeShippingShowInHeaderMobile</name>
            <label>Display progress bar on Basket button for Mobile Devices</label>
            <label lang="de-DE">Fortschrittsbalken auf den Knopf "Warenkorb" anzeigen für mobile Geräte</label>
            <defaultValue>true</defaultValue>
            <helpText>A pop-up with the loader and message will open from the "Shopping Cart" button either on hover  or any page redirection/refresh (for Mobile Devices)</helpText>
            <helpText lang="de-DE">Ein Popup-Fenster mit dem Ladeprogramm und mit der Nachricht wird von dem Knopf "Warenkorb" entweder beim Schweben oder bei jeder Seitenumleitung / Aktualisierung geöffnet (für mobile Geräte)</helpText>
        </input-field>
        <input-field type="int">
            <name>ngsAchieveFreeShippingWidgetIconSizeMobile</name>
            <label>Basket Truck Icon Size for Mobile Devices (px)</label>
            <label lang="de-DE">Lastwagen-Symbol Größe im Korb für mobile Geräte (px)</label>
            <defaultValue>26</defaultValue>
            <min>8</min>
            <max>48</max>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetIconColorMobile</name>
            <label>Basket Truck Icon Color for Mobile Devices</label>
            <label lang="de-DE">Lastwagen-Symbol Farbe im Korb für mobile Geräte</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the color of truck which is displayed on navigation basket for Mobile devices</helpText>
            <helpText lang="de-DE">Dies ist die Farbe des Lastwagens, der im Navigationskorb für Mobilgeräte angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetBackgroundColorMobile</name>
            <label>Basket Info tool-tip Background Color for Mobile Devices</label>
            <label lang="de-DE">Tooltip Hintergrundfarbe der Information im Korb für mobile Geräte</label>
            <defaultValue>#FFFFFF</defaultValue>
            <helpText>This is the background color of tool-tip which appears on click on the truck button (on navigation basket) for Mobile devices</helpText>
            <helpText lang="de-DE">Dies ist die Hintergrundfarbe des Tooltips, das beim Klicken auf den Lastwagen-Knopf (auf dem Navigationskorb) für mobile Geräte erscheint</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetBorderColorMobile</name>
            <label>Basket Info tool-tip Border Color for Mobile Devices</label>
            <label lang="de-DE">Tooltip Rahmenfarbe der Information im Korb für mobile Geräte</label>
            <defaultValue>#DADAE5</defaultValue>
            <helpText>This is the border color of tool-tip which appears on click on the truck button (on navigation basket) for Mobile devices</helpText>
            <helpText lang="de-DE">Dies ist die Rahmenfarbe des Tooltips, das beim Klicken auf den Lastwagen-Knopf (auf dem Navigationskorb) für mobile Geräte erscheint</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingWidgetTextColorMobile</name>
            <label>Basket Info tool-tip Text Color for Mobile Devices</label>
            <label lang="de-DE">Die Textfarbe des Tooltips im Korb für mobile Geräte</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the text color of tool-tip which appears on click on the truck button (on navigation basket) for Mobile devices</helpText>
            <helpText lang="de-DE">Dies ist die Textfarbe des Tooltips, die beim Klicken auf den Lastwagen-Knopf (auf dem Navigationskorb) für Mobilgeräte erscheint</helpText>
        </input-field>
    </card>

    <card>
        <title>AJAX Cart configurations</title>
        <title lang="de-DE">AJAX Cart Konfiguration</title>

        <input-field type="bool">
            <name>ngsAchieveFreeShippingShowInBasket</name>
            <label>Display in Shopping Cart menu</label>
            <label lang="de-DE">Im Einkaufswagenmenü anzeigen</label>
            <defaultValue>true</defaultValue>
            <helpText>The loader with message will be shown in the Shopping Cart menu</helpText>
            <helpText lang="de-DE">Der Lader mit der Nachricht wird im Einkaufswagenmenü angezeigt</helpText>
        </input-field>
        <input-field type="int">
            <name>ngsAchieveFreeShippingBasketIconSize</name>
            <label>Shopping Cart menu Truck Size (px)</label>
            <label lang="de-DE">Lastwagen-Größe im Einkaufswagenmenü (px)</label>
            <defaultValue>36</defaultValue>
            <min>8</min>
            <max>48</max>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingBasketIconColor</name>
            <label>Shopping Cart menu Truck Color</label>
            <label lang="de-DE">Lastwagen-Farbe im Einkaufswagenmenü</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the color of truck which is displayed on right menu of the shopping cart</helpText>
            <helpText lang="de-DE">Dies ist die Farbe des Lastwagens, die im rechten Menü des Einkaufswagens angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingBasketBackgroundColor</name>
            <label>Shopping Cart menu Info Background Color</label>
            <label lang="de-DE">Hintergrundfarbe der Information im Einkaufswagenmenü</label>
            <defaultValue>#FFFFFF</defaultValue>
            <helpText>This is the background color of info which is displayed on right menu of the shopping cart</helpText>
            <helpText lang="de-DE">Dies ist die Hintergrundfarbe der Information, die im rechten Menü des Einkaufswagens angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingBasketBorderColor</name>
            <label>Shopping Cart menu Info Border Color</label>
            <label lang="de-DE">Rahmenfarbe der Information im Einkaufswagenmenü</label>
            <defaultValue>#DADAE5</defaultValue>
            <helpText>This is the color of info border which is displayed on right menu of the shopping cart</helpText>
            <helpText lang="de-DE">Dies ist die Farbe des Informationsrahmens, der im rechten Menü des Einkaufswagens angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingBasketTextColor</name>
            <label>Shopping Cart menu Info Text Color</label>
            <label lang="de-DE">Textfarbe der Information im Einkaufswagenmenü</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the color of info text which is displayed on right menu of the shopping cart</helpText>
            <helpText lang="de-DE">Dies ist die Farbe des Informationstextes, der im rechten Menü des Einkaufswagens angezeigt wird</helpText>
        </input-field>
    </card>

    <card>
        <title>Checkout configurations</title>
        <title lang="de-DE">Checkout Konfiguration</title>

        <input-field type="bool">
            <name>ngsAchieveFreeShippingShowInCheckoutCart</name>
            <label>Display on Checkout Cart page</label>
            <label lang="de-DE">Auf der Checkout-Warenkorbseite anzeigen</label>
            <defaultValue>true</defaultValue>
            <helpText>The loader with message will be shown on Checkout Cart page</helpText>
            <helpText lang="de-DE">Der Lader mit der Nachricht wird auf der Warenkorbseite angezeigt</helpText>
        </input-field>
        <input-field type="bool">
            <name>ngsAchieveFreeShippingShowInCheckoutConfirm</name>
            <label>Display on Checkout Confirm page</label>
            <label lang="de-DE">Auf der Checkout-Bestätigungsseite anzeigen</label>
            <defaultValue>true</defaultValue>
            <helpText>The loader with message will be shown on Checkout Confirm page</helpText>
            <helpText lang="de-DE">Der Lader mit der Nachricht wird auf der Checkout-Bestätigungsseite angezeigt</helpText>
        </input-field>
        <input-field type="int">
            <name>ngsAchieveFreeShippingCheckoutIconSize</name>
            <label>Checkout Page Truck Icon Size (px)</label>
            <label lang="de-DE">Die Lastwagen-Symbolgröße auf der Checkout-Seite (px)</label>
            <defaultValue>36</defaultValue>
            <min>8</min>
            <max>48</max>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingCheckoutIconColor</name>
            <label>Checkout Page Truck Icon Color</label>
            <label lang="de-DE">Die Lastwagen-Symbolfarbe auf der Checkout-Seite</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the color of truck which is displayed on Checkout pages</helpText>
            <helpText lang="de-DE">Dies ist die Farbe des Lastwagens, der auf den Checkoutseiten angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingCheckoutBackgroundColor</name>
            <label>Checkout Page Truck Background Color</label>
            <label lang="de-DE">Die Hintergrundfarbe der Lastwagen-Symbolfarbe auf der Checkout-Seite</label>
            <defaultValue>#FFFFFF</defaultValue>
            <helpText>This is the background color of truck which is displayed on Checkout pages</helpText>
            <helpText lang="de-DE">Dies ist die Hintergrundfarbe der Information, die auf den Checkout-Seiten angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingCheckoutBorderColor</name>
            <label>Checkout Page Truck Border Color</label>
            <label lang="de-DE">Die Rahmenfarbe der Lastwagen-Symbolfarbe auf der Checkout-Seite</label>
            <defaultValue>#DADAE5</defaultValue>
            <helpText>This is the border color of truck which is displayed on on Checkout pages</helpText>
            <helpText lang="de-DE">Dies ist die Rahmenfarbe der des Lastwagens, die auf den Checkout-Seiten angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingCheckoutTextColor</name>
            <label>Checkout Page Info Text Color</label>
            <label lang="de-DE">Die Farbe des Informationstextes auf der Checkout-Seite</label>
            <defaultValue>#4AA3DF</defaultValue>
            <helpText>This is the color of info text which is displayed on Checkout pages</helpText>
            <helpText lang="de-DE">Dies ist die Farbe des Informationstextes, der auf den Checkout-Seiten angezeigt wird</helpText>
        </input-field>
        <input-field type="colorpicker">
            <name>ngsAchieveFreeShippingCheckoutTextBackgroundColor</name>
            <label>Checkout Page Info Text Background Color</label>
            <label lang="de-DE">Die Hintergrundfarbe des Informationstextes auf der Checkout-Seite</label>
            <defaultValue>#DADAE5</defaultValue>
            <helpText>This is the background color of info text which is displayed on Checkout pages</helpText>
            <helpText lang="de-DE">Dies ist die Hintergrundfarbe des Informationstextes, der auf den Checkout-Seiten angezeigt wird</helpText>
        </input-field>
    </card>

</config>