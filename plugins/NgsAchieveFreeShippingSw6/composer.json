{"name": "ngs/achieve-free-shipping", "description": "Ngs Achieve Free Shipping", "version": "v1.1.0", "type": "shopware-platform-plugin", "license": "proprietary", "authors": [{"name": "Naghashyan Solutions LLC", "homepage": "https://naghashyan.com"}], "require": {"shopware/core": "~6.2 || ~6.5"}, "extra": {"shopware-plugin-class": "Ngs\\AchieveFreeShipping\\NgsAchieveFreeShippingSw6", "plugin-icon": "src/Resources/config/plugin.png", "label": {"de-DE": "Erreichen kostenlosen Versand", "en-GB": "Achieve Free Shipping"}, "description": {"de-DE": "'Erreichen kostenlosen Versand' -Plugin ist hier, um Ihren Kunden zu helfen, ihre Bestellungen zu organisieren, indem sie die kostenlose Versandmöglichkeit als Ziel haben. Sie haben vielleicht eine Frage, wie es gemacht werden könnte? Hier ist eine einfache Lösung. Dieses Plugin zeigt, wie viel Summe im Warenkorb übrig ist, bis der Nutzer den kostenlosen Versand erreicht. Das Plugin ist der beste Weg, um den Kunden anzuziehen, mehr Produkte zu kaufen, so werden die Verkäufe des Shopes wachsen.\n        Wenn der Benutzer einen Artikel zum Warenkorb hinzufügt, die verbleibenden Kosten, die für den kostenlosen Versand der Bestellung erforderlich sind, werden berechnet. Die Restkosten werden auf Basis des Parameters \"Versand frei von\" (Konfigurationen -> Versandkosten -> Versandmethode) berechnet. Auf der Website wird es mit einem Lader und einer kurzen Nachricht angezeigt. Mit der Hilfe der Konfigurationen kann Shop-Manager auswählen, auf welchen Seiten der Lader mit der Nachricht angezeigt werden soll, folgende Varianten sind verfügbar: Warenkorb, Checkout Cart Seite, Checkout Bestätigen Seite, Zahlung und Versand Seite. Darüber hinaus gibt es ein Popup-Fenster mit dem Lader, das von dem Knopf \"Warenkorb\" entweder beim Schweben oder bei jeder Seitenumleitung / Aktualisierung geöffnet wird. Das kleine \"Lastwagen\" -Symbol bleibt auf den Knopf, nachdem das Pop-up geschlossen wurde. Unter mobilen Geräten wird das Pop-up durch einen \"Lastwagen\" -Knopf neben dem Warenkorb ersetzt. Der Shop-Manager kann auch eine Standardlieferungsmethode festlegen, nach dem wird die kostenlose Lieferung verbleibenden Kosten gezählt, bis der Benutzer eine geeignete Methode wählt. Im Fall, wenn eine neue Versandmethode im Shop hinzugefügt wird, wird es automatisch in den Plugin-Konfigurationen angezeigt. Im Fall, wenn es mindestens ein kostenloser Versandartikel im Warenkorb gibt, wird der Lader mit der Nachricht nicht angezeigt, da der Versand schon frei sein wird. Es wird auch nicht angezeigt, wenn die Summe der Produktkosten im Warenkorb die freie Versandgrenze überschritten hat, nach dem freie Versandkosten angezeigt werden, bis der Benutzer eine beliebige Methode wählt.  Es gibt auch die Möglichkeit, Größe und Farbe des Symbols, des Textes usw. für verschiedene Seiten zu ändern. So wird das Plugin perfekt mit Ihrem Shop-Design passen.", "en-GB": "'Achieve Free Shipping' plugin is here to help your customers to organize their orders by having the free shipping opportunity as a goal. You may have a question how it could be done? Here is a simple solution. This plugin shows how much amount is left in the basket till user achieves the free shipping. The plugin is the best way to attract Customers to buy more products, so the sales of the shop will grow.\n        When the user adds an item to the basket, the remaining cost, that is required for free shipping of the order, is being calculated. Remaining cost is calculated based on the \"Shipping Free from\" parameter value (Configurations --> Shipping costs --> Shipping method). On the website this is displayed with a loader and a short message. With the help of configurations, the Shop Manager can choose on which pages the loader with message should be shown, the following variants are available: Basket, Checkout Cart page, Checkout Confirm page, Payment and Dispatch page.  In addition, there is a pop-up with the loader which opens from the \"Shopping Cart\" button either on hover  or any page redirection/refresh. The small ''truck'' icon stays on the button after pop-up closes. Under mobile devices, the pop-up is replaced with a \"truck\" button next to the basket. The Shop Manager can also set a default delivery method, according to which free delivery remaining cost will be counted until the user chooses a proper method. In case if a new shipping method is added in the shop, it will automatically appear in the plugin configurations. In case if there is at least one free shipping item in the basket, the loader with the message will not be shown, as the shipping will already be free. It will not be shown also when the sum of products cost in the Basket have crossed the free shipping border.  according to which free delivery cost will be displayed until the user chooses any method. There is also an opportunity to change colors of the icon, text etc. for different pages. So the plugin will perfectly match with your shop design."}}, "autoload": {"psr-4": {"Ngs\\AchieveFreeShipping\\": "src/"}}}