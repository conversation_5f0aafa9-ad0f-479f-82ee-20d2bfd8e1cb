<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <!-- Entities -->
        <service id="Acris\StockNotification\Custom\StockNotificationDefinition">
            <tag name="shopware.entity.definition" entity="acris_stock_notification" />
        </service>

        <service id="Acris\StockNotification\Custom\StockNotificationProductDefinition">
            <tag name="shopware.entity.definition" entity="acris_stock_notification_product" />
        </service>

        <!-- Extensions -->
        <service id="Acris\StockNotification\Custom\ProductExtension">
            <tag name="shopware.entity.extension"/>
        </service>

        <!-- Controller -->
        <service id="Acris\StockNotification\Storefront\Controller\StockNotificationController" public="true">
            <argument type="service" id="Acris\StockNotification\Core\Content\StockNotification\SalesChannel\StockNotificationNotifyRoute"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <call method="setTwig">
                <argument type="service" id="twig"/>
            </call>
        </service>

        <!-- Service -->
        <service id="Acris\StockNotification\Core\Content\StockNotification\SalesChannel\StockNotificationNotifyRoute" public="true">
            <argument type="service" id="acris_stock_notification.repository" />
            <argument type="service" id="acris_stock_notification_product.repository" />
            <argument type="service" id="Shopware\Core\Framework\Validation\DataValidator"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="shopware.rate_limiter" />
            <argument type="service" id="Symfony\Component\HttpFoundation\RequestStack" />
        </service>

        <service id="Acris\StockNotification\Core\Content\StockNotification\SalesChannel\StockNotificationConfirmRoute" public="true">
            <argument type="service" id="acris_stock_notification.repository" />
            <argument type="service" id="Shopware\Core\Framework\Validation\DataValidator"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <!-- Cart -->
        <service id="Acris\StockNotification\Core\Content\Product\Cart\ProductCartProcessor">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <tag name="shopware.cart.processor" priority="3000"/>
            <tag name="shopware.cart.collector" priority="5050"/>
        </service>

        <!-- Product -->
        <service id="Acris\StockNotification\Core\Content\Product\ProductMaxPurchaseCalculator" public="true" decorates="Shopware\Core\Content\Product\AbstractProductMaxPurchaseCalculator">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Acris\StockNotification\Core\Content\Product\ProductMaxPurchaseCalculator.inner"/>
        </service>

        <!-- Subscriber -->
        <service id="Acris\StockNotification\Storefront\Subscriber\StockNotificationSubscriber">
            <argument type="service" id="Acris\StockNotification\Core\Content\StockNotification\SalesChannel\StockNotificationConfirmRoute"/>
            <argument type="service" id="Shopware\Core\Framework\Event\BusinessEventCollector"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Acris\StockNotification\Storefront\Subscriber\StockNotificationElasticsearchSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <!-- Scheduled task -->
        <service id="Acris\StockNotification\ScheduledTask\StockNotificationTask">
            <tag name="shopware.scheduled.task"/>
        </service>

        <service id="Acris\StockNotification\ScheduledTask\StockNotificationTaskHandler">
            <argument type="service" id="scheduled_task.repository"/>
            <argument type="service" id="acris_stock_notification.repository"/>
            <argument type="service" id="acris_stock_notification_product.repository" />
            <argument type="service" id="language.repository"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="messenger.message_handler"/>
        </service>

        <!-- Resolver -->
        <service id="Acris\StockNotification\Core\Content\Cms\DataResolver\Element\StockNotificationResolver">
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Detail\ProductConfiguratorLoader"/>
            <argument type="service" id="product_review.repository"/>
            <tag name="shopware.cms.data_resolver"/>
        </service>

        <!-- Flows -->
        <service id="Acris\StockNotification\Core\Content\Flow\Dispatching\Storer\StockNotificationRecipientStorer">
            <argument type="service" id="acris_stock_notification.repository"/>
            <tag name="flow.storer"/>
        </service>
        <service id="Acris\StockNotification\Core\Content\Flow\Dispatching\Storer\UrlStorer">
            <tag name="flow.storer"/>
        </service>

        <!-- Decorated ElasticsearchProductDefinition service -->
        <service id="Acris\StockNotification\Elasticsearch\Product\ElasticsearchProductDefinitionDecorated" decorates="Shopware\Elasticsearch\Product\ElasticsearchProductDefinition" decoration-priority="-200">
            <argument type="service" id="Acris\StockNotification\Elasticsearch\Product\ElasticsearchProductDefinitionDecorated.inner"/>
            <tag name="shopware.es.definition"/>
        </service>
    </services>
</container>
