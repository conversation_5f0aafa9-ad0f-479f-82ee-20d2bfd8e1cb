/*! For license information please see acris-stock-notification-c-s.js.LICENSE.txt */
!function(t){var e={};function i(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,i),o.l=!0,o.exports}i.m=t,i.c=e,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)i.d(n,o,function(e){return t[e]}.bind(null,o));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p=(window.__sw__.assetPath + '/bundles/acrisstocknotificationcs/'),i(i.s="b9t+")}({"+Gdr":function(t){t.exports=JSON.parse('{"acris-stock-notification-products":{"general":{"mainMenuItemGeneral":"Übersicht nach Produkten","placeholderSearchBar":"Suche nach Lagerstands Benachrichtigung","description":"Lagerstands Benachrichtigung"},"list":{"columnProduct":"Produkt","columnProductNumber":"Produkt Nummer","columnRegisteredCustomers":"Registrierte Kunden","columnSentNotifications":"Gesendete Benachrichtigungen","columnOpenOptIn":"Verbleibende Benachrichtigungen","fieldSelectOptionNotSet":"E-Mail Adresse wurde registriert, Double-Opt-in ausständig.","fieldSelectOptionOptIn":"E-Mail Adresse erfolgreich für die Benachrichtigung registriert.","fieldSelectOptionNotified":"Lagerstandsinfo wurde per E-Mail versandt.","buttonDelete":"Löschen","modalTitleDelete":"Löschen","contentEmpty":"Es werden keine Lagerstands Benachrichtigungen eingefügt","textDeleteConfirm":"Möchten Sie die Lagerstands Benachrichtigung löschen?","buttonCancel":"Abbrechen","buttonAdd":"Hinzufügen","contextMenuEdit":"Bearbeiten","textHeadline":"Benachrichtigung über den Lagerstand von Produkten","titleSidebarItemRefresh":"Aktualisieren"},"detail":{"buttonCancel":"Abbrechen","buttonSave":"Speichern","cardTitle":"Einstellungen","fieldTitleLabelEmail":"Email","fieldTitlePlaceholderEmail":"Email eingeben...","fieldTitleLabelName":"Name","fieldTitlePlaceholderName":"Name eingeben...","fieldTitleLabelStatus":"Status","fieldTitlePlaceholderStatus":"Status auswählen...","fieldSelectOptionNotSet":"E-Mail Adresse wurde registriert, Double-Opt-in ausständig.","fieldSelectOptionOptIn":"E-Mail Adresse erfolgreich für die Benachrichtigung registriert.","fieldSelectOptionNotified":"Lagerstandsinfo wurde per E-Mail versandt.","fieldTitleLabelNotifiedDateTime":"Zuletzt benachrichtigt am","fieldTitleLabelHash":"Hash","fieldTitlePlaceholderDbPort":"Hash eingeben...","fieldTitleLabelNotified":"Benachrichtigt","fieldTitleHelpTextNotified":"Wenn Benachrichtigung aktiv ist, dann wird der Kunde bereits über die Produktverfügbarkeit informiert.","fieldTitleLabelProduct":"Produkt","fieldTitlePlaceholderProduct":"Produkt auswählen...","fieldTitleLabelSalesChannel":"Verkaufskanal","fieldTitlePlaceholderSalesChannel":"Verkaufskanal auswählen...","fieldTitleLabelLanguage":"Sprache","fieldTitlePlaceholderLanguage":"Sprache auswählen...","textHeadline":"Liste der registrierten Benutzer für das Produkt: ","titleNotificationError":"Fehler","titleNotificationSuccess":"Erfolg","messageSaveError":"Lagerstands Benachrichtigung konnte nicht gespeichert werden.","messageSaveSuccess":"Die Lagerstands Benachrichtigung wurde gespeichert."}}}')},"/Lyn":function(t,e,i){var n=i("zrtg");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("6c385b01",n,!0,{})},"0BRV":function(t){t.exports=JSON.parse('{"acris-stock-notification-products":{"general":{"mainMenuItemGeneral":"Overview based on products","placeholderSearchBar":"Search for stock notifications","description":"Stock notification"},"list":{"columnProduct":"Product","columnProductNumber":"Product number","columnRegisteredCustomers":"Registered customers","columnSentNotifications":"Sent notifications","columnOpenOptIn":"Remaining notifications","fieldSelectOptionNotSet":"E-mail address has been registered, double opt-in pending.","fieldSelectOptionOptIn":"E-mail address successfully registered to notify.","fieldSelectOptionNotified":"Stock info mail was sent.","buttonDelete":"Delete","modalTitleDelete":"Delete","contentEmpty":"No stock notifications are inserted","textDeleteConfirm":"Do you want to delete the stock notification?","buttonCancel":"Cancel","buttonAdd":"Add","contextMenuEdit":"Edit","textHeadline":"Stock notification for products","titleSidebarItemRefresh":"Refresh"},"detail":{"buttonCancel":"Cancel","buttonSave":"Save","cardTitle":"Settings","fieldTitleLabelEmail":"Email","fieldTitlePlaceholderEmail":"Enter email...","fieldTitleLabelName":"Customer name","fieldTitlePlaceholderName":"Enter customer name...","fieldTitleLabelStatus":"Status","fieldTitlePlaceholderStatus":"Select status...","fieldSelectOptionNotSet":"E-mail address has been registered, double opt-in pending.","fieldSelectOptionOptIn":"E-mail address successfully registered to notify.","fieldSelectOptionNotified":"Stock info mail was sent.","fieldTitleLabelHash":"Hash","fieldTitlePlaceholderDbPort":"Enter hash...","fieldTitleLabelNotified":"Notified","fieldTitleLabelNotifiedDateTime":"Last notified on","fieldTitleHelpTextNotified":"If notified is active, then customer is already notified for product availability.","fieldTitleLabelProduct":"Product","fieldTitlePlaceholderProduct":"Select product...","fieldTitleLabelSalesChannel":"Sales channel","fieldTitlePlaceholderSalesChannel":"Select sales channel...","fieldTitleLabelLanguage":"Language","fieldTitlePlaceholderLanguage":"Select language","textHeadline":"List of registered users for product:","titleNotificationError":"Error","titleNotificationSuccess":"Success","messageSaveError":"Stock notification could not be saved.","messageSaveSuccess":"Stock notification has been saved."}}}')},"6eI9":function(t,e){function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(){"use strict";n=function(){return t};var t={},e=Object.prototype,o=e.hasOwnProperty,r=Object.defineProperty||function(t,e,i){t[e]=i.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,i){return t[e]=i}}function d(t,e,i,n){var o=e&&e.prototype instanceof m?e:m,a=Object.create(o.prototype),c=new N(n||[]);return r(a,"_invoke",{value:S(t,i,c)}),a}function f(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}t.wrap=d;var p={};function m(){}function h(){}function g(){}var b={};u(b,c,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(P([])));y&&y!==e&&o.call(y,c)&&(b=y);var _=g.prototype=m.prototype=Object.create(b);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function n(r,a,c,s){var l=f(t[r],t,a);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==i(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,c,s)}),(function(t){n("throw",t,c,s)})):e.resolve(d).then((function(t){u.value=t,c(u)}),(function(t){return n("throw",t,c,s)}))}s(l.arg)}var a;r(this,"_invoke",{value:function(t,i){function o(){return new e((function(e,o){n(t,i,e,o)}))}return a=a?a.then(o,o):o()}})}function S(t,e,i){var n="suspendedStart";return function(o,r){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw r;return O()}for(i.method=o,i.arg=r;;){var a=i.delegate;if(a){var c=x(a,i);if(c){if(c===p)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var s=f(t,e,i);if("normal"===s.type){if(n=i.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:i.done}}"throw"===s.type&&(n="completed",i.method="throw",i.arg=s.arg)}}}function x(t,e){var i=e.method,n=t.iterator[i];if(void 0===n)return e.delegate=null,"throw"===i&&t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method)||"return"!==i&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+i+"' method")),p;var o=f(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,p;var r=o.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):r:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function P(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,n=function e(){for(;++i<t.length;)if(o.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=g,r(_,"constructor",{value:g,configurable:!0}),r(g,"constructor",{value:h,configurable:!0}),h.displayName=u(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,u(t,l,"GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},k(w.prototype),u(w.prototype,s,(function(){return this})),t.AsyncIterator=w,t.async=function(e,i,n,o,r){void 0===r&&(r=Promise);var a=new w(d(e,i,n,o),r);return t.isGeneratorFunction(i)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(_),u(_,l,"Generator"),u(_,c,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),i=[];for(var n in e)i.push(n);return i.reverse(),function t(){for(;i.length;){var n=i.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function i(i,n){return a.type="throw",a.arg=t,e.next=i,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n],a=r.completion;if("root"===r.tryLoc)return i("end");if(r.tryLoc<=this.prev){var c=o.call(r,"catchLoc"),s=o.call(r,"finallyLoc");if(c&&s){if(this.prev<r.catchLoc)return i(r.catchLoc,!0);if(this.prev<r.finallyLoc)return i(r.finallyLoc)}else if(c){if(this.prev<r.catchLoc)return i(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return i(r.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var r=n;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=t,a.arg=e,r?(this.method="next",this.next=r.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),E(i),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var o=n.arg;E(i)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,i){return this.delegate={iterator:P(t),resultName:e,nextLoc:i},"next"===this.method&&(this.arg=void 0),p}},t}function o(t,e,i,n,o,r,a){try{var c=t[r](a),s=c.value}catch(t){return void i(t)}c.done?e(s):Promise.resolve(s).then(n,o)}var r=Shopware,a=r.Component,c=r.State;a.override("sw-product-list",{inject:["systemConfigApiService"],methods:{getList:function(){var t,e=this;return(t=n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.getEmailNotificationConfig(),e.$super("getList");case 2:case"end":return t.stop()}}),t)})),function(){var e=this,i=arguments;return new Promise((function(n,r){var a=t.apply(e,i);function c(t){o(a,n,r,c,s,"next",t)}function s(t){o(a,n,r,c,s,"throw",t)}c(void 0)}))})()},getEmailNotificationConfig:function(){this.systemConfigApiService.getValues("AcrisStockNotificationCS.config").then((function(t){c.commit("acrisEmailNotificationState/setAcrisEmailNotification",t["AcrisStockNotificationCS.config.emailNotification"])}))}}})},"77XI":function(t){t.exports=JSON.parse('{"acris-stock-notification-list":{"general":{"mainMenuItemGeneral":"Stock notification","descriptionTextModule":"Email notifications for products out of stock"},"index":{"textHeadline":"Stock notification"}}}')},E116:function(t,e,i){},JUTR:function(t,e,i){},"M0/V":function(t){t.exports=JSON.parse('{"acris-stock-notification-list":{"general":{"mainMenuItemGeneral":"Lagerstands Benachrichtigung","descriptionTextModule":"Lagerstands Benachrichtigung für nicht langerde Artikel, die wieder lagernd sind."},"index":{"textHeadline":"Lagerstands Benachrichtigung"}}}')},MDyN:function(t,e,i){var n=i("clMx");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("2cf3f2d8",n,!0,{})},OJvX:function(t,e,i){},P8hj:function(t,e,i){"use strict";function n(t,e){for(var i=[],n={},o=0;o<e.length;o++){var r=e[o],a=r[0],c={id:t+":"+o,css:r[1],media:r[2],sourceMap:r[3]};n[a]?n[a].parts.push(c):i.push(n[a]={id:a,parts:[c]})}return i}i.r(e),i.d(e,"default",(function(){return m}));var o="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var r={},a=o&&(document.head||document.getElementsByTagName("head")[0]),c=null,s=0,l=!1,u=function(){},d=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(t,e,i,o){l=i,d=o||{};var a=n(t,e);return h(a),function(e){for(var i=[],o=0;o<a.length;o++){var c=a[o];(s=r[c.id]).refs--,i.push(s)}e?h(a=n(t,e)):a=[];for(o=0;o<i.length;o++){var s;if(0===(s=i[o]).refs){for(var l=0;l<s.parts.length;l++)s.parts[l]();delete r[s.id]}}}}function h(t){for(var e=0;e<t.length;e++){var i=t[e],n=r[i.id];if(n){n.refs++;for(var o=0;o<n.parts.length;o++)n.parts[o](i.parts[o]);for(;o<i.parts.length;o++)n.parts.push(b(i.parts[o]));n.parts.length>i.parts.length&&(n.parts.length=i.parts.length)}else{var a=[];for(o=0;o<i.parts.length;o++)a.push(b(i.parts[o]));r[i.id]={id:i.id,refs:1,parts:a}}}}function g(){var t=document.createElement("style");return t.type="text/css",a.appendChild(t),t}function b(t){var e,i,n=document.querySelector("style["+f+'~="'+t.id+'"]');if(n){if(l)return u;n.parentNode.removeChild(n)}if(p){var o=s++;n=c||(c=g()),e=_.bind(null,n,o,!1),i=_.bind(null,n,o,!0)}else n=g(),e=k.bind(null,n),i=function(){n.parentNode.removeChild(n)};return e(t),function(n){if(n){if(n.css===t.css&&n.media===t.media&&n.sourceMap===t.sourceMap)return;e(t=n)}else i()}}var v,y=(v=[],function(t,e){return v[t]=e,v.filter(Boolean).join("\n")});function _(t,e,i,n){var o=i?"":n.css;if(t.styleSheet)t.styleSheet.cssText=y(e,o);else{var r=document.createTextNode(o),a=t.childNodes;a[e]&&t.removeChild(a[e]),a.length?t.insertBefore(r,a[e]):t.appendChild(r)}}function k(t,e){var i=e.css,n=e.media,o=e.sourceMap;if(n&&t.setAttribute("media",n),d.ssrId&&t.setAttribute(f,e.id),o&&(i+="\n/*# sourceURL="+o.sources[0]+" */",i+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),t.styleSheet)t.styleSheet.cssText=i;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(i))}}},UfhG:function(t,e,i){},ZWMc:function(t,e,i){var n=i("nk+K");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("6758a934",n,!0,{})},"aN/t":function(t,e){Shopware.State.registerModule("acrisEmailNotificationState",{namespaced:!0,state:{isEmpty:!0,acrisEmailNotification:null},getters:{isEmpty:function(t){return t.isEmpty},acrisEmailNotification:function(t){return t.acrisEmailNotification}},mutations:{setIsEmpty:function(t,e){t.isEmpty=e},setAcrisEmailNotification:function(t,e){t.acrisEmailNotification=e||null}}})},"b9t+":function(t,e,i){"use strict";i.r(e);function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(){o=function(){return t};var t={},e=Object.prototype,i=e.hasOwnProperty,r=Object.defineProperty||function(t,e,i){t[e]=i.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,i){return t[e]=i}}function d(t,e,i,n){var o=e&&e.prototype instanceof m?e:m,a=Object.create(o.prototype),c=new N(n||[]);return r(a,"_invoke",{value:S(t,i,c)}),a}function f(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}t.wrap=d;var p={};function m(){}function h(){}function g(){}var b={};u(b,c,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(P([])));y&&y!==e&&i.call(y,c)&&(b=y);var _=g.prototype=m.prototype=Object.create(b);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(r,a,c,s){var l=f(t[r],t,a);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==n(d)&&i.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,c,s)}),(function(t){o("throw",t,c,s)})):e.resolve(d).then((function(t){u.value=t,c(u)}),(function(t){return o("throw",t,c,s)}))}s(l.arg)}var a;r(this,"_invoke",{value:function(t,i){function n(){return new e((function(e,n){o(t,i,e,n)}))}return a=a?a.then(n,n):n()}})}function S(t,e,i){var n="suspendedStart";return function(o,r){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw r;return O()}for(i.method=o,i.arg=r;;){var a=i.delegate;if(a){var c=x(a,i);if(c){if(c===p)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var s=f(t,e,i);if("normal"===s.type){if(n=i.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:i.done}}"throw"===s.type&&(n="completed",i.method="throw",i.arg=s.arg)}}}function x(t,e){var i=e.method,n=t.iterator[i];if(void 0===n)return e.delegate=null,"throw"===i&&t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method)||"return"!==i&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+i+"' method")),p;var o=f(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,p;var r=o.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):r:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function P(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(i.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=g,r(_,"constructor",{value:g,configurable:!0}),r(g,"constructor",{value:h,configurable:!0}),h.displayName=u(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,u(t,l,"GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},k(w.prototype),u(w.prototype,s,(function(){return this})),t.AsyncIterator=w,t.async=function(e,i,n,o,r){void 0===r&&(r=Promise);var a=new w(d(e,i,n,o),r);return t.isGeneratorFunction(i)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(_),u(_,l,"Generator"),u(_,c,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),i=[];for(var n in e)i.push(n);return i.reverse(),function t(){for(;i.length;){var n=i.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(i,n){return a.type="throw",a.arg=t,e.next=i,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o],a=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var c=i.call(r,"catchLoc"),s=i.call(r,"finallyLoc");if(c&&s){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(c){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var r=o;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=t,a.arg=e,r?(this.method="next",this.next=r.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),E(i),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var o=n.arg;E(i)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,i){return this.delegate={iterator:P(t),resultName:e,nextLoc:i},"next"===this.method&&(this.arg=void 0),p}},t}function r(t,e,i,n,o,r,a){try{var c=t[r](a),s=c.value}catch(t){return void i(t)}c.done?e(s):Promise.resolve(s).then(n,o)}function a(t){return function(){var e=this,i=arguments;return new Promise((function(n,o){var a=t.apply(e,i);function c(t){r(a,n,o,c,s,"next",t)}function s(t){r(a,n,o,c,s,"throw",t)}c(void 0)}))}}function c(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function s(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?c(Object(i),!0).forEach((function(e){l(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):c(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function l(t,e,i){return(e=function(t){var e=function(t,e){if("object"!==n(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var o=i.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===n(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var u=Shopware,d=u.Component,f=u.State,p=Shopware.Component.getComponentHelper().mapGetters;d.override("sw-product-deliverability-form",s(s({template:'{% block sw_product_deliverability_form_max_purchase_field %}\n    {% parent %}\n\n    {% block acris_stock_notification_send_email_notification_field %}\n        <sw-inherit-wrapper\n                v-if="product && product.customFields && notificationConfig != \'alwaysActive\'"\n                v-model="product.customFields.acris_stock_notification_email_notification"\n                :hasParent="!!parentProduct.id">\n            <template #content="props">\n\n                <sw-field\n                        type="switch"\n                        name="sw-field--product-send-notification"\n                        bordered\n                        :mapInheritance="props"\n                        :label="$tc(\'acris-stock-notification.detail.sendNotificationLabel\')"\n                        :helpText="$tc(\'acris-stock-notification.detail.sendNotificationHelpText\')"\n                        :disabled="props.isInherited || !allowEdit"\n                        :value="props.currentValue"\n                        @change="props.updateCurrentValue">\n                </sw-field>\n\n            </template>\n        </sw-inherit-wrapper>\n    {% endblock %}\n\n    {% block acris_stock_notification_send_email_notification_field_set_inactive %}\n        <sw-inherit-wrapper\n                v-if="product && product.customFields && notificationConfig == \'alwaysActive\'"\n                v-model="product.customFields.acris_stock_notification_email_notification_inactive"\n                :hasParent="!!parentProduct.id">\n            <template #content="props">\n\n                <sw-field\n                        type="switch"\n                        name="sw-field--product-send-notification-inactive"\n                        bordered\n                        :mapInheritance="props"\n                        :label="$tc(\'acris-stock-notification.detail.sendNotificationInactiveLabel\')"\n                        :helpText="$tc(\'acris-stock-notification.detail.sendNotificationInactiveHelpText\')"\n                        :disabled="props.isInherited || !allowEdit"\n                        :value="props.currentValue"\n                        @change="props.updateCurrentValue">\n                </sw-field>\n\n            </template>\n        </sw-inherit-wrapper>\n    {% endblock %}\n\n{% endblock %}\n',inject:["systemConfigApiService"],data:function(){return{notificationConfig:null}}},p("acrisEmailNotificationState",["isEmpty","acrisEmailNotification"])),{},{methods:{createdComponent:function(){var t=this;return a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.checkCustomFields(),t.notificationConfig=f.get("acrisEmailNotificationState").acrisEmailNotification,t.notificationConfig){e.next=5;break}return e.next=5,t.getEmailNotificationConfig();case 5:t.$super("createdComponent");case 6:case"end":return e.stop()}}),e)})))()},getEmailNotificationConfig:function(){var t=this;return a(o().mark((function e(){var i;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.systemConfigApiService.getValues("AcrisStockNotificationCS.config");case 2:i=e.sent,t.notificationConfig=i["AcrisStockNotificationCS.config.emailNotification"],f.commit("acrisEmailNotificationState/setAcrisEmailNotification",t.notificationConfig);case 5:case"end":return e.stop()}}),e)})))()},checkCustomFields:function(){this.product&&null==this.product.customFields&&(this.product.customFields={acris_stock_notification_email_notification:null,acris_stock_notification_email_notification_inactive:null})}}}));i("6eI9");function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(){h=function(){return t};var t={},e=Object.prototype,i=e.hasOwnProperty,n=Object.defineProperty||function(t,e,i){t[e]=i.value},o="function"==typeof Symbol?Symbol:{},r=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,i){return t[e]=i}}function l(t,e,i,o){var r=e&&e.prototype instanceof f?e:f,a=Object.create(r.prototype),c=new N(o||[]);return n(a,"_invoke",{value:S(t,i,c)}),a}function u(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var d={};function f(){}function p(){}function g(){}var b={};s(b,r,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(P([])));y&&y!==e&&i.call(y,r)&&(b=y);var _=g.prototype=f.prototype=Object.create(b);function k(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,r,a,c){var s=u(t[n],t,r);if("throw"!==s.type){var l=s.arg,d=l.value;return d&&"object"==m(d)&&i.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,c)}),(function(t){o("throw",t,a,c)})):e.resolve(d).then((function(t){l.value=t,a(l)}),(function(t){return o("throw",t,a,c)}))}c(s.arg)}var r;n(this,"_invoke",{value:function(t,i){function n(){return new e((function(e,n){o(t,i,e,n)}))}return r=r?r.then(n,n):n()}})}function S(t,e,i){var n="suspendedStart";return function(o,r){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw r;return O()}for(i.method=o,i.arg=r;;){var a=i.delegate;if(a){var c=x(a,i);if(c){if(c===d)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var s=u(t,e,i);if("normal"===s.type){if(n=i.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:i.done}}"throw"===s.type&&(n="completed",i.method="throw",i.arg=s.arg)}}}function x(t,e){var i=e.method,n=t.iterator[i];if(void 0===n)return e.delegate=null,"throw"===i&&t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method)||"return"!==i&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+i+"' method")),d;var o=u(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var r=o.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,d):r:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function P(t){if(t){var e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(i.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=g,n(_,"constructor",{value:g,configurable:!0}),n(g,"constructor",{value:p,configurable:!0}),p.displayName=s(g,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},k(w.prototype),s(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,i,n,o,r){void 0===r&&(r=Promise);var a=new w(l(e,i,n,o),r);return t.isGeneratorFunction(i)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(_),s(_,c,"Generator"),s(_,r,(function(){return this})),s(_,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),i=[];for(var n in e)i.push(n);return i.reverse(),function t(){for(;i.length;){var n=i.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(i,n){return a.type="throw",a.arg=t,e.next=i,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o],a=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var c=i.call(r,"catchLoc"),s=i.call(r,"finallyLoc");if(c&&s){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(c){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var r=o;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=t,a.arg=e,r?(this.method="next",this.next=r.finallyLoc,d):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),E(i),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var o=n.arg;E(i)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,i){return this.delegate={iterator:P(t),resultName:e,nextLoc:i},"next"===this.method&&(this.arg=void 0),d}},t}function g(t,e,i,n,o,r,a){try{var c=t[r](a),s=c.value}catch(t){return void i(t)}c.done?e(s):Promise.resolve(s).then(n,o)}var b=Shopware,v=b.Component,y=b.Mixin,_=Shopware.Data.Criteria;v.register("acris-stock-notification-list",{template:'{% block acris_stock_notification_list %}\n\n<sw-page class="acris-stock-notification-list">\n    {% block acris_stock_notification_list_search_bar %}\n        <template slot="search-bar">\n            <sw-search-bar initialSearchType="Stock notfication"\n                           :placeholder="$tc(\'acris-stock-notification.general.placeholderSearchBar\')"\n                           :initialSearch="term"\n                           @search="onSearch">\n            </sw-search-bar>\n        </template>\n    {% endblock %}\n\n    {% block acris_stock_notification_list_smart_bar_header %}\n        <template slot="smart-bar-header">\n            {% block acris_stock_notification_list_smart_bar_header_title %}\n                <h2>\n                    {% block acris_stock_notification_list_smart_bar_header_title_text %}\n                        {{ $tc(\'acris-stock-notification.list.textHeadline\') }}\n                        {% endblock %}\n\n                        {% block acris_stock_notification_list_smart_bar_header_amount %}\n                        <span v-if="!isLoading" class="sw-page__smart-bar-amount">\n                        ({{ total }})\n                        </span>\n                    {% endblock %}\n                </h2>\n            {% endblock %}\n        </template>\n    {% endblock %}\n\n    {% block acris_stock_notification_list_smart_bar_actions %}\n    <template slot="smart-bar-actions">\n        {% block acris_stock_notification_list_smart_bar_actions_add %}\n        <sw-button variant="primary" :routerLink="{ name: \'acris.stock.notification.create\' }">\n            {{ $tc(\'acris-stock-notification.list.buttonAdd\') }}\n        </sw-button>\n        {% endblock %}\n    </template>\n    {% endblock %}\n\n    {% block acris_stock_notification_list_cardview %}\n    <template slot="content">\n        <div class="acris-stock-notification-list__content">\n            {% block acris_stock_notification_list_content %}\n            <sw-entity-listing class="acris-stock-notification-list-grid"\n                               v-if="items && total > 0"\n                               :items="items"\n                               :repository="entityRepository"\n                               detailRoute="acris.stock.notification.detail"\n                               :showSeleciton="true"\n                               :isLoading="isLoading"\n                               :columns="columns">\n\n                {% block acris_stock_notification_list_grid_columns_status %}\n                <template #column-status="{ item }">\n                    {% block acris_stock_notification_list_grid_columns_status_content %}\n                    <template>\n                        <router-link :title="$tc(\'acris-stock-notification.list.contextMenuEdit\')"\n                                      :to="{ name: \'acris.stock.notification.detail\', params: { id: item.id } }">\n                            {% block acris_stock_notification_list_columns_status_link %}\n                            <template v-if="item.status == \'notSet\'">{{ $tc(\'acris-stock-notification.list.fieldSelectOptionNotSet\') }}</template>\n                            <template v-if="item.status == \'optIn\'">{{ $tc(\'acris-stock-notification.list.fieldSelectOptionOptIn\') }}</template>\n                            <template v-if="item.status == \'notified\'">{{ $tc(\'acris-stock-notification.list.fieldSelectOptionNotified\') }}</template>\n                            {% endblock %}\n                        </router-link>\n                    </template>\n                    {% endblock %}\n                </template>\n                {% endblock %}\n\n                {% block acris_stock_notification_list_grid_columns_notified %}\n                    <template #column-notified="{ item }">\n                        <sw-icon name="regular-checkmark-xs" small v-if="item.notified" class="is--active"></sw-icon>\n                        <sw-icon name="regular-times-s" small v-else class="is--inactive"></sw-icon>\n                    </template>\n                {% endblock %}\n\n                {% block acris_stock_notification_list_grid_columns_notified_date_time %}\n                    <template #column-notifiedDateTime="{ item }">\n                        {{ item.notifiedDateTime | date({hour: \'2-digit\', minute: \'2-digit\', second: \'2-digit\'}) }}\n                    </template>\n                {% endblock %}\n\n                {% block acris_stock_notification_list_grid_columns_created_at %}\n                    <template #column-createdAt="{ item }">\n                        {{ item.createdAt | date({hour: \'2-digit\', minute: \'2-digit\', second: \'2-digit\'}) }}\n                    </template>\n                {% endblock %}\n\n                <template #pagination>\n                    {% block sw_order_list_grid_pagination %}\n                    <sw-pagination :page="page"\n                                   :limit="limit"\n                                   :total="total"\n                                   :total-visible="7"\n                                   @page-change="onPageChange">\n                    </sw-pagination>\n                    {% endblock %}\n                </template>\n\n            </sw-entity-listing>\n            {% endblock %}\n\n            {% block acris_stock_notification_list_empty_state %}\n            <sw-empty-state v-if="!isLoading && total <= 0" :title="$tc(\'acris-stock-notification.list.contentEmpty\')"></sw-empty-state>\n            {% endblock %}\n\n        </div>\n    </template>\n    {% endblock %}\n\n    {% block acris_stock_notification_list_sidebar %}\n    <template #sidebar>\n        <sw-sidebar class="acris-stock-notification-list__sidebar">\n            {% block acris_stock_notification_list_sidebar_item %}\n            <sw-sidebar-item\n                icon="regular-undo"\n                :title="$tc(\'acris-stock-notification.list.titleSidebarItemRefresh\')"\n                @click="onRefresh">\n            </sw-sidebar-item>\n            {% endblock %}\n            <sw-sidebar-filter-panel\n                    entity="acris_stock_notification"\n                    :store-key="storeKey"\n                    :active-filter-number="activeFilterNumber"\n                    :filters="listFilters"\n                    :defaults="defaultFilters"\n                    @criteria-changed="updateCriteria"\n            />\n        </sw-sidebar>\n    </template>\n    {% endblock %}\n\n</sw-page>\n{% endblock %}\n',inject:["repositoryFactory","filterFactory"],mixins:[y.getByName("listing"),y.getByName("notification"),y.getByName("placeholder")],data:function(){return{items:null,isLoading:!1,showDeleteModal:!1,repository:null,total:0,activeFilterNumber:0,filterCriteria:[],defaultFilters:["notified-filter"],storeKey:"grid.filter.acris_stock_notification"}},metaInfo:function(){return{title:this.$createTitle()}},watch:{notificationCriteria:{handler:function(){this.getList()},deep:!0}},computed:{entityRepository:function(){return this.repositoryFactory.create("acris_stock_notification")},listFilters:function(){return this.filterFactory.create("acris_stock_notification",this.listFilterOptions)},listFilterOptions:function(){return{"notified-filter":{property:"notified",label:this.$tc("acris-stock-notification.list.notificationFilterLabel")}}},columns:function(){return this.getColumns()},notificationCriteria:function(){var t=new _(this.page,this.limit);return t.setTerm(this.term),this.filterCriteria.forEach((function(e){t.addFilter(e)})),t}},methods:{getList:function(){var t,e=this;return(t=h().mark((function t(){var i;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Shopware.Service("filterService").mergeWithStoredFilters(e.storeKey,e.notificationCriteria);case 3:i=t.sent,e.activeFilterNumber=i.filters.length,i.setTerm(e.term),i.addAssociation("product"),i.addAssociation("salesChannel"),i.addAssociation("language"),e.entityRepository.search(i,Shopware.Context.api).then((function(t){return e.total=t.total,e.items=t,e.isLoading=!1,t})).catch((function(){e.isLoading=!1}));case 10:case"end":return t.stop()}}),t)})),function(){var e=this,i=arguments;return new Promise((function(n,o){var r=t.apply(e,i);function a(t){g(r,n,o,a,c,"next",t)}function c(t){g(r,n,o,a,c,"throw",t)}a(void 0)}))})()},onDelete:function(t){this.showDeleteModal=t},onCloseDeleteModal:function(){this.showDeleteModal=!1},getColumns:function(){return[{property:"createdAt",label:"acris-stock-notification.list.columnCreatedAt",routerLink:"acris.stock.notification.detail",align:"center",allowResize:!0},{property:"email",inlineEdit:"string",label:"acris-stock-notification.list.columnEmail",routerLink:"acris.stock.notification.detail",allowResize:!0,primary:!0},{property:"name",inlineEdit:"string",label:"acris-stock-notification.list.columnName",routerLink:"acris.stock.notification.detail",allowResize:!0,primary:!0},{property:"product.name",label:"acris-stock-notification.list.columnProduct",routerLink:"acris.stock.notification.products.detail",allowResize:!0},{property:"product.productNumber",label:"acris-stock-notification.list.columnProductNumber",routerLink:"acris.stock.notification.products.detail",allowResize:!0},{property:"status",label:"acris-stock-notification.list.columnStatus",routerLink:"acris.stock.notification.detail",allowResize:!0},{property:"notified",label:"acris-stock-notification.list.columnNotified",routerLink:"acris.stock.notification.detail",align:"center",allowResize:!0},{property:"notifiedDateTime",label:"acris-stock-notification.list.columnNotifiedDateTime",routerLink:"acris.stock.notification.detail",align:"center",allowResize:!0},{property:"salesChannel.name",label:"acris-stock-notification.list.columnSalesChannel",routerLink:"acris.stock.notification.detail",allowResize:!0},{property:"language.name",label:"acris-stock-notification.list.columnLanguage",routerLink:"acris.stock.notification.detail",allowResize:!0}]},updateCriteria:function(t){this.page=1,this.filterCriteria=t}}});var k=Shopware.Component,w=Shopware.Utils;k.extend("acris-stock-notification-create","acris-stock-notification-detail",{template:"",beforeRouteEnter:function(t,e,i){t.name.includes("acris.stock.notification.create")&&!t.params.id&&(t.params.id=w.createId(),t.params.newItem=!0),i()},methods:{getEntity:function(){this.item=this.repository.create(Shopware.Context.api),this.item.hash=w.createId(),this.item.status="optIn",this.item.notified=!1},saveFinish:function(){this.isSaveSuccessful=!1,this.$router.push({name:"acris.stock.notification.detail",params:{id:this.item.id}})},onClickSave:function(){var t=this;this.isLoading=!0;var e=this.$tc("acris-stock-notification.detail.titleNotificationError"),i=this.$tc("acris-stock-notification.detail.messageSaveError"),n=this.$tc("acris-stock-notification.detail.titleNotificationSuccess"),o=this.$tc("acris-stock-notification.detail.messageSaveSuccess");this.repository.save(this.item,Shopware.Context.api).then((function(){t.isLoading=!1,t.createNotificationSuccess({title:n,message:o}),t.$router.push({name:"acris.stock.notification.detail",params:{id:t.item.id}})})).catch((function(){t.isLoading=!1,t.createNotificationError({title:e,message:i})}))}}});function S(t){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function x(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function L(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?x(Object(i),!0).forEach((function(e){E(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):x(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function E(t,e,i){return(e=function(t){var e=function(t,e){if("object"!==S(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!==S(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===S(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var N=Shopware.Component,P=Shopware.Data.Criteria,O=Shopware.Mixin,C=Shopware.Component.getComponentHelper().mapPropertyErrors;N.register("acris-stock-notification-detail",{template:'{% block acris_stock_notification_detail %}\n<sw-page class="acris-stock-notification-detail" v-if="item">\n\n    {% block acris_stock_notification_detail_smart_bar_header %}\n    <template slot="smart-bar-header">\n        {% block acris_stock_notification_detail_smart_bar_header_title %}\n        <h2>{{ $tc(\'acris-stock-notification.detail.textHeadline\') }}</h2>\n        {% endblock %}\n    </template>\n    {% endblock %}\n\n    {% block acris_stock_notification_detail_smart_bar_actions %}\n    <template slot="smart-bar-actions">\n        {% block acris_stock_notification_detail_smart_bar_actions_cancel %}\n        <sw-button :disabled="item.isLoading" :routerLink="{ name: \'acris.stock.notification.index\' }">\n            {{ $tc(\'acris-stock-notification.detail.buttonCancel\') }}\n        </sw-button>\n        {% endblock %}\n\n        {% block acris_stock_notification_detail_smart_bar_actions_save %}\n        <sw-button-process\n            class="acris-stock-notification-detail__save-action"\n            :isLoading="isLoading"\n            :processSuccess="processSuccess"\n            variant="primary"\n            :disabled="isLoading || !item.email || !item.name || !item.productId || !item.salesChannelId || !item.languageId"\n            @process-finish="saveFinish"\n            @click="onClickSave">\n            {{ $tc(\'acris-stock-notification.detail.buttonSave\') }}\n        </sw-button-process>\n        {% endblock %}\n    </template>\n    {% endblock %}\n\n\n    {% block acris_stock_notification_detail_content %}\n    <sw-card-view slot="content">\n\n        {% block acris_stock_notification_detail_content_card %}\n        <sw-card :isLoading="isLoading" v-if="item"\n                 :title="$tc(\'acris-stock-notification.detail.cardTitle\')">\n\n            {% block acris_stock_notification_detail_content_company_created_at %}\n                <template v-if="item.createdAt"></template><div class="sw-field--switch sw-field__label">{{ $tc(\'acris-stock-notification.detail.fieldTitleLabelCreatedAt\') }}: <strong>{{ item.createdAt | date({hour: \'2-digit\', minute: \'2-digit\', second: \'2-digit\'}) }}</strong></div>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_email %}\n            <sw-email-field\n                required\n                class="acris-stock-notification-detail__item_email"\n                :error="itemEmailError"\n                :label="$tc(\'acris-stock-notification.detail.fieldTitleLabelEmail\')"\n                :placeholder="$tc(\'acris-stock-notification.detail.fieldTitlePlaceholderEmail\')"\n                v-model="item.email">\n            </sw-email-field>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_name %}\n            <sw-text-field v-model="item.name"\n                           required\n                           class="acris-stock-notification-detail__item_name"\n                           :label="$tc(\'acris-stock-notification.detail.fieldTitleLabelName\')"\n                           :placeholder="$tc(\'acris-stock-notification.detail.fieldTitlePlaceholderName\')">\n            </sw-text-field>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_product %}\n                <sw-entity-single-select v-model="item.productId"\n                                         entity="product"\n                                         required\n                                         :label="$tc(\'acris-stock-notification.detail.fieldTitleLabelProduct\')"\n                                         :placeholder="$tc(\'acris-stock-notification.detail.fieldTitlePlaceholderProduct\')">\n                </sw-entity-single-select>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_status %}\n            <sw-single-select v-model="item.status"\n                          disabled\n                           :options="options"\n                           class="acris-stock-notification-detail__item_status"\n                           :label="$tc(\'acris-stock-notification.detail.fieldTitleLabelStatus\')"\n                           :placeholder="$tc(\'acris-stock-notification.detail.fieldTitlePlaceholderStatus\')">\n            </sw-single-select>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_company_notified_checkbox_select %}\n            <sw-field v-model="item.notified"\n                      type="switch"\n                      disabled\n                      class="acris-stock-notification-detail__item_notified"\n                      :label="$tc(\'acris-stock-notification.detail.fieldTitleLabelNotified\')"\n                      :helpText="$tc(\'acris-stock-notification.detail.fieldTitleHelpTextNotified\')">\n            </sw-field>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_company_notified_date_time %}\n                <template v-if="item.notifiedDateTime"></template><div class="sw-field--switch sw-field__label">{{ $tc(\'acris-stock-notification.detail.fieldTitleLabelNotifiedDateTime\') }}: <strong>{{ item.notifiedDateTime | date({hour: \'2-digit\', minute: \'2-digit\', second: \'2-digit\'}) }}</strong></div>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_sales_channel %}\n            <sw-entity-single-select v-model="item.salesChannelId"\n                                     entity="sales_channel"\n                                     required\n                                     :label="$tc(\'acris-stock-notification.detail.fieldTitleLabelSalesChannel\')"\n                                     :placeholder="$tc(\'acris-stock-notification.detail.fieldTitlePlaceholderSalesChannel\')">\n            </sw-entity-single-select>\n            {% endblock %}\n\n            {% block acris_stock_notification_detail_content_language %}\n            <sw-entity-single-select v-model="item.languageId"\n                                     entity="language"\n                                     required\n                                     :label="$tc(\'acris-stock-notification.detail.fieldTitleLabelLanguage\')"\n                                     :placeholder="$tc(\'acris-stock-notification.detail.fieldTitlePlaceholderLanguage\')">\n            </sw-entity-single-select>\n            {% endblock %}\n\n        </sw-card>\n        {% endblock %}\n\n    </sw-card-view>\n    {% endblock %}\n\n</sw-page>\n{% endblock %}\n',inject:["repositoryFactory","context"],mixins:[O.getByName("notification"),O.getByName("placeholder")],data:function(){return{item:null,isLoading:!1,processSuccess:!1,repository:null,isSaveSuccessful:!1}},metaInfo:function(){return{title:this.$createTitle()}},created:function(){this.createdComponent()},computed:L(L({},C("item",["email"])),{},{stockNotificationCriteria:function(){var t=new P;return t.addAssociation("product"),t.addAssociation("salesChannel"),t},options:function(){return[{label:this.$tc("acris-stock-notification.detail.fieldSelectOptionNotSet"),value:"notSet"},{label:this.$tc("acris-stock-notification.detail.fieldSelectOptionOptIn"),value:"optIn"},{label:this.$tc("acris-stock-notification.detail.fieldSelectOptionNotified"),value:"notified"}]}}),methods:{createdComponent:function(){this.repository=this.repositoryFactory.create("acris_stock_notification"),this.getEntity()},getEntity:function(){var t=this;this.repository.get(this.$route.params.id,Shopware.Context.api,this.stockNotificationCriteria).then((function(e){t.item=e}))},onClickSave:function(){var t=this;this.isLoading=!0;var e=this.$tc("acris-stock-notification.detail.titleNotificationError"),i=this.$tc("acris-stock-notification.detail.messageSaveError"),n=this.$tc("acris-stock-notification.detail.titleNotificationSuccess"),o=this.$tc("acris-stock-notification.detail.messageSaveSuccess");this.isSaveSuccessful=!1,this.isLoading=!0,this.repository.save(this.item,Shopware.Context.api).then((function(){t.getEntity(),t.isLoading=!1,t.processSuccess=!0,t.createNotificationSuccess({title:n,message:o})})).catch((function(){t.isLoading=!1,t.createNotificationError({title:e,message:i})}))},saveFinish:function(){this.processSuccess=!1}}});i("/Lyn");var T=i("qAn5"),j=i("f5v3");Shopware.Module.register("acris-stock-notification",{type:"plugin",name:"acris-stock-notification",title:"acris-stock-notification.general.mainMenuItemGeneral",description:"acris-stock-notification.general.description",color:"#9AA8B5",icon:"regular-bell",favicon:"icon-module-settings.png",snippets:{"de-DE":T,"en-GB":j},routes:{index:{component:"acris-stock-notification-list",path:"index",meta:{parentPath:"acris.stock.notification.list.index"}},detail:{component:"acris-stock-notification-detail",path:"detail/:id",meta:{parentPath:"acris.stock.notification.index"}},create:{component:"acris-stock-notification-create",path:"create",meta:{parentPath:"acris.stock.notification.index"}}}});var I=Shopware.Mixin;Shopware.Component.register("acris-stock-notification-index",{template:'{% block acris_stock_notification_index %}\n    <sw-page class="acris-stock-notification-index sw-settings-index">\n        {% block acris_stock_notification_index_smart_bar_header %}\n            <template slot="smart-bar-header">\n                {% block acris_stock_notification_index_smart_bar_header_title %}\n                    <h2>\n                        {% block acris_stock_notification_index_smart_bar_header_title_text %}\n                            {{ $tc(\'acris-stock-notification-list.index.textHeadline\') }}\n                        {% endblock %}\n                    </h2>\n                {% endblock %}\n            </template>\n        {% endblock %}\n\n        {% block acris_stock_notification_index_content %}\n            <template #content>\n                {% block acris_stock_notification_index_content_card_view %}\n                    <sw-card-view>\n                        {% block acris_stock_notification_index_content_card %}\n                            <sw-card class="acris-stock-notification-list_index__card">\n                                {% block acris_stock_notification_index_content_grid %}\n                                    <div class="sw-settings__content-grid">\n                                        {% block acris_stock_notification_index_card_item %}\n                                            {% block acris_stock_notification %}\n                                                <sw-settings-item\n                                                    :label="$tc(\'acris-stock-notification.general.mainMenuItemGeneral\')"\n                                                    :to="{ name: \'acris.stock.notification.index\' }"\n                                                    class="acris-settings-item">\n                                                    <template slot="icon">\n                                                        <sw-icon name="regular-question-circle"></sw-icon>\n\n                                                    </template>\n                                                </sw-settings-item>\n                                            {% endblock %}\n                                            {% block acris_stock_notification_products %}\n                                                <sw-settings-item\n                                                    :label="$tc(\'acris-stock-notification-products.general.mainMenuItemGeneral\')"\n                                                    :to="{ name: \'acris.stock.notification.products.index\' }"\n                                                    class="acris-settings-item">\n                                                    <template slot="icon">\n                                                        <sw-icon name="regular-bars-circle"></sw-icon>\n                                                    </template>\n                                                </sw-settings-item>\n                                            {% endblock %}\n                                        {% endblock %}\n                                    </div>\n                                {% endblock %}\n                            </sw-card>\n                        {% endblock %}\n                    </sw-card-view>\n                {% endblock %}\n            </template>\n        {% endblock %}\n    </sw-page>\n{% endblock %}\n',mixins:[I.getByName("listing"),I.getByName("notification"),I.getByName("placeholder")],metaInfo:function(){return{title:this.$createTitle()}}});i("fBxN");var $=i("M0/V"),D=i("77XI");Shopware.Module.register("acris-stock-notification-list",{type:"plugin",name:"acris-stock-notification",title:"acris-stock-notification-list.general.mainMenuItemGeneral",description:"acris-stock-notification-list.general.descriptionTextModule",version:"1.0.0",targetVersion:"1.0.0",color:"#a6c836",icon:"regular-question-circle",snippets:{"de-DE":$,"en-GB":D},routes:{index:{component:"acris-stock-notification-index",path:"index",icon:"regular-question-circle",meta:{parentPath:"sw.settings.index"}}},settingsItem:[{name:"acris-stock-notification-list-index",to:"acris.stock.notification.list.index",label:"acris-stock-notification-list.general.mainMenuItemGeneral",group:"plugins",icon:"regular-question-circle"}]});i("ZWMc");var F=Shopware,M=F.Component,A=F.Mixin,B=Shopware.Data.Criteria;M.register("acris-stock-notification-products-list",{template:'{% block acris_stock_notification_products_list %}\n    <sw-page class="acris-stock-notification-products-list">\n        {% block acris_stock_notification_products_list_search_bar %}\n            <template slot="search-bar">\n                <sw-search-bar initialSearchType="Stock notfication"\n                               :placeholder="$tc(\'acris-stock-notification-products.general.placeholderSearchBar\')"\n                               :initialSearch="term"\n                               @search="onSearch">\n                </sw-search-bar>\n            </template>\n        {% endblock %}\n\n        {% block acris_stock_notification_products_list_smart_bar_header %}\n            <template slot="smart-bar-header">\n                {% block acris_stock_notification_products_list_smart_bar_header_title %}\n                    <h2>\n                        {% block acris_stock_notification_products_list_smart_bar_header_title_text %}\n                            {{ $tc(\'acris-stock-notification-products.list.textHeadline\') }}\n                        {% endblock %}\n\n                        {% block acris_stock_notification_products_list_smart_bar_header_amount %}\n                            <span v-if="!isLoading" class="sw-page__smart-bar-amount">\n                        ({{ total }})\n                        </span>\n                        {% endblock %}\n                    </h2>\n                {% endblock %}\n            </template>\n        {% endblock %}\n\n        {% block acris_stock_notification_products_list_cardview %}\n            <template slot="content">\n                <div class="acris-stock-notification-products-list__content">\n                    {% block acris_stock_notification_products_list_content %}\n                        <sw-entity-listing class="acris-stock-notification-products-list-grid"\n                                           v-if="items && total > 0"\n                                           :items="items"\n                                           :repository="entityRepository"\n                                           detailRoute="acris.stock.notification.products.detail"\n                                           :showSeleciton="true"\n                                           :isLoading="isLoading"\n                                           :columns="columns">\n\n                            {% block acris_stock_notification_products_list_grid_columns_product_name %}\n                                <template #column-product.name="{ item }">\n                                    <router-link :title="$tc(\'acris-stock-notification-products.list.columnProduct\')"\n                                                 target="_blank"\n                                                 v-if="item.product.parentId"\n                                                 :to="{ name: \'sw.product.detail\', params: { id: item.productId } }">\n                                        <sw-product-variant-info :variations="item.product.variation"\n                                                                 class="acris-stock-notification-products-variant-info">\n                                            {{ item.product.translated.name || item.product.name }}\n                                        </sw-product-variant-info>\n                                    </router-link>\n\n                                    <router-link :title="$tc(\'acris-stock-notification-products.list.columnProduct\')"\n                                                 target="_blank"\n                                                 v-else\n                                                 :to="{ name: \'sw.product.detail\', params: { id: item.productId } }">\n                                        {{ item.product.translated.name || item.product.name }}\n                                    </router-link>\n                                </template>\n                            {% endblock %}\n\n                            {% block acris_stock_notification_products_list_grid_columns_product_number %}\n                                <template #column-product.productNumber="{ item }">\n                                    <router-link\n                                        :title="$tc(\'acris-stock-notification-products.list.columnProductNumber\')"\n                                        target="_blank"\n                                        :to="{ name: \'sw.product.detail\', params: { id: item.productId } }">\n                                        {{ item.product.productNumber }}\n                                    </router-link>\n                                </template>\n                            {% endblock %}\n\n                            {% block acris_stock_notification_products_list_grid_columns_registered %}\n                                <template #column-registered="{ item }">\n                                    <div\n                                        v-if="item.product.extensions.acrisStockNotifications && item.product.extensions.acrisStockNotifications.length > 0">\n                                        <router-link\n                                            :title="$tc(\'acris-stock-notification-products.list.columnRegisteredCustomers\')"\n                                            :to="{ name: \'acris.stock.notification.products.detail\', params: { id: item.product.extensions.acrisStockNotifications.first().id } }">\n                                            {{ item.registered }}\n                                        </router-link>\n                                    </div>\n                                    <div v-else>\n                                        0\n                                    </div>\n                                </template>\n                            {% endblock %}\n\n                            {% block acris_stock_notification_products_list_grid_columns_sent %}\n                                <template #column-sent="{ item }">\n                                    <div\n                                        v-if="item.product.extensions.acrisStockNotifications && item.product.extensions.acrisStockNotifications.length > 0">\n                                        <router-link\n                                                :title="$tc(\'acris-stock-notification-products.list.columnSentNotifications\')"\n                                                :to="{ name: \'acris.stock.notification.products.detail\', params: { id: item.product.extensions.acrisStockNotifications.first().id },\n                                                        query: {\n                                                            limit: 25,\n                                                            \'grid.filter.acris_stock_notification_product\': JSON.stringify({\n                                                              \'notified-filter\': {\n                                                                value: \'true\',\n                                                                criteria: [{ type: \'equals\', field: \'notified\', value: true }]\n                                                              }\n                                                        })\n                                                      }}">\n                                            {{ item.sent }}\n                                        </router-link>\n                                    </div>\n                                    <div v-else>\n                                        0\n                                    </div>\n                                </template>\n                            {% endblock %}\n\n                            {% block acris_stock_notification_products_list_grid_columns_open %}\n                                <template #column-open="{ item }">\n                                    <div\n                                        v-if="item.product.extensions.acrisStockNotifications && item.product.extensions.acrisStockNotifications.length > 0">\n                                        <router-link\n                                                :title="$tc(\'acris-stock-notification-products.list.columnOpenOptIn\')"\n                                                :to="{ name: \'acris.stock.notification.products.detail\', params: { id: item.product.extensions.acrisStockNotifications.first().id },\n                                                query: {\n                                                        limit: 25,\n                                                        \'grid.filter.acris_stock_notification_product\': JSON.stringify({\n                                                          \'notified-filter\': {\n                                                            value: \'false\',\n                                                            criteria: [{ type: \'equals\', field: \'notified\', value: false }]\n                                                          }\n                                                        })\n                                                      }\n                                                       }">\n                                            {{ item.open }}\n                                        </router-link>\n                                    </div>\n                                    <div v-else>\n                                        0\n                                    </div>\n                                </template>\n                            {% endblock %}\n\n                            {% block acris_stock_notification_products_list_grid_columns_actions %}\n                                <template #actions="{ item }">\n                                    {% block acris_stock_notification_products_list_grid_columns_actions_edit %}\n                                        <div\n                                            v-if="item.product.extensions.acrisStockNotifications && item.product.extensions.acrisStockNotifications.length > 0">\n                                            <sw-context-menu-item\n                                                :routerLink="{ name: \'acris.stock.notification.products.detail\', params: { id: item.product.extensions.acrisStockNotifications.first().id } }">\n                                                {{ $tc(\'acris-stock-notification-products.list.contextMenuEdit\') }}\n                                            </sw-context-menu-item>\n                                        </div>\n                                        <div v-else>\n                                            <div class="edit-disabled">\n                                                {{ $tc(\'acris-stock-notification-products.list.contextMenuEdit\') }}\n                                            </div>\n                                        </div>\n                                    {% endblock %}\n                                </template>\n\n                            {% endblock %}\n\n                            <template #pagination>\n                                {% block sw_order_list_grid_pagination %}\n                                    <sw-pagination :page="page"\n                                                   :limit="limit"\n                                                   :total="total"\n                                                   :total-visible="7"\n                                                   @page-change="onPageChange">\n                                    </sw-pagination>\n                                {% endblock %}\n                            </template>\n\n                        </sw-entity-listing>\n                    {% endblock %}\n\n                    {% block acris_stock_notification_products_list_empty_state %}\n                        <sw-empty-state v-if="!isLoading && total <= 0"\n                                        :title="$tc(\'acris-stock-notification-products.list.contentEmpty\')"></sw-empty-state>\n                    {% endblock %}\n                </div>\n            </template>\n        {% endblock %}\n\n        {% block acris_stock_notification_products_list_sidebar %}\n            <template #sidebar>\n                <sw-sidebar class="acris-stock-notification-products-list__sidebar">\n                    {% block acris_stock_notification_products_list_sidebar_item %}\n                        <sw-sidebar-item\n                            icon="regular-undo"\n                            :title="$tc(\'acris-stock-notification-products.list.titleSidebarItemRefresh\')"\n                            @click="onRefresh">\n                        </sw-sidebar-item>\n                    {% endblock %}\n                </sw-sidebar>\n            </template>\n        {% endblock %}\n    </sw-page>\n{% endblock %}\n',inject:["repositoryFactory"],mixins:[A.getByName("listing"),A.getByName("notification"),A.getByName("placeholder")],data:function(){return{items:null,isLoading:!1,showDeleteModal:!1,repository:null,total:0}},metaInfo:function(){return{title:this.$createTitle()}},computed:{entityRepository:function(){return this.repositoryFactory.create("acris_stock_notification_product")},productRepository:function(){return this.repositoryFactory.create("product")},columns:function(){return this.getColumns()}},methods:{getList:function(){var t=this;this.isLoading=!0;var e=new B(this.page,this.limit);e.setTerm(this.term),e.addAssociation("product.acrisStockNotifications"),e.addAssociation("product.options.group"),e.addSorting(B.sort("open","desc")),this.entityRepository.search(e,Shopware.Context.api).then((function(e){t.items=e;var i=!1;t.items.forEach((function(e){if(e.product&&e.product.parentId&&t.productRepository.get(e.product.parentId,Shopware.Context.api).then((function(t){e.product.name=t.name,e.product.translated.name=t.translated.name})),e&&e.product&&e.product.extensions&&e.product.extensions.acrisStockNotifications&&e.product.extensions.acrisStockNotifications.length>0){e.registered!==e.product.extensions.acrisStockNotifications.length&&(i=!0),e.registered=e.product.extensions.acrisStockNotifications.length;var n=0,o=0;e.product.extensions.acrisStockNotifications.forEach((function(t){!0===t.notified?n++:o++})),e.sent!==n&&(i=!0),e.open!==o&&(i=!0),e.sent=n,e.open=o}})),t.total=t.items.total,t.items&&t.total>0&&!0===i&&t.entityRepository.saveAll(t.items,Shopware.Context.api).then((function(){t.isLoading=!1})).catch((function(){t.isLoading=!1}))})).catch((function(){t.isLoading=!1}))},onDelete:function(t){this.showDeleteModal=t},onCloseDeleteModal:function(){this.showDeleteModal=!1},getColumns:function(){return[{property:"product.name",label:"acris-stock-notification-products.list.columnProduct",routerLink:"acris.stock.notification.products.detail",allowResize:!0},{property:"product.productNumber",label:"acris-stock-notification-products.list.columnProductNumber",routerLink:"acris.stock.notification.products.detail",allowResize:!0},{property:"registered",label:"acris-stock-notification-products.list.columnRegisteredCustomers",routerLink:"acris.stock.notification.products.detail",allowResize:!0},{property:"sent",label:"acris-stock-notification-products.list.columnSentNotifications",routerLink:"acris.stock.notification.products.detail",allowResize:!0},{property:"open",label:"acris-stock-notification-products.list.columnOpenOptIn",routerLink:"acris.stock.notification.products.detail",allowResize:!0}]}}});var R=Shopware.Component,G=Shopware.Utils;R.extend("acris-stock-notification-products-create","acris-stock-notification-products-detail",{template:"",beforeRouteEnter:function(t,e,i){t.name.includes("acris.stock.notification.create")&&!t.params.id&&(t.params.id=G.createId(),t.params.newItem=!0),i()},methods:{getEntity:function(){this.item=this.repository.create(Shopware.Context.api),this.item.hash=G.createId(),this.item.status="optIn",this.item.notified=!1},saveFinish:function(){this.isSaveSuccessful=!1,this.$router.push({name:"acris.stock.notification.detail",params:{id:this.item.id}})},onClickSave:function(){var t=this;this.isLoading=!0;var e=this.$tc("acris-stock-notification.detail.titleNotificationError"),i=this.$tc("acris-stock-notification.detail.messageSaveError"),n=this.$tc("acris-stock-notification.detail.titleNotificationSuccess"),o=this.$tc("acris-stock-notification.detail.messageSaveSuccess");this.repository.save(this.item,Shopware.Context.api).then((function(){t.isLoading=!1,t.createNotificationSuccess({title:n,message:o}),t.$router.push({name:"acris.stock.notification.detail",params:{id:t.item.id}})})).catch((function(){t.isLoading=!1,t.createNotificationError({title:e,message:i})}))}}});i("r1Et");function H(t){return(H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(){z=function(){return t};var t={},e=Object.prototype,i=e.hasOwnProperty,n=Object.defineProperty||function(t,e,i){t[e]=i.value},o="function"==typeof Symbol?Symbol:{},r=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,i){return t[e]=i}}function l(t,e,i,o){var r=e&&e.prototype instanceof f?e:f,a=Object.create(r.prototype),c=new L(o||[]);return n(a,"_invoke",{value:k(t,i,c)}),a}function u(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var d={};function f(){}function p(){}function m(){}var h={};s(h,r,(function(){return this}));var g=Object.getPrototypeOf,b=g&&g(g(E([])));b&&b!==e&&i.call(b,r)&&(h=b);var v=m.prototype=f.prototype=Object.create(h);function y(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function o(n,r,a,c){var s=u(t[n],t,r);if("throw"!==s.type){var l=s.arg,d=l.value;return d&&"object"==H(d)&&i.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,c)}),(function(t){o("throw",t,a,c)})):e.resolve(d).then((function(t){l.value=t,a(l)}),(function(t){return o("throw",t,a,c)}))}c(s.arg)}var r;n(this,"_invoke",{value:function(t,i){function n(){return new e((function(e,n){o(t,i,e,n)}))}return r=r?r.then(n,n):n()}})}function k(t,e,i){var n="suspendedStart";return function(o,r){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw r;return N()}for(i.method=o,i.arg=r;;){var a=i.delegate;if(a){var c=w(a,i);if(c){if(c===d)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var s=u(t,e,i);if("normal"===s.type){if(n=i.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:i.done}}"throw"===s.type&&(n="completed",i.method="throw",i.arg=s.arg)}}}function w(t,e){var i=e.method,n=t.iterator[i];if(void 0===n)return e.delegate=null,"throw"===i&&t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method)||"return"!==i&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+i+"' method")),d;var o=u(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var r=o.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,d):r:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function E(t){if(t){var e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(i.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:N}}function N(){return{value:void 0,done:!0}}return p.prototype=m,n(v,"constructor",{value:m,configurable:!0}),n(m,"constructor",{value:p,configurable:!0}),p.displayName=s(m,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(v),t},t.awrap=function(t){return{__await:t}},y(_.prototype),s(_.prototype,a,(function(){return this})),t.AsyncIterator=_,t.async=function(e,i,n,o,r){void 0===r&&(r=Promise);var a=new _(l(e,i,n,o),r);return t.isGeneratorFunction(i)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},y(v),s(v,c,"Generator"),s(v,r,(function(){return this})),s(v,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),i=[];for(var n in e)i.push(n);return i.reverse(),function t(){for(;i.length;){var n=i.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=E,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(i,n){return a.type="throw",a.arg=t,e.next=i,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o],a=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var c=i.call(r,"catchLoc"),s=i.call(r,"finallyLoc");if(c&&s){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(c){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var r=o;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=t,a.arg=e,r?(this.method="next",this.next=r.finallyLoc,d):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),x(i),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var o=n.arg;x(i)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,i){return this.delegate={iterator:E(t),resultName:e,nextLoc:i},"next"===this.method&&(this.arg=void 0),d}},t}function q(t,e,i,n,o,r,a){try{var c=t[r](a),s=c.value}catch(t){return void i(t)}c.done?e(s):Promise.resolve(s).then(n,o)}var V=Shopware.Component,U=Shopware.Data.Criteria,J=Shopware.Mixin;V.register("acris-stock-notification-products-detail",{template:'{% block acris_stock_notification_products_detail %}\n<sw-page class="acris-stock-notification-products-detail" v-if="item">\n    {% block acris_stock_notification_products_detail_smart_bar_header %}\n        <template slot="smart-bar-header">\n            {% block acris_stock_notification_products_detail_smart_bar_header_title %}\n                <h2 v-if="item.product.parentId">{{ $tc(\'acris-stock-notification-products.detail.textHeadline\') }} <sw-product-variant-info :variations="item.product.variation" class="acris-stock-notification-products-variant-info">\n                        {{ item.product.translated.name || item.product.name }}\n                    </sw-product-variant-info><span v-if="!isLoading" class="sw-page__smart-bar-amount">\n                            ({{ total }})\n                            </span></h2>\n                <h2 v-else>{{ $tc(\'acris-stock-notification-products.detail.textHeadline\') }} {{ item.product.translated.name }}<span v-if="!isLoading" class="sw-page__smart-bar-amount">\n                            ({{ total }})\n                            </span></h2>\n            {% endblock %}\n        </template>\n    {% endblock %}\n\n    {% block acris_stock_notification_products_list_cardview %}\n    <template slot="content">\n        <div class="acris-stock-notification-list__content">\n            {% block acris_stock_notification_products_list_content %}\n            <sw-entity-listing class="acris-stock-notification-products-list-grid"\n                               v-if="items && total > 0"\n                               :items="items"\n                               :repository="stockNotificationRepository"\n                               detailRoute="acris.stock.notification.detail"\n                               :showSeleciton="true"\n                               :isLoading="isLoading"\n                               :columns="columns">\n                {% block acris_stock_notification_products_list_grid_columns_status %}\n                <template #column-status="{ item }">\n                    {% block acris_stock_notification_products_list_grid_columns_status_content %}\n                    <template>\n                        <router-link :title="$tc(\'acris-stock-notification.list.contextMenuEdit\')"\n                                     :to="{ name: \'acris.stock.notification.detail\', params: { id: item.id } }">\n                            {% block acris_stock_notification_products_list_columns_status_link %}\n                            <template v-if="item.status == \'notSet\'">{{ $tc(\'acris-stock-notification.list.fieldSelectOptionNotSet\') }}</template>\n                            <template v-if="item.status == \'optIn\'">{{ $tc(\'acris-stock-notification.list.fieldSelectOptionOptIn\') }}</template>\n                            <template v-if="item.status == \'notified\'">{{ $tc(\'acris-stock-notification.list.fieldSelectOptionNotified\') }}</template>\n                            {% endblock %}\n                        </router-link>\n                    </template>\n                    {% endblock %}\n                </template>\n                {% endblock %}\n\n                {% block acris_stock_notification_products_list_grid_columns_notified %}\n            <template #column-notified="{ item }">\n                <sw-icon name="regular-checkmark-xs" small v-if="item.notified" class="is--active"></sw-icon>\n                <sw-icon name="regular-times-s" small v-else class="is--inactive"></sw-icon>\n            </template>\n                {% endblock %}\n\n                {% block acris_stock_notification_products_list_grid_columns_notified_date_time %}\n                <template #column-notifiedDateTime="{ item }">\n                {{ item.notifiedDateTime | date({hour: \'2-digit\', minute: \'2-digit\', second: \'2-digit\'}) }}\n                </template>\n                {% endblock %}\n\n                <template #pagination>\n                    {% block sw_order_list_grid_pagination %}\n                        <sw-pagination :page="page"\n                                       :limit="limit"\n                                       :total="total"\n                                       :total-visible="7"\n                                       @page-change="onPageChange">\n                        </sw-pagination>\n                    {% endblock %}\n                </template>\n\n            </sw-entity-listing>\n            {% endblock %}\n\n            {% block acris_stock_notification_products_list_empty_state %}\n                <sw-empty-state v-if="!isLoading && total <= 0" :title="$tc(\'acris-stock-notification.list.contentEmpty\')"></sw-empty-state>\n            {% endblock %}\n        </div>\n    </template>\n    {% endblock %}\n\n    {% block acris_stock_notification_products_list_sidebar %}\n        <template #sidebar>\n            <sw-sidebar class="acris-stock-notification-list__sidebar">\n                {% block acris_stock_notification_products_list_sidebar_item %}\n                    <sw-sidebar-item\n                        icon="regular-undo"\n                        :title="$tc(\'acris-stock-notification.list.titleSidebarItemRefresh\')"\n                        @click="onRefresh">\n                    </sw-sidebar-item>\n                {% endblock %}\n                <sw-sidebar-filter-panel\n                        entity="acris_stock_notification"\n                        :store-key="storeKey"\n                        :active-filter-number="activeFilterNumber"\n                        :filters="listFilters"\n                        :defaults="defaultFilters"\n                        @criteria-changed="updateCriteria"\n                />\n            </sw-sidebar>\n        </template>\n    {% endblock %}\n\n</sw-page>\n{% endblock %}\n\n',inject:["repositoryFactory","context","filterFactory"],mixins:[J.getByName("listing"),J.getByName("notification"),J.getByName("placeholder")],data:function(){return{item:null,items:null,isLoading:!1,repository:null,isSaveSuccessful:!1,total:0,activeFilterNumber:0,filterCriteria:[],itemsFiltered:null,entityProductId:"",defaultFilters:["notified-filter"],storeKey:"grid.filter.acris_stock_notification_product"}},metaInfo:function(){return{title:this.$createTitle()}},created:function(){this.createdComponent()},computed:{stockNotificationCriteria:function(){var t=new U;return t.addAssociation("product.options.group"),t.addAssociation("salesChannel"),t},stockNotificationRepository:function(){return this.repositoryFactory.create("acris_stock_notification")},productRepository:function(){return this.repositoryFactory.create("product")},columns:function(){return this.getColumns()},listFilters:function(){return this.filterFactory.create("acris_stock_notification",this.listFilterOptions)},listFilterOptions:function(){return{"notified-filter":{property:"notified",label:this.$tc("acris-stock-notification.list.notificationFilterLabel")}}},notificationCriteria:function(){var t=new U(this.page,this.limit);return t.setTerm(this.term),this.filterCriteria.forEach((function(e){t.addFilter(e)})),t}},watch:{notificationCriteria:{handler:function(){this.getList()},deep:!0}},methods:{reportTypeOptions:function(){return[{label:this.$tc("acris-b2b-report-form.optionTypeAll"),value:"sumPurchases"},{label:this.$tc("acris-b2b-report-form.optionTypeCustomer"),value:"contactPurchases"},{label:this.$tc("acris-b2b-report-form.optionTypeProduct"),value:"productPurchases"}]},createdComponent:function(){this.getEntity()},getEntity:function(){var t=this;this.isLoading=!0,this.stockNotificationRepository.get(this.$route.params.id,Shopware.Context.api,this.stockNotificationCriteria).then((function(e){t.item=e,t.item.product&&t.item.product.parentId&&t.productRepository.get(t.item.product.parentId,Shopware.Context.api).then((function(e){t.item.product.name=e.name,t.item.product.translated.name=e.translated.name})),t.entityProductId=e.productId,t.getList()}))},getList:function(){var t,e=this;return(t=z().mark((function t(){var i;return z().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.entityProductId){t.next=2;break}return t.abrupt("return");case 2:return e.isLoading=!0,t.next=5,Shopware.Service("filterService").mergeWithStoredFilters(e.storeKey,e.notificationCriteria);case 5:i=t.sent,e.activeFilterNumber=i.filters.length,i.setTerm(e.term),i.addAssociation("product"),i.addFilter(U.equals("productId",e.entityProductId)),e.stockNotificationRepository.search(i,Shopware.Context.api).then((function(t){return e.items=t,e.total=e.items.total,e.isLoading=!1,t})).catch((function(){e.isLoading=!1}));case 11:case"end":return t.stop()}}),t)})),function(){var e=this,i=arguments;return new Promise((function(n,o){var r=t.apply(e,i);function a(t){q(r,n,o,a,c,"next",t)}function c(t){q(r,n,o,a,c,"throw",t)}a(void 0)}))})()},getColumns:function(){return[{property:"email",inlineEdit:"string",label:"acris-stock-notification.list.columnEmail",routerLink:"acris.stock.notification.detail",allowResize:!0,primary:!0},{property:"name",inlineEdit:"string",label:"acris-stock-notification.list.columnName",routerLink:"acris.stock.notification.detail",allowResize:!0,primary:!0},{property:"status",label:"acris-stock-notification.list.columnStatus",routerLink:"acris.stock.notification.detail",allowResize:!0},{property:"notified",label:"acris-stock-notification.list.columnNotified",routerLink:"acris.stock.notification.detail",align:"center",allowResize:!0},{property:"notifiedDateTime",label:"acris-stock-notification.list.columnNotifiedDateTime",routerLink:"acris.stock.notification.detail",align:"center",allowResize:!0}]},updateCriteria:function(t){this.page=1,this.filterCriteria=t}}});i("MDyN");var K=i("+Gdr"),Y=i("0BRV");Shopware.Module.register("acris-stock-notification-products",{type:"plugin",name:"acris-stock-notification",title:"acris-stock-notification-products.general.mainMenuItemGeneral",description:"acris-stock-notification-products.general.description",color:"#9AA8B5",icon:"regular-bars-circle",favicon:"icon-module-settings.png",snippets:{"de-DE":K,"en-GB":Y},routes:{index:{component:"acris-stock-notification-products-list",path:"index",meta:{parentPath:"acris.stock.notification.list.index"}},detail:{component:"acris-stock-notification-products-detail",path:"detail/:id",meta:{parentPath:"acris.stock.notification.products.index"}},create:{component:"acris-stock-notification-products-create",path:"create",meta:{parentPath:"acris.stock.notification.products.index"}}}});Shopware.Component.register("sw-cms-block-acris-stock-notification",{template:'{% block sw_cms_block_acris_stock_notification %}\n    <div class="sw-cms-block-acris-stock-notification">\n        <slot name="column"></slot>\n    </div>\n{% endblock %}\n'});i("pb9t");Shopware.Component.register("sw-cms-preview-acris-stock-notification",{template:'{% block sw_cms_block_acris_stock_notification_preview %}\n    <div class="sw-cms-preview-acris-stock-notification">\n        <sw-alert variant="warning" :showIcon="true" :closable="false">\n            Lorem ipsum dolor sit amet.\n        </sw-alert>\n\n        <div class="sw-cms-preview-acris-stock-notification__placeholders">\n            <span class="sw-cms-preview-acris-stock-notification__element"></span>\n            <span class="sw-cms-preview-acris-stock-notification__action">Lorem</span>\n        </div>\n    </div>\n{% endblock %}\n'}),Shopware.Service("cmsService").registerCmsBlock({name:"acris-stock-notification",label:"acris-stock-notification.blocks.label",category:"commerce",component:"sw-cms-block-acris-stock-notification",previewComponent:"sw-cms-preview-acris-stock-notification",defaultConfig:{marginBottom:"20px",marginTop:"20px",marginLeft:"20px",marginRight:"20px",sizingMode:"boxed"},slots:{column:{type:"acris-stock-notification"}}});i("eLl0");var W=Shopware,Z=W.Component,X=W.Mixin;Z.register("sw-cms-el-acris-stock-notification",{template:'{% block sw_cms_element_acris_stock_notification %}\n    <div class="sw-cms-el-acris-stock-notification">\n\n        <sw-alert variant="warning" :showIcon="true" :closable="false">\n            {{ $tc(\'acris-stock-notification.elements.message\') }}\n        </sw-alert>\n\n        {% block sw_cms_element_acris_stock_notification_mail %}\n            <div class="sw-cms-el-acris-stock-notification-group">\n                <div class="sw-cms-el-acris-stock-notification__input">\n                    <div class="sw-cms-el-acris-stock-notification__text">\n                        {{ $tc(\'acris-stock-notification.elements.inputPlaceholder\') }}\n                    </div>\n                </div>\n\n                <div class="sw-cms-el-acris-stock-notification__action">\n                    {{ $tc(\'acris-stock-notification.elements.buttonText\') }}\n                </div>\n            </div>\n        {% endblock %}\n\n    </div>\n{% endblock %}\n',mixins:[X.getByName("cms-element")],computed:{pageType:function(){var t,e,i;return null!==(t=null===(e=this.cmsPageState)||void 0===e||null===(i=e.currentPage)||void 0===i?void 0:i.type)&&void 0!==t?t:""},isProductPageType:function(){return"product_detail"==this.pageType}},watch:{pageType:function(t){this.$set(this.element,"locked","product_detail"==t)}},created:function(){this.createdComponent()},methods:{createdComponent:function(){this.initElementConfig("acris-stock-notification"),this.initElementData("acris-stock-notification"),this.$set(this.element,"locked",this.isProductPageType),this.isProductPageType&&(this.element.config.product.value="product")}}});function Q(t){return(Q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function et(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?tt(Object(i),!0).forEach((function(e){it(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):tt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function it(t,e,i){return(e=function(t){var e=function(t,e){if("object"!==Q(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!==Q(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Q(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var nt=Shopware,ot=nt.Component,rt=nt.Mixin,at=nt.Utils,ct=Shopware.Data.Criteria;ot.register("sw-cms-el-config-acris-stock-notification",{template:'{% block sw_cms_element_acris_stock_notification_config %}\n    <div class="sw-cms-el-config-acris-stock-notification">\n        {% block sw_cms_element_acris_stock_notification_config_product_waring %}\n        <sw-alert\n            v-if="isProductPage"\n            class="sw-cms-el-config-buy-box__warning" variant="info">\n        {{ $tc(\'sw-cms.elements.buyBox.infoText.tooltipSettingDisabled\') }}\n        </sw-alert>\n        {% endblock %}\n\n        {% block sw_cms_element_acris_stock_notification_config_product_select %}\n        <sw-entity-single-select\n            v-if="!isProductPage"\n            v-model="element.config.product.value"\n            ref="cmsProductSelection"\n            entity="product"\n            :label="$tc(\'sw-cms.elements.buyBox.config.label.selection\')"\n            :placeholder="$tc(\'sw-cms.elements.buyBox.config.placeholder.selection\')"\n            :criteria="productCriteria"\n            :context="productSelectContext"\n            @change="onProductChange">\n\n            {% block sw_cms_element_acris_stock_notification_config_product_variant_label %}\n            <template #selection-label-property="{ item }">\n                <sw-product-variant-info :variations="item.variation">\n                    {{ item.translated.name || item.name }}\n                </sw-product-variant-info>\n            </template>\n            {% endblock %}\n\n            {% block sw_cms_element_acris_stock_notification_config_product_select_result_item %}\n                <template #result-item="{ item, index }">\n                    <li is="sw-select-result" v-bind="{ item, index }">\n\n                        {% block sw_cms_element_acris_stock_notification_config_single_select_base_results_list_result_label %}\n                            <span class="sw-select-result__result-item-text">\n                                  <sw-product-variant-info :variations="item.variation">\n                                        {{ item.translated.name || item.name }}\n                                  </sw-product-variant-info>\n                             </span>\n                        {% endblock %}\n\n                    </li>\n                </template>\n            {% endblock %}\n\n        </sw-entity-single-select>\n        {% endblock %}\n    </div>\n{% endblock %}\n',mixins:[rt.getByName("cms-element")],inject:["repositoryFactory"],computed:{productRepository:function(){return this.repositoryFactory.create("product")},productSelectContext:function(){return et(et({},Shopware.Context.api),{},{inheritance:!0})},productCriteria:function(){var t=new ct;return t.addAssociation("options.group"),t},selectedProductCriteria:function(){var t=new ct;return t.addAssociation("deliveryTime"),t},isProductPage:function(){return"product_detail"==at.get(this.cmsPageState,"currentPage.type")}},created:function(){this.createdComponent()},methods:{createdComponent:function(){this.initElementConfig("acris-stock-notification")},onProductChange:function(t){var e=this;t?this.productRepository.get(t,this.productSelectContext,this.selectedProductCriteria).then((function(i){e.element.config.product.value=t,e.$set(e.element.data,"productId",t),e.$set(e.element.data,"product",i)})):(this.element.config.product.value=null,this.$set(this.element.data,"productId",null),this.$set(this.element.data,"product",null)),this.$emit("element-update",this.element)},emitUpdateEl:function(){this.$emit("element-update",this.element)}}});i("mswp");function st(t){return(st="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function ut(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?lt(Object(i),!0).forEach((function(e){dt(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):lt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function dt(t,e,i){return(e=function(t){var e=function(t,e){if("object"!==st(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!==st(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===st(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}Shopware.Component.register("sw-cms-el-preview-acris-stock-notification",{template:'{% block sw_cms_element_acris_stock_notification_preview %}\n    <div class="sw-cms-el-preview-acris-stock-notification">\n        <sw-alert variant="warning" :showIcon="true" :closable="false">\n            Lorem ipsum dolor sit amet.\n        </sw-alert>\n\n        <div class="sw-cms-el-preview-acris-stock-notification__placeholders">\n            <span class="sw-cms-el-preview-acris-stock-notification__element"></span>\n            <span class="sw-cms-el-preview-acris-stock-notification__action">Lorem</span>\n        </div>\n    </div>\n{% endblock %}\n'});var ft=Shopware.Data.Criteria,pt=new ft;pt.addAssociation("deliveryTime"),Shopware.Service("cmsService").registerCmsElement({name:"acris-stock-notification",label:"acris-stock-notification.elements.label",component:"sw-cms-el-acris-stock-notification",configComponent:"sw-cms-el-config-acris-stock-notification",previewComponent:"sw-cms-el-preview-acris-stock-notification",defaultConfig:{product:{source:"static",value:null,required:!0,entity:{name:"product",criteria:pt}}},collect:function(t){var e=ut(ut({},Shopware.Context.api),{},{inheritance:!0}),i={};return Object.keys(t.config).forEach((function(n){if("mapped"!=t.config[n].source){var o=t.config[n],r=o.entity,a=o.value;if(r&&a){var c=r.name,s=ut({value:[a],key:n,searchCriteria:r.criteria?r.criteria:new ft},r);s.searchCriteria.setIds(s.value),s.context=e,i["entity-".concat(c)]=s}}})),i}});function mt(t){return(mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ht(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function gt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ht(Object(i),!0).forEach((function(e){bt(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ht(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function bt(t,e,i){return(e=function(t){var e=function(t,e){if("object"!==mt(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!==mt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===mt(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var vt=Shopware,yt=vt.Component,_t=vt.State,kt=Shopware.Data.Criteria,wt=Shopware.Component.getComponentHelper().mapGetters;yt.override("sw-bulk-edit-product",gt(gt({template:"\n{% block sw_bulk_edit_product %}\n    {% parent() %}\n{% endblock %}\n",inject:["feature","bulkEditApiFactory","repositoryFactory"],data:function(){return{emailNotificationConfig:null}}},wt("acrisEmailNotificationState",["isEmpty","acrisEmailNotification"])),{},{computed:{customFieldSetCriteria:function(){var t=new kt(1,null);return this.emailNotificationConfig=_t.get("acrisEmailNotificationState").acrisEmailNotification,"alwaysActive"===this.emailNotificationConfig?t.addFilter(kt.multi("OR",[kt.equals("relations.entityName","product"),kt.equals("name","acris_stock_notification_inactive")])):t.addFilter(kt.multi("OR",[kt.equals("relations.entityName","product"),kt.equals("name","acris_stock_notification")])),t}}}));i("aN/t")},clMx:function(t,e,i){},eLl0:function(t,e,i){var n=i("UfhG");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("32910fbe",n,!0,{})},f5v3:function(t){t.exports=JSON.parse('{"acris-stock-notification":{"general":{"mainMenuItemGeneral":"List of stock notification","placeholderSearchBar":"Search stock notifications","description":"Stock notification"},"list":{"columnEmail":"Email","columnName":"Name","columnStatus":"Status","columnNotified":"Notified","columnNotifiedDateTime":"Last notified on","columnProduct":"Product","columnProductNumber":"Product number","columnSalesChannel":"Sales channel","columnLanguage":"Language","columnCreatedAt":"Registered on","fieldSelectOptionNotSet":"E-mail address has been registered, double opt-in pending.","fieldSelectOptionOptIn":"E-mail address successfully registered to notify.","fieldSelectOptionNotified":"Stock info mail was sent.","buttonDelete":"Delete","modalTitleDelete":"Delete","contentEmpty":"No stock notifications are inserted","textDeleteConfirm":"Do you want to delete the stock notification?","buttonCancel":"Cancel","buttonAdd":"Add","contextMenuEdit":"Edit","textHeadline":"Stock notification","titleSidebarItemRefresh":"Refresh","notificationFilterLabel":"Notified"},"detail":{"buttonCancel":"Cancel","buttonSave":"Save","cardTitle":"Settings","fieldTitleLabelEmail":"Email","fieldTitlePlaceholderEmail":"Enter email...","fieldTitleLabelName":"Customer name","fieldTitlePlaceholderName":"Enter customer name...","fieldTitleLabelStatus":"Status","fieldTitlePlaceholderStatus":"Select status...","fieldSelectOptionNotSet":"E-mail address has been registered, double opt-in pending.","fieldSelectOptionOptIn":"E-mail address successfully registered to notify.","fieldSelectOptionNotified":"Stock info mail was sent.","fieldTitleLabelHash":"Hash","fieldTitlePlaceholderDbPort":"Enter hash...","fieldTitleLabelNotified":"Notified","fieldTitleLabelNotifiedDateTime":"Last notified on","fieldTitleLabelCreatedAt":"Registered on","fieldTitleHelpTextNotified":"If notified is active, then customer is already notified for product availability.","fieldTitleLabelProduct":"Product","fieldTitlePlaceholderProduct":"Select product...","fieldTitleLabelSalesChannel":"Sales channel","fieldTitlePlaceholderSalesChannel":"Select sales channel...","fieldTitleLabelLanguage":"Language","fieldTitlePlaceholderLanguage":"Select language","textHeadline":"Stock notification","titleNotificationError":"Error","titleNotificationSuccess":"Success","messageSaveError":"Stock notification could not be saved.","messageSaveSuccess":"Stock notification has been saved."}}}')},fBxN:function(t,e,i){var n=i("OJvX");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("4bb20f1a",n,!0,{})},g09z:function(t,e,i){},mswp:function(t,e,i){var n=i("E116");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("5f9b78e8",n,!0,{})},"nk+K":function(t,e,i){},pb9t:function(t,e,i){var n=i("g09z");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("1b8962ea",n,!0,{})},qAn5:function(t){t.exports=JSON.parse('{"acris-stock-notification":{"general":{"mainMenuItemGeneral":"Übersicht Benachrichtigungen","placeholderSearchBar":"Suche nach Lagerstands Benachrichtigung","description":"Lagerstands Benachrichtigung"},"list":{"columnEmail":"Email","columnName":"Name","columnStatus":"Status","columnNotified":"Benachrichtigt","columnNotifiedDateTime":"Zuletzt benachrichtigt am","columnProduct":"Produkt","columnProductNumber":"Produkt Nummer","columnSalesChannel":"Verkaufskanal","columnLanguage":"Sprache","columnCreatedAt":"Eingetragen am","fieldSelectOptionNotSet":"E-Mail Adresse wurde registriert, Double-Opt-in ausständig.","fieldSelectOptionOptIn":"E-Mail Adresse erfolgreich für die Benachrichtigung registriert.","fieldSelectOptionNotified":"Lagerstandsinfo wurde per E-Mail versandt.","buttonDelete":"Löschen","modalTitleDelete":"Löschen","contentEmpty":"Es werden keine Lagerstands Benachrichtigungen eingefügt","textDeleteConfirm":"Möchten Sie die Lagerstands Benachrichtigung löschen?","buttonCancel":"Abbrechen","buttonAdd":"Hinzufügen","contextMenuEdit":"Bearbeiten","textHeadline":"Lagerstands Benachrichtigung","titleSidebarItemRefresh":"Aktualisieren","notificationFilterLabel":"Bereits benachrichtigt"},"detail":{"buttonCancel":"Abbrechen","buttonSave":"Speichern","cardTitle":"Einstellungen","fieldTitleLabelEmail":"Email","fieldTitlePlaceholderEmail":"Email eingeben...","fieldTitleLabelName":"Name","fieldTitlePlaceholderName":"Name eingeben...","fieldTitleLabelStatus":"Status","fieldTitlePlaceholderStatus":"Status auswählen...","fieldSelectOptionNotSet":"E-Mail Adresse wurde registriert, Double-Opt-in ausständig.","fieldSelectOptionOptIn":"E-Mail Adresse erfolgreich für die Benachrichtigung registriert.","fieldSelectOptionNotified":"Lagerstandsinfo wurde per E-Mail versandt.","fieldTitleLabelNotifiedDateTime":"Zuletzt benachrichtigt am","fieldTitleLabelCreatedAt":"Eingetragen am","fieldTitleLabelHash":"Hash","fieldTitlePlaceholderDbPort":"Hash eingeben...","fieldTitleLabelNotified":"Benachrichtigt","fieldTitleHelpTextNotified":"Wenn Benachrichtigung aktiv ist, dann wird der Kunde bereits über die Produktverfügbarkeit informiert.","fieldTitleLabelProduct":"Produkt","fieldTitlePlaceholderProduct":"Produkt auswählen...","fieldTitleLabelSalesChannel":"Verkaufskanal","fieldTitlePlaceholderSalesChannel":"Verkaufskanal auswählen...","fieldTitleLabelLanguage":"Sprache","fieldTitlePlaceholderLanguage":"Sprache auswählen...","textHeadline":"Lagerstands Benachrichtigung","titleNotificationError":"Fehler","titleNotificationSuccess":"Erfolg","messageSaveError":"Lagerstands Benachrichtigung konnte nicht gespeichert werden.","messageSaveSuccess":"Die Lagerstands Benachrichtigung wurde gespeichert."}}}')},r1Et:function(t,e,i){var n=i("JUTR");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);(0,i("P8hj").default)("54a5f88d",n,!0,{})},zrtg:function(t,e,i){}});
//# sourceMappingURL=acris-stock-notification-c-s.js.map