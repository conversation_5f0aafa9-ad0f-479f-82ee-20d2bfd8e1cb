{"acrisStockNotification": {"detail": {"stockNotificationMessageWarning": "Notify me when the item is available.", "stockNotificationInputPlaceholder": "Enter e-mail...", "stockNotificationButtonText": "Notify", "stockNotificationMessageSuccess": "Thank you! Your e-mail address is verified. You will be notified when the product is available.", "stockNotificationMessageExistWarning": "You have already signed up for a notification for this product!", "notificationPersistedSuccess": "To complete the notification process, search your inbox for our confirmation email and click on the link provided with it.", "notificationPersistedInfo": "If you did not received an email, please repeat the process or contact our support team."}}, "checkout": {"acrisProductStockNotificationAddToCartErrorMessage": "The product with the number %productNumber% cannot be added to the shopping cart."}}