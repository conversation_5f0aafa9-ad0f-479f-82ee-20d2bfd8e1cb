<?php declare(strict_types=1);

namespace Acris\StockNotification\Storefront\Controller;

use Acris\StockNotification\Core\Content\StockNotification\SalesChannel\AbstractStockNotificationNotifyRoute;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Shopware\Core\Framework\Validation\Exception\ConstraintViolationException;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Shopware\Storefront\Framework\Routing\RequestTransformer;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class StockNotificationController extends StorefrontController
{
    public const NOTIFY = 'notify';

    private AbstractStockNotificationNotifyRoute $stockNotificationRoute;

    public function __construct(
        AbstractStockNotificationNotifyRoute $stockNotificationRoute
    ) {
        $this->stockNotificationRoute = $stockNotificationRoute;
    }

    #[Route(path: '/form/acris-stock-notification', name: 'frontend.form.acris-stock-notification.register.handle', defaults: ['XmlHttpRequest' => true, '_captcha' => true], methods: ['POST'])]
    public function handleStockNotification(Request $request, RequestDataBag $data, SalesChannelContext $context): JsonResponse
    {
        $notified = $data->get('option') === self::NOTIFY;
        if ($notified) {
            $response = $this->handleNotification($request, $data, $context);
        } else {
            $response = $this->createActionResponse($request);
        }

        return new JsonResponse($response);
    }

    private function handleNotification(Request $request, RequestDataBag $data, SalesChannelContext $context): array
    {
        try {
            $data->set('storefrontUrl', $request->attributes->get(RequestTransformer::SALES_CHANNEL_ABSOLUTE_BASE_URL) . $request->attributes->get(RequestTransformer::SALES_CHANNEL_BASE_URL));

            $notifyResponse = $this->stockNotificationRoute->notify($data, $context, false);
            if ($notifyResponse->isSuccess() === true) {
                if (!$notifyResponse->isEmailExists() === true) {
                    $response[] = [
                        'type' => 'success',
                        'alert' => $this->renderView('@Storefront/storefront/utilities/alert.html.twig', [
                            'type' => 'success',
                            'list' => [$this->trans('acrisStockNotification.detail.notificationPersistedSuccess')],
                        ])
                    ];
                    $response[] = [
                        'type' => 'info',
                        'alert' => $this->renderView('@Storefront/storefront/utilities/alert.html.twig', [
                            'type' => 'info',
                            'list' => [$this->trans('acrisStockNotification.detail.notificationPersistedInfo')],
                        ]),
                    ];
                } else {
                    $response[] = [
                        'type' => 'success',
                        'message' => 'emailExist',
                        'alert' => $this->renderView('@Storefront/storefront/utilities/alert.html.twig', [
                            'type' => 'warning',
                            'list' => [$this->trans('acrisStockNotification.detail.stockNotificationMessageExistWarning')],
                        ]),
                    ];
                }
            } else {
                $response[] = [
                    'type' => 'danger',
                    'alert' => $this->renderView('@Storefront/storefront/utilities/alert.html.twig', [
                        'type' => 'danger',
                        'list' => [$this->trans('error.message-default')],
                    ]),
                ];
            }
        } catch (ConstraintViolationException $exception) {
            $errors = [];
            foreach ($exception->getViolations() as $error) {
                $errors[] = $error->getMessage();
            }
            $response[] = [
                'type' => 'danger',
                'alert' => $this->renderView('@Storefront/storefront/utilities/alert.html.twig', [
                    'type' => 'danger',
                    'list' => $errors,
                ]),
            ];
        } catch (\Exception $exception) {
            $response[] = [
                'type' => 'danger',
                'alert' => $this->renderView('@Storefront/storefront/utilities/alert.html.twig', [
                    'type' => 'danger',
                    'list' => [$this->trans('error.message-default')],
                ]),
            ];
        }

        return $response;
    }
}
