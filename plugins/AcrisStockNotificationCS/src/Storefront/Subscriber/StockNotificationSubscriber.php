<?php declare(strict_types=1);

namespace Acris\StockNotification\Storefront\Subscriber;

use Acris\StockNotification\Core\Content\StockNotification\SalesChannel\AbstractStockNotificationConfirmRoute;
use Shopware\Core\Framework\Event\BusinessEventCollector;
use Shopware\Core\Framework\Event\BusinessEventCollectorEvent;
use Shopware\Core\Framework\Struct\ArrayEntity;
use Shopware\Storefront\Page\Product\ProductPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class StockNotificationSubscriber implements EventSubscriberInterface
{
    public function __construct(private readonly AbstractStockNotificationConfirmRoute $stockNotificationConfirmRoute, private readonly BusinessEventCollector $businessEventCollector)
    {
    }

    public const CUSTOM_EVENTS_CLASSES = [
        'Acris\StockNotification\Core\Content\StockNotification\Event\StockNotificationRegisterEvent',
        'Acris\StockNotification\Core\Content\StockNotification\Event\StockNotificationConfirmEvent',
        'Acris\StockNotification\Core\Content\StockNotification\Event\StockNotificationNotifyEvent'
    ];

    public static function getSubscribedEvents()
    {
        return [
            BusinessEventCollectorEvent::NAME => [
                ['onBusinessEventCollect', 200]
            ],
            ProductPageLoadedEvent::class => [
                ['onProductPageLoaded', 200]
            ]
        ];
    }

    public function onBusinessEventCollect(BusinessEventCollectorEvent $event): void
    {
        $result = $event->getCollection();
        foreach (self::CUSTOM_EVENTS_CLASSES as $class) {
            $definition = $this->businessEventCollector->define($class);

            if (!$definition) {
                continue;
            }
            $result->set($definition->getName(), $definition);
        }
    }

    public function onProductPageLoaded(ProductPageLoadedEvent $event): void
    {
        $queryDataBag = $event->getRequest()->query;
        if (!empty($queryDataBag) && $queryDataBag->has('em') && $queryDataBag->has('hash')) {
            $notificationConfirmed = true;

            try {
                $this->stockNotificationConfirmRoute->confirm($queryDataBag, $event->getSalesChannelContext());
            } catch (\Throwable $throwable) {
                $notificationConfirmed = false;
            }
            $event->getPage()->addExtension('notificationConfirmed', new ArrayEntity(['notificationConfirmed' => $notificationConfirmed]));
        }
    }
}
