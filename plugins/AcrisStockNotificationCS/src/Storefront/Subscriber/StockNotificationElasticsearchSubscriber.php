<?php declare(strict_types=1);

namespace Acris\StockNotification\Storefront\Subscriber;

use Shopware\Elasticsearch\Product\Event\ElasticsearchProductCustomFieldsMappingEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class StockNotificationElasticsearchSubscriber implements EventSubscriberInterface
{
    public function __construct()
    {
    }

    public static function getSubscribedEvents()
    {
        return [
            ElasticsearchProductCustomFieldsMappingEvent::class => [
                ['onElasticsearchProductCustomFieldsMapping', 200]
            ]
        ];
    }

    public function onElasticsearchProductCustomFieldsMapping(ElasticsearchProductCustomFieldsMappingEvent $event): void
    {
        $event->setMapping('acris_stock_notification_email_notification', 'bool');
        $event->setMapping('acris_stock_notification_email_notification_inactive', 'bool');
    }
}
