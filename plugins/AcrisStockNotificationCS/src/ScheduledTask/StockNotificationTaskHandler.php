<?php declare(strict_types=1);

namespace Acris\StockNotification\ScheduledTask;

use A<PERSON>ris\StockNotification\Core\Content\StockNotification\Event\StockNotificationNotifyEvent;
use A<PERSON>ris\StockNotification\Core\Content\StockNotification\SalesChannel\StockNotificationNotifyRoute;
use A<PERSON>ris\StockNotification\Custom\StockNotificationEntity;
use Acris\StockNotification\Custom\StockNotificationProductEntity;
use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NotFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;
use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskHandler;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\Language\LanguageEntity;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class StockNotificationTaskHandler extends ScheduledTaskHandler
{
    private const STACK_SIZE = 200;
    private EntityRepository $stockNotificationRepository;
    private EntityRepository $stockNotificationProductRepository;
    private EntityRepository $languageRepository;
    private EventDispatcherInterface $eventDispatcher;
    private Connection $connection;

    public function __construct(
        EntityRepository $scheduledTaskRepository,
        EntityRepository $stockNotificationRepository,
        EntityRepository $stockNotificationProductRepository,
        EntityRepository $languageRepository,
        EventDispatcherInterface  $eventDispatcher,
        Connection                $connection
    )
    {
        parent::__construct($scheduledTaskRepository);
        $this->stockNotificationRepository = $stockNotificationRepository;
        $this->stockNotificationProductRepository = $stockNotificationProductRepository;
        $this->languageRepository = $languageRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->connection = $connection;
    }

    public static function getHandledMessages(): iterable
    {
        return [
            StockNotificationTask::class,
        ];
    }

    public function run(): void
    {
        $defaultContext = Context::createDefaultContext();
        $languageResult = $this->languageRepository->searchIds((new Criteria()), $defaultContext);

        if (!$languageResult->firstId() || $languageResult->getTotal() <= 0) return;
        $availableProductIds = $this->getAvailableProductIds();

        $languageIds = $languageResult->getIds();

        /** @var LanguageEntity $languageId */
        foreach ($languageIds as $languageId) {
            $languageIdChain = $defaultContext->getLanguageIdChain();
            array_unshift($languageIdChain, $languageId);
            $context = new Context(new SystemSource(), $defaultContext->getRuleIds(), $defaultContext->getCurrencyId(), $languageIdChain, $defaultContext->getVersionId(), $defaultContext->getCurrencyFactor(), true, $defaultContext->getTaxState());

            $criteriaIds = new Criteria();
            $criteriaIds->setTotalCountMode(Criteria::TOTAL_COUNT_MODE_EXACT);
            $criteriaIds->addFilter(new EqualsFilter('status', StockNotificationNotifyRoute::STATUS_OPT_IN))
                ->addFilter(new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('notified', true)]))
                ->addFilter(new EqualsFilter('languageId', $languageId));

            if (!empty($availableProductIds)) {
                $criteriaIds->addFilter(new EqualsAnyFilter('productId', $availableProductIds));
            }

            /** @var IdSearchResult $searchResult */
            $searchResult = $this->stockNotificationRepository->searchIds($criteriaIds, $context);
            if ($searchResult->getTotal() <= 0 || !$searchResult->firstId()) {
                continue;
            }

            $criteria = new Criteria();
            $criteria->addAssociation('product');
            $criteria->addAssociation('language');
            $criteria->addAssociation('salesChannel');
            $criteria->addFilter(new EqualsFilter('status', StockNotificationNotifyRoute::STATUS_OPT_IN))
                ->addFilter(new EqualsFilter('notified', false))
                ->addFilter(new EqualsFilter('languageId', $languageId));

            if (!empty($availableProductIds)) {
                $criteria->addFilter(new EqualsAnyFilter('productId', $availableProductIds));
            }

            $pages = ceil($searchResult->getTotal() / self::STACK_SIZE);
            for ($i = 0; $i < $pages; $i++) {
                $offset = $i * self::STACK_SIZE;
                $stockNotificationPerPage = $this->stockNotificationRepository->search($criteria->setLimit(self::STACK_SIZE)->setOffset($offset), $context);
                /** @var StockNotificationEntity $element */
                foreach ($stockNotificationPerPage->getEntities()->getElements() as $element) {
                    $event = new StockNotificationNotifyEvent($context, $element, $element->getSalesChannelId());
                    $this->eventDispatcher->dispatch($event);
                    $this->stockNotificationRepository->update([
                        [
                            'id' => $element->getId(),
                            'notified' => true,
                            'status' => StockNotificationNotifyRoute::STATUS_NOTIFIED,
                            'notifiedDateTime' => new \DateTime()
                        ]
                    ], $context);

                    $this->upsertStockNotificationForProduct($element->getProductId(), $context);
                }
            }
        }
    }

    private function upsertStockNotificationForProduct(?string $productId, Context $context): void
    {
        if (empty($productId)) return;
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('productId', $productId));

        /** @var StockNotificationProductEntity $registeredProduct */
        $registeredProduct = $this->stockNotificationProductRepository->search($criteria, $context)->first();

        if (empty($registeredProduct)) return;

        $sent = $registeredProduct->getSent() + 1;

        $data = [
            [
                'id' => $registeredProduct->getId(),
                'sent' => $sent,
                'open' => $registeredProduct->getRegistered() - $sent
            ]
        ];

        $this->stockNotificationProductRepository->upsert($data, $context);
    }

    private function getAvailableProductIds(): array
    {
        $productIds = [];

        $fetchedIds = $this->connection->fetchAllAssociative('SELECT id FROM product WHERE available_stock >= min_purchase OR (min_purchase IS NULL AND available_stock > 0);');
        if (!empty($fetchedIds) && is_array($fetchedIds)) {
            foreach ($fetchedIds as $fetchedId) {
                if (!is_array($fetchedId) || !array_key_exists('id', $fetchedId) || empty($fetchedId['id'])) {
                    continue;
                }

                try {
                    $id = Uuid::fromBytesToHex($fetchedId['id']);
                    if (Uuid::isValid($id)) {
                        $productIds[] = $id;
                    }
                } catch (\Throwable $e) {
                    continue;
                }
            }
        }

        return $productIds;
    }
}
