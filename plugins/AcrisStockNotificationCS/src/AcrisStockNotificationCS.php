<?php declare(strict_types=1);

namespace Acris\StockNotification;

use A<PERSON>ris\StockNotification\Core\Content\StockNotification\Event\StockNotificationConfirmEvent;
use A<PERSON>ris\StockNotification\Core\Content\StockNotification\Event\StockNotificationNotifyEvent;
use A<PERSON>ris\StockNotification\Core\Content\StockNotification\Event\StockNotificationRegisterEvent;
use A<PERSON>ris\StockNotification\Custom\StockNotificationEntity;
use A<PERSON>ris\StockNotification\Custom\StockNotificationProductEntity;
use Doctrine\DBAL\Connection;
use Shopware\Core\Content\Flow\Dispatching\Action\SendMailAction;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\ActivateContext;
use Shopware\Core\Framework\Plugin\Context\UpdateContext;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\CustomField\CustomFieldTypes;
use Shopware\Core\System\Snippet\SnippetEntity;

class AcrisStockNotificationCS extends Plugin
{
    const STACK_SIZE = 500;
    const CUSTOM_FIELD_SET_NAME = 'acris_stock_notification';
    const CUSTOM_FIELD_SET_LABEL_EN = 'ACRIS Stock Notification';
    const CUSTOM_FIELD_SET_LABEL_DE = 'ACRIS Lagerstands Benachrichtigung';
    const CUSTOM_FIELD_EMAIL_NOTIFICATION_NAME = 'acris_stock_notification_email_notification';
    const CUSTOM_FIELD_EMAIL_NOTIFICATION_LABEL_EN = 'Benachrichtigungsfunktion anzeigen';
    const CUSTOM_FIELD_EMAIL_NOTIFICATION_LABEL_DE = 'Show e-mail notification';
    const CUSTOM_FIELD_SET_INACTIVE_NAME = 'acris_stock_notification_inactive';
    const CUSTOM_FIELD_SET_INACTIVE_LABEL_EN = 'ACRIS Stock Notification inactive';
    const CUSTOM_FIELD_SET_INACTIVE_LABEL_DE = 'ACRIS Lagerstands Benachrichtigung inaktiv';
    const CUSTOM_FIELD_EMAIL_NOTIFICATION_INACTIVE_NAME = 'acris_stock_notification_email_notification_inactive';
    const CUSTOM_FIELD_EMAIL_NOTIFICATION_INACTIVE_LABEL_EN = 'Benachrichtigungsfunktion nicht anzeigen';
    const CUSTOM_FIELD_EMAIL_NOTIFICATION_INACTIVE_LABEL_DE = 'Hide e-mail notification';
    const DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFICATION_REGISTER = 'stockNotificationRegister';
    const DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_DOUBLE_OPT_IN = 'stockNotificationDoubleOptIn';
    const DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFY = 'stockNotificationNotify';
    const DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_CONFIRM_NAME = 'Stock notification double opt-in confirmed';
    const DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_REGISTER_NAME = 'Stock notification double opt-in registered';
    const DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_NOTIFY_NAME = 'Stock notification notified';

    public function install(InstallContext $context): void
    {
        $this->addCustomFields($context->getContext());
        $this->addCustomFieldsForNotification($context->getContext());
    }

    public function activate(ActivateContext $activateContext): void
    {
        $this->insertDefaultData($activateContext->getContext());
        $this->upsertStockNotificationForProducts($activateContext->getContext());

    }

    public function postUpdate(UpdateContext $context): void
    {
        if(version_compare($context->getCurrentPluginVersion(), '2.2.0', '<')
            && version_compare($context->getUpdatePluginVersion(), '2.2.0', '>=')) {
            if($context->getPlugin()->isActive() === true) {
                $this->upsertStockNotificationForProducts($context->getContext());
            }
        }

        if(version_compare($context->getCurrentPluginVersion(), '3.0.0', '<')
            && version_compare($context->getUpdatePluginVersion(), '3.0.0', '>=')) {
            if($context->getPlugin()->isActive() === true) {
                $this->upsertFlowEvents($context->getContext());
            }
        }

        if(version_compare($context->getCurrentPluginVersion(), '3.2.0', '<')
            && version_compare($context->getUpdatePluginVersion(), '3.2.0', '>=')) {
            if($context->getPlugin()->isActive() === true) {
                $this->addCustomFieldsForNotification($context->getContext());
            }
        }

        $this->insertDefaultData($context->getContext());
    }

    public function uninstall(UninstallContext $context): void
    {
        if ($context->keepUserData()) {
            return;
        }
        $this->removeCustomFields($context->getContext(), [self::CUSTOM_FIELD_SET_NAME, self::CUSTOM_FIELD_SET_INACTIVE_NAME]);
        $this->removeTables();
        $this->removeDefaultMailTemplate($context->getContext());
    }

    private function insertDefaultData(Context $context): void
    {
        $this->insertDefaultMailTemplate($context);
    }

    private function addCustomFields(Context $context): void
    {
        /* Check for snippets if they exist for custom fields */
        $this->checkForExistingCustomFieldSnippets($context);

        $customFieldSet = $this->container->get('custom_field_set.repository');
        if($customFieldSet->search((new Criteria())->addFilter(new EqualsFilter('name', self::CUSTOM_FIELD_SET_NAME)), $context)->count() > 0) return;
        $customFieldSet->create([[
            'name' => self::CUSTOM_FIELD_SET_NAME,
            'config' => [
                'label' => [
                    'de-DE' => self::CUSTOM_FIELD_SET_LABEL_DE,
                    'en-GB' => self::CUSTOM_FIELD_SET_LABEL_EN
                ]
            ],
            'customFields' => [
                ['name' => self::CUSTOM_FIELD_EMAIL_NOTIFICATION_NAME, 'type' => CustomFieldTypes::BOOL,
                    'config' => [
                        'componentName' => 'sw-field',
                        'type' => 'switch',
                        'customFieldType' => 'switch',
                        'customFieldPosition' => 1,
                        'label' => [
                            'de-DE' => self::CUSTOM_FIELD_EMAIL_NOTIFICATION_LABEL_DE,
                            'en-GB' => self::CUSTOM_FIELD_EMAIL_NOTIFICATION_LABEL_EN
                        ]
                    ]]
            ],
        ]], $context);
    }

    private function addCustomFieldsForNotification(Context $context): void
    {
        /* Check for snippets if they exist for custom fields */
        $this->checkForExistingCustomFieldSnippets($context);

        $customFieldSet = $this->container->get('custom_field_set.repository');
        if($customFieldSet->search((new Criteria())->addFilter(new EqualsFilter('name', self::CUSTOM_FIELD_SET_INACTIVE_NAME)), $context)->count() > 0) return;
        $customFieldSet->create([[
            'name' => self::CUSTOM_FIELD_SET_INACTIVE_NAME,
            'config' => [
                'label' => [
                    'de-DE' => self::CUSTOM_FIELD_SET_INACTIVE_LABEL_DE,
                    'en-GB' => self::CUSTOM_FIELD_SET_INACTIVE_LABEL_EN
                ]
            ],
            'customFields' => [
                ['name' => self::CUSTOM_FIELD_EMAIL_NOTIFICATION_INACTIVE_NAME, 'type' => CustomFieldTypes::BOOL,
                    'config' => [
                        'componentName' => 'sw-field',
                        'type' => 'switch',
                        'customFieldType' => 'switch',
                        'customFieldPosition' => 1,
                        'label' => [
                            'de-DE' => self::CUSTOM_FIELD_EMAIL_NOTIFICATION_INACTIVE_LABEL_DE,
                            'en-GB' => self::CUSTOM_FIELD_EMAIL_NOTIFICATION_INACTIVE_LABEL_EN
                        ]
                    ]]
            ],
        ]], $context);
    }



    private function insertDefaultMailTemplate(Context $context)
    {
        $mailTemplateTypeRepository = $this->container->get('mail_template_type.repository');

        // Stock notification register
        /** @var IdSearchResult $mailTemplateTypeStockNotificationRegisterSearchResult */
        $mailTemplateTypeStockNotificationRegisterSearchResult = $mailTemplateTypeRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('technicalName', self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFICATION_REGISTER)), $context);
        if(empty($mailTemplateTypeStockNotificationRegisterSearchResult->firstId())) {
            $mailTemplateTypeStockNotificationRegisterId = Uuid::randomHex();
            $mailTemplateStockNotificationRegisterId = Uuid::randomHex();
            $mailTemplateTypeStockNotificationRegisterData = [
                'id' => $mailTemplateTypeStockNotificationRegisterId,
                'technicalName' => self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFICATION_REGISTER,
                'availableEntities' => ['stockNotificationRecipient' => 'acris_stock_notification', 'salesChannel' => 'sales_channel'],
                'translations' => [
                    'de-DE' => [
                        'name' => 'ACRIS Lagerstands Benachrichtigung Double-Opt-In Registrierung'
                    ],
                    'en-GB' => [
                        'name' => 'ACRIS Stock Notification double opt-in registration'
                    ]
                ],
                'name' => 'ACRIS Stock Notification double opt-in registration',
                'mailTemplates' => [
                    [
                        'id' => $mailTemplateStockNotificationRegisterId,
                        'systemDefault' => true,
                        'translations' => [
                            'de-DE' => [
                                'senderName' => '{{ salesChannel.name }}',
                                'subject' => '{{ salesChannel.name }} - Registrierung der E-Mail Adresse für Lagerstands Benachrichtigung',
                                'description' => 'Double-Opt-In für die Registrierung der E-Mail zur Lagerstands Benachrichtigung.',
                                'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/de-DE/stock-notification-register.html.twig'),
                                'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/de-DE/stock-notification-register.html.twig')
                            ],
                            'en-GB' => [
                                'senderName' => '{{ salesChannel.name }}',
                                'subject' => '{{ salesChannel.name }} - Registration of the email address for stock level notification',
                                'description' => 'Double opt-in for the registration of the stock level notification email.',
                                'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/en-GB/stock-notification-register.html.twig'),
                                'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/en-GB/stock-notification-register.html.twig')
                            ],
                        ],
                        'senderName' => '{{ salesChannel.name }}',
                        'subject' => '{{ salesChannel.name }} - Registration of the email address for stock level notification',
                        'description' => 'Double opt-in for the registration of the stock level notification email.',
                        'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/en-GB/stock-notification-register.html.twig'),
                        'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/en-GB/stock-notification-register.html.twig')
                    ]
                ]
            ];
            $mailTemplateTypeRepository->upsert([$mailTemplateTypeStockNotificationRegisterData], $context);

            // upsert flow builder
            $this->upsertFlowBuilder(self::DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_REGISTER_NAME, StockNotificationRegisterEvent::EVENT_NAME, $mailTemplateStockNotificationRegisterId, $mailTemplateTypeStockNotificationRegisterId, $context);
        }

        // Stock notification confirm
        /** @var IdSearchResult $mailTemplateTypeStockNotificationConfirmSearchResult */
        $mailTemplateTypeStockNotificationConfirmSearchResult = $mailTemplateTypeRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('technicalName', self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_DOUBLE_OPT_IN)), $context);
        if(empty($mailTemplateTypeStockNotificationConfirmSearchResult->firstId())) {
            $mailTemplateTypeStockNotificationConfirmId = Uuid::randomHex();
            $mailTemplateStockNotificationConfirmId = Uuid::randomHex();
            $mailTemplateTypeStockNotificationConfirmData = [
                'id' => $mailTemplateTypeStockNotificationConfirmId,
                'technicalName' => self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_DOUBLE_OPT_IN,
                'availableEntities' => ['stockNotificationRecipient' => 'acris_stock_notification', 'salesChannel' => 'sales_channel'],
                'translations' => [
                    'de-DE' => [
                        'name' => 'ACRIS Lagerstands Benachrichtigung Double-Opt-In Bestätigung'
                    ],
                    'en-GB' => [
                        'name' => 'ACRIS Stock Notification double opt-in confirmation'
                    ]
                ],
                'name' => 'ACRIS Stock Notification double opt-in confirmation',
                'mailTemplates' => [
                    [
                        'id' => $mailTemplateStockNotificationConfirmId,
                        'systemDefault' => true,
                        'translations' => [
                            'de-DE' => [
                                'senderName' => '{{ salesChannel.name }}',
                                'subject' => '{{ salesChannel.name }} - E-Mail Adresse für Lagerstands Benachrichtigung wurde bestätigt',
                                'description' => 'Double-Opt-In wurde korrekt durchgeführt und die E-Mail Addresse wurde für die automatische Lagerstands Benachrichtigung erfolgreich registriert.',
                                'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/de-DE/stock-notification-confirm.html.twig'),
                                'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/de-DE/stock-notification-confirm.html.twig')
                            ],
                            'en-GB' => [
                                'senderName' => '{{ salesChannel.name }}',
                                'subject' => '{{ salesChannel.name }} - Email address for stock notification has been confirmed',
                                'description' => 'Double-Opt-In was performed correctly and the email address was successfully registered for the automatic stock level notification.',
                                'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/en-GB/stock-notification-confirm.html.twig'),
                                'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/en-GB/stock-notification-confirm.html.twig')
                            ],
                        ],
                        'senderName' => '{{ salesChannel.name }}',
                        'subject' => '{{ salesChannel.name }} - Email address for stock notification has been confirmed',
                        'description' => 'Double-Opt-In was performed correctly and the email address was successfully registered for the automatic stock level notification.',
                        'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/en-GB/stock-notification-confirm.html.twig'),
                        'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/en-GB/stock-notification-confirm.html.twig')
                    ]
                ]
            ];
            $mailTemplateTypeRepository->upsert([$mailTemplateTypeStockNotificationConfirmData], $context);

            // upsert flow builder
            $this->upsertFlowBuilder(self::DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_CONFIRM_NAME, StockNotificationConfirmEvent::EVENT_NAME, $mailTemplateStockNotificationConfirmId, $mailTemplateTypeStockNotificationConfirmId, $context);
        }

        // Stock notification notify
        /** @var IdSearchResult $mailTemplateTypeStockNotificationNotifySearchResult */
        $mailTemplateTypeStockNotificationNotifySearchResult = $mailTemplateTypeRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('technicalName', self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFY)), $context);
        if(empty($mailTemplateTypeStockNotificationNotifySearchResult->firstId())) {
            $mailTemplateTypeStockNotificationNotifyId = Uuid::randomHex();
            $mailTemplateStockNotificationNotifyId = Uuid::randomHex();
            $mailTemplateTypeStockNotificationNotifyData = [
                'id' => $mailTemplateTypeStockNotificationNotifyId,
                'technicalName' => self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFY,
                'availableEntities' => ['stockNotificationRecipient' => 'acris_stock_notification', 'salesChannel' => 'sales_channel'],
                'translations' => [
                    'de-DE' => [
                        'name' => 'ACRIS Lagerstands Benachrichtigung'
                    ],
                    'en-GB' => [
                        'name' => 'ACRIS Stock Notification'
                    ]
                ],
                'name' => 'ACRIS Stock Notification',
                'mailTemplates' => [
                    [
                        'id' => $mailTemplateStockNotificationNotifyId,
                        'systemDefault' => true,
                        'translations' => [
                            'de-DE' => [
                                'senderName' => '{{ salesChannel.name }}',
                                'subject' => 'Das Produkt {{ stockNotificationRecipient.product.translated.name }} ist wieder verfügbar!',
                                'description' => 'E-Mail zur Information, dass ein Artikel wieder lagernd ist.',
                                'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/de-DE/stock-notification-notify.html.twig'),
                                'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/de-DE/stock-notification-notify.html.twig')
                            ],
                            'en-GB' => [
                                'senderName' => '{{ salesChannel.name }}',
                                'subject' => 'The product {{ stockNotificationRecipient.product.translated.name }} is available again!',
                                'description' => 'E-mail informing that an item is back in stock.',
                                'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/en-GB/stock-notification-notify.html.twig'),
                                'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/en-GB/stock-notification-notify.html.twig')
                            ],
                        ],
                        'senderName' => '{{ salesChannel.name }}',
                        'subject' => 'The product {{ stockNotificationRecipient.product.translated.name }} is available again!',
                        'description' => 'E-mail informing that an item is back in stock.',
                        'contentHtml' => file_get_contents($this->path . '/Resources/mail-template/html/en-GB/stock-notification-notify.html.twig'),
                        'contentPlain' => file_get_contents($this->path . '/Resources/mail-template/plain/en-GB/stock-notification-notify.html.twig')
                    ]
                ]
            ];
            $mailTemplateTypeRepository->upsert([$mailTemplateTypeStockNotificationNotifyData], $context);

            // upsert flow builder
            $this->upsertFlowBuilder(self::DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_NOTIFY_NAME, StockNotificationNotifyEvent::EVENT_NAME, $mailTemplateStockNotificationNotifyId, $mailTemplateTypeStockNotificationNotifyId, $context);
        }
    }

    private function removeDefaultMailTemplate(Context $context)
    {
        $mailTemplateTypeRepository = $this->container->get('mail_template_type.repository');

        /** @var IdSearchResult $mailTemplateTypeStockNotificationSearchResult */
        $mailTemplateTypeStockNotificationSearchResult = $mailTemplateTypeRepository->searchIds((new Criteria())->addFilter(new EqualsAnyFilter('technicalName', [self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFICATION_REGISTER, self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_DOUBLE_OPT_IN, self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFY])), $context);
        if($mailTemplateTypeStockNotificationSearchResult->firstId()) {
            $mailTemplateRepository = $this->container->get('mail_template.repository');
            $deleteTemplateTypeData = [];
            $deleteTemplateData = [];
            foreach ($mailTemplateTypeStockNotificationSearchResult->getIds() as $templateTypeId) {
                /** @var IdSearchResult $mailTemplateSearchResult */
                $mailTemplateSearchResult = $mailTemplateRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('mailTemplateTypeId', $templateTypeId)), $context);

                if ($mailTemplateSearchResult->firstId()) {
                    foreach ($mailTemplateSearchResult->getIds() as $id) {
                        $deleteTemplateData[] = ['id' => $id];
                    }
                }
                $deleteTemplateTypeData[] = ['id' => $templateTypeId];

            }

            if (!empty($deleteTemplateData)) {
                $mailTemplateRepository->delete($deleteTemplateData, $context);
            }
            if (!empty($deleteTemplateTypeData)) {
                $mailTemplateTypeRepository->delete($deleteTemplateTypeData, $context);
            }
        }

        //remove flow events
        $flowRepository = $this->container->get('flow.repository');
        /** @var IdSearchResult $flowSearchResult */
        $flowSearchResult = $flowRepository->searchIds((new Criteria())->addFilter(new EqualsAnyFilter('eventName', [StockNotificationRegisterEvent::EVENT_NAME, StockNotificationConfirmEvent::EVENT_NAME, StockNotificationNotifyEvent::EVENT_NAME])), $context);
        if($flowSearchResult->firstId()) {
            $deleteBusinessEvents = [];
            foreach ($flowSearchResult->getIds() as $id) {
                $deleteBusinessEvents[] = ['id' => $id];
            }
            if (!empty($deleteBusinessEvents)) {
                $flowRepository->delete($deleteBusinessEvents, $context);
            }
        }
    }

    private function removeTables(): void
    {
        $connection = $this->container->get(Connection::class);
        $connection->executeStatement('ALTER TABLE product DROP COLUMN acrisStockNotifications');
        $connection->executeStatement('DROP TABLE IF EXISTS `acris_stock_notification`');
    }

    private function removeCustomFields(Context $context, array $setNames): void
    {
        /* Check for snippets if they exist for custom fields */
        $this->checkForExistingCustomFieldSnippets($context);

        $customFieldSet = $this->container->get('custom_field_set.repository');
        foreach ($setNames as $setName) {
            $id = $customFieldSet->searchIds((new Criteria())->addFilter(new EqualsFilter('name', $setName)), $context)->firstId();
            if($id) $customFieldSet->delete([['id' => $id]], $context);
        }
    }

    private function checkForExistingCustomFieldSnippets(Context $context)
    {
        /** @var EntityRepository $snippetRepository */
        $snippetRepository = $this->container->get('snippet.repository');

        $criteria = new Criteria();
        $criteria->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, [
            new EqualsFilter('translationKey', 'customFields.' . self::CUSTOM_FIELD_EMAIL_NOTIFICATION_NAME)
        ]));

        /** @var EntitySearchResult $searchResult */
        $searchResult = $snippetRepository->search($criteria, $context);

        if ($searchResult->count() > 0) {
            $snippetIds = [];
            /** @var SnippetEntity $snippet */
            foreach ($searchResult->getEntities()->getElements() as $snippet) {
                $snippetIds[] = [
                    'id' => $snippet->getId()
                ];
            }

            if (!empty($snippetIds)) {
                $snippetRepository->delete($snippetIds, $context);
            }
        }
    }

    private function upsertStockNotificationForProducts(Context $context): void
    {
        /** @var EntityRepository $stockNotificationRepository */
        $stockNotificationRepository = $this->container->get('acris_stock_notification.repository');

        /** @var EntityRepository $stockNotificationProductRepository */
        $stockNotificationProductRepository = $this->container->get('acris_stock_notification_product.repository');

        $criteriaIds = new Criteria();
        $criteriaIds->addFilter(new EqualsFilter('registered', false));
        $criteriaIds->setTotalCountMode(Criteria::TOTAL_COUNT_MODE_EXACT);

        /** @var IdSearchResult $searchResult */
        $searchResult = $stockNotificationRepository->searchIds($criteriaIds, $context);

        if ($searchResult->getTotal() <= 0 || !$searchResult->firstId()) {
            return;
        }

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('registered', false));
        $criteria->addAssociation('product');

        $pages = ceil($searchResult->getTotal() / self::STACK_SIZE);
        for ($i = 0; $i < $pages; $i++) {
            $updateIds = [];
            $offset = $i * self::STACK_SIZE;
            $stockNotificationPerPage = $stockNotificationRepository->search($criteria->setLimit(self::STACK_SIZE)->setOffset($offset), $context);
            /** @var StockNotificationEntity $element */
            foreach ($stockNotificationPerPage->getEntities()->getElements() as $element) {
                if (empty($element->getProductId())) continue;
                $updateIds[] = [
                    'id' => $element->getId(),
                    'registered' => true
                ];

                $this->upsertStockNotificationForProduct($element->getProductId(), $element->getNotified(), $stockNotificationProductRepository, $context);
            }

            if (!empty($updateIds)) $stockNotificationRepository->upsert($updateIds, $context);
        }
    }

    private function upsertStockNotificationForProduct(string $productId, ?bool $isNotified, EntityRepository $repository, Context $context): void
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('productId', $productId));

        /** @var StockNotificationProductEntity $registeredProduct */
        $registeredProduct = $repository->search($criteria, $context)->first();
        if (empty($registeredProduct)) {
            $registered = 1;
            $sent = $isNotified ? 1 : 0;
            $open = $registered - $sent;
            $data = [
                [
                    'productId' => $productId,
                    'registered' => $registered,
                    'sent' => $sent,
                    'open' => $open,
                ]
            ];
        } else {
            $registered = $registeredProduct->getRegistered() + 1;
            $sent = $isNotified ? $registeredProduct->getSent() + 1 : $registeredProduct->getSent();
            $open = $registered - $sent;
            $data = [
                [
                    'id' => $registeredProduct->getId(),
                    'registered' => $registered,
                    'sent' => $sent,
                    'open' => $open,
                ]
            ];
        }

        $repository->upsert($data, $context);
    }

    private function upsertFlowEvents(Context $context): void
    {
        $mailTemplateTypeRepository = $this->container->get('mail_template_type.repository');
        $mailTemplateRepository = $this->container->get('mail_template.repository');

        // Stock notification register
        /** @var IdSearchResult $mailTemplateTypeStockNotificationRegisterSearchResult */
        $mailTemplateTypeStockNotificationRegisterSearchResult = $mailTemplateTypeRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('technicalName', self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFICATION_REGISTER)), $context);
        if($mailTemplateTypeStockNotificationRegisterSearchResult->getTotal() > 0 && !empty($mailTemplateTypeStockNotificationRegisterSearchResult->firstId())) {
            $mailTemplateTypeStockNotificationRegisterId = $mailTemplateTypeStockNotificationRegisterSearchResult->firstId();
            /** @var IdSearchResult $mailTemplateStockNotificationRegisterSearchResult */
            $mailTemplateStockNotificationRegisterSearchResult = $mailTemplateRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('mailTemplateTypeId', $mailTemplateTypeStockNotificationRegisterId)), $context);
            if ($mailTemplateStockNotificationRegisterSearchResult->getTotal() > 0 && !empty($mailTemplateStockNotificationRegisterSearchResult->firstId()) ) {
                $mailTemplateStockNotificationRegisterId = $mailTemplateStockNotificationRegisterSearchResult->firstId();

                // upsert flow builder
                $this->upsertFlowBuilder(self::DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_REGISTER_NAME, StockNotificationRegisterEvent::EVENT_NAME, $mailTemplateStockNotificationRegisterId, $mailTemplateTypeStockNotificationRegisterId, $context);
            }
        }

        // Stock notification confirm
        /** @var IdSearchResult $mailTemplateTypeStockNotificationConfirmSearchResult */
        $mailTemplateTypeStockNotificationConfirmSearchResult = $mailTemplateTypeRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('technicalName', self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_DOUBLE_OPT_IN)), $context);
        if($mailTemplateTypeStockNotificationConfirmSearchResult->getTotal() > 0 && !empty($mailTemplateTypeStockNotificationConfirmSearchResult->firstId())) {
            $mailTemplateTypeStockNotificationConfirmId = $mailTemplateTypeStockNotificationConfirmSearchResult->firstId();
            /** @var IdSearchResult $mailTemplateStockNotificationConfirmSearchResult */
            $mailTemplateStockNotificationConfirmSearchResult = $mailTemplateRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('mailTemplateTypeId', $mailTemplateTypeStockNotificationConfirmId)), $context);
            if ($mailTemplateStockNotificationConfirmSearchResult->getTotal() > 0 && !empty($mailTemplateStockNotificationConfirmSearchResult->firstId()) ) {
                $mailTemplateStockNotificationConfirmId = $mailTemplateStockNotificationConfirmSearchResult->firstId();

                // upsert flow builder
                $this->upsertFlowBuilder(self::DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_CONFIRM_NAME, StockNotificationConfirmEvent::EVENT_NAME, $mailTemplateStockNotificationConfirmId, $mailTemplateTypeStockNotificationConfirmId, $context);
            }
        }

        // Stock notification notify
        /** @var IdSearchResult $mailTemplateTypeStockNotificationNotifySearchResult */
        $mailTemplateTypeStockNotificationNotifySearchResult = $mailTemplateTypeRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('technicalName', self::DEFAULT_MAIL_TEMPLATE_TYPE_STOCK_NOTIFY)), $context);
        if($mailTemplateTypeStockNotificationNotifySearchResult->getTotal() > 0 && !empty($mailTemplateTypeStockNotificationNotifySearchResult->firstId())) {
            $mailTemplateTypeStockNotificationNotifyId = $mailTemplateTypeStockNotificationNotifySearchResult->firstId();
            /** @var IdSearchResult $mailTemplateStockNotificationNotifySearchResult */
            $mailTemplateStockNotificationNotifySearchResult = $mailTemplateRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('mailTemplateTypeId', $mailTemplateTypeStockNotificationNotifyId)), $context);
            if ($mailTemplateStockNotificationNotifySearchResult->getTotal() > 0 && !empty($mailTemplateStockNotificationNotifySearchResult->firstId()) ) {
                $mailTemplateStockNotificationNotifyId = $mailTemplateStockNotificationNotifySearchResult->firstId();

                // upsert flow builder
                $this->upsertFlowBuilder(self::DEFAULT_FLOW_EVENT_STOCK_NOTIFICATION_NOTIFY_NAME, StockNotificationNotifyEvent::EVENT_NAME, $mailTemplateStockNotificationNotifyId, $mailTemplateTypeStockNotificationNotifyId, $context);
            }
        }
    }

    private function upsertFlowBuilder(string $name, string $eventName, string $mailTemplateId, string $mailTemplateTypeId, Context $context): void
    {
        $flowRepository = $this->container->get('flow.repository');
        $flowRepository->upsert([[
            "name" => $name,
            "eventName" => $eventName,
            "priority" => 10,
            "active" => true,
            "sequences" => [
                [
                    "actionName" => SendMailAction::getName(),
                    "config" => [
                        "mailTemplateId" => $mailTemplateId,
                        'mail_template_type_id' => $mailTemplateTypeId,
                        "recipient" => [
                            "type" => "default",
                            "data" => []
                        ]
                    ],
                    "position" => 1
                ]
            ]
        ]], $context);
    }
}
