<?php declare(strict_types=1);

namespace Acris\StockNotification\Elasticsearch\Product;

use OpenSearchDSL\Query\Compound\BoolQuery;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Log\Package;
use Shopware\Elasticsearch\Framework\AbstractElasticsearchDefinition;
use Shopware\Elasticsearch\Product\ElasticsearchProductDefinition;

#[Package('core')]
class ElasticsearchProductDefinitionDecorated extends ElasticsearchProductDefinition
{
    public function __construct(private readonly AbstractElasticsearchDefinition $parent)
    {
    }

    public function getEntityDefinition(): EntityDefinition
    {
        return $this->parent->getEntityDefinition();
    }

    public function getMapping(Context $context): array
    {
        $mapping = $this->parent->getMapping($context);

        return $this->assignStockNotificationMapping($mapping);
    }

    public function extendDocuments(array $documents, Context $context): array
    {
        return $this->parent->extendDocuments($documents, $context);
    }

    public function buildTermQuery(Context $context, Criteria $criteria): BoolQuery
    {
        return $this->parent->buildTermQuery($context, $criteria);
    }

    public function fetch(array $ids, Context $context): array
    {
        return $this->parent->fetch($ids, $context);
    }

    private function assignStockNotificationMapping(array $mapping): array
    {
        if (!array_key_exists('properties', $mapping) || !is_array($mapping['properties']) || !array_key_exists('customFields', $mapping['properties']) || !is_array($mapping['properties']['customFields']) || !array_key_exists('properties', $mapping['properties']['customFields']) || !is_array($mapping['properties']['customFields']['properties'])) {
            return $mapping;
        }

        if (!array_key_exists('acris_stock_notification_email_notification', $mapping['properties']['customFields']['properties'])) {
            $mapping['properties']['customFields']['properties']['acris_stock_notification_email_notification'] = self::BOOLEAN_FIELD;
        }

        if (!array_key_exists('acris_stock_notification_email_notification_inactive', $mapping['properties']['customFields']['properties'])) {
            $mapping['properties']['customFields']['properties']['acris_stock_notification_email_notification_inactive'] = self::BOOLEAN_FIELD;
        }

        return $mapping;
    }
}
