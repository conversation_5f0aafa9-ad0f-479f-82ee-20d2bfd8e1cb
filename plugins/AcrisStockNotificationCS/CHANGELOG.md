# 4.4.1
- Optimization of the form validation.

# 4.4.0
- Added new option for showing privacy on stock notification (to inherent from shopware settings - Settings->Log-in and sign-up->Data protection information must be accepted via a checkbox)

# 4.3.4
- Improves plugin compatibility with Elasticsearch.

# 4.3.3
- Admin filter for notified status on notification product detail page

# 4.3.2
- The checkbox field or the text for the data protection notice is only displayed if the e-mail field is also displayed.

# 4.3.1
- Added 'Notified' filter on 'Stock notification for products' page.

# 4.3.0
- Added 'Notified' filter.

# 4.2.0
- Added new data privacy options

# 4.1.2
- Change the interval stored in the standard Scheduled Task from 1x daily to 1x hourly.

# 4.1.1
- Optimizes handling of the form request response.

# 4.1.0
- Adds plugin configuration for hiding of the delivery time information when the stock notification is displayed at the product page in the Storefront.
- Optimizes stock notification product calculation on the cart recalculation.

# 4.0.7
- Adds validation on sending of the stock notification request.

# 4.0.6
- Optimizes adding the product to the cart when the stock notification feature is active for the product.

# 4.0.5
- Logic of orderable quantity and stock notification optimized
- Renaming of the plugin setting for orderable products with active stock notification

# 4.0.4
- Logic problem of orderable quantity and stock notification fixed

# 4.0.3
- Optimizes checking of the available products.

# 4.0.2
- Optimizes displaying of the stock notification for products listing in the Administration.

# 4.0.1
- Optimizes overview based on products module in the Administration.

# 4.0.0
- Compatibility with Shopware 6.5.

# 3.5.2
- Display problem on backorder products fixed

# 3.5.1
- Logic of quantity selection improved

# 3.5.0
- Plugin settings for quantity selection were combined
- Better wording of plugin settings

# 3.4.0
- New plugin setting to limit the quantity selection from the product

# 3.3.0
- Added registry date to stock notification table columns and detail page.

# 3.2.7
- Fixed product available for purchase email error.

# 3.2.6
- Fixed email notification for product variants.

# 3.2.5
- Fixes issue with stock notification not sending.

# 3.2.4
- Fixes a problem with loading the listing "Stock notification for products" in the administration

# 3.2.3
- Optimizes the loading of the plugin configuration in the administration

# 3.2.2
- Fixes a problem loading the email notification field on the product page in administration

# 3.2.1
- Optimization of the product page in the administration
- Optimization of the shopping cart logic of products with stock level notifications

# 3.2.0
- New plugin setting to activate the stock notifications.

# 3.1.5
- Change of the plugin name and the manufacturer links.

# 3.1.4
- Optimizes loading of the stock notification products in the scheduled task.

# 3.1.3
- Optimizes displaying of the stock notification form.

# 3.1.2
- Moves ACRIS E-Mail Notification to custom fields to save the additional fields correctly.

# 3.1.1
- Optimized bulk edit for custom fields.

# 3.1.0
- Bulk edit for e-mail notification in Administration product edit page.

# 3.0.3
- Optimizes loading of the stock notification at the Cms product detail page in Storefront.

# 3.0.2
- Fixes error on administration building.

# 3.0.1
- Optimizes plugin image.
- Improves compatibility with Shopware >= 6.4.10.0.
- Optimizes plugin color in administration.

# 3.0.0
- Migration from business events to the flow builder.
- Improved compatibility with Shopware 6.4.6*.

# 2.2.3
- Optimizes stock notification CMS element.

# 2.2.2
- Adds registered flag for the stock notifications.

# 2.2.1
- Optimizes list of registered users for product in Administration.

# 2.2.0
- Optimizes storing stock notification data for products.

# 2.1.1
- Optimizes loading of stock notification for the products in Administration.

# 2.1.0
- Added shopping experience element for the stock notification

# 2.0.1
- Optimization of the scheduled task.

# 2.0.0
- Improved compatibility with Shopware 6.4*.

# 1.2.1
- Fixes an issue where the wrong product name was written to the stock notification registry.

# 1.2.0
- Added new administration view with numbers of registered users to a product.

# 1.1.3
- Fixed setting next execution time for stock notification scheduled task.

# 1.1.2
- Fixed setting of storefront URL for confirming email.

# 1.1.1
- Fixed missing default language for creating default data on plugin installation.

# 1.1.0
- Fixed help text for showing e-mail notification on product page.
- Added product number column to notification list.

# 1.0.2
- Fixes a problem with the display of the "Add to cart" button in the product listing

# 1.0.1
- Improved compatibility with other plugins and themes.

# 1.0.0
- Release.
