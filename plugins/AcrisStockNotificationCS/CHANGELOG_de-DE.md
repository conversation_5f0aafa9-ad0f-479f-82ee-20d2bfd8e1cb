# 4.4.1
- Optimierung der Formular Validierung.

# 4.4.0
- Neue Option für die Anzeige des Datenschutzes bei Bestandsmeldungen hinzugefügt (aus den Shopware-Einstellungen zu übernehmen - Einstellungen->Anmeldung und Registrierung-> Datenschutzbestimmungen müssen über eine Checkbox akzeptiert werden)

# 4.3.4
- Verbessert die Kompatibilität des Plugins mit Elasticsearch.

# 4.3.3
- Admin-<PERSON>lter für den Benachrichtigungsstatus auf der Detailseite des Benachrichtigungsprodukts

# 4.3.2
- Das Checkbox Feld oder der Text für den Datenschutzhinweis wird nur angezeigt, wenn auch das E-Mail Feld angezeigt wird.

# 4.3.1
- Filter „Benachrichtigt“ auf der Seite „Bestandsbenachrichtigung für Produkte“ hinzugefügt.

# 4.3.0
- Filter 'Bereits benachrichtigt' hinzugefügt.

# 4.2.0
- Neue Datenschutzoptionen hinzugefügt

# 4.1.2
- Änderung des im Standard beim Scheduled Task hinterlegten Intervalls von 1x täglich auf 1x stündlich.

# 4.1.1
- Optimiert den Umgang mit der Antwort auf die Formularanfrage.

# 4.1.0
- Fügt die Plugin-Konfiguration für das Ausblenden der Lieferzeitinformationen hinzu, wenn die Bestandsmeldung auf der Produktseite in der Storefront angezeigt wird.
- Optimiert die Produktberechnung der Bestandsmeldung bei der Neuberechnung des Warenkorbs.

# 4.0.7
- Fügt die Validierung beim Senden der Bestandsmeldungsanforderung hinzu.

# 4.0.6
- Optimiert das Hinzufügen des Produkts zum Warenkorb, wenn die Bestandsbenachrichtigungsfunktion für das Produkt aktiv ist.

# 4.0.5
- Logik der bestellbaren Menge und der Lagerstandsbenachrichtiung optimiert
- Umgenennung der Plugineinstellung für bestellbare Produkte mit aktiver Lagerstandsbenachrichtigung

# 4.0.4
- Logik Problem der bestellbaren Menge und der Lagerstandsbenachrichtigung behoben

# 4.0.3
- Optimiert die Überprüfung der verfügbaren Produkte.

# 4.0.2
- Optimiert die Anzeige der Bestandsmeldung für die Produktauflistung in der Verwaltung.

# 4.0.1
- Optimiert die Übersicht basierend auf dem Produktmodul in der Verwaltung.

# 4.0.0
- Kompatibilität mit Shopware 6.5.

# 3.5.2
- Anzeigeproblem bei Produkten mit Nachbestellung behoben

# 3.5.1
- Logik der Mengenauswahl verbessert

# 3.5.0
- Plugin Einstellungen für die Mengenauswahl wurden kombiniert
- Bessere Benennung der Plugin Einstellungen

# 3.4.0
- Neue Plugin Einstellung um die Mengenauswahl vom Produkt zu begrenzen

# 3.3.0
- Das Registrierungsdatum wurde zu den Spalten der Bestandsmeldetabelle und der Detailseite hinzugefügt.

# 3.2.7
- Fehler in der E-Mail "Produkt zum Kauf verfügbar" behoben.

# 3.2.6
- E-Mail-Benachrichtigung für Produktvarianten behoben.

# 3.2.5
- Behebt ein Problem, bei dem Bestandsmeldungen nicht gesendet werden.

# 3.2.4
- Behebt ein Problem beim Laden der Auflistung "Benachrichtigung über den Lagerstand von Produkten" in der Administration

# 3.2.3
- Optimiert das Laden der Plugin Konfiguration in der Administration

# 3.2.2
- Behebt einen Problem beim Laden des E-Mail-Benachrichtigungs Feldes bei der Produktseite in der Administration

# 3.2.1
- Optimierung der Produktseite in der Administration
- Optimierung der Warenkorb Logik von Produkten mit Lagerstandbenachrichtigungen

# 3.2.0
- Neue Plugin Einstellung zum aktivieren der Lagerstandsbenachrichtigungen.

# 3.1.5
- Änderung des Pluginnamens und der Hersteller Links.

# 3.1.4
- Optimiert das Laden der Bestandsmeldungsprodukte in der geplanten Aufgabe.

# 3.1.3
- Optimiert die Anzeige des Bestandsmeldungsformulars.

# 3.1.2
- Verschiebt die ACRIS E-Mail-Benachrichtigung in benutzerdefinierte Felder, um die zusätzlichen Felder korrekt zu speichern.

# 3.1.1
- Optimierte Massenbearbeitung für benutzerdefinierte Felder.

# 3.1.0
- Massenbearbeitung für E-Mail-Benachrichtigung auf der Produktbearbeitungsseite der Verwaltung.

# 3.0.3
- Optimiert das Laden der Bestandsmeldung auf der Cms-Produktdetailseite in Storefront.

# 3.0.2
- Behebt einen Fehler beim Aufbau der Verwaltung.

# 3.0.1
- Optimiert das Plugin-Image.
- Verbessert die Kompatibilität mit Shopware >= 6.4.10.0.
- Optimiert die Plugin-Farbe in der Verwaltung.

# 3.0.0
- Migration von Business Events zum Flow Builder.
- Verbesserte Kompatibilität mit Shopware 6.4.6*.

# 2.2.3
- Optimiert Lager Benachrichtigung CMS Element.

# 2.2.2
- Hinzufügung eines Registrierungskennzeichens für die Bestandsmeldungen.

# 2.2.1
- Optimiert die Liste der registrierten Benutzer für das Produkt in der Verwaltung.

# 2.2.0
- Optimiert die Speicherung von Bestandsmeldungsdaten für Produkte.

# 2.1.1
- Optimiert das Laden der Bestandsmeldung für die Produkte in der Verwaltung.

# 2.1.0
- Einkaufserlebnis-Element für die Lagerstandsbenachrichtigung hinzugefügt

# 2.0.1
- Optimierung der geplanten Aufgabe.

# 2.0.0
- Kompatibilität mit Shopware 6.4* hergestellt.

# 1.2.1
- Behebt ein Problem, bei dem der falsche Produktname in die Registrierung der Lagerstandsbenachrichtigung geschrieben wurde.

# 1.2.0
- Neue Verwaltungsansicht mit Anzahl der registrierten Benutzer zu einem Produkt hinzugefügt.

# 1.1.3
- Die Einstellung der nächsten Ausführungszeit für die geplante Aufgabe "Bestandsmeldung" wurde korrigiert.

# 1.1.2
- Die Einstellung der Schaufenster-URL für die Bestätigungs-E-Mail wurde korrigiert.

# 1.1.1
- Fehlende Standardsprache für die Erstellung von Standarddaten bei der Plugin-Installation behoben.

# 1.1.0
- Hilfetext für die Anzeige der E-Mail-Benachrichtigung auf der Produktseite korrigiert.
- Spalte mit Produktnummer zur Benachrichtigungsliste hinzugefügt.

# 1.0.2
- Behebt ein Problem bei der Anzeige des "In den Warenkorb" Buttons im Produktlisting

# 1.0.1
- Verbesserte Kompatibilität mit anderen Plugins und Themes.

# 1.0.0
- Veröffentlichung.
