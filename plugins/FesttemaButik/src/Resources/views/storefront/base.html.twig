{% sw_extends '@Storefront/storefront/base.html.twig' %}

{% block base_body %}
    <body class="{% block base_body_classes %}is-ctl-{% if controllerName is not empty %}{{ controllerName|lower }}{% endif %} is-act-{% if controllerAction is not empty %}{{ controllerAction|lower }}{% endif %}{% endblock %}">

    {% block base_body_inner %}
        {% block base_noscript %}
            <noscript class="noscript-main">
                {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                    type: 'info',
                    content: 'general.noscriptNotice'|trans|sw_sanitize,
                    iconCache: false
                } %}
            </noscript>
        {% endblock %}

        {% block base_header %}
            <header class="header-main">
                {% block base_header_inner %}
                    <div class="container">
                        {% sw_include '@Storefront/storefront/layout/header/header.html.twig' %}
                    </div>
                {% endblock %}
            </header>
        {% endblock %}

        {% block base_main %}
            <main class="content-main no-padding">
                {% block base_flashbags %}
                    <div class="flashbags container">
                        {% for type, messages in app.flashes %}
                            {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with { type: type, list: messages } %}
                        {% endfor %}
                    </div>
                {% endblock %}

                {% block base_main_inner %}
                    <div class="container">
                        {% block base_main_container %}
                            <div class="container-main no-padding">
                                {% block base_content %}{% endblock %}
                            </div>
                        {% endblock %}
                    </div>
                {% endblock %}
            </main>
        {% endblock %}

    {% endblock %}

    </body>
{% endblock %}

