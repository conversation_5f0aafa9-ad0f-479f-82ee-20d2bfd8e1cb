{% sw_extends '@Storefront/storefront/page/content/product-detail.html.twig' %}

{% block base_main_inner %}
    <div class="container-main no-padding">
        {% block page_content %}

            {% block cms_content %}
                {% set cmsPageClasses = ('cms-page ' ~ page.cmsPage.cssClass|striptags)|trim %}
                <div class="{{ cmsPageClasses }}" itemscope itemtype="https://schema.org/Product">
                    {% block page_content_blocks %}
                        {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': page.cmsPage} %}
                    {% endblock %}
                </div>
            {% endblock %}
        {% endblock %}
    </div>
{% endblock %}
