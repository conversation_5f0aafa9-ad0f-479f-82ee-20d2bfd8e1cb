<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\Handler;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Profitmetrics\Profitmetrics\Defaults\CustomFieldDefaults;
use Profitmetrics\Profitmetrics\MessageQueue\Message\OrderExportMessage;
use Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\OrderExportTask;
use Profitmetrics\Profitmetrics\Service\OrderExportCriteriaBuilder;
use Shopware\Core\Checkout\Order\OrderCollection;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskCollection;
use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskHandler;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;

/**
 * Handles scheduled order export tasks by dispatching individual order export messages.
 */
#[AsMessageHandler(handles: OrderExportTask::class)]
final class OrderExportTaskHandler extends ScheduledTaskHandler
{
    /**
     * @param EntityRepository<ScheduledTaskCollection> $scheduledTaskRepository For managing scheduled tasks
     * @param MessageBusInterface $messageBus For dispatching the cleanup message
     * @param EntityRepository<OrderCollection> $orderRepository For managing orders
     * @param OrderExportCriteriaBuilder $orderCriteriaService For building order export criteria
     */
    public function __construct(
        EntityRepository $scheduledTaskRepository,
        private readonly MessageBusInterface $messageBus,
        private readonly EntityRepository $orderRepository,
        private readonly OrderExportCriteriaBuilder $orderCriteriaService,
        private readonly Connection $connection,
    ) {
        parent::__construct($scheduledTaskRepository);
    }

    /**
     * @throws ExceptionInterface
     * @throws Exception
     */
    public function run(): void
    {
        $context = new Context(new SystemSource());
        $orderIds = $this->getExportableOrderIds($context);

        if (empty($orderIds)) {
            return;
        }

        $this->markOrdersAsInProgress($orderIds);
        $this->dispatchExportMessages($orderIds);
    }

    /**
     * @return array<string>
     */
    private function getExportableOrderIds(Context $context): array
    {
        $criteria = $this->orderCriteriaService->createExportCriteria($context);
        /** @var array<string> */
        return $this->orderRepository->searchIds($criteria, $context)->getIds();
    }

    /**
     * Updates order states to indicate export is in progress using direct connection
     *
     * @param array<string> $orderIds Orders to mark as in progress
     *
     * @throws Exception
     */
    private function markOrdersAsInProgress(array $orderIds): void
    {
        $customFields = json_encode([
            CustomFieldDefaults::PM_ORDER_EXPORT_STATE => CustomFieldDefaults::EXPORT_STATE_IN_PROGRESS,
        ]);

        // Convert hex IDs to bytes and create placeholders
        $binaryOrderIds = array_map(
            fn (string $id) => sprintf('UNHEX(\'%s\')', $id),
            $orderIds
        );

        $this->connection->executeStatement(
            sprintf(
                'UPDATE `order`
                SET `custom_fields` = JSON_MERGE_PATCH(
                    COALESCE(`custom_fields`, "{}"),
                    :customFields
                )
                WHERE `id` IN (%s)',
                implode(',', $binaryOrderIds)
            ),
            ['customFields' => $customFields]
        );
    }

    /**
     * @param array<string> $orderIds
     *
     * @throws ExceptionInterface
     */
    private function dispatchExportMessages(array $orderIds): void
    {
        foreach ($orderIds as $orderId) {
            $this->messageBus->dispatch(new OrderExportMessage($orderId));
        }
    }
}
