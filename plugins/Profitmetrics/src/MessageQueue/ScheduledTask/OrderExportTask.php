<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask;

use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTask;

/**
 * Scheduled task for sending order data to ProfitMetrics.
 */
final class OrderExportTask extends ScheduledTask
{
    public const TASK_NAME = 'profitmetrics.order_export';
    public const TASK_INTERVAL = 300; // 5 minutes

    public static function getTaskName(): string
    {
        return self::TASK_NAME;
    }

    public static function getDefaultInterval(): int
    {
        return self::TASK_INTERVAL;
    }
}
