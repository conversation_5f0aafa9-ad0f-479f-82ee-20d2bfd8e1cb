<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask;

use Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTask;

/**
 * Scheduled task for cleaning up visitor tracking data.
 */
final class VisitorCleanupTask extends ScheduledTask
{
    public const TASK_NAME = 'profitmetrics.visitor_cleanup';
    public const TASK_INTERVAL = 86400; // 24 hours (1 day)

    public static function getTaskName(): string
    {
        return self::TASK_NAME;
    }

    public static function getDefaultInterval(): int
    {
        return self::TASK_INTERVAL;
    }
}
