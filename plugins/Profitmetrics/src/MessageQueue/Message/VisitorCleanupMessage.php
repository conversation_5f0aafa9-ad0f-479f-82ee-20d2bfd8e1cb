<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\Message;

/**
 * Message for triggering visitor data cleanup.
 */
class VisitorCleanupMessage
{
    public function __construct(
        private readonly int $retentionDays = 90
    ) {
    }

    public function getRetentionDays(): int
    {
        return $this->retentionDays;
    }
}
