<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\MessageQueue\Message\Handler;

use Profitmetrics\Profitmetrics\Core\Profitmetrics\VisitorEntity;
use Profitmetrics\Profitmetrics\Core\Service\OrderDataTransformer;
use Profitmetrics\Profitmetrics\Core\Service\ProfitMetricsApiClient;
use Profitmetrics\Profitmetrics\Defaults\ConfigDefaults;
use Profitmetrics\Profitmetrics\Defaults\CustomFieldDefaults;
use Profitmetrics\Profitmetrics\MessageQueue\Message\OrderExportMessage;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Checkout\Order\OrderCollection;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Plugin\PluginCollection;
use Shopware\Core\Framework\Store\Services\InstanceService;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;

/**
 * Exports order data to ProfitMetrics API asynchronously
 */
#[AsMessageHandler]
final class OrderExportMessageHandler
{
    final public const VERSION = '2';
    public const VERSION3UH = '3uh';

    /**
     * @param EntityRepository<OrderCollection> $orderRepository
     * @param EntityRepository<EntityCollection<VisitorEntity>> $visitorRepository
     * @param EntityRepository<PluginCollection> $pluginRepository
     */
    public function __construct(
        private readonly SystemConfigService $systemConfigService,
        private readonly EntityRepository $orderRepository,
        private readonly EntityRepository $visitorRepository,
        private readonly EntityRepository $pluginRepository,
        private readonly OrderDataTransformer $dataTransformer,
        private readonly ProfitMetricsApiClient $apiClient,
        private readonly InstanceService $instanceService,
        private readonly ErrorHandler $errorHandler
    ) {
    }

    /**
     * Processes an order export message by loading the order data and sending it to ProfitMetrics API
     *
     * @throws \Throwable If order processing fails
     */
    public function __invoke(OrderExportMessage $message): void
    {
        $context = new Context(new SystemSource());

        // 1. Load the order with all required associations
        $criteria = new Criteria([$message->getOrderId()]);
        $this->addOrderAssociations($criteria);

        /** @var OrderEntity|null $order */
        $order = $this->orderRepository->search($criteria, $context)->first();
        if (!$order) {
            throw new \RuntimeException('Order not found: ' . $message->getOrderId());
        }

        // Get visitor information if available
        $visitorId = $order->getCustomFields()[CustomFieldDefaults::PM_ORDER_VISITOR_ID] ?? '';
        $visitorInfo = $this->getOrderVisitorInfo($context, $visitorId);

        // Build request data
        $requestData = $this->getRequestData($visitorInfo, $order, $context);

        try {
            // Send the order data to ProfitMetrics API
            $response = $this->apiClient->getRequest($requestData);
            if ($response->success) {
                $this->markOrderAsCompleted($order->getId(), $context);
            } else {
                $this->markOrderAsFailed($order->getId(), $context);
                $this->errorHandler->log(
                    'Order export failed',
                    [
                        'orderId' => $order->getId(),
                        'requestData' => $requestData,
                        'response' => $response->content,
                    ]
                );
            }
        } catch (ExceptionInterface $e) {
            $this->markOrderAsFailed($order->getId(), $context);
            $this->errorHandler->logException(
                'Order export failed - API error',
                $e,
                [
                    'orderId' => $order->getId(),
                    'requestData' => $requestData,
                ]
            );
        }
    }

    /**
     * Build the request payload for ProfitMetrics API
     *
     * @return array<string, mixed>
     */
    private function getRequestData(?VisitorEntity $visitorInfo, OrderEntity $order, Context $context): array
    {
        $salesChannelId = $order->getSalesChannelId();

        //headless mode == true 'VERSION3UH: 3uh'
        $baseData = [
            'v' => $this->systemConfigService->get(ConfigDefaults::CONFIG_HEADLESS_MODE) ? self::VERSION3UH : self::VERSION,
            'cv' => $this->getVersionString($context),
            'ts' => $order->getOrderDateTime()->getTimestamp(),
            'pid' => $this->systemConfigService->get(
                ConfigDefaults::CONFIG_PROFIT_METRICS_ID,
                $salesChannelId
            ),
            'o' => json_encode($this->dataTransformer->transform($order)),
        ];
        if ($visitorInfo) {
            $visitorData = [
                'gacid' => $visitorInfo->getGacid(),
                'gacid_source' => $visitorInfo->getGacidSource(),
                'gclid' => $visitorInfo->getGclid(),
                'fbp' => $visitorInfo->getFbp(),
                'fbc' => $visitorInfo->getFbc(),
                'cua' => $visitorInfo->getCua(),
                'cip' => $visitorInfo->getCip(),
                't' => urlencode((string) $visitorInfo->getThrackspec()),
                'ga4_sessionid' => $visitorInfo->getGa4SessionId(),
                'ga4_sessioncount' => $visitorInfo->getGa4SessionCount(),
            ];

            if (!$this->systemConfigService->get(ConfigDefaults::CONFIG_HEADLESS_MODE)) {
                $visitorData = array_merge($visitorData, [
                    'cc_statistics' => $visitorInfo->getCcStatistics() ? 'true' : 'false',
                    'cc_marketing' => $visitorInfo->getCcMarketing() ? 'true' : 'false',
                ]);
            }

            return array_filter(array_merge($baseData, $visitorData));
        }

        return array_filter($baseData);
    }

    /**
     * Retrieves visitor tracking data for an order
     */
    private function getOrderVisitorInfo(Context $context, string $visitorId): ?VisitorEntity
    {
        if (!Uuid::isValid($visitorId)) {
            return null;
        }

        /** @var VisitorEntity|null */
        return $this->visitorRepository->search(
            new Criteria([$visitorId]),
            $context
        )->get($visitorId);
    }

    /**
     * Adds required order associations to the search criteria
     */
    private function addOrderAssociations(Criteria $criteria): void
    {
        $criteria->addAssociation('lineItems');
        $criteria->addAssociation('lineItems.product');
        $criteria->addAssociation('currency');
        $criteria->addAssociation('transactions');
        $criteria->addAssociation('transactions.paymentMethod');
        $criteria->addAssociation('deliveries');
        $criteria->addAssociation('deliveries.shippingOrderAddress.country');
        $criteria->addAssociation('deliveries.shippingMethod');
    }

    /**
     *  Updates order status to mark export as success
     */
    private function markOrderAsCompleted(string $orderId, Context $context): void
    {
        $this->orderRepository->update([
            [
                'id' => $orderId,
                'customFields' => [
                    CustomFieldDefaults::PM_ORDER_SENT_DATE => (new \DateTime())->format(Defaults::STORAGE_DATE_TIME_FORMAT),
                    CustomFieldDefaults::PM_ORDER_EXPORT_STATE => CustomFieldDefaults::EXPORT_STATE_COMPLETED,
                ],
            ],
        ], $context);
    }

    /**
     * Updates order status to mark export as failed using direct connection to avoid mutating the entity
     */
    private function markOrderAsFailed(string $orderId, Context $context): void
    {
        $this->orderRepository->update([
            [
                'id' => $orderId,
                'customFields' => [
                    CustomFieldDefaults::PM_ORDER_EXPORT_STATE => CustomFieldDefaults::EXPORT_STATE_FAILED,
                ],
            ],
        ], $context);
    }

    /**
     * Retrieves current plugin version
     *
     * @return string Version number or '0.0.0' if not found
     */
    private function getVersionString(Context $context): string
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('name', 'Profitmetrics'));

        $plugins = $this->pluginRepository->search($criteria, $context)->getEntities();

        foreach ($plugins as $plugin) {
            return 'shopware_' . $this->instanceService->getShopwareVersion() . '__' . $plugin->getVersion();
        }

        return '0.0.0';
    }
}
