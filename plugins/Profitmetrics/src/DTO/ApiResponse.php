<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\DTO;

/**
 * Data Transfer Object for API responses.
 */
final class ApiResponse
{
    public function __construct(
        public string $content,
        public int $statusCode,
        public bool $success
    ) {
    }
}
