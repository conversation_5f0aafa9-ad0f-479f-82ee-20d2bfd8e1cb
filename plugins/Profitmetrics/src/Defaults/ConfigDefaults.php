<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Defaults;

/**
 * Defines constants for plugin configuration identifiers.
 */
final class ConfigDefaults
{
    public const CONFIG_ACTIVE = 'Profitmetrics.config.active';
    public const CONFIG_PROFIT_METRICS_ID = 'Profitmetrics.config.profitMetricsId';
    public const CONFIG_ORDER_STATUSES = 'Profitmetrics.config.orderStatuses';
    public const CONFIG_MAX_ORDER_AGE = 'Profitmetrics.config.maxOrderAge';
    public const CONFIG_BLOCK_BEFORE_CONSENT = 'Profitmetrics.config.blockBeforeConsent';
    public const CONFIG_SEND_ORDERS_NOW_API = 'Profitmetrics.config.SendOrdersNowApi';
    public const CONFIG_PROFIT_METRICS_DOWNLOAD_CUSTOMER = 'Profitmetrics.config.profitMetricsDownloadCustomer';
    public const CONFIG_PROFIT_METRICS_API_TEST = 'Profitmetrics.config.profitMetricsApiTest';
    public const CONFIG_GOOGLE_CONVERSION_ID = 'Profitmetrics.config.googleConversionId';
    public const CONFIG_CONVERSION_BOOSTER_GOOGLE_ADS = 'Profitmetrics.config.conversionBoosterGoogleAds';
    public const CONFIG_ALL_PAGES_JAVASCRIPT = 'Profitmetrics.config.allPagesJavascript';
    public const CONFIG_HEADLESS_MODE = 'Profitmetrics.config.headlessMode';
}
