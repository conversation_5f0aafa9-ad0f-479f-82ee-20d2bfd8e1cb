<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Defaults;

/**
 * Defines constants for tracking-related cookies and session key identifiers.
 */
final class TrackingDefaults
{
    public const PROFITMETRICS_COOKIE_TRACKING_DATA = 'pmTPTrack';
}
