!function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p=(window.__sw__.assetPath + '/bundles/profitmetrics/'),r(r.s="y9Of")}({"+W8B":function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(i=o.key,c=void 0,c=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===r(c)?c:String(c)),o)}var i,c}function i(e,t){return(i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=a(e);if(t){var o=a(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return s(this,r)}}function s(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var u=Shopware.Classes.ApiService,l=Shopware.Application,p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&i(e,t)}(l,e);var t,r,s,a=c(l);function l(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"profitmetrics-api";return n(this,l),a.call(this,e,t,r)}return t=l,(r=[{key:"testConfig",value:function(e){var t=this.getBasicHeaders({});return this.httpClient.post("_action/".concat(this.getApiBasePath(),"/verify"),e,t).then((function(e){return u.handleResponse(e)}))}}])&&o(t.prototype,r),s&&o(t,s),Object.defineProperty(t,"prototype",{writable:!1}),l}(u);l.addServiceProvider("PmApiService",(function(e){var t=l.getContainer("init");return new p(t.httpClient,e.loginService)}))},"19wI":function(e){e.exports=JSON.parse('{"profitmetrics-api":{"order":{"title":"Export Orders","success":"Order export was successfully finished","error":"Order export failed, check logs for more information."},"test":{"title":"Test API connection","success":"Connection tested with success","error":"Connection could not be established."},"customerDownload":{"title":"Export customers","success":"Customers were successfully downloaded","error":"Customer download failed, check logs for more information."}},"profitmetrics":{"send-orders":{"label":"Send orders now"},"api-test":{"label":"Test API connection"},"customer-download-button":{"label":"Download customers"}},"sw-sales-channel":{"detail":{"productComparison":{"templates":{"template-label":{"profitMetrics":"ProfitMetrics"}}}}}}')},"D1/N":function(e){e.exports=JSON.parse('{"profitmetrics-api":{"order":{"title":"Export Orders","success":"Order export was successfully finished","error":"Order export failed, check logs for more information."},"test":{"title":"Test API forbindelse","success":"Oprettelse af forbindelse testet med success","error":"Forbindelse kunne ikke oprettes. Sikre at konfigurationsdataen er korrekt"},"customerDownload":{"title":"Eksporter kunder","success":"Kunder blev eksporteret succesfuldt","error":"Kunde eksport mislykkedes, tjek venligst logs for mere information."}},"profitmetrics":{"send-orders":{"label":"Send ordrer"},"api-test":{"label":"Test API-forbindelse"},"customer-download-button":{"label":"Download kunder"}},"sw-sales-channel":{"detail":{"productComparison":{"templates":{"template-label":{"profitMetrics":"ProfitMetrics"}}}}}}')},ZWQu:function(e,t){function r(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var o=Shopware.Component,i=Shopware.Data.Criteria,c=Shopware.Utils.get;o.extend("order-status-multiselect","sw-entity-multi-id-select",{inheritAttrs:!1,inject:["repositoryFactory"],props:{entity:{type:String,required:!1,default:"state_machine"},criteria:{type:Object,required:!1,default:function(){var e=new i;return e.addAssociation("stateMachine"),e}},repository:{type:Object,required:!1,default:function(){return this.repositoryFactory.create("state_machine_state")}}}}),o.override("sw-entity-multi-select",{methods:{displayLabelProperty:function(e){var t,r;return"order-status-multiselect"===(null===(t=this.$parent)||void 0===t||null===(r=t.$attrs)||void 0===r?void 0:r.componentName)?this.getOrderStatusLabel(e):this.defaultDisplayLabelProperty(e)},defaultDisplayLabelProperty:function(e){var t=this;return e?(Array.isArray(this.labelProperty)?r(this.labelProperty):[this.labelProperty]).map((function(r){return t.getKey(e,r)||t.getKey(e,"translated.".concat(r))})).join(" "):""},getOrderStatusLabel:function(e){var t,r;if(!e)return"";var n=this.getKey(e,"name")||this.getKey(e,"translated.name")||"",o=this.getKey(e.stateMachine,"name")||this.getKey(e.stateMachine,"translated.name")||"";return"order_delivery.state"===(null===(t=e.stateMachine)||void 0===t?void 0:t.technicalName)&&"Order state"===(null===(r=e.stateMachine)||void 0===r?void 0:r.name)&&(o="Order delivery state"),"".concat(o," - ").concat(n).trim()},getKey:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return c(e,t,r)}}})},cMpp:function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(i=o.key,c=void 0,c=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===r(c)?c:String(c)),o)}var i,c}function i(e,t){return(i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=a(e);if(t){var o=a(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return s(this,r)}}function s(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var u=Shopware.Classes.ApiService,l=Shopware.Application,p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&i(e,t)}(l,e);var t,r,s,a=c(l);function l(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"profitmetrics-api";return n(this,l),a.call(this,e,t,r)}return t=l,(r=[{key:"exportOrders",value:function(e){var t=this.getBasicHeaders({});return this.httpClient.post("_action/".concat(this.getApiBasePath(),"/order-export"),e,{headers:t}).then((function(e){return u.handleResponse(e)}))}}])&&o(t.prototype,r),s&&o(t,s),Object.defineProperty(t,"prototype",{writable:!1}),l}(u);l.addServiceProvider("PmApiOrderExportService",(function(e){var t=l.getContainer("init");return new p(t.httpClient,e.loginService)}))},l4n7:function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(i=o.key,c=void 0,c=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===r(c)?c:String(c)),o)}var i,c}function i(e,t){return(i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=a(e);if(t){var o=a(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return s(this,r)}}function s(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var u=Shopware.Classes.ApiService,l=Shopware.Application,p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&i(e,t)}(l,e);var t,r,s,a=c(l);function l(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"profitmetrics-api";return n(this,l),a.call(this,e,t,r)}return t=l,(r=[{key:"downloadCustomers",value:function(e){var t=this.getBasicHeaders({});return this.httpClient.get("_action/".concat(this.getApiBasePath(),"/customer-export"),e,t).then((function(e){return u.handleResponse(e)}))}}])&&o(t.prototype,r),s&&o(t,s),Object.defineProperty(t,"prototype",{writable:!1}),l}(u);l.addServiceProvider("PmApiCustomerExportService",(function(e){var t=l.getContainer("init");return new p(t.httpClient,e.loginService)}))},xFw9:function(e){e.exports=JSON.parse('{"profitmetrics-api":{"order":{"title":"Export Orders","success":"Order export was successfully finished","error":"Order export failed, check logs for more information."},"test":{"title":"Test API connection","success":"Verbindung wurde erfolgreich getestet","error":"Verbindung konnte nicht hergestellt werden. Bitte prüfe die Zugangsdaten"},"customerDownload":{"title":"Kundenexport","success":"Kunden wurden erfolgreich heruntergeladen","error":"Kundenexport ist fehlgeschlagen, bitte überprüfen Sie die Logs für weitere Informationen."}},"profitmetrics":{"send-orders":{"label":"Bestellungen senden"},"api-test":{"label":"API-Verbindung testen"},"customer-download-button":{"label":"Kunden herunterladen"}},"sw-sales-channel":{"detail":{"productComparison":{"templates":{"template-label":{"profitMetrics":"ProfitMetrics"}}}}}}')},y9Of:function(e,t,r){"use strict";r.r(t);r("+W8B"),r("cMpp"),r("l4n7"),r("ZWQu");var n=Shopware,o=n.Component,i=n.Mixin;o.register("pm-button-api-verify-button",{template:'<div class="sw-field">\n    <sw-button-process\n        :isLoading="isLoading"\n        :processSuccess="isSaveSuccessful"\n        size="small"\n        @process-finish="saveFinish"\n        @click="testApi"\n    >{{ $tc(\'profitmetrics.api-test.label\') }}</sw-button-process>\n</div>\n',inject:["PmApiService"],mixins:[i.getByName("notification")],data:function(){return{isLoading:!1,isSaveSuccessful:!1}},computed:{pluginConfig:function(){var e=null,t=this;do{e=(t=t.$parent).actualConfigData}while(!e);return e[t.currentSalesChannelId]}},methods:{saveFinish:function(){this.isSaveSuccessful=!1},testApi:function(){var e=this;this.isLoading=!0,this.PmApiService.testConfig(this.pluginConfig).then((function(t){t.success?(e.isSaveSuccessful=!0,e.createNotificationSuccess({title:e.$tc("profitmetrics-api.test.title"),message:t.message||e.$tc("profitmetrics-api.test.success")})):e.createNotificationError({title:e.$tc("profitmetrics-api.test.title"),message:t.message||e.$tc("profitmetrics-api.test.error")})})).catch((function(t){e.createNotificationError({title:e.$tc("profitmetrics-api.test.title"),message:t.message||e.$tc("profitmetrics-api.test.error")})})).finally((function(){e.isLoading=!1}))}}});var c=Shopware,s=c.Component,a=c.Mixin;s.register("pm-button-customer-export",{template:'<div>\n    <sw-button-process\n        :isLoading="isLoading"\n        :processSuccess="isSaveSuccessful"\n        variant="primary"\n        @process-finish="saveFinish"\n        @click="downloadCustomers"\n    >{{ $tc(\'profitmetrics.customer-download-button.label\') }}</sw-button-process>\n</div>\n',inject:["PmApiCustomerExportService"],mixins:[a.getByName("notification")],data:function(){return{isLoading:!1,isSaveSuccessful:!1}},computed:{pluginConfig:function(){var e=null,t=this;do{e=(t=t.$parent).actualConfigData}while(!e);return e[t.currentSalesChannelId]}},methods:{saveFinish:function(){this.isSaveSuccessful=!1},downloadCustomers:function(){var e=this;this.isLoading=!0,this.PmApiCustomerExportService.downloadCustomers(this.pluginConfig).then((function(t){var r=new Blob([t],{type:"application/csv"}),n=document.createElement("a");n.href=window.URL.createObjectURL(r);var o=new Date;n.download="pm_customers_"+o.toLocaleDateString("sv").replace(/-/g,"")+"_"+o.toTimeString().slice(0,8).replace(/:/g,"")+".csv",document.body.appendChild(n),n.click(),document.body.removeChild(n),e.isSaveSuccessful=!0,e.createNotificationSuccess({title:e.$tc("profitmetrics-api.customerDownload.title"),message:e.$tc("profitmetrics-api.customerDownload.success")})})).catch((function(t){e.createNotificationError({title:e.$tc("profitmetrics-api.customerDownload.title"),message:t.message||e.$tc("profitmetrics-api.customerDownload.error")})})).finally((function(){e.isLoading=!1}))}}});var u=Shopware,l=u.Component,p=u.Mixin;l.register("pm-button-order-export",{template:'<div>\n    <sw-button-process\n        :isLoading="isLoading"\n        :processSuccess="isSaveSuccessful"\n        variant="primary"\n        @process-finish="isSaveSuccessful = false"\n        @click="exportOrders"\n    >{{ $tc(\'profitmetrics.send-orders.label\') }}</sw-button-process>\n</div>\n',inject:["PmApiOrderExportService"],mixins:[p.getByName("notification")],data:function(){return{isLoading:!1,isSaveSuccessful:!1}},computed:{pluginConfig:function(){for(var e=this;e.$parent&&!e.actualConfigData;)e=e.$parent;return e.actualConfigData?e.actualConfigData.null:null}},methods:{exportOrders:function(){var e=this;this.isLoading=!0,this.PmApiOrderExportService.exportOrders(this.pluginConfig).then((function(t){t.success?(e.isSaveSuccessful=!0,e.createNotificationSuccess({title:e.$tc("profitmetrics-api.order.title"),message:t.message||e.$tc("profitmetrics-api.order.success")})):e.createNotificationError({title:e.$tc("profitmetrics-api.order.title"),message:t.message||e.$tc("profitmetrics-api.order.error")})})).catch((function(t){e.createNotificationError({title:e.$tc("profitmetrics-api.order.title"),message:t.message||e.$tc("profitmetrics-api.order.error")})})).finally((function(){e.isLoading=!1}))}}});Shopware.Service("exportTemplateService").registerProductExportTemplate({name:"profitMetrics",translationKey:"sw-sales-channel.detail.productComparison.templates.template-label.profitMetrics",headerTemplate:'<?xml version="1.0" encoding="UTF-8" ?>\n<rss xmlns:g="http://base.google.com/ns/1.0" xmlns:pm="https://my.profitmetrics.io/ns/1.0">\n    <channel>\n'.trim(),bodyTemplate:"<item>\n    {# @var product \\Shopware\\Core\\Content\\Product\\SalesChannel\\SalesChannelProductEntity #}\n    <g:id>{{ product.productNumber }}</g:id>\n    <title>{{ product.translated.name|escape }}</title>\n    <g:image_link>{% if product.cover and product.cover.media %}{{ product.cover.media.url }}{% endif %}</g:image_link>\n    <link>{{ seoUrl('frontend.detail.page', {'productId': product.id}) }}</link>\n\n    {% set price = product.price.getCurrencyPrice(context.currencyId) %}\n    {% if product.price.getCurrencyPrice(context.currencyId).listPrice != null %}\n        {% set price = product.price.getCurrencyPrice(context.currencyId).listPrice %}\n    {% endif %}\n    {% set price = price.gross %}\n    <g:price>{{ price|number_format(context.currency.totalRounding.decimals, '.', '') }}</g:price>\n    <pm:price_currency>{{ context.currency.isoCode }}</pm:price_currency>\n\n    {# @var purchasePrice \\Shopware\\Core\\Framework\\DataAbstractionLayer\\Pricing\\Price #}\n    {% set purchasePrice = null %}\n    {% if product.purchasePrices %}\n        {% set purchasePrice = product.purchasePrices.currencyPrice(context.currencyId) %}\n    {% endif %}\n    <pm:price_buy>{%- if purchasePrice -%}{{ purchasePrice.net }}{%- endif -%}</pm:price_buy>\n    <pm:price_buy_currency>{{ context.currency.isoCode }}</pm:price_buy_currency>\n\n    <pm:num_stock>{{ product.availableStock }}</pm:num_stock>\n    <tax_state>{{ context.taxState }}</tax_state>\n</item>\n",footerTemplate:"</channel>\n</rss>\n".trim(),fileName:"profitMetrics.xml",encoding:"UTF-8",fileFormat:"xml",includeVariants:!0,generateByCronjob:!1,interval:86400});var f=r("xFw9"),d=r("19wI"),m=r("D1/N");Shopware.Locale.extend("de-DE",f),Shopware.Locale.extend("en-GB",d),Shopware.Locale.extend("da-DK",m)}});