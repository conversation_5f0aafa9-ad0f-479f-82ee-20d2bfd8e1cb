"use strict";
(self["webpackChunk"] = self["webpackChunk"] || []).push([["profitmetrics"],{

/***/ 8553:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "D": () => (/* binding */ COOKIE_CONFIGURATION_UPDATE),
/* harmony export */   "Z": () => (/* binding */ CookieConfiguration)
/* harmony export */ });
/* harmony import */ var src_plugin_system_plugin_class__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6285);
/* harmony import */ var src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7606);
/* harmony import */ var src_plugin_offcanvas_ajax_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2615);
/* harmony import */ var src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3637);
/* harmony import */ var src_helper_viewport_detection_helper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7474);
/* harmony import */ var src_service_http_client_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(8254);
/* harmony import */ var src_utility_loading_indicator_element_loading_indicator_util__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4690);
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return typeof key === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (typeof input !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (typeof res !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
/**
 *
 * CookieConfiguration plugin
 * --------------------------
 * Renders the configuration template inside an offCanvas
 *
 * Applies its "openOffCanvas"-eventHandler to the following selector:
 * 1) '.js-cookie-configuration-button button'
 * 2) `[href="${window.router['frontend.cookie.offcanvas']}"]`
 *
 * Can be opened manually using the public method "openOffCanvas"
 *
 * The cookie form is defined via CookieController.php
 * Cookies marked as "required" (see CookieController.php) are ignored, since they are assumed to be set manually
 *
 * Configuration changes are pushed to the global (document) event "CookieConfiguration_Update"
 *
 */

/* global PluginManager */









// this event will be published via a global (document) EventEmitter
const COOKIE_CONFIGURATION_UPDATE = 'CookieConfiguration_Update';
class CookieConfiguration extends src_plugin_system_plugin_class__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z {
  init() {
    this.lastState = {
      active: [],
      inactive: []
    };
    this._httpClient = new src_service_http_client_service__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z();
    this._registerEvents();
  }

  /**
   * Registers the events for displaying the offCanvas
   * Applies the event to all elements using the "buttonOpenSelector" or "customLinkSelector"
   *
   * @private
   */
  _registerEvents() {
    const {
      submitEvent,
      buttonOpenSelector,
      customLinkSelector,
      globalButtonAcceptAllSelector
    } = this.options;
    Array.from(document.querySelectorAll(buttonOpenSelector)).forEach(button => {
      button.addEventListener(submitEvent, this.openOffCanvas.bind(this));
    });
    Array.from(document.querySelectorAll(customLinkSelector)).forEach(customLink => {
      customLink.addEventListener(submitEvent, this._handleCustomLink.bind(this));
    });
    Array.from(document.querySelectorAll(globalButtonAcceptAllSelector)).forEach(customLink => {
      customLink.addEventListener(submitEvent, this._acceptAllCookiesFromCookieBar.bind(this));
    });
  }

  /**
   * Registers events required by the offCanvas template
   *
   * @private
   */
  _registerOffCanvasEvents() {
    const {
      submitEvent,
      buttonSubmitSelector,
      buttonAcceptAllSelector,
      wrapperToggleSelector
    } = this.options;
    const offCanvas = this._getOffCanvas();
    if (offCanvas) {
      const button = offCanvas.querySelector(buttonSubmitSelector);
      const buttonAcceptAll = offCanvas.querySelector(buttonAcceptAllSelector);
      const checkboxes = Array.from(offCanvas.querySelectorAll('input[type="checkbox"]'));
      const wrapperTrigger = Array.from(offCanvas.querySelectorAll(wrapperToggleSelector));
      if (button) {
        button.addEventListener(submitEvent, this._handleSubmit.bind(this, src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z));
      }
      if (buttonAcceptAll) {
        buttonAcceptAll.addEventListener(submitEvent, this._acceptAllCookiesFromOffCanvas.bind(this, src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z));
      }
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener(submitEvent, this._handleCheckbox.bind(this));
      });
      wrapperTrigger.forEach(trigger => {
        trigger.addEventListener(submitEvent, this._handleWrapperTrigger.bind(this));
      });
    }
  }

  /**
   * Prevent the event default e.g. for anchor elements using the href-selector
   *
   * @param event
   * @private
   */
  _handleCustomLink(event) {
    event.preventDefault();
    this.openOffCanvas();
  }
  _handleUpdateListener(active, inactive) {
    const updatedCookies = this._getUpdatedCookies(active, inactive);
    document.$emitter.publish(COOKIE_CONFIGURATION_UPDATE, updatedCookies);
  }

  /**
   * Compare the current in-/active cookies to the initialState and return updated cookies only
   *
   * @param active
   * @param inactive
   * @private
   */
  _getUpdatedCookies(active, inactive) {
    const {
      lastState
    } = this;
    const updated = {};
    active.forEach(currentCheckbox => {
      if (lastState.inactive.includes(currentCheckbox)) {
        updated[currentCheckbox] = true;
      }
    });
    inactive.forEach(currentCheckbox => {
      if (lastState.active.includes(currentCheckbox)) {
        updated[currentCheckbox] = false;
      }
    });
    return updated;
  }

  /**
   * Public method to open the offCanvas
   *
   * @param {function|null} callback
   */
  openOffCanvas(callback) {
    const {
      offCanvasPosition
    } = this.options;
    const url = window.router['frontend.cookie.offcanvas'];
    const isFullwidth = src_helper_viewport_detection_helper__WEBPACK_IMPORTED_MODULE_5__/* ["default"].isXS */ .Z.isXS();
    this._hideCookieBar();
    src_plugin_offcanvas_ajax_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_1__/* ["default"].open */ .Z.open(url, false, this._onOffCanvasOpened.bind(this, callback), offCanvasPosition, undefined, undefined, isFullwidth);
  }

  /**
   * Public method to close the offCanvas
   *
   * @param callback
   */
  closeOffCanvas(callback) {
    src_plugin_offcanvas_ajax_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_1__/* ["default"].close */ .Z.close();
    if (typeof callback === 'function') {
      callback();
    }
  }

  /**
   * Private method to apply events to the cookie-configuration template
   * Also sets the initial checkbox state based on currently set cookies
   *
   * @private
   */
  _onOffCanvasOpened(callback) {
    this._registerOffCanvasEvents();
    this._setInitialState();
    this._setInitialOffcanvasState();
    PluginManager.initializePlugins();
    if (typeof callback === 'function') {
      callback();
    }
  }
  _hideCookieBar() {
    const cookiePermissionPlugin = PluginManager.getPluginInstances('CookiePermission');
    if (cookiePermissionPlugin && cookiePermissionPlugin[0]) {
      cookiePermissionPlugin[0]._hideCookieBar();
      cookiePermissionPlugin[0]._removeBodyPadding();
    }
  }

  /**
   * Sets the `lastState` of the current cookie configuration, either passed as
   * parameter `cookies`, otherwise it is loaded by parsing the DOM of the off
   * canvas sidebar
   *
   * @param {?Array} cookies
   * @private
   */
  _setInitialState(cookies = null) {
    const availableCookies = cookies || this._getCookies('all');
    const activeCookies = [];
    const inactiveCookies = [];
    availableCookies.forEach(({
      cookie,
      required
    }) => {
      const isActive = src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"].getItem */ .Z.getItem(cookie);
      if (isActive || required) {
        activeCookies.push(cookie);
      } else {
        inactiveCookies.push(cookie);
      }
    });
    this.lastState = {
      active: activeCookies,
      inactive: inactiveCookies
    };
  }

  /**
   * Preselect coherent checkboxes in the off canvas sidebar
   *
   * @private
   */
  _setInitialOffcanvasState() {
    const activeCookies = this.lastState.active;
    const offCanvas = this._getOffCanvas();
    activeCookies.forEach(activeCookie => {
      const target = offCanvas.querySelector(`[data-cookie="${activeCookie}"]`);
      target.checked = true;
      this._childCheckboxEvent(target);
    });
  }

  /**
   * From click target, try to find the cookie group container and toggle the open state
   *
   * @param event
   * @private
   */
  _handleWrapperTrigger(event) {
    event.preventDefault();
    const {
      entriesActiveClass,
      entriesClass,
      groupClass
    } = this.options;
    const {
      target
    } = event;
    const cookieEntryContainer = this._findParentEl(target, entriesClass, groupClass);
    if (cookieEntryContainer) {
      const active = cookieEntryContainer.classList.contains(entriesActiveClass);
      if (active) {
        cookieEntryContainer.classList.remove(entriesActiveClass);
      } else {
        cookieEntryContainer.classList.add(entriesActiveClass);
      }
    }
  }

  /**
   * Determine whether the target checkbox is a parent or a child checkbox
   *
   * @param event
   * @private
   */
  _handleCheckbox(event) {
    const {
      parentInputClass
    } = this.options;
    const {
      target
    } = event;
    const callback = target.classList.contains(parentInputClass) ? this._parentCheckboxEvent : this._childCheckboxEvent;
    callback.call(this, target);
  }

  /**
   * Recursively checks the provided elements parent for the first class parameter
   * Stops the recursion, if the parentElement contains the second class parameter
   *
   * @param el
   * @param findClass
   * @param abortClass
   * @returns {*|HTMLElement|*}
   * @private
   */
  _findParentEl(el, findClass, abortClass = null) {
    while (!!el && !el.classList.contains(abortClass)) {
      if (el.classList.contains(findClass)) {
        return el;
      }
      el = el.parentElement;
    }
    return null;
  }
  _isChecked(target) {
    return !!target.checked;
  }

  /**
   * De-/select all checkboxes of the current group
   *
   * @param target
   * @private
   */
  _parentCheckboxEvent(target) {
    const {
      groupClass
    } = this.options;
    const newState = this._isChecked(target);
    const group = this._findParentEl(target, groupClass);
    this._toggleWholeGroup(newState, group);
  }

  /**
   *
   * Trigger a change event for the "select-all" checkbox of the childs group
   *
   * @param target
   * @private
   */
  _childCheckboxEvent(target) {
    const {
      groupClass
    } = this.options;
    const newState = this._isChecked(target);
    const group = this._findParentEl(target, groupClass);
    this._toggleParentCheckbox(newState, group);
  }

  /**
   * Toogle each checkbox inside the given group
   *
   * @param state
   * @param group
   * @private
   */
  _toggleWholeGroup(state, group) {
    Array.from(group.querySelectorAll('input')).forEach(checkbox => {
      checkbox.checked = state;
    });
  }

  /**
   * Toggle a groups "select-all" checkbox according to changes to its child checkboxes
   * "Check, if any child checkbox is checked" / "Uncheck, if no child checkboxes are checked"
   *
   * @param state
   * @param group
   * @private
   */
  _toggleParentCheckbox(state, group) {
    const {
      parentInputSelector
    } = this.options;
    const checkboxes = Array.from(group.querySelectorAll(`input:not(${parentInputSelector})`));
    const activeCheckboxes = Array.from(group.querySelectorAll(`input:not(${parentInputSelector}):checked`));
    if (checkboxes.length > 0) {
      const parentCheckbox = group.querySelector(parentInputSelector);
      if (parentCheckbox) {
        const checked = activeCheckboxes.length > 0;
        const indeterminate = checked && activeCheckboxes.length !== checkboxes.length;
        parentCheckbox.checked = checked;
        parentCheckbox.indeterminate = indeterminate;
      }
    }
  }

  /**
   * Event handler for the 'Save' button inside the offCanvas
   *
   * Removes unselected cookies, if already set
   * Sets or refreshes selected cookies
   *
   * @private
   */
  _handleSubmit() {
    const activeCookies = this._getCookies('active');
    const inactiveCookies = this._getCookies('inactive');
    const {
      cookiePreference
    } = this.options;
    const activeCookieNames = [];
    const inactiveCookieNames = [];
    inactiveCookies.forEach(({
      cookie
    }) => {
      inactiveCookieNames.push(cookie);
      if (src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"].getItem */ .Z.getItem(cookie)) {
        src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"].removeItem */ .Z.removeItem(cookie);
      }
    });

    /**
     * Cookies without value are passed to the updateListener
     * ( see "_handleUpdateListener" method )
     */
    activeCookies.forEach(({
      cookie,
      value,
      expiration
    }) => {
      activeCookieNames.push(cookie);
      if (cookie && value) {
        src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"].setItem */ .Z.setItem(cookie, value, expiration);
      }
    });
    src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"].setItem */ .Z.setItem(cookiePreference, '1', '30');
    this._handleUpdateListener(activeCookieNames, inactiveCookieNames);
    this.closeOffCanvas();
  }

  /**
   * Accepts all cookies. Pass `true` to the loadIntoMemory parameter to load the DOM into memory instead of
   * opening the OffCanvas menu.
   *
   * @param loadIntoMemory
   */
  acceptAllCookies(loadIntoMemory = false) {
    if (!loadIntoMemory) {
      this._handleAcceptAll();
      this.closeOffCanvas();
      return;
    }
    src_utility_loading_indicator_element_loading_indicator_util__WEBPACK_IMPORTED_MODULE_6__/* ["default"].create */ .Z.create(this.el);
    const url = window.router['frontend.cookie.offcanvas'];
    this._httpClient.get(url, response => {
      const dom = new DOMParser().parseFromString(response, 'text/html');
      this._handleAcceptAll(dom);
      src_utility_loading_indicator_element_loading_indicator_util__WEBPACK_IMPORTED_MODULE_6__/* ["default"].remove */ .Z.remove(this.el);
      this._hideCookieBar();
    });
  }

  /**
   * Event handler for the 'Allow all'-button in the cookie bar.
   * It loads the DOM into memory before searching for, and accepting the cookies.
   *
   * @private
   */
  _acceptAllCookiesFromCookieBar() {
    return this.acceptAllCookies(true);
  }

  /**
   * Event handler for the 'Allow all'-button in the off canvas view.
   * It uses the DOM from the Off Canvas container to search for, and accept the cookies.
   * After accepting, it closes the OffCanvas sidebar.
   *
   * @private
   */
  _acceptAllCookiesFromOffCanvas() {
    return this.acceptAllCookies();
  }

  /**
   * This will set and refresh all registered cookies.
   *
   * @param {?(Document|HTMLElement)} offCanvas
   * @private
   */
  _handleAcceptAll(offCanvas = null) {
    const allCookies = this._getCookies('all', offCanvas);
    this._setInitialState(allCookies);
    const {
      cookiePreference
    } = this.options;
    allCookies.forEach(({
      cookie,
      value,
      expiration
    }) => {
      if (cookie && value) {
        src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"].setItem */ .Z.setItem(cookie, value, expiration);
      }
    });
    src_helper_storage_cookie_storage_helper__WEBPACK_IMPORTED_MODULE_4__/* ["default"].setItem */ .Z.setItem(cookiePreference, '1', '30');
    this._handleUpdateListener(allCookies.map(({
      cookie
    }) => cookie), []);
  }

  /**
   * Get cookies passed to the configuration template
   * Can be filtered by "all", "active" or "inactive"
   *
   * Always excludes "required" cookies, since they are assumed to be set separately.
   *
   * @param type
   * @param {?(Document|HTMLElement)} offCanvas
   * @returns {Array}
   * @private
   */
  _getCookies(type = 'all', offCanvas = null) {
    const {
      cookieSelector
    } = this.options;
    if (!offCanvas) {
      offCanvas = this._getOffCanvas();
    }
    return Array.from(offCanvas.querySelectorAll(cookieSelector)).filter(cookieInput => {
      switch (type) {
        case 'all':
          return true;
        case 'active':
          return this._isChecked(cookieInput);
        case 'inactive':
          return !this._isChecked(cookieInput);
        default:
          return false;
      }
    }).map(filteredInput => {
      const {
        cookie,
        cookieValue,
        cookieExpiration,
        cookieRequired
      } = filteredInput.dataset;
      return {
        cookie,
        value: cookieValue,
        expiration: cookieExpiration,
        required: cookieRequired
      };
    });
  }

  /**
   * Returns the current offcanvas element if available
   *
   * @returns {*}
   * @private
   */
  _getOffCanvas() {
    const elements = src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z ? src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_2__/* ["default"].getOffCanvas */ .Z.getOffCanvas() : [];
    return elements && elements.length > 0 ? elements[0] : false;
  }
}
_defineProperty(CookieConfiguration, "options", {
  offCanvasPosition: 'left',
  submitEvent: 'click',
  cookiePreference: 'cookie-preference',
  cookieSelector: '[data-cookie]',
  buttonOpenSelector: '.js-cookie-configuration-button button',
  buttonSubmitSelector: '.js-offcanvas-cookie-submit',
  buttonAcceptAllSelector: '.js-offcanvas-cookie-accept-all',
  globalButtonAcceptAllSelector: '.js-cookie-accept-all-button',
  wrapperToggleSelector: '.offcanvas-cookie-entries span',
  parentInputSelector: '.offcanvas-cookie-parent-input',
  customLinkSelector: `[href="${window.router['frontend.cookie.offcanvas']}"]`,
  entriesActiveClass: 'offcanvas-cookie-entries--active',
  entriesClass: 'offcanvas-cookie-entries',
  groupClass: 'offcanvas-cookie-group',
  parentInputClass: 'offcanvas-cookie-parent-input'
});

/***/ }),

/***/ 2615:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ AjaxOffCanvas)
/* harmony export */ });
/* harmony import */ var src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3637);
/* harmony import */ var src_service_http_client_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8254);
/* harmony import */ var src_utility_loading_indicator_loading_indicator_util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7906);




// xhr call storage
let xhr = null;

/**
 * @package storefront
 */
class AjaxOffCanvas extends src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z {
  /**
   * Fire AJAX request to get the offcanvas content
   *
   * @param {string} url
   * @param {*|boolean} data
   * @param {function|null} callback
   * @param {'left'|'right'} position
   * @param {boolean} closable
   * @param {number} delay
   * @param {boolean} fullwidth
   * @param {array|string} cssClass
   */
  static open(url = false, data = false, callback = null, position = 'left', closable = true, delay = src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_0__/* ["default"].REMOVE_OFF_CANVAS_DELAY */ .Z.REMOVE_OFF_CANVAS_DELAY(), fullwidth = false, cssClass = '') {
    if (!url) {
      throw new Error('A url must be given!');
    }
    // avoid multiple backdrops
    src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_0__/* .OffCanvasInstance._removeExistingOffCanvas */ .r._removeExistingOffCanvas();
    const offCanvas = src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_0__/* .OffCanvasInstance._createOffCanvas */ .r._createOffCanvas(position, fullwidth, cssClass, closable);
    this.setContent(url, data, callback, closable, delay);
    src_plugin_offcanvas_offcanvas_plugin__WEBPACK_IMPORTED_MODULE_0__/* .OffCanvasInstance._openOffcanvas */ .r._openOffcanvas(offCanvas);
  }

  /**
   * Method to change the content of the already visible OffCanvas via xhr
   *
   * @param {string} url
   * @param {*} data
   * @param {function} callback
   * @param {boolean} closable
   * @param {number} delay
   */
  static setContent(url, data, callback, closable, delay) {
    const client = new src_service_http_client_service__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z();
    super.setContent(`<div class="offcanvas-content-container">${src_utility_loading_indicator_loading_indicator_util__WEBPACK_IMPORTED_MODULE_2__/* ["default"].getTemplate */ .Z.getTemplate()}</div>`, closable, delay);

    // interrupt already running ajax calls
    if (xhr) xhr.abort();
    const cb = response => {
      super.setContent(response, closable, delay);
      // if a callback function is being injected execute it after opening the OffCanvas
      if (typeof callback === 'function') {
        callback(response);
      }
    };
    if (data) {
      xhr = client.post(url, data, AjaxOffCanvas.executeCallback.bind(this, cb));
    } else {
      xhr = client.get(url, AjaxOffCanvas.executeCallback.bind(this, cb));
    }
  }

  /**
   * Executes the given callback
   * and initializes all plugins
   *
   * @param {function} cb
   * @param {string} response
   */
  static executeCallback(cb, response) {
    if (typeof cb === 'function') {
      cb(response);
    }
    window.PluginManager.initializePlugins();
  }
}

/***/ }),

/***/ 3637:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (/* binding */ OffCanvas),
/* harmony export */   "r": () => (/* binding */ OffCanvasInstance)
/* harmony export */ });
/* harmony import */ var src_helper_device_detection_helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9658);
/* harmony import */ var src_helper_emitter_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(2005);
/* harmony import */ var src_helper_iterator_helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1966);



const OFF_CANVAS_CLASS = 'offcanvas';
const OFF_CANVAS_FULLWIDTH_CLASS = 'is-fullwidth';
const OFF_CANVAS_CLOSE_TRIGGER_CLASS = 'js-offcanvas-close';
const REMOVE_OFF_CANVAS_DELAY = 350;

/**
 * OffCanvas uses Bootstraps OffCanvas JavaScript implementation
 * @see https://getbootstrap.com/docs/5.2/components/offcanvas
 * @package storefront
 */
class OffCanvasSingleton {
  constructor() {
    this.$emitter = new src_helper_emitter_helper__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z();
  }

  /**
   * Open the offcanvas and its backdrop
   * @param {string} content
   * @param {function|null} callback
   * @param {'left'|'right'|'bottom'} position
   * @param {boolean} closable
   * @param {number} delay
   * @param {boolean} fullwidth
   * @param {array|string} cssClass
   */
  open(content, callback, position, closable, delay, fullwidth, cssClass) {
    // avoid multiple backdrops
    this._removeExistingOffCanvas();
    const offCanvas = this._createOffCanvas(position, fullwidth, cssClass, closable);
    this.setContent(content, closable, delay);
    this._openOffcanvas(offCanvas, callback);
  }

  /**
   * Method to change the content of the already visible OffCanvas
   * @param {string} content
   * @deprecated tag:v6.6.0 - Parameter `closable` is deprecated. The `closable` parameter will be set by the `open` method only instead.
   * @param {boolean} closable
   * @param {number} delay
   */
  setContent(content, closable, delay) {
    const offCanvas = this.getOffCanvas();
    if (!offCanvas[0]) {
      return;
    }
    offCanvas[0].innerHTML = content;

    // register events again
    this._registerEvents(delay);
  }

  /**
   * adds an additional class to the offcanvas
   *
   * @param {string} className
   */
  setAdditionalClassName(className) {
    const offCanvas = this.getOffCanvas();
    offCanvas[0].classList.add(className);
  }

  /**
   * Determine list of existing offcanvas
   * @returns {NodeListOf<Element>}
   * @private
   */
  getOffCanvas() {
    return document.querySelectorAll(`.${OFF_CANVAS_CLASS}`);
  }

  /**
   * Close the offcanvas and its backdrop when the browser goes back in history
   * @param {number} delay
   */
  close(delay) {
    const OffCanvasElements = this.getOffCanvas();
    src_helper_iterator_helper__WEBPACK_IMPORTED_MODULE_1__/* ["default"].iterate */ .Z.iterate(OffCanvasElements, offCanvas => {
      const offCanvasInstance = bootstrap.Offcanvas.getInstance(offCanvas);
      offCanvasInstance.hide();
    });
    setTimeout(() => {
      this.$emitter.publish('onCloseOffcanvas', {
        offCanvasContent: OffCanvasElements
      });
    }, delay);
  }

  /**
   * Callback for close button, goes back in browser history to trigger close
   * @returns {void}
   */
  goBackInHistory() {
    window.history.back();
  }

  /**
   * Returns whether any OffCanvas exists or not
   * @returns {boolean}
   */
  exists() {
    return this.getOffCanvas().length > 0;
  }

  /**
   * Opens the offcanvas and its backdrop
   *
   * @param {HTMLElement} offCanvas
   * @param {function} callback
   *
   * @private
   */
  _openOffcanvas(offCanvas, callback) {
    OffCanvasSingleton.bsOffcanvas.show();
    window.history.pushState('offcanvas-open', '');

    // if a callback function is being injected execute it after opening the OffCanvas
    if (typeof callback === 'function') {
      callback();
    }
  }

  /**
   * Register events
   * @param {number} delay
   * @private
   */
  _registerEvents(delay) {
    const event = src_helper_device_detection_helper__WEBPACK_IMPORTED_MODULE_2__/* ["default"].isTouchDevice */ .Z.isTouchDevice() ? 'touchend' : 'click';
    const offCanvasElements = this.getOffCanvas();

    // Ensure OffCanvas is removed from the DOM and events are published.
    src_helper_iterator_helper__WEBPACK_IMPORTED_MODULE_1__/* ["default"].iterate */ .Z.iterate(offCanvasElements, offCanvas => {
      const onBsClose = () => {
        setTimeout(() => {
          offCanvas.remove();
          this.$emitter.publish('onCloseOffcanvas', {
            offCanvasContent: offCanvasElements
          });
        }, delay);
        offCanvas.removeEventListener('hide.bs.offcanvas', onBsClose);
      };
      offCanvas.addEventListener('hide.bs.offcanvas', onBsClose);
    });
    window.addEventListener('popstate', this.close.bind(this, delay), {
      once: true
    });
    const closeTriggers = document.querySelectorAll(`.${OFF_CANVAS_CLOSE_TRIGGER_CLASS}`);
    src_helper_iterator_helper__WEBPACK_IMPORTED_MODULE_1__/* ["default"].iterate */ .Z.iterate(closeTriggers, trigger => trigger.addEventListener(event, this.close.bind(this, delay)));
  }

  /**
   * Remove all existing offcanvas from DOM
   * @private
   */
  _removeExistingOffCanvas() {
    OffCanvasSingleton.bsOffcanvas = null;
    const offCanvasElements = this.getOffCanvas();
    return src_helper_iterator_helper__WEBPACK_IMPORTED_MODULE_1__/* ["default"].iterate */ .Z.iterate(offCanvasElements, offCanvas => offCanvas.remove());
  }

  /**
   * Defines the position of the offcanvas by setting css class
   * @param {'left'|'right'|'bottom'} position
   * @returns {string}
   * @private
   */
  _getPositionClass(position) {
    if (position === 'left') {
      return 'offcanvas-start';
    }
    if (position === 'right') {
      return 'offcanvas-end';
    }
    return `offcanvas-${position}`;
  }

  /**
   * Creates the offcanvas element prototype including all relevant settings,
   * appends it to the DOM and returns the HTMLElement for further processing
   * @param {'left'|'right'|'bottom'} position
   * @param {boolean} fullwidth
   * @param {array|string} cssClass
   * @param {boolean} closable
   * @returns {HTMLElement}
   * @private
   */
  _createOffCanvas(position, fullwidth, cssClass, closable) {
    const offCanvas = document.createElement('div');
    offCanvas.classList.add(OFF_CANVAS_CLASS);
    offCanvas.classList.add(this._getPositionClass(position));
    if (fullwidth === true) {
      offCanvas.classList.add(OFF_CANVAS_FULLWIDTH_CLASS);
    }
    if (cssClass) {
      const type = typeof cssClass;
      if (type === 'string') {
        offCanvas.classList.add(cssClass);
      } else if (Array.isArray(cssClass)) {
        cssClass.forEach(value => {
          offCanvas.classList.add(value);
        });
      } else {
        throw new Error(`The type "${type}" is not supported. Please pass an array or a string.`);
      }
    }
    document.body.appendChild(offCanvas);
    OffCanvasSingleton.bsOffcanvas = new bootstrap.Offcanvas(offCanvas, {
      // Only use "static" mode (no close via click on backdrop) when "closable" option is explicitly set to "false".
      backdrop: closable === false ? 'static' : true
    });
    return offCanvas;
  }
}

/**
 * Create the OffCanvas instance.
 * @type {Readonly<OffCanvasSingleton>}
 */
const OffCanvasInstance = Object.freeze(new OffCanvasSingleton());
class OffCanvas {
  /**
   * Open the OffCanvas
   * @param {string} content
   * @param {function|null} callback
   * @param {'left'|'right'|'bottom'} position
   * @param {boolean} closable
   * @param {number} delay
   * @param {boolean} fullwidth
   * @param {array|string} cssClass
   */
  static open(content, callback = null, position = 'left', closable = true, delay = REMOVE_OFF_CANVAS_DELAY, fullwidth = false, cssClass = '') {
    OffCanvasInstance.open(content, callback, position, closable, delay, fullwidth, cssClass);
  }

  /**
   * Change content of visible OffCanvas
   * @param {string} content
   * @param {boolean} closable
   * @param {number} delay
   */
  static setContent(content, closable = true, delay = REMOVE_OFF_CANVAS_DELAY) {
    OffCanvasInstance.setContent(content, closable, delay);
  }

  /**
   * adds an additional class to the offcanvas
   *
   * @param {string} className
   */
  static setAdditionalClassName(className) {
    OffCanvasInstance.setAdditionalClassName(className);
  }

  /**
   * Close the OffCanvas
   * @param {number} delay
   */
  static close(delay = REMOVE_OFF_CANVAS_DELAY) {
    OffCanvasInstance.close(delay);
  }

  /**
   * Returns whether any OffCanvas exists or not
   * @returns {boolean}
   */
  static exists() {
    return OffCanvasInstance.exists();
  }

  /**
   * returns all existing offcanvas elements
   *
   * @returns {NodeListOf<Element>}
   */
  static getOffCanvas() {
    return OffCanvasInstance.getOffCanvas();
  }

  /**
   * Expose constant
   * @returns {number}
   */
  static REMOVE_OFF_CANVAS_DELAY() {
    return REMOVE_OFF_CANVAS_DELAY;
  }
}

/***/ }),

/***/ 9066:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var src_plugin_cookie_cookie_configuration_plugin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8553);


function _pm_geturlparm(parmname) {
  var regex = new RegExp("[\\?&]" + parmname + "=([^&#]*)");
  var result = regex.exec(location.search);
  return result === null ? "" : decodeURIComponent(result[1].replace(/\+/g, " "));
}
function _pm_getGclid() {
  let gclid = _pm_geturlparm("gclid");
  if (gclid !== '') {
    return gclid;
  }
  let gclidfromGclAw = _pm_getcookie('_gcl_aw');
  if (gclidfromGclAw != null) {
    let gclAwSplitAll = gclidfromGclAw.split('.');
    if (gclAwSplitAll.length >= 3) {
      return gclidfromGclAw.substring(gclAwSplitAll[0].length + gclAwSplitAll[1].length + 1 + 1); // each +1 corresponds to '.'s
    }
  }

  let gclidfromFPGCLAW = _pm_getcookie('FPGCLAW');
  if (gclidfromFPGCLAW != null) {
    const fpgSplitAll = gclidfromFPGCLAW.split('.');
    if (fpgSplitAll.length >= 3) {
      return gclidfromFPGCLAW.substring(fpgSplitAll[0].length + fpgSplitAll[1].length + 1 + 1); // each +1 corresponds to '.'s
    }
  }
}

function _pm_getcookie(cookiename) {
  cookiename += "=";
  if (document.cookie.indexOf(cookiename) !== -1) {
    var idxofSource = document.cookie.indexOf(cookiename) + cookiename.length;
    var idxofEnd = document.cookie.indexOf(";", idxofSource);
    var cookval = "";
    if (idxofEnd === -1) {
      cookval = document.cookie.substr(idxofSource);
    } else {
      cookval = document.cookie.substr(idxofSource, idxofEnd - idxofSource);
    }
    if (cookval.length !== 0) {
      return cookval;
    } else {
      return null;
    }
  }
}
function pmGetGa4SessionId() {
  // example: _ga_333DA3AABC=GS1.1.1695210760.1.1.1695210809.11.0.0
  // above example was modified as it was taken from a customer, but format remains intact
  const retImploded = document.cookie.split(';').filter(c => c.indexOf('_ga_') !== -1).map(c => c.trim().split('.')).filter(c => null != c && typeof c !== 'undefined' && typeof c.length === 'number' && c.length >= 4) // must have atleast enough for count
  .map(c => c[0].substring(4, c[0].indexOf('=')) + ":" + c[2]) // index 2 for session id
  .join(',');
  return null != retImploded && retImploded.length > 0 ? retImploded : null;
}
function pmGetGa4SessionCount() {
  const retImploded = document.cookie.split(';').filter(c => c.indexOf('_ga_') !== -1).map(c => c.trim().split('.')).filter(c => null != c && typeof c !== 'undefined' && typeof c.length === 'number' && c.length >= 4) // must have atleast enough for count
  .map(c => c[0].substring(4, c[0].indexOf('=')) + ":" + c[3]) // index 3 for session count
  .join(',');
  return null != retImploded && retImploded.length > 0 ? retImploded : null;
}
function _pm_getStoredTPTrack() {
  var ret = _pm_getcookie("pmTPTrack");
  if (null != ret && ret.length > 0) {
    ret = JSON.parse(decodeURIComponent(ret));
  } else {
    ret = {
      gclid: null,
      gacid: null,
      gacid_source: null,
      fbp: null,
      fbc: null,
      gbraid: null,
      wbraid: null,
      sccid: null,
      ttclid: null,
      msclkid: null,
      twclid: null,
      ga4SessionId: null,
      ga4SessionCount: null,
      timestamp: (new Date() / 1E3 | 0) - 100
    };
  }
  return ret;
}
function _pm_storeTPTrack(tptrack) {
  var _pm_old_tpTrackCookVal = _pm_getcookie("pmTPTrack");
  var _pm_tpTrackCookVal = encodeURIComponent(JSON.stringify(tptrack));
  document.cookie = "pmTPTrack=" + _pm_tpTrackCookVal + "; path=/";
}
function _pm_GetGacidFromTracker() {
  if (typeof ga == 'function') {
    try {
      ga(function (tracker) {
        var gacid = tracker.get('clientId');
        if (null != gacid) {
          var _pm_curPMTPTrack = _pm_getStoredTPTrack();
          if (_pm_curPMTPTrack.gacid !== gacid) {
            _pm_curPMTPTrack.gacid = gacid;
            _pm_curPMTPTrack.gacid_source = "gatracker";
            _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
            _pm_storeTPTrack(_pm_curPMTPTrack);
          }
        }
      });
    } catch (eee) {}
  } else {
    setTimeout(_pm_GetGacidFromTracker, 100);
  }
}
function load_pmTPTrack() {
  var _pm_curPMTPTrack = _pm_getStoredTPTrack();
  var _pm_newFBC = _pm_getcookie("_fbc");
  if (null != _pm_newFBC && _pm_curPMTPTrack.fbc !== _pm_newFBC) {
    _pm_curPMTPTrack.fbc = _pm_newFBC;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_newFBP = _pm_getcookie("_fbp");
  if (null != _pm_newFBP && _pm_curPMTPTrack.fbp !== _pm_newFBP) {
    _pm_curPMTPTrack.fbp = _pm_newFBP;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_newGacid = _pm_getcookie("_ga");
  if (null != _pm_newGacid && _pm_curPMTPTrack.gacid_source !== "gatracker" && _pm_curPMTPTrack.gacid !== _pm_newGacid) {
    _pm_curPMTPTrack.gacid = _pm_newGacid;
    _pm_curPMTPTrack.gacid_source = "gacookie";
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_newGclid = _pm_getGclid();
  if (_pm_newGclid !== "") {
    _pm_curPMTPTrack.gclid = _pm_newGclid;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_gbraid = _pm_geturlparm("gbraid");
  if (_pm_gbraid !== "") {
    _pm_curPMTPTrack.gbraid = _pm_gbraid;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_wbraid = _pm_geturlparm("wbraid");
  if (_pm_wbraid !== "") {
    _pm_curPMTPTrack.wbraid = _pm_wbraid;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_sccid = _pm_geturlparm("sccid");
  if (_pm_sccid !== "") {
    _pm_curPMTPTrack.sccid = _pm_sccid;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_ttclid = _pm_geturlparm("ttclid");
  if (_pm_ttclid !== "") {
    _pm_curPMTPTrack.ttclid = _pm_ttclid;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_msclkid = _pm_geturlparm("msclkid");
  if (_pm_msclkid !== "") {
    _pm_curPMTPTrack.msclkid = _pm_msclkid;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_twclid = _pm_geturlparm("twclid");
  if (_pm_twclid !== "") {
    _pm_curPMTPTrack.twclid = _pm_twclid;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_ga4SessionId = pmGetGa4SessionId();
  if (_pm_ga4SessionId != null && _pm_ga4SessionId !== "") {
    _pm_curPMTPTrack.ga4SessionId = _pm_ga4SessionId;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }
  var _pm_ga4SessionCount = pmGetGa4SessionCount();
  if (_pm_ga4SessionCount != null && _pm_ga4SessionCount !== "") {
    _pm_curPMTPTrack.ga4SessionCount = _pm_ga4SessionCount;
    _pm_curPMTPTrack.timestamp = new Date() / 1E3 | 0;
  }

  // Temp solution
  _pm_curPMTPTrack.cc_marketing = window.cc_marketing;
  _pm_curPMTPTrack.cc_statistics = window.cc_statistics;
  _pm_storeTPTrack(_pm_curPMTPTrack);
  _pm_GetGacidFromTracker();

  // Set previousDecision
  localStorage.setItem('pfm-consent-granted', true);
}

// Set default consent state
window.cc_statistics = false;
window.cc_marketing = false;
window.addEventListener("load", function (_event) {
  var _Cookiebot, _Cookiebot$consent, _Cookiebot2, _Cookiebot2$consent, _CookieInformation, _CookieInformation2, _OnetrustActiveGroups, _OnetrustActiveGroups2, _getCkyConsent, _getCkyConsent$catego, _getCkyConsent2, _getCkyConsent2$categ, _CookieFirst, _CookieFirst$consent, _CookieFirst2, _CookieFirst2$consent, _CookieScript, _CookieScript$instanc, _CookieScript$instanc2, _CookieScript$instanc3, _CookieScript2, _CookieScript2$instan, _CookieScript2$instan2, _CookieScript2$instan3, _window$google_tag_da, _window$google_tag_da2, _window$google_tag_da3, _window$google_tag_da4, _window$google_tag_da5, _window$google_tag_da6, _window$google_tag_da7, _window$google_tag_da8, _window$google_tag_da9;
  let previousDecision = localStorage.getItem('pfm-consent-granted');
  if (!window.blockScriptBeforeConsent || previousDecision) {
    // Set default consent to true
    window.cc_statistics = true;
    window.cc_marketing = true;
    load_pmTPTrack();
  }

  //Shopware
  document.addEventListener('CookieConfiguration_Update', event => {
    const {
      pmTPTrack
    } = event.detail;
    if (pmTPTrack === true) {
      window.cc_statistics = true;
      window.cc_marketing = true;
      load_pmTPTrack();
    }
  });

  // CookieBot
  // Get consent
  if (typeof Cookiebot !== 'undefined' && (_Cookiebot = Cookiebot) !== null && _Cookiebot !== void 0 && (_Cookiebot$consent = _Cookiebot.consent) !== null && _Cookiebot$consent !== void 0 && _Cookiebot$consent.statistics && (_Cookiebot2 = Cookiebot) !== null && _Cookiebot2 !== void 0 && (_Cookiebot2$consent = _Cookiebot2.consent) !== null && _Cookiebot2$consent !== void 0 && _Cookiebot2$consent.marketing) {
    cc_statistics = Cookiebot.consent.statistics;
    cc_marketing = Cookiebot.consent.marketing;
    load_pmTPTrack();
  } else {
    // Add event listener
    window.addEventListener('CookiebotOnConsentReady', function () {
      var _Cookiebot3, _Cookiebot3$consent, _Cookiebot4, _Cookiebot4$consent;
      if ((_Cookiebot3 = Cookiebot) !== null && _Cookiebot3 !== void 0 && (_Cookiebot3$consent = _Cookiebot3.consent) !== null && _Cookiebot3$consent !== void 0 && _Cookiebot3$consent.statistics && (_Cookiebot4 = Cookiebot) !== null && _Cookiebot4 !== void 0 && (_Cookiebot4$consent = _Cookiebot4.consent) !== null && _Cookiebot4$consent !== void 0 && _Cookiebot4$consent.marketing) {
        cc_statistics = Cookiebot.consent.statistics;
        cc_marketing = Cookiebot.consent.marketing;
        load_pmTPTrack();
      }
    });
  }

  // CookieInformation
  // Get consent
  if (typeof CookieInformation !== 'undefined' && (_CookieInformation = CookieInformation) !== null && _CookieInformation !== void 0 && _CookieInformation.getConsentGivenFor('cookie_cat_statistic') && (_CookieInformation2 = CookieInformation) !== null && _CookieInformation2 !== void 0 && _CookieInformation2.getConsentGivenFor('cookie_cat_marketing')) {
    cc_statistics = CookieInformation.getConsentGivenFor('cookie_cat_statistic');
    cc_marketing = CookieInformation.getConsentGivenFor('cookie_cat_marketing');
    load_pmTPTrack();
  } else {
    // Add event listener
    window.addEventListener('CookieInformationConsentGiven', function () {
      var _CookieInformation3, _CookieInformation4;
      if ((_CookieInformation3 = CookieInformation) !== null && _CookieInformation3 !== void 0 && _CookieInformation3.getConsentGivenFor('cookie_cat_statistic') && (_CookieInformation4 = CookieInformation) !== null && _CookieInformation4 !== void 0 && _CookieInformation4.getConsentGivenFor('cookie_cat_marketing')) {
        cc_statistics = CookieInformation.getConsentGivenFor('cookie_cat_statistic');
        cc_marketing = CookieInformation.getConsentGivenFor('cookie_cat_marketing');
        load_pmTPTrack();
      }
    });
  }

  // OneTrust
  // Get consent
  if (typeof OneTrust !== 'undefined' && (_OnetrustActiveGroups = OnetrustActiveGroups) !== null && _OnetrustActiveGroups !== void 0 && _OnetrustActiveGroups.includes("2") && (_OnetrustActiveGroups2 = OnetrustActiveGroups) !== null && _OnetrustActiveGroups2 !== void 0 && _OnetrustActiveGroups2.includes("4")) {
    cc_statistics = OnetrustActiveGroups.includes("2");
    cc_marketing = OnetrustActiveGroups.includes("4");
    load_pmTPTrack();
  } else {
    // Add event listener
    window.addEventListener("OneTrustGroupsUpdated", event => {
      var _event$detail, _event$detail2;
      if (event !== null && event !== void 0 && (_event$detail = event.detail) !== null && _event$detail !== void 0 && _event$detail.some(group => group.includes("4")) && event !== null && event !== void 0 && (_event$detail2 = event.detail) !== null && _event$detail2 !== void 0 && _event$detail2.some(group => group.includes("2"))) {
        cc_statistics = event.detail.some(group => group.includes("4"));
        cc_marketing = event.detail.some(group => group.includes("2"));
        load_pmTPTrack();
      }
    });
  }

  // CookieYes
  // Get consent
  if (typeof getCkyConsent !== 'undefined' && (_getCkyConsent = getCkyConsent()) !== null && _getCkyConsent !== void 0 && (_getCkyConsent$catego = _getCkyConsent.categories) !== null && _getCkyConsent$catego !== void 0 && _getCkyConsent$catego.analytics && (_getCkyConsent2 = getCkyConsent()) !== null && _getCkyConsent2 !== void 0 && (_getCkyConsent2$categ = _getCkyConsent2.categories) !== null && _getCkyConsent2$categ !== void 0 && _getCkyConsent2$categ.advertisement) {
    cc_statistics = getCkyConsent().categories.analytics;
    cc_marketing = getCkyConsent().categories.advertisement;
    load_pmTPTrack();
  } else {
    // Add event listener
    document.addEventListener("cookieyes_consent_update", function (eventData) {
      var _eventData$detail, _eventData$detail$acc, _eventData$detail2, _eventData$detail2$ac;
      if (eventData !== null && eventData !== void 0 && (_eventData$detail = eventData.detail) !== null && _eventData$detail !== void 0 && (_eventData$detail$acc = _eventData$detail.accepted) !== null && _eventData$detail$acc !== void 0 && _eventData$detail$acc.includes("analytics") && eventData !== null && eventData !== void 0 && (_eventData$detail2 = eventData.detail) !== null && _eventData$detail2 !== void 0 && (_eventData$detail2$ac = _eventData$detail2.accepted) !== null && _eventData$detail2$ac !== void 0 && _eventData$detail2$ac.includes("advertisement")) {
        cc_statistics = eventData.detail.accepted.includes("analytics");
        cc_marketing = eventData.detail.accepted.includes("advertisement");
        load_pmTPTrack();
      }
    });
  }

  // CookieFirst
  // Get consent
  if (typeof CookieFirst !== 'undefined' && (_CookieFirst = CookieFirst) !== null && _CookieFirst !== void 0 && (_CookieFirst$consent = _CookieFirst.consent) !== null && _CookieFirst$consent !== void 0 && _CookieFirst$consent.performance && (_CookieFirst2 = CookieFirst) !== null && _CookieFirst2 !== void 0 && (_CookieFirst2$consent = _CookieFirst2.consent) !== null && _CookieFirst2$consent !== void 0 && _CookieFirst2$consent.advertising) {
    cc_statistics = CookieFirst.consent.performance;
    cc_marketing = CookieFirst.consent.advertising;
    load_pmTPTrack();
  } else {
    // Add event listener
    window.addEventListener('cf_consent', function (event) {
      var _event$detail3, _event$detail4;
      if (event !== null && event !== void 0 && (_event$detail3 = event.detail) !== null && _event$detail3 !== void 0 && _event$detail3.performance && event !== null && event !== void 0 && (_event$detail4 = event.detail) !== null && _event$detail4 !== void 0 && _event$detail4.advertising) {
        cc_statistics = event.detail.performance;
        cc_marketing = event.detail.advertising;
        load_pmTPTrack();
      }
    });
    // Add event listener
    window.addEventListener('cf_consent_loaded', function (event) {
      var _event$detail5, _event$detail6;
      if (event !== null && event !== void 0 && (_event$detail5 = event.detail) !== null && _event$detail5 !== void 0 && _event$detail5.performance && event !== null && event !== void 0 && (_event$detail6 = event.detail) !== null && _event$detail6 !== void 0 && _event$detail6.advertising) {
        cc_statistics = event.detail.performance;
        cc_marketing = event.detail.advertising;
        load_pmTPTrack();
      }
    });
  }

  // CookieScript
  // Get consent
  if (typeof CookieScript !== 'undefined' && (_CookieScript = CookieScript) !== null && _CookieScript !== void 0 && (_CookieScript$instanc = _CookieScript.instance) !== null && _CookieScript$instanc !== void 0 && (_CookieScript$instanc2 = _CookieScript$instanc.currentState()) !== null && _CookieScript$instanc2 !== void 0 && (_CookieScript$instanc3 = _CookieScript$instanc2.categories) !== null && _CookieScript$instanc3 !== void 0 && _CookieScript$instanc3.includes("performance") && (_CookieScript2 = CookieScript) !== null && _CookieScript2 !== void 0 && (_CookieScript2$instan = _CookieScript2.instance) !== null && _CookieScript2$instan !== void 0 && (_CookieScript2$instan2 = _CookieScript2$instan.currentState()) !== null && _CookieScript2$instan2 !== void 0 && (_CookieScript2$instan3 = _CookieScript2$instan2.categories) !== null && _CookieScript2$instan3 !== void 0 && _CookieScript2$instan3.includes("targeting")) {
    cc_statistics = CookieScript.instance.currentState().categories.includes("performance");
    cc_marketing = CookieScript.instance.currentState().categories.includes("targeting");
    load_pmTPTrack();
  } else {
    // Add event listener
    window.addEventListener('CookieScriptCategory-strict', function () {
      var _CookieScript3, _CookieScript3$instan, _CookieScript3$instan2, _CookieScript3$instan3, _CookieScript4, _CookieScript4$instan, _CookieScript4$instan2, _CookieScript4$instan3;
      if ((_CookieScript3 = CookieScript) !== null && _CookieScript3 !== void 0 && (_CookieScript3$instan = _CookieScript3.instance) !== null && _CookieScript3$instan !== void 0 && (_CookieScript3$instan2 = _CookieScript3$instan.currentState()) !== null && _CookieScript3$instan2 !== void 0 && (_CookieScript3$instan3 = _CookieScript3$instan2.categories) !== null && _CookieScript3$instan3 !== void 0 && _CookieScript3$instan3.includes("performance") && (_CookieScript4 = CookieScript) !== null && _CookieScript4 !== void 0 && (_CookieScript4$instan = _CookieScript4.instance) !== null && _CookieScript4$instan !== void 0 && (_CookieScript4$instan2 = _CookieScript4$instan.currentState()) !== null && _CookieScript4$instan2 !== void 0 && (_CookieScript4$instan3 = _CookieScript4$instan2.categories) !== null && _CookieScript4$instan3 !== void 0 && _CookieScript4$instan3.includes("targeting")) {
        cc_statistics = CookieScript.instance.currentState().categories.includes("performance");
        cc_marketing = CookieScript.instance.currentState().categories.includes("targeting");
        load_pmTPTrack();
      }
    });
  }

  // Google Consent Mode
  // Get consent
  if (typeof window.google_tag_data !== 'undefined' && (_window$google_tag_da = window.google_tag_data) !== null && _window$google_tag_da !== void 0 && _window$google_tag_da.ics && (_window$google_tag_da2 = window.google_tag_data) !== null && _window$google_tag_da2 !== void 0 && (_window$google_tag_da3 = _window$google_tag_da2.ics) !== null && _window$google_tag_da3 !== void 0 && (_window$google_tag_da4 = _window$google_tag_da3.entries) !== null && _window$google_tag_da4 !== void 0 && (_window$google_tag_da5 = _window$google_tag_da4.ad_storage) !== null && _window$google_tag_da5 !== void 0 && _window$google_tag_da5.update && (_window$google_tag_da6 = window.google_tag_data) !== null && _window$google_tag_da6 !== void 0 && (_window$google_tag_da7 = _window$google_tag_da6.ics) !== null && _window$google_tag_da7 !== void 0 && (_window$google_tag_da8 = _window$google_tag_da7.entries) !== null && _window$google_tag_da8 !== void 0 && (_window$google_tag_da9 = _window$google_tag_da8.analytics_storage) !== null && _window$google_tag_da9 !== void 0 && _window$google_tag_da9.update) {
    cc_marketing = window.google_tag_data.ics.entries.ad_storage.update;
    cc_statistics = window.google_tag_data.ics.entries.analytics_storage.update;
    load_pmTPTrack();
  } else {
    var _window$google_tag_da10, _window$google_tag_da11;
    // Add event listener
    (_window$google_tag_da10 = window.google_tag_data) === null || _window$google_tag_da10 === void 0 ? void 0 : (_window$google_tag_da11 = _window$google_tag_da10.ics) === null || _window$google_tag_da11 === void 0 ? void 0 : _window$google_tag_da11.addListener(["ad_storage", "analytics_storage"], function (event) {
      var _window$google_tag_da12, _window$google_tag_da13, _window$google_tag_da14, _window$google_tag_da15, _window$google_tag_da16, _window$google_tag_da17, _window$google_tag_da18, _window$google_tag_da19;
      if ((_window$google_tag_da12 = window.google_tag_data) !== null && _window$google_tag_da12 !== void 0 && (_window$google_tag_da13 = _window$google_tag_da12.ics) !== null && _window$google_tag_da13 !== void 0 && (_window$google_tag_da14 = _window$google_tag_da13.entries) !== null && _window$google_tag_da14 !== void 0 && (_window$google_tag_da15 = _window$google_tag_da14.ad_storage) !== null && _window$google_tag_da15 !== void 0 && _window$google_tag_da15.update && (_window$google_tag_da16 = window.google_tag_data) !== null && _window$google_tag_da16 !== void 0 && (_window$google_tag_da17 = _window$google_tag_da16.ics) !== null && _window$google_tag_da17 !== void 0 && (_window$google_tag_da18 = _window$google_tag_da17.entries) !== null && _window$google_tag_da18 !== void 0 && (_window$google_tag_da19 = _window$google_tag_da18.analytics_storage) !== null && _window$google_tag_da19 !== void 0 && _window$google_tag_da19.update) {
        cc_marketing = window.google_tag_data.ics.entries.ad_storage.update;
        cc_statistics = window.google_tag_data.ics.entries.analytics_storage.update;
        load_pmTPTrack();
      }
    });
  }
});

// Necessary for the webpack hot module reloading server
if (false) {}

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor-node","vendor-shared"], () => (__webpack_exec__(9066)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);