const { Component, Mixin } = Shopware;
import template from './index.html.twig';

Component.register('pm-button-customer-export', {
    template,

    inject: ['PmApiCustomerExportService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            isSaveSuccessful: false
        };
    },

    computed: {
        pluginConfig() {
            let config = null;
            let component = this;

            do {
                component = component.$parent;
                config = component.actualConfigData;
            } while (!config);

            const currentSalesChannelId = component.currentSalesChannelId;
            return config[currentSalesChannelId];
        }
    },

    methods: {
        saveFinish() {
            this.isSaveSuccessful = false;
        },

        downloadCustomers() {
            this.isLoading = true;
            this.PmApiCustomerExportService
            .downloadCustomers(this.pluginConfig)
            .then((res) => {
                // On success, res should be CSV data
                const blob = new Blob([res], { type: 'application/csv' });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                const now = new Date();
                link.download = 'pm_customers_'
                    + now.toLocaleDateString('sv').replace(/-/g, '')
                    + '_'
                    + now.toTimeString().slice(0, 8).replace(/:/g, '')
                    + '.csv';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                this.isSaveSuccessful = true;
                this.createNotificationSuccess({
                    title: this.$tc('profitmetrics-api.customerDownload.title'),
                    message: this.$tc('profitmetrics-api.customerDownload.success')
                });
            })
            .catch((error) => {
                this.createNotificationError({
                    title: this.$tc('profitmetrics-api.customerDownload.title'),
                    message: error.message || this.$tc('profitmetrics-api.customerDownload.error')
                });
            })
            .finally(() => {
                this.isLoading = false;
            });
        }
    }
});
