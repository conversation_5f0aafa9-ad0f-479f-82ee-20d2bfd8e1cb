const { Component, Mixin } = Shopware;
import template from './index.html.twig';

Component.register('pm-button-api-verify-button', {
    template,

    inject: ['PmApiService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            isSaveSuccessful: false
        };
    },

    computed: {
        pluginConfig() {
            let config = null;
            let component = this;

            do {
                component = component.$parent;
                config = component.actualConfigData;
            } while (!config);

            const currentSalesChannelId = component.currentSalesChannelId;
            return config[currentSalesChannelId];
        }
    },

    methods: {
        saveFinish() {
            this.isSaveSuccessful = false;
        },

        testApi() {
            this.isLoading = true;
            this.PmApiService
            .testConfig(this.pluginConfig)
            .then((res) => {
                if (res.success) {
                    this.isSaveSuccessful = true;
                    this.createNotificationSuccess({
                        title: this.$tc('profitmetrics-api.test.title'),
                        message: res.message || this.$tc('profitmetrics-api.test.success')
                    });
                } else {
                    this.createNotificationError({
                        title: this.$tc('profitmetrics-api.test.title'),
                        message: res.message || this.$tc('profitmetrics-api.test.error')
                    });
                }
            })
            .catch((error) => {
                this.createNotificationError({
                    title: this.$tc('profitmetrics-api.test.title'),
                    message: error.message || this.$tc('profitmetrics-api.test.error')
                });
            })
            .finally(() => {
                this.isLoading = false;
            });
        }
    }
});
