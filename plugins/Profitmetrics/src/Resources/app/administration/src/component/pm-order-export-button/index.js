const { Component, Mixin } = Shopware;
import template from './index.html.twig';

Component.register('pm-button-order-export', {
    template,

    inject: ['PmApiOrderExportService'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            isLoading: false,
            isSaveSuccessful: false
        };
    },

    computed: {
        pluginConfig() {
            let element = this;
            while (element.$parent && !element.actualConfigData) {
                element = element.$parent;
            }

            if (!element.actualConfigData) {
                return null;
            }

            return element.actualConfigData.null;
        }
    },

    methods: {
        exportOrders() {
            this.isLoading = true;
            this.PmApiOrderExportService
            .exportOrders(this.pluginConfig)
            .then((res) => {
                if (res.success) {
                    this.isSaveSuccessful = true;
                    this.createNotificationSuccess({
                        title: this.$tc('profitmetrics-api.order.title'),
                        message: res.message || this.$tc('profitmetrics-api.order.success')
                    });
                } else {
                    this.createNotificationError({
                        title: this.$tc('profitmetrics-api.order.title'),
                        message: res.message || this.$tc('profitmetrics-api.order.error')
                    });
                }
            })
            .catch((error) => {
                this.createNotificationError({
                    title: this.$tc('profitmetrics-api.order.title'),
                    message: error.message || this.$tc('profitmetrics-api.order.error')
                });
            })
            .finally(() => {
                this.isLoading = false;
            });
        }
    }
});
