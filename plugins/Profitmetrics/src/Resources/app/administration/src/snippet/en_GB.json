{"profitmetrics-api": {"order": {"title": "Export Orders", "success": "Order export was successfully finished", "error": "Order export failed, check logs for more information."}, "test": {"title": "Test API connection", "success": "Connection tested with success", "error": "Connection could not be established."}, "customerDownload": {"title": "Export customers", "success": "Customers were successfully downloaded", "error": "Customer download failed, check logs for more information."}}, "profitmetrics": {"send-orders": {"label": "Send orders now"}, "api-test": {"label": "Test API connection"}, "customer-download-button": {"label": "Download customers"}}, "sw-sales-channel": {"detail": {"productComparison": {"templates": {"template-label": {"profitMetrics": "ProfitMetrics"}}}}}}