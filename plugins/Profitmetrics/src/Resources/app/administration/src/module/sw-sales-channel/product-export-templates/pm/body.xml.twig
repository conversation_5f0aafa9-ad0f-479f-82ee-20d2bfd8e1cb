<item>
    {# @var product \Shopware\Core\Content\Product\SalesChannel\SalesChannelProductEntity #}
    <g:id>{{ product.productNumber }}</g:id>
    <title>{{ product.translated.name|escape }}</title>
    <g:image_link>{% if product.cover and product.cover.media %}{{ product.cover.media.url }}{% endif %}</g:image_link>
    <link>{{ seoUrl('frontend.detail.page', {'productId': product.id}) }}</link>

    {% set price = product.price.getCurrencyPrice(context.currencyId) %}
    {% if product.price.getCurrencyPrice(context.currencyId).listPrice != null %}
        {% set price = product.price.getCurrencyPrice(context.currencyId).listPrice %}
    {% endif %}
    {% set price = price.gross %}
    <g:price>{{ price|number_format(context.currency.totalRounding.decimals, '.', '') }}</g:price>
    <pm:price_currency>{{ context.currency.isoCode }}</pm:price_currency>

    {# @var purchasePrice \Shopware\Core\Framework\DataAbstractionLayer\Pricing\Price #}
    {% set purchasePrice = null %}
    {% if product.purchasePrices %}
        {% set purchasePrice = product.purchasePrices.currencyPrice(context.currencyId) %}
    {% endif %}
    <pm:price_buy>{%- if purchasePrice -%}{{ purchasePrice.net }}{%- endif -%}</pm:price_buy>
    <pm:price_buy_currency>{{ context.currency.isoCode }}</pm:price_buy_currency>

    <pm:num_stock>{{ product.availableStock }}</pm:num_stock>
    <tax_state>{{ context.taxState }}</tax_state>
</item>
