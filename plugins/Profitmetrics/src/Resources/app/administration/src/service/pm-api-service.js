const ApiService = Shopware.Classes.ApiService;
const { Application } = Shopware;

class PmApiService extends ApiService {
    constructor(httpClient, loginService, apiEndpoint = 'profitmetrics-api') {
        super(httpClient, loginService, apiEndpoint);
    }

    testConfig(data) {
        const headers = this.getBasicHeaders({});

        return this.httpClient
            .post(
                `_action/${this.getApiBasePath()}/verify`,
                data,
                headers
            ).then((response) => {
                return ApiService.handleResponse(response);
            });
    }
}

Application.addServiceProvider('PmApiService', (container) => {
    const initContainer = Application.getContainer('init');
    return new PmApiService(initContainer.httpClient, container.loginService);
});
