const ApiService = Shopware.Classes.ApiService;
const { Application } = Shopware;

class PmApiCustomerExportService extends ApiService {
    constructor(httpClient, loginService, apiEndpoint = 'profitmetrics-api') {
        super(httpClient, loginService, apiEndpoint);
    }

    downloadCustomers(data) {
        const headers = this.getBasicHeaders({});

        return this.httpClient
            .get(
                `_action/${this.getApiBasePath()}/customer-export`,
                data,
                headers
            ).then((response) => {
                return ApiService.handleResponse(response);
            });
    }
}

Application.addServiceProvider('PmApiCustomerExportService', (container) => {
    const initContainer = Application.getContainer('init');
    return new PmApiCustomerExportService(initContainer.httpClient, container.loginService);
});
