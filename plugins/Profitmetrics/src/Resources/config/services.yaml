services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Core Services
  Profitmetrics\Profitmetrics\Service\CustomFieldService:
    public: true
    arguments:
      $customFieldSetRepository: '@custom_field_set.repository'
      $customFieldRepository: '@custom_field.repository'
      $connection: '@Doctrine\DBAL\Connection'

  Profitmetrics\Profitmetrics\Core\Service\OrderDataTransformer:
    arguments:
      - '@Profitmetrics\Profitmetrics\Core\Service\LineItemTransformer'
      - '@Profitmetrics\Profitmetrics\Core\Service\WeightCalculator'


  Profitmetrics\Profitmetrics\Core\Service\WeightCalculator:
    arguments:
      - '@Doctrine\DBAL\Connection'
      - '@Profitmetrics\Profitmetrics\Service\ErrorHandler'

  Profitmetrics\Profitmetrics\Core\Service\LineItemTransformer: ~

  Profitmetrics\Profitmetrics\Service\OrderExportCriteriaBuilder:
    arguments:
      $stateRepository: '@state_machine_state.repository'
      $systemConfigService: '@Shopware\Core\System\SystemConfig\SystemConfigService'

  Profitmetrics\Profitmetrics\Core\Service\ProfitMetricsApiClient:
    arguments:
      $httpClient: '@http_client' # Psr\Http\Client\ClientInterface
      $errorHandler: '@Profitmetrics\Profitmetrics\Service\ErrorHandler'

  # Controllers
  Profitmetrics\Profitmetrics\Core\Tracking\IndexController:
    public: true
    arguments:
      - '@VisitorTrackingSubscriber'
    calls:
      - method: setContainer
        arguments:
          - '@service_container'

  Profitmetrics\Profitmetrics\Core\Api\ApiController:
    public: true
    arguments:
      - '@Profitmetrics\Profitmetrics\Core\Service\ProfitMetricsApiClient'
      - '@Profitmetrics\Profitmetrics\Service\ErrorHandler'

  Profitmetrics\Profitmetrics\Core\Api\CustomerExportController:
    public: true

  Profitmetrics\Profitmetrics\Core\Api\OrderExportController:
    public: true
    arguments:
      $orderCriteriaService: '@Profitmetrics\Profitmetrics\Service\OrderExportCriteriaBuilder'
      $orderRepository: '@order.repository'
      $connection: '@Doctrine\DBAL\Connection'
      $messageBus: '@messenger.bus.shopware'

  # Commands
  Profitmetrics\Profitmetrics\Command\OrderExportCommand:
    arguments:
      $errorHandler: '@Profitmetrics\Profitmetrics\Service\ErrorHandler'
      $orderCriteriaService: '@Profitmetrics\Profitmetrics\Service\OrderExportCriteriaBuilder'
      $orderRepository: '@order.repository'
      $messageBus: '@messenger.bus.shopware'
    tags:
      - { name: console.command }

  Profitmetrics\Profitmetrics\Command\VisitorCleanupCommand:
    arguments:
      - '@Doctrine\DBAL\Connection'
      - '@Profitmetrics\Profitmetrics\Service\ErrorHandler'
    tags:
      - { name: console.command }

  # Scheduled Tasks
  Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\VisitorCleanupTask:
    tags:
      - { name: shopware.scheduled.task }

  Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\OrderExportTask:
    tags:
      - { name: shopware.scheduled.task }

  Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\Handler\VisitorCleanupTaskHandler:
    arguments:
      - '@scheduled_task.repository'
      - '@messenger.bus.shopware'
      - '@Profitmetrics\Profitmetrics\Service\ErrorHandler'
    tags:
      - { name: messenger.message_handler }

  Profitmetrics\Profitmetrics\MessageQueue\ScheduledTask\Handler\OrderExportTaskHandler:
    arguments:
      $scheduledTaskRepository: '@scheduled_task.repository'
      $orderCriteriaService: '@Profitmetrics\Profitmetrics\Service\OrderExportCriteriaBuilder'
      $orderRepository: '@order.repository'
      $messageBus: '@messenger.bus.shopware'
      $connection: '@Doctrine\DBAL\Connection'
    tags:
      - { name: messenger.message_handler }

  Profitmetrics\Profitmetrics\MessageQueue\Message\Handler\VisitorCleanupMessageHandler:
    arguments:
      $connection: '@Doctrine\DBAL\Connection'
      $errorHandler: '@Profitmetrics\Profitmetrics\Service\ErrorHandler'
    tags:
      - { name: messenger.message_handler }

  Profitmetrics\Profitmetrics\MessageQueue\Message\Handler\OrderExportMessageHandler:
    arguments:
      $systemConfigService: '@Shopware\Core\System\SystemConfig\SystemConfigService'
      $orderRepository: '@order.repository'
      $visitorRepository: '@profitmetrics_visitor.repository'
      $pluginRepository: '@plugin.repository'
      $dataTransformer: '@Profitmetrics\Profitmetrics\Core\Service\OrderDataTransformer'
      $apiClient: '@Profitmetrics\Profitmetrics\Core\Service\ProfitMetricsApiClient'
      $instanceService: '@Shopware\Core\Framework\Store\Services\InstanceService'
      $errorHandler: '@Profitmetrics\Profitmetrics\Service\ErrorHandler'
    tags:
      - { name: messenger.message_handler }

  # Subscribers
  Profitmetrics\Profitmetrics\Storefront\Subscriber\VisitorTrackingSubscriber:
    arguments:
      - '@Shopware\Core\System\SystemConfig\SystemConfigService'
      - '@Doctrine\DBAL\Connection'
      - '@Profitmetrics\Profitmetrics\Service\ErrorHandler'
    tags:
      - { name: kernel.event_subscriber }

  # Alias for VisitorTrackingSubscriber
  VisitorTrackingSubscriber:
    alias: Profitmetrics\Profitmetrics\Storefront\Subscriber\VisitorTrackingSubscriber
    public: true

  Profitmetrics\Profitmetrics\Storefront\Subscriber\OrderConversionSubscriber:
    arguments:
      $systemConfigService: '@Shopware\Core\System\SystemConfig\SystemConfigService'
      $connection: '@Doctrine\DBAL\Connection'
      $orderRepository: '@order.repository'
      $errorHandler: '@Profitmetrics\Profitmetrics\Service\ErrorHandler'
    tags:
      - { name: kernel.event_subscriber }

  # Repositories and Definitions
  Profitmetrics\Profitmetrics\Core\Profitmetrics\VisitorDefinition:
    tags:
      - { name: shopware.entity.definition }

  # Cookie Providers
  Profitmetrics\Profitmetrics\Framework\Cookie\CustomCookieProvider:
    decorates: Shopware\Storefront\Framework\Cookie\CookieProviderInterface
    arguments:
      - '@Profitmetrics\Profitmetrics\Framework\Cookie\CustomCookieProvider.inner'

  # Logging
  profitmetrics.logging:
    class: Monolog\Logger
    arguments:
      - 'Profitmetrics_Profitmetrics'
      - ['@profitmetrics.plugin.rotatingHandler']

  profitmetrics.plugin.rotatingHandler:
    class: Monolog\Handler\RotatingFileHandler
    arguments:
      - '%kernel.logs_dir%/profitmetrics-%kernel.environment%.log'

  Profitmetrics\Profitmetrics\Service\ErrorHandler:
    public: true
    arguments:
      - '@profitmetrics.logging'
      - '@log_entry.repository'
