<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>General</title>
        <title lang="de-DE">Allgemein</title>
        <title lang="da-DK">Generel</title>

        <input-field type="bool">
            <name>active</name>
            <label>Active</label>
            <label lang="de-DE">Aktiviert</label>
            <label lang="da-DK">Aktiveret</label>
            <defaultValue>true</defaultValue>
        </input-field>

        <input-field>
            <name>profitMetricsId</name>
            <required>true</required>
            <label>Public ID</label>
            <label lang="de-DE">Public ID</label>
            <label lang="da-DK">Public ID</label>
            <helpText>Public ID from ProfitMetrics</helpText>
            <helpText lang="de-DE">Public ID aus ProfitMetrics</helpText>
            <helpText lang="da-DK">Public ID fra ProfitMetrics</helpText>
            <placeholder>1A2B3C4D5E6F7G8H</placeholder>
        </input-field>

        <component name="pm-button-api-verify-button">
            <name>ApiVerify</name>
        </component>

        <component name="order-status-multiselect">
            <name>orderStatuses</name>
            <entity>state_machine_state</entity>
            <label>Order status</label>
            <label lang="de-DE">Ordnungszustände</label>
            <label lang="da-DK">Ordretilstande</label>
            <helpText>Choose order states to send to ProfitMetrics. Empty fields will send only completed and paid orders</helpText>
            <helpText lang="de-DE">Wählen Sie Bestellstatus für ProfitMetrics aus. Leere Felder senden nur abgeschlossene und bezahlte Bestellungen</helpText>
            <helpText lang="da-DK">Vælg ordrestatus at sende til ProfitMetrics. Tomme felter sender kun gennemførte og betalte ordrer</helpText>
        </component>

        <input-field type="int">
            <name>maxOrderAge</name>
            <label>Order lookback window (days)</label>
            <label lang="de-DE">Maximales Bestellalter (Tage)</label>
            <label lang="da-DK">Ordre dato maks (dage) gammel</label>
            <helpText>This setting works only with headless mode enabled. Orders older than X days won't be exported</helpText>
            <helpText lang="de-DE">Diese Einstellung funktioniert nur bei aktiviertem Headless-Modus. Bestellungen älter als X Tage werden nicht exportiert</helpText>
            <helpText lang="da-DK">Denne indstilling fungerer kun når headless-tilstand er aktiveret. Ordrer ældre end X dage eksporteres ikke</helpText>
            <defaultValue>7</defaultValue>
            <placeholder>7</placeholder>
        </input-field>

        <input-field type="bool">
            <name>blockBeforeConsent</name>
            <label>Block tracking before consent</label>
            <helpText>Your tracking script (pmTPTrack.js) won't initialize until cookie consent is granted. Compatible with CookieBot, CookieInformation, OneTrust, CookieYes, CookieFirst,
                CookieScript, and Google Consent Mode
            </helpText>
            <defaultValue>false</defaultValue>
        </input-field>

        <component name="pm-button-order-export">
            <name>OrderExport</name>
            <helpText>Your orders sync automatically every 10 minutes</helpText>
            <helpText lang="de-DE">Ihre Bestellungen werden alle 10 Minuten automatisch synchronisiert</helpText>
            <helpText lang="da-DK">Dine ordrer synkroniseres automatisk hvert 10. minut</helpText>
        </component>
    </card>

    <card>
        <title>Tracking</title>
        <title lang="de-DE">Sendungsverfolgung</title>
        <title lang="da-DK">Sporing</title>

        <input-field>
            <name>googleConversionId</name>
            <placeholder>AW-1234567890</placeholder>
            <label>Google Ads Conversion ID</label>
            <label lang="de-DE">Google Ads Conversion ID speichern</label>
            <label lang="da-DK">Store Google Ads-konverterings-id</label>
            <helpText>Google Ads Conversion ID (AW-0000000000)</helpText>
            <helpText lang="de-DE">Google Ads Conversion ID (AW-0000000000)</helpText>
            <helpText lang="da-DK">Google Ads Conversion ID (AW-0000000000)</helpText>
        </input-field>

        <input-field type="textarea">
            <name>conversionBoosterGoogleAds</name>
            <label>Conversion Booster for Google Ads</label>
            <label lang="de-DE">Conversion-Booster für Google Ads-Skript</label>
            <label lang="da-DK">Konverteringsbooster til Google Ads-script</label>
            <helpText><![CDATA[
                Add your script without script tags - it will be wrapped during rendering.<br>
                Assign these required order tracking variables:<br><br>
                var pmOrderValue = ORDER_TOTAL_VALUE;<br>
                var pmOrderCurrency = ORDER_CURRENCY;<br>
                var pmTransactionId = ORDER_ID;<br><br>
                Note: Always use the exact constants ORDER_TOTAL_VALUE, ORDER_CURRENCY, and ORDER_ID.
            ]]></helpText>

            <helpText lang="de-DE"><![CDATA[
                Fügen Sie Ihr Skript ohne Script-Tags ein - es wird beim Rendering eingebunden.<br>
                Weisen Sie diese erforderlichen Bestellverfolgungsvariablen zu:<br><br>
                var pmOrderValue = ORDER_TOTAL_VALUE;<br>
                var pmOrderCurrency = ORDER_CURRENCY;<br>
                var pmTransactionId = ORDER_ID;<br><br>
                Hinweis: Verwenden Sie immer die exakten Konstanten ORDER_TOTAL_VALUE, ORDER_CURRENCY und ORDER_ID.
            ]]></helpText>

            <helpText lang="da-DK"><![CDATA[
                Tilføj dit script uden script-tags - det bliver indkapslet under rendering.<br>
                Tildel disse påkrævede ordresporingvariabler:<br><br>
                var pmOrderValue = ORDER_TOTAL_VALUE;<br>
                var pmOrderCurrency = ORDER_CURRENCY;<br>
                var pmTransactionId = ORDER_ID;<br><br>
                Bemærk: Brug altid de præcise konstanter ORDER_TOTAL_VALUE, ORDER_CURRENCY og ORDER_ID.
            ]]></helpText>
        </input-field>

        <input-field type="textarea">
            <name>allPagesJavascript</name>
            <label>Google Analytics 4 Script</label>
            <label lang="de-DE">Google Analytics 4 Script</label>
            <label lang="da-DK">Google Analytics 4 Script</label>
            <helpText>Include your script WITH script tags - the code won't be modified during rendering</helpText>
            <helpText lang="de-DE">Fügen Sie Ihr Skript mit Script-Tags ein - der Code wird beim Rendering nicht verändert</helpText>
            <helpText lang="da-DK">Inkluder dit script med script-tags - koden ændres ikke under rendering</helpText>
        </input-field>

    </card>

    <card>
        <title>Advanced</title>
        <title lang="de-DE">Fortschrittlich</title>
        <title lang="da-DK">Avanceret</title>

        <input-field type="bool">
            <name>headlessMode</name>
            <label>Activate Headless Mode</label>
            <label lang="de-DE">Headless-Modus aktiviert</label>
            <label lang="da-DK">Aktiveret 'headless' tilstand</label>
            <defaultValue>false</defaultValue>
            <helpText>
                <![CDATA[Contact <a href="mailto:<EMAIL>" target='_blank'><EMAIL></a> to get your tracking script. You'll need to add this script to all pages]]></helpText>

            <helpText lang="de-DE">
                <![CDATA[Kontaktieren Sie <a href="mailto:<EMAIL>" target='_blank'><EMAIL></a> um Ihr Tracking-Script zu erhalten. Sie müssen dieses Script auf allen Seiten einbinden]]></helpText>

            <helpText lang="da-DK">
                <![CDATA[Kontakt <a href="mailto:<EMAIL>" target='_blank'><EMAIL></a> for at få dit tracking-script. Du skal tilføje dette script på alle sider]]></helpText>
        </input-field>

        <input-field type="bool">
            <name>useHashedEmail</name>
            <label>Use hashed email in exports</label>
            <label lang="de-DE">Gehashte E-Mail in Exporten verwenden</label>
            <label lang="da-DK">Brug hashet e-mail i eksporter</label>
            <helpText>When enabled, emails will be hashed using multiple formats (SHA256, MD5) in exports</helpText>
            <helpText lang="de-DE">Wenn aktiviert, werden E-Mails in Exporten mit verschiedenen Formaten (SHA256, MD5) gehasht</helpText>
            <helpText lang="da-DK">Når aktiveret, vil e-mails blive hashet med flere formater (SHA256, MD5) i eksporter</helpText>
            <defaultValue>true</defaultValue>
        </input-field>

        <component name="pm-button-customer-export">
            <name>CustomerExport</name>
        </component>
    </card>
</config>
