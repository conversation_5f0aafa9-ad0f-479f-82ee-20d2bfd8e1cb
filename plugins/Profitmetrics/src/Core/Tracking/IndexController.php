<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Tracking;

use Profitmetrics\Profitmetrics\Storefront\Subscriber\VisitorTrackingSubscriber;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['storefront']])]
/**
 * Controller handling ProfitMetrics tracking endpoints.
 *
 * This controller processes incoming tracking requests and manages the storage
 * of tracking data. It ensures proper validation of incoming data and provides
 * appropriate responses for tracking events.
 */
final class IndexController extends StorefrontController
{
    /**
     * @param VisitorTrackingSubscriber $profitmetricsTracking Service handling tracking data processing
     */
    public function __construct(
        private readonly VisitorTrackingSubscriber $profitmetricsTracking
    ) {
    }

    #[Route(
        path: '/profitmetrics/tracking',
        name: 'frontend.store.profitmetrics.tracking',
        options: ['seo' => false],
        defaults: ['XmlHttpRequest' => true, 'csrf_protected' => false],
        methods: ['POST']
    )]
    /**
     * Processes incoming tracking data and returns the tracking timestamp.
     *
     * This endpoint receives tracking data via POST request, processes it through
     * the tracking service, and returns the timestamp of the tracking event.
     * If no timestamp is available, returns an error response.
     *
     * @param Request $request The incoming request containing tracking data
     *
     * @throws \JsonException
     *
     * @return Response JSON response containing the timestamp or error message
     */
    public function handleTracking(Request $request): Response
    {
        $trackingData = $this->profitmetricsTracking->getTrackingDataFromRequest($request);
        $timestamp = $trackingData['timestamp'] ?? null;

        if ($timestamp === null || $timestamp === '') {
            return new JsonResponse(
                ['error' => 'Invalid tracking data: missing timestamp'],
                Response::HTTP_BAD_REQUEST
            );
        }

        return new JsonResponse(
            ['timestamp' => (string) $timestamp],
            Response::HTTP_OK
        );
    }
}
