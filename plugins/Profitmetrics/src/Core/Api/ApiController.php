<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Api;

use Profitmetrics\Profitmetrics\Core\Service\ProfitMetricsApiClient;
use Profitmetrics\Profitmetrics\Defaults\ConfigDefaults;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;

#[Route(defaults: ['_routeScope' => ['administration']])]
final class ApiController
{
    public function __construct(
        private readonly ProfitMetricsApiClient $profitMetricsClient,
        private readonly ErrorHandler $errorHandler
    ) {
    }

    /**
     * Verifies ProfitMetrics API credentials by checking the provided ID.
     */
    #[Route(
        path: '/api/_action/profitmetrics-api/verify',
        name: 'profitmetrics.action.api.verify',
        defaults: ['_httpCache' => false, 'XmlHttpRequest' => 'true', 'auth_required' => false],
        methods: ['POST']
    )]
    public function verifyCredentials(RequestDataBag $dataBag): JsonResponse
    {
        $profitMetricsId = $dataBag->get(ConfigDefaults::CONFIG_PROFIT_METRICS_ID);

        if (empty($profitMetricsId)) {
            $this->errorHandler->log(
                'ProfitMetrics ID is missing',
                ['profitMetricsId' => null],
                Response::HTTP_BAD_REQUEST
            );

            return $this->createJsonResponse(false, 'ProfitMetrics ID is required', Response::HTTP_BAD_REQUEST);
        }

        try {
            $request = $this->profitMetricsClient->getRequest(['pid' => $profitMetricsId]);

            if (!$request->success) {
                $this->errorHandler->log(
                    'Invalid ProfitMetrics ID',
                    ['profitMetricsId' => $profitMetricsId],
                    Response::HTTP_BAD_REQUEST
                );

                return $this->createJsonResponse(false, 'API verification failed', Response::HTTP_BAD_REQUEST);
            }

            return $this->createJsonResponse(true, 'API connection verified successfully');
        } catch (ExceptionInterface $error) {
            $this->errorHandler->logException(
                'An unknown error occurred while verifying ProfitMetrics API credentials',
                $error
            );

            return $this->createJsonResponse(false, 'API verification failed: ' . $error->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Creates a standardized JSON response with success/failure status and a message.
     */
    private function createJsonResponse(bool $success, string $message, int $statusCode = 200): JsonResponse
    {
        return new JsonResponse([
            'success' => $success,
            'message' => $message,
        ], $statusCode);
    }
}
