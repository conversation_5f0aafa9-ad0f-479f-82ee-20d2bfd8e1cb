<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Api;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Profitmetrics\Profitmetrics\Defaults\CustomFieldDefaults;
use Profitmetrics\Profitmetrics\MessageQueue\Message\OrderExportMessage;
use Profitmetrics\Profitmetrics\Service\OrderExportCriteriaBuilder;
use Shopware\Core\Checkout\Order\OrderCollection;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

/**
 * API endpoint for manually triggering order exports to ProfitMetrics
 */
#[Route(defaults: ['_routeScope' => ['administration']])]
final class OrderExportController
{
    /**
     * @param EntityRepository<OrderCollection> $orderRepository
     */
    public function __construct(
        private OrderExportCriteriaBuilder $orderCriteriaService,
        private EntityRepository $orderRepository,
        private Connection $connection,
        private MessageBusInterface $messageBus
    ) {
    }

    /**
     * Initiates export of eligible orders by marking them for processing and dispatching export messages.
     */
    #[Route(
        path: '/api/_action/profitmetrics-api/order-export',
        name: 'profitmetrics.action.order.export',
        methods: ['POST']
    )]
    public function getOrdersForExport(Context $context): JsonResponse
    {
        try {
            $orderIds = $this->getExportableOrderIds($context);

            if (!empty($orderIds)) {
                $this->markOrdersAsInProgress($orderIds);
                $this->dispatchExportMessages($orderIds);

                return new JsonResponse([
                    'success' => true,
                    'message' => 'Order export initiated successfully.',
                    'total' => \count($orderIds),
                ]);
            }

            return new JsonResponse([
                'success' => true,
                'message' => 'No eligible orders found for export.',
                'total' => 0,
            ]);

        } catch (ExceptionInterface|Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Order export failed: ' . $e->getMessage(),
                'total' => 0,
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @return array<string>
     */
    private function getExportableOrderIds(Context $context): array
    {
        $criteria = $this->orderCriteriaService->createExportCriteria($context, false);

        return $this->orderRepository->search($criteria, $context)->getIds();
    }

    /**
     * @param array<string> $orderIds
     *
     * @throws Exception
     */
    private function markOrdersAsInProgress(array $orderIds): void
    {
        $customFields = json_encode([
            CustomFieldDefaults::PM_ORDER_EXPORT_STATE => CustomFieldDefaults::EXPORT_STATE_IN_PROGRESS,
        ]);

        // Convert hex IDs to bytes
        $binaryOrderIds = array_map(
            fn (string $id) => sprintf('UNHEX(\'%s\')', $id),
            $orderIds
        );

        $this->connection->executeStatement(
            sprintf(
                'UPDATE `order`
                    SET `custom_fields` = JSON_MERGE_PATCH(
                        COALESCE(`custom_fields`, "{}"),
                        :customFields
                    )
                  WHERE `id` IN (%s)',
                implode(',', $binaryOrderIds)
            ),
            ['customFields' => $customFields]
        );
    }

    /**
     * @param array<string> $orderIds
     */
    private function dispatchExportMessages(array $orderIds): void
    {
        foreach ($orderIds as $orderId) {
            $this->messageBus->dispatch(new OrderExportMessage($orderId));
        }
    }
}
