<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Api;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Core\Checkout\Order\Aggregate\OrderCustomer\OrderCustomerDefinition;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Controller for exporting customer data to a CSV file.
 */
#[Route(defaults: ['_routeScope' => ['administration']])]
final class CustomerExportController
{
    /**
     * @var string
     */
    private const DATE_FORMAT = 'Y-m-d\TH:i:s\Z';

    /**
     * @var string
     */
    private const FILENAME = 'customers.csv';

    /**
     * @var int
     */
    private const CHUNK_SIZE = 1000;

    public function __construct(
        private readonly Connection $connection,
        private readonly ErrorHandler $errorHandler,
        private readonly SystemConfigService $systemConfigService
    ) {
    }

    /**
     * Handles the export of customers to a CSV file.
     */
    #[Route(
        path: '/api/_action/profitmetrics-api/customer-export',
        name: 'profitmetrics.action.customer.export',
        methods: ['GET']
    )]
    public function exportCustomers(): Response
    {
        try {
            // Return the CSV as a streamed response:
            return new StreamedResponse(
                callback: $this->generateCustomerExport(...),
                status: Response::HTTP_OK,
                headers: [
                    'Content-Type' => 'text/csv; charset=UTF-8',
                    'Content-Disposition' => sprintf('attachment; filename="%s"', self::FILENAME),
                ]
            );

        } catch (\Exception $e) {
            // If we catch an error before streaming CSV, return JSON to indicate failure
            $this->errorHandler->log(
                'Error during customer export',
                ['error' => $e->getMessage()],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );

            return new JsonResponse([
                'success' => false,
                'message' => 'Customer export failed: ' . $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generates the customer export CSV by writing headers and customer data in chunks.
     *
     * @throws Exception When database operations fail
     * @throws \RuntimeException When output stream operations fail
     */
    private function generateCustomerExport(): void
    {
        $output = $this->openOutputStream();

        try {
            $this->writeCustomerData($output);
        } finally {
            fclose($output);
        }
    }

    /**
     * Opens the output stream for writing the CSV.
     *
     * @return resource
     *
     * @throws \RuntimeException
     */
    private function openOutputStream()
    {
        $output = fopen('php://output', 'wb');
        if ($output === false) {
            throw new \RuntimeException('Failed to open output stream for customer export');
        }

        return $output;
    }

    /**
     * Writes customer data to the CSV in chunks.
     *
     * @param resource $output
     *
     * @throws Exception
     */
    private function writeCustomerData($output): void
    {
        // Write CSV headers first:
        if (fputcsv($output, $this->getCsvHeaders()) === false) {
            throw new \RuntimeException('Failed to write CSV headers');
        }

        $offset = 0;
        while ($customers = $this->fetchCustomerDataChunk($offset)) {
            foreach ($customers as $customer) {
                try {
                    if (fputcsv($output, $this->mapCustomerData($customer)) === false) {
                        $this->errorHandler->log(
                            'Failed to write customer data to CSV',
                            ['email_hash' => $this->hashEmail($customer['email'])],
                            Response::HTTP_INTERNAL_SERVER_ERROR
                        );
                    }
                } catch (\Throwable $e) {
                    $this->errorHandler->log(
                        'Error processing customer data',
                        [
                            'error' => $e->getMessage(),
                            'email_hash' => $this->hashEmail($customer['email']),
                        ],
                        Response::HTTP_INTERNAL_SERVER_ERROR
                    );
                    // Skip this row, but continue exporting the rest
                    continue;
                }
            }
            $offset += self::CHUNK_SIZE;
        }
    }

    /**
     * Fetches a paginated chunk of customer data from the database.
     *
     * @param int $offset The starting offset for pagination
     *
     * @return array<int, array<string, string>> Array of customer data
     *
     * @throws Exception
     */
    private function fetchCustomerDataChunk(int $offset): array
    {
        return $this->connection->createQueryBuilder()
            ->select('DISTINCT email, MIN(created_at) as created_at')
            ->from(OrderCustomerDefinition::ENTITY_NAME)
            ->groupBy('email')
            ->orderBy('created_at', 'ASC')
            ->setMaxResults(self::CHUNK_SIZE)
            ->setFirstResult($offset)
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * Returns the appropriate CSV headers (hashed vs. plain email).
     */
    private function getCsvHeaders(): array
    {
        // Only include "email" if useHashedEmail is false; otherwise export the hashed fields
        if (!$this->systemConfigService->get('Profitmetrics.config.useHashedEmail')) {
            return ['email', 'firstOrderTimestamp'];
        }

        return ['emailSha256', 'emailSha256GA', 'emailMd5', 'firstOrderTimestamp'];
    }

    /**
     * Maps a single customer's data into CSV row format.
     */
    private function mapCustomerData(array $customer): array
    {
        $email = trim($customer['email']);

        if ($this->systemConfigService->get('Profitmetrics.config.useHashedEmail')) {
            $data = [
                $this->hashEmail($email),
                $this->hashEmailGA($email),
                $this->hashEmailMd5($email),
            ];
        } else {
            $data = [$email];
        }

        // The first order timestamp is always appended:
        $data[] = $this->formatTimestamp($customer['created_at']);
        return $data;
    }

    private function hashEmail(string $email): string
    {
        return hash('sha256', strtolower(trim($email)));
    }

    private function hashEmailGA(string $email): string
    {
        return hash('sha256', $email);
    }

    private function hashEmailMd5(string $email): string
    {
        return md5(strtolower(trim($email)));
    }

    /**
     * Formats a timestamp to the specified date format.
     */
    private function formatTimestamp(string $timestamp): string
    {
        $dateTime = new \DateTimeImmutable($timestamp);
        return $dateTime->format(self::DATE_FORMAT);
    }
}
