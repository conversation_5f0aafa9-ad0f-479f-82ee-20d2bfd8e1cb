<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Service;

use Monolog\Level;
use Profitmetrics\Profitmetrics\DTO\ApiResponse;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class ProfitMetricsApiClient
{
    private const API_URL = 'https://my.profitmetrics.io/l.php';
    private const INVALID_RESPONSES = ['//pidnofound', '//pidnull', '//unknown'];

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly ErrorHandler $errorHandler,
    ) {
    }

    /**
     * Sends a GET request to the ProfitMetrics API and returns the response.
     *
     * @param array<string, mixed> $queryParams The query parameters to send
     *
     * @throws ExceptionInterface When a critical error occurs
     */
    public function getRequest(array $queryParams): ApiResponse
    {
        try {
            $response = $this->httpClient->request('GET', self::API_URL, [
                'query' => $queryParams,
                'timeout' => 10,
                'max_duration' => 10,
            ]);

            $content = trim($response->getContent());
            $statusCode = $response->getStatusCode();

            return new ApiResponse(
                content: $content,
                statusCode: $statusCode,
                success: !\in_array($content, self::INVALID_RESPONSES, true)
            );
        } catch (ExceptionInterface $e) {
            $this->errorHandler->log('API request failed', [
                'query' => $queryParams,
                'exception' => $e->getMessage(),
            ], Level::Error->value);

            throw $e;
        }
    }
}
