<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Service;

use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Order\Aggregate\OrderLineItem\OrderLineItemEntity;

/**
 * Service responsible for transforming individual order line items.
 */
class LineItemTransformer
{
    /**
     * Transforms a single OrderLineItemEntity into the required array format.
     *
     * @return array<string, mixed>
     */
    public function transform(OrderLineItemEntity $item): array
    {
        $price = $this->calculatePrice($item);
        $transformedData = [
            'qty' => $item->getQuantity(),
            'priceExVat' => (float) sprintf('%.2f', $price),
        ];

        if ($item->getType() === LineItem::PRODUCT_LINE_ITEM_TYPE) {
            $product = $item->getProduct();
            $transformedData['sku'] = $product !== null ? $product->getProductNumber() : $item->getLabel();
        } elseif ($item->getType() === LineItem::CUSTOM_LINE_ITEM_TYPE) {
            $transformedData['sku'] = $item->getLabel();
        }

        return $transformedData;
    }

    /**
     * Calculates the price excluding VAT for a line item.
     */
    private function calculatePrice(OrderLineItemEntity $item): float
    {
        $price = 0.0;
        $priceInfo = $item->getPrice();

        if ($priceInfo === null) {
            return $price;
        }

        $price = $priceInfo->getUnitPrice();
        $tax = $priceInfo->getCalculatedTaxes()->first();
        if ($tax !== null) {
            $quantity = $item->getQuantity() ?: 1;
            $price = ($tax->getPrice() - $tax->getTax()) / $quantity;
        }

        return $price;
    }
}
