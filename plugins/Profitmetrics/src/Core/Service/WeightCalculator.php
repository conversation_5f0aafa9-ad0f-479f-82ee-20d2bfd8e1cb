<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Service;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Order\Aggregate\OrderLineItem\OrderLineItemEntity;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Content\Product\ProductDefinition;
use Shopware\Core\Framework\Uuid\Uuid;

/**
 * Service responsible for calculating the weight of order items.
 */
class WeightCalculator
{
    private Connection $connection;

    private ErrorHandler $errorHandler;

    public function __construct(
        Connection $connection,
        ErrorHandler $errorHandler
    ) {
        $this->connection = $connection;
        $this->errorHandler = $errorHandler;
    }

    /**
     * Calculates the total weight for the given order.
     *
     * @return float Total weight in grams.
     */
    public function calculateTotalWeight(OrderEntity $order): float
    {
        $totalWeight = 0.0;

        /** @var OrderLineItemEntity $item */
        foreach ($order->getLineItems()->filterByType(LineItem::PRODUCT_LINE_ITEM_TYPE) as $item) {
            $totalWeight += $this->calculateItemWeight($item);
        }

        return $totalWeight;
    }

    /**
     * Calculates the weight for a single order line item.
     *
     * @return float Weight in grams.
     */
    private function calculateItemWeight(OrderLineItemEntity $item): float
    {
        $weight = 0.0;
        $product = $item->getProduct();

        if ($product) {
            $itemWeight = $product->getWeight();
            if ($itemWeight === null && $product->getParentId()) {
                $itemWeight = $this->fetchParentWeight($product->getParentId());
            }

            if ($itemWeight !== null) {
                $weight += ($itemWeight * 1000) * $item->getQuantity();
            }
        }

        return $weight;
    }

    /**
     * Fetches the weight of the parent product from the database.
     */
    private function fetchParentWeight(string $parentId): float
    {
        try {
            return $this->executeWeightQuery($parentId);
        } catch (\Exception $exception) {
            $this->errorHandler->logException('Failed to fetch parent product weight', $exception, [
                'parentId' => $parentId,
            ]);

            return 0.0;
        }
    }

    /**
     * Executes the database query to fetch product weight.
     *
     * @throws Exception
     */
    private function executeWeightQuery(string $parentId): float
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('COALESCE(weight, 0.0) as weight')
            ->from(ProductDefinition::ENTITY_NAME)
            ->where('id = :id')
            ->setParameter('id', Uuid::fromHexToBytes($parentId));

        /** @var string|false $result */
        $result = $queryBuilder->executeQuery()->fetchOne();

        return $result !== false ? (float) $result : 0.0;
    }
}
