<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Profitmetrics;

use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;

/**
 * @phpstan-property-read string|null $token
 * @phpstan-property-read string|null $orderId
 * @phpstan-property-read string|null $orderVersionId
 */
final class VisitorEntity extends Entity
{
    use EntityIdTrait;

    protected ?string $token = null;

    protected ?string $orderId = null;

    protected ?string $orderVersionId = null;

    protected ?string $gacid = null;

    protected ?string $gacidSource = null;

    protected ?string $gclid = null;

    protected ?string $fbp = null;

    protected ?string $fbc = null;

    protected ?string $cua = null;

    protected ?string $cip = null;

    protected ?string $ga4SessionId = null;

    protected ?string $ga4SessionCount = null;

    protected ?string $thrackspec = null;

    protected ?int $timestamp = null;

    protected ?int $ccStatistics = null;

    protected ?int $ccMarketing = null;

    public function setGacid(?string $value): void
    {
        $this->gacid = $value;
    }

    public function getGacid(): ?string
    {
        return $this->gacid;
    }

    public function setGacidSource(?string $value): void
    {
        $this->gacidSource = $value;
    }

    public function getGacidSource(): ?string
    {
        return $this->gacidSource;
    }

    public function setGclid(?string $value): void
    {
        $this->gclid = $value;
    }

    public function getGclid(): ?string
    {
        return $this->gclid;
    }

    public function setFbp(string $value): void
    {
        $this->fbp = $value;
    }

    public function getFbp(): ?string
    {
        return $this->fbp;
    }

    public function setFbc(?string $value): void
    {
        $this->fbc = $value;
    }

    public function getFbc(): ?string
    {
        return $this->fbc;
    }

    public function setCua(?string $value): void
    {
        $this->cua = $value;
    }

    public function getCua(): ?string
    {
        return $this->cua;
    }

    public function setCip(?string $value): void
    {
        $this->cip = $value;
    }

    public function getCip(): ?string
    {
        return $this->cip;
    }

    public function setThrackspec(?string $value): void
    {
        $this->thrackspec = $value;
    }

    public function getThrackspec(): ?string
    {
        return $this->thrackspec;
    }

    public function setTimestamp(?int $value): void
    {
        $this->timestamp = $value;
    }

    public function getTimestamp(): ?int
    {
        return $this->timestamp;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(?string $token): void
    {
        $this->token = $token;
    }

    public function getGa4SessionId(): ?string
    {
        return $this->ga4SessionId;
    }

    public function setGa4SessionId(?string $ga4SessionId): void
    {
        $this->ga4SessionId = $ga4SessionId;
    }

    public function getGa4SessionCount(): ?string
    {
        return $this->ga4SessionCount;
    }

    public function setGa4SessionCount(?string $ga4SessionCount): void
    {
        $this->ga4SessionCount = $ga4SessionCount;
    }

    public function getOrderId(): ?string
    {
        return $this->orderId;
    }

    public function setOrderId(?string $orderId): void
    {
        $this->orderId = $orderId;
    }

    public function getOrderVersionId(): ?string
    {
        return $this->orderVersionId;
    }

    public function setOrderVersionId(?string $orderVersionId): void
    {
        $this->orderVersionId = $orderVersionId;
    }

    public function setCcMarketing(?int $value): void
    {
        $this->ccMarketing = $value;
    }

    public function getCcMarketing(): ?int
    {
        return $this->ccMarketing;
    }

    public function setCcStatistics(?int $value): void
    {
        $this->ccStatistics = $value;
    }

    public function getCcStatistics(): ?int
    {
        return $this->ccStatistics;
    }
}
