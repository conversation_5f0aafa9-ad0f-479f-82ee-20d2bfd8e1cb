<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Profitmetrics;

use Shopware\Core\Checkout\Order\OrderDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Field;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IntField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\OneToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ReferenceVersionField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class VisitorDefinition extends EntityDefinition
{
    /**
     * @var string
     */
    final public const ENTITY_NAME = 'profitmetrics_visitor';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getEntityClass(): string
    {
        return VisitorEntity::class;
    }

    public function getCollectionClass(): string
    {
        return VisitorCollection::class;
    }

    /**
     * @return FieldCollection<Field>
     */
    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new IdField('id', 'id'))->addFlags(new Required(), new PrimaryKey()),
            new StringField('token', 'token', 255),
            new FkField('order_id', 'orderId', OrderDefinition::class),
            new ReferenceVersionField(OrderDefinition::class, 'order_version_id'),
            new StringField('gacid', 'gacid', 255),
            new StringField('gacid_source', 'gacidSource', 255),
            new StringField('gclid', 'gclid', 255),
            new StringField('fbp', 'fbp', 255),
            new StringField('fbc', 'fbc', 255),
            new StringField('cua', 'cua', 500),
            new StringField('cip', 'cip', 40),
            new StringField('ga4_session_id', 'ga4SessionId', 255),
            new StringField('ga4_session_count', 'ga4SessionCount', 255),
            new IntField('cc_statistics', 'ccStatistics', null),
            new IntField('cc_marketing', 'ccMarketing', null),
            new StringField('thrackspec', 'thrackspec', 255),
            new IntField('timestamp', 'timestamp', null, null),

            new OneToOneAssociationField(
                'order',
                'order_id',
                'id',
                OrderDefinition::class,
                false
            ),
        ]);
    }
}
