<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Core\Profitmetrics;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void               add(VisitorEntity $entity)
 * @method void               set(string $key, VisitorEntity $entity)
 * @method VisitorEntity[]    getIterator()
 * @method VisitorEntity[]    getElements()
 * @method VisitorEntity|null get(string $key)
 * @method VisitorEntity|null first()
 * @method VisitorEntity|null last()
 */
/**
 * @extends EntityCollection<VisitorEntity>
 */
final class VisitorCollection extends EntityCollection
{
    public function getExpectedClass(): string
    {
        return VisitorEntity::class;
    }
}
