<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Framework\Cookie;

use Profitmetrics\Profitmetrics\Defaults\TrackingDefaults;
use Shopware\Storefront\Framework\Cookie\CookieProviderInterface;

/**
 * Provides custom cookie configuration for ProfitMetrics tracking.
 */
final class CustomCookieProvider implements CookieProviderInterface
{
    private const MARKETING_GROUP_IDENTIFIER = 'marketing';
    private const COOKIE_VALUE = '1';
    private const COOKIE_SNIPPET_NAME = 'ProfitMetrics.cookie.TPTrack.name';
    private const GROUP_SNIPPET_NAME = 'cookie.groupMarketing';
    private const GROUP_SNIPPET_DESCRIPTION = 'cookie.groupMarketingDescription';

    private const PROFITMETRICS_COOKIE = [
        'snippet_name' => self::COOKIE_SNIPPET_NAME,
        'cookie' => TrackingDefaults::PROFITMETRICS_COOKIE_TRACKING_DATA,
        'value' => self::COOKIE_VALUE,
    ];

    private const DEFAULT_MARKETING_GROUP = [
        'snippet_name' => self::GROUP_SNIPPET_NAME,
        'snippet_description' => self::GROUP_SNIPPET_DESCRIPTION,
        'entries' => [],
    ];

    public function __construct(
        private readonly CookieProviderInterface $originalProvider
    ) {
    }

    /**
     * Retrieves the cookie groups, adding the ProfitMetrics cookie to the marketing group.
     */
    /**
     * @return array<int, array<string, mixed>>
     */
    public function getCookieGroups(): array
    {
        /** @var array<int, array<string, mixed>> $cookieGroups */
        $cookieGroups = array_values($this->originalProvider->getCookieGroups());

        $marketingGroup = $this->findMarketingGroup($cookieGroups);

        if ($marketingGroup !== null) {
            return $this->addCookieToExistingGroup($cookieGroups, $marketingGroup);
        }

        return $this->createNewMarketingGroup($cookieGroups);
    }

    /**
     * Finds the marketing group within the cookie groups.
     *
     * @param array<int, array<string, mixed>> $cookieGroups
     */
    /**
     * @param array<int, array<string, mixed>> $cookieGroups
     */
    private function findMarketingGroup(array $cookieGroups): ?int
    {
        foreach ($cookieGroups as $index => $group) {
            if (!isset($group['snippet_name'])) {
                continue;
            }

            if (!\is_string($group['snippet_name']) || $group['snippet_name'] === '') {
                continue;
            }

            if (str_contains(strtolower($group['snippet_name']), self::MARKETING_GROUP_IDENTIFIER)) {
                return $index;
            }
        }

        return null;
    }

    /**
     * Adds the ProfitMetrics cookie to an existing marketing group.
     *
     * @param array<int, array<string, mixed>> $cookieGroups
     *
     * @return array<int, array<string, mixed>>
     */
    private function addCookieToExistingGroup(array $cookieGroups, int $groupIndex): array
    {
        if (!isset($cookieGroups[$groupIndex]['entries'])) {
            $cookieGroups[$groupIndex]['entries'] = [];
        }

        // Check if cookie already exists
        $entries = &$cookieGroups[$groupIndex]['entries'];
        if (!\is_array($entries)) {
            $entries = [];
        }

        foreach ($entries as $entry) {
            if (isset($entry['cookie']) && $entry['cookie'] === TrackingDefaults::PROFITMETRICS_COOKIE_TRACKING_DATA) {
                return $cookieGroups; // Cookie already exists, return unchanged
            }
        }

        $cookieGroups[$groupIndex]['entries'][] = self::PROFITMETRICS_COOKIE;

        return $cookieGroups;
    }

    /**
     * Creates a new marketing group and adds the ProfitMetrics cookie to it.
     *
     * @param array<int, array<string, mixed>> $cookieGroups
     *
     * @return array<int, array<string, mixed>>
     */
    private function createNewMarketingGroup(array $cookieGroups): array
    {
        $marketingGroup = self::DEFAULT_MARKETING_GROUP;
        $marketingGroup['entries'] = [self::PROFITMETRICS_COOKIE];

        // Ensure we don't duplicate groups if marketing group exists with different case
        foreach ($cookieGroups as $group) {
            if (isset($group['snippet_name'])
                && strtolower((string) $group['snippet_name']) === strtolower(self::GROUP_SNIPPET_NAME)) {
                $index = array_search($group, $cookieGroups, true);
                if ($index !== false) {
                    return $this->addCookieToExistingGroup($cookieGroups, $index);
                }
            }
        }

        return array_merge($cookieGroups, [$marketingGroup]);
    }
}
