<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Storefront\Subscriber;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Profitmetrics\Profitmetrics\Core\Profitmetrics\VisitorDefinition;
use Profitmetrics\Profitmetrics\Defaults\ConfigDefaults;
use Profitmetrics\Profitmetrics\Defaults\CustomFieldDefaults;
use Profitmetrics\Profitmetrics\Defaults\TrackingDefaults;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\PlatformRequest;
use Shopware\Core\System\SalesChannel\Event\SalesChannelContextTokenChangeEvent;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\KernelEvents;

final class VisitorTrackingSubscriber implements EventSubscriberInterface
{
    private const BOT_PATTERNS = '/bot|spider|crawler|symfony/';
    private const TRACKING_MAX_LENGTH = 100;
    private const UTM_PARAMETERS = ['utm_source', 'utm_campaign', 'utm_medium'];

    private SystemConfigService $systemConfigService;

    private Connection $connection;

    private ErrorHandler $errorHandler;

    public function __construct(
        SystemConfigService $systemConfigService,
        Connection $connection,
        ErrorHandler $errorHandler
    ) {
        $this->systemConfigService = $systemConfigService;
        $this->connection = $connection;
        $this->errorHandler = $errorHandler;
    }

    /**
     * Returns subscribed events for visitor tracking and context token changes
     *
     * @return array<string, array<int, int|string>|string>
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::CONTROLLER => ['trackVisitor', -15],
            SalesChannelContextTokenChangeEvent::class => 'contextTokenChanged',
        ];
    }

    /**
     * Tracks visitor data on controller events
     *
     * Updates visitor tracking information based on request data
     * and sales channel context
     */
    public function trackVisitor(ControllerEvent $event): void
    {
        $request = $event->getRequest();

        if (!$this->isValidRequest($request)) {
            return;
        }

        $context = $request->attributes->get(PlatformRequest::ATTRIBUTE_SALES_CHANNEL_CONTEXT_OBJECT);
        $salesChannelId = $request->attributes->getAlnum(PlatformRequest::ATTRIBUTE_SALES_CHANNEL_ID);

        if (!$this->isTrackingEnabled($salesChannelId)) {
            return;
        }

        try {
            $this->updateVisitorWithToken($request, $context);
            $trackingData = $this->getTrackingDataFromRequest($request);
            $this->upsertVisitor($trackingData, $context->getToken(), $context);
        } catch (\Exception $e) {
            $this->errorHandler->logException(
                'Error during visitor tracking',
                $e,
                ['request' => $request->getRequestUri()]
            )->addToLogEntry($context);
        }
    }

    /**
     * Updates visitor records when the sales channel context token changes
     *
     * @param SalesChannelContextTokenChangeEvent $event The token change event
     */
    public function contextTokenChanged(SalesChannelContextTokenChangeEvent $event): void
    {
        try {
            $this->connection->executeStatement(
                'UPDATE profitmetrics_visitor SET token = :newToken
                WHERE token = :oldToken AND order_id IS NULL',
                [
                    'newToken' => $event->getCurrentToken(),
                    'oldToken' => $event->getPreviousToken(),
                ]
            );
        } catch (\Exception $e) {
            $this->errorHandler->logException(
                'Error updating visitor token',
                $e,
                [
                    'newToken' => $event->getCurrentToken(),
                    'oldToken' => $event->getPreviousToken(),
                ]
            )->addToLogEntry($event->getContext());
        }
    }

    /**
     * Extracts tracking data from request cookies
     *
     * @param Request $request The HTTP request
     *
     * @throws \JsonException
     *
     * @return array<string, mixed> Sanitized tracking data
     */
    public function getTrackingDataFromRequest(Request $request): array
    {
        $cookieData = $this->sanitizeJson(
            (string) ($request->cookies->get(TrackingDefaults::PROFITMETRICS_COOKIE_TRACKING_DATA) ?? '')
        );

        return $this->buildTrackingData($request, $cookieData);
    }

    /**
     * Builds complete tracking dataset from request and cookie data
     *
     * @param Request $request The HTTP request
     * @param array<string, mixed> $cookieData Existing cookie data
     *
     * @throws \JsonException
     *
     * @return array<string, mixed> Combined tracking data
     */
    private function buildTrackingData(Request $request, array $cookieData): array
    {
        $currentTimestamp = time();
        $timestamp = $cookieData['timestamp'] ?? $currentTimestamp;
        $trackingData = [];

        // Process tracking IDs using arrow functions
        $processTrackingId = fn ($value): string => substr(
            htmlspecialchars((string) $value, \ENT_QUOTES, 'UTF-8'),
            0,
            self::TRACKING_MAX_LENGTH
        );

        // Process click IDs
        $gclid = $request->query->get('gclid');
        $fbclid = $request->query->get('fbclid');
        $fbp = $request->query->get('fbp');

        if ($gclid) {
            $trackingData['gclid'] = $processTrackingId($gclid);
            $timestamp = $currentTimestamp;
        } elseif (isset($cookieData['gclid'])) {
            $trackingData['gclid'] = $processTrackingId($cookieData['gclid']);
        }

        if ($fbp) {
            $trackingData['fbp'] = $processTrackingId($fbp);
            $timestamp = $currentTimestamp;
        } elseif (isset($cookieData['fbp'])) {
            $trackingData['fbp'] = $processTrackingId($cookieData['fbp']);
        }

        if ($fbclid) {
            $trackingData['fbc'] = $processTrackingId($fbclid);
            $timestamp = $currentTimestamp;
        } elseif (isset($cookieData['fbc'])) {
            $trackingData['fbc'] = $processTrackingId($cookieData['fbc']);
        }

        $visitorData = $this->getVisitorData($request, $cookieData);
        if (!empty($visitorData)) {
            $trackingData['thrackspec'] = $visitorData;
            $timestamp = $currentTimestamp;
        }

        // Additional tracking data with sanitization
        $trackingData['cua'] = $this->sanitizeUserAgent($request->headers->get('User-Agent', ''));
        $trackingData['cip'] = filter_var($request->getClientIp(), \FILTER_VALIDATE_IP);
        $trackingData['cc_statistics'] = $cookieData['cc_statistics'] ?? null;
        $trackingData['cc_marketing'] = $cookieData['cc_marketing'] ?? null;

        $gaData = $this->extractGaDataFromCookies($request);
        $trackingData = array_merge($trackingData, $gaData);

        $trackingData['timestamp'] = $timestamp;

        return array_filter($trackingData);
    }

    /**
     * Extracts Google Analytics data from cookies
     *
     * @param Request $request The HTTP request
     *
     * @return array<string, string|null> GA4 session data
     */
    private function extractGaDataFromCookies(Request $request): array
    {
        $cookies = $request->cookies->all();

        if (!isset($cookies['pmTPTrack'])) {
            return [];
        }

        $payload = json_decode($cookies['pmTPTrack'], true) ?: [];
        $result = [];

        if (!empty($payload['gacid'])) {
            $result['gacid'] = $payload['gacid'];
        }

        if (!empty($payload['gacidSource'])) {
            $result['gacid_source'] = $payload['gacidSource'];
        }

        if (!empty($payload['ga4SessionId'])) {
            $result['ga4_session_id'] = $payload['ga4SessionId'];
        }

        if (!empty($payload['ga4SessionCount'])) {
            $result['ga4_session_count'] = $payload['ga4SessionCount'];
        }

        return $result;
    }



    /**
     * Extracts UTM tracking parameters from query parameters
     *
     * @param array<string|int, string|array<string, string>> $queryParams Request query parameters
     *
     * @return array<string, string> Sanitized UTM parameters
     */
    private function extractUtmParameters(array $queryParams): array
    {
        return array_reduce(
            self::UTM_PARAMETERS,
            function (array $utmParams, string $key) use ($queryParams): array {
                if (isset($queryParams[$key]) && (\is_string($queryParams[$key]) || is_numeric($queryParams[$key]))) {
                    $utmParams[$key] = substr(
                        htmlspecialchars((string) $queryParams[$key], \ENT_QUOTES, 'UTF-8'),
                        0,
                        self::TRACKING_MAX_LENGTH
                    );
                }

                return $utmParams;
            },
            []
        );
    }

    /**
     * Retrieves visitor tracking data from request and referrer
     *
     * @param Request $request The HTTP request
     * @param array<string, mixed> $cookieData Existing cookie data
     *
     * @throws \JsonException
     *
     * @return string JSON encoded visitor data
     */
    private function getVisitorData(Request $request, array $cookieData): string
    {
        $queryParams = $request->query->all();
        $utmParams = $this->extractUtmParameters($queryParams);

        if (empty($utmParams)) {
            $referer = $request->headers->get('referer');
            if ($referer) {
                $queryString = (string) (parse_url($referer, \PHP_URL_QUERY) ?? '');
                parse_str($queryString, $queryParams);
                /** @var array<string|int, string|array<string, string>> $queryParams */
                $utmParams = array_merge($utmParams, $this->extractUtmParameters($queryParams));
            }
        }

        return !empty($utmParams) ? json_encode($utmParams, \JSON_THROW_ON_ERROR) : '';
    }

    /**
     * Validates if request should be tracked
     *
     * Checks IP exclusions, sales channel context, and bot patterns
     */
    private function isValidRequest(Request $request): bool
    {
        if (!$request->attributes->has(PlatformRequest::ATTRIBUTE_SALES_CHANNEL_ID)) {
            return false;
        }

        $userAgent = strtolower($request->headers->get('User-Agent', ''));
        if (empty($userAgent) || preg_match(self::BOT_PATTERNS, $userAgent)) {
            return false;
        }

        $routeScope = $request->attributes->get('_routeScope', []);

        return \in_array('storefront', $routeScope, true);
    }

    /**
     * Checks if tracking is enabled for sales channel
     *
     * Uses cached config values for performance
     */
    private function isTrackingEnabled(string $salesChannelId): bool
    {
        // Try to get sales channel-specific config
        return $this->systemConfigService->get(ConfigDefaults::CONFIG_ACTIVE, $salesChannelId);
    }

    /**
     * Creates or updates visitor tracking record
     *
     * @param array<string, mixed> $trackingData Visitor tracking data
     * @param string $token Context token
     */
    private function upsertVisitor(array $trackingData, string $token, SalesChannelContext $context): void
    {
        try {
            $existingVisitor = $this->connection->fetchAssociative(
                'SELECT * FROM profitmetrics_visitor WHERE token = :token AND order_id IS NULL',
                ['token' => $token]
            );

            if ($existingVisitor) {
                $this->connection->update(
                    VisitorDefinition::ENTITY_NAME,
                    $trackingData,
                    ['token' => $token, 'order_id' => null]
                );
            } else {
                $trackingData['id'] = Uuid::randomBytes();
                $trackingData['token'] = $context->getToken();
                $trackingData['created_at'] = (new \DateTime())->format(Defaults::STORAGE_DATE_TIME_FORMAT);

                $this->connection->insert(
                    VisitorDefinition::ENTITY_NAME,
                    array_merge($trackingData, ['token' => $token])
                );
            }
        } catch (DBALException|\Exception $e) {
            $this->errorHandler->logException(
                'Error during visitor upsert',
                $e,
                [
                    'token' => $token,
                    'trackingData' => $trackingData,
                ]
            )->addToLogEntry($context->getContext());
        }
    }

    /**
     * Updates existing visitor record with new context token
     *
     * @param Request $request The HTTP request
     * @param SalesChannelContext $context Sales channel context
     */
    private function updateVisitorWithToken(Request $request, SalesChannelContext $context): void
    {
        $visitorId = $request->getSession()->get(CustomFieldDefaults::PM_ORDER_VISITOR_ID);

        if (!$visitorId) {
            return;
        }

        try {
            $this->connection->executeStatement(
                'UPDATE profitmetrics_visitor
                SET token = :token
                WHERE id = :visitorId
                AND token != :token
                AND order_id IS NULL',
                [
                    'token' => $context->getToken(),
                    'visitorId' => $visitorId,
                ]
            );
        } catch (\Exception $e) {
            $this->errorHandler->logException(
                'Error updating visitor token (deprecated method)',
                $e,
                [
                    'visitorId' => $visitorId,
                    'token' => $context->getToken(),
                ]
            )->addToLogEntry($context->getContext());
        }
    }

    /**
     * Safely decodes JSON string to array
     *
     * @param string $json JSON string to decode
     *
     * @return array<string, mixed> Decoded data or empty array
     */
    private function sanitizeJson(string $json): array
    {
        try {
            $data = json_decode($json, true, 512, \JSON_THROW_ON_ERROR);

            return \is_array($data) ? $data : [];
        } catch (\JsonException $error) {
            return [];
        }
    }

    /**
     * Sanitizes user agent string
     *
     * Removes special characters and limits length
     */
    private function sanitizeUserAgent(string $userAgent): string
    {
        return substr(
            preg_replace('/[^\w\-.()\/\s]/u', '', $userAgent),
            0,
            self::TRACKING_MAX_LENGTH
        );
    }
}
