<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Service;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Profitmetrics\Profitmetrics\Defaults\CustomFieldDefaults as CF;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\System\CustomField\Aggregate\CustomFieldSet\CustomFieldSetCollection;
use Shopware\Core\System\CustomField\Aggregate\CustomFieldSet\CustomFieldSetEntity;
use Shopware\Core\System\CustomField\CustomFieldTypes;

/**
 * Service for managing custom fields in the Shopware system.
 *
 * This service handles the creation, management, and cleanup of custom fields
 * used for tracking ProfitMetrics-specific data in products and orders.
 */
class CustomFieldService
{
    /**
     * Create custom fields for products and orders.
     *
     * Sets up all required custom fields for product pricing and order tracking.
     * Creates field sets if they don't exist and adds individual fields with proper labels.
     *
     * @param Context $context Shopware context for database operations
     *
     * @throws \RuntimeException If custom field creation fails
     */
    private const COMMON_LABELS = [
        'en-GB' => 'Profitmetrics',
        'da-DK' => 'Profitmetrics',
        'de-DE' => 'Profitmetrics',
    ];

    /**
     * Initialize custom field service with required dependencies.
     *
     * @param EntityRepository<CustomFieldSetCollection> $customFieldSetRepository Repository for custom field sets
     * @param EntityRepository<EntityCollection<Entity>> $customFieldRepository Repository for custom fields
     * @param Connection $connection Database connection for direct queries
     */
    public function __construct(
        private readonly EntityRepository $customFieldSetRepository,
        private readonly EntityRepository $customFieldRepository,
        private readonly Connection $connection,
    ) {
    }

    /**
     * Add a custom field set if it doesn't exist.
     *
     * Creates a new custom field set with given name and labels,
     * or returns existing one if already present.
     *
     * @param string $name Technical name of the field set
     * @param array<string, string> $labels Display labels in different languages
     * @param string $entity Entity type this field set belongs to
     * @param Context $context Shopware context for database operations
     *
     * @throws \RuntimeException If field set creation fails
     *
     * @return CustomFieldSetEntity The created or existing field set
     */
    public function addCustomFieldSet(
        string $name,
        array $labels,
        string $entity,
        Context $context
    ): CustomFieldSetEntity {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('name', $name));

        /** @var CustomFieldSetEntity|null $customFieldSet */
        $customFieldSet = $this->customFieldSetRepository->search($criteria, $context)->first();
        if (!$customFieldSet) {
            $this->customFieldSetRepository->create(
                [
                    [
                        'name' => $name,
                        'config' => [
                            'label' => $labels,
                        ],
                        'relations' => [
                            [
                                'entityName' => $entity,
                            ],
                        ],
                    ],
                ],
                $context
            );

            /** @var CustomFieldSetEntity|null $customFieldSet */
            $customFieldSet = $this->customFieldSetRepository->search($criteria, $context)->first();
        }

        if (!$customFieldSet) {
            throw new \RuntimeException('Failed to create or retrieve CustomFieldSet');
        }

        return $customFieldSet;
    }

    /**
     * Create custom fields for products and orders.
     *
     * Sets up all required custom fields for product pricing and order tracking.
     * Creates field sets if they don't exist and adds individual fields with proper labels.
     *
     * @param Context $context Shopware context for database operations
     *
     * @throws \RuntimeException If custom field creation fails
     */
    public function createCustomFields(Context $context): void
    {
        // Product custom fields
        $productSet = $this->addCustomFieldSet(
            CF::PM_FIELD_SET_PRODUCT,
            self::COMMON_LABELS,
            'product',
            $context
        );

        // Add buy price field if it doesn't exist
        if (!$this->customFieldExists($productSet->getId(), CF::PM_PRODUCT_BUY_PRICE, $context)) {
            $this->createCustomField(
                CF::PM_PRODUCT_BUY_PRICE,
                CustomFieldTypes::FLOAT,
                [
                    'en-GB' => 'Buy Price',
                    'da-DK' => 'Købspris',
                    'de-DE' => 'Kaufpreis',
                ],
                $productSet->getId(),
                1,
                $context
            );
        }

        // Order custom fields
        $orderSet = $this->addCustomFieldSet(
            CF::PM_FIELD_SET_ORDER,
            self::COMMON_LABELS,
            'order',
            $context
        );

        // Define order tracking fields
        $orderCustomFields = [
            [
                'name' => CF::PM_ORDER_GOOGLE_ADS_SCRIPT,
                'type' => CustomFieldTypes::TEXT,
                'config' => [
                    'label' => [
                        'en-GB' => 'Google Ads Script',
                        'da-DK' => 'Google Ads Script',
                        'de-DE' => 'Google Ads Skript',
                    ],
                    'componentName' => 'sw-field',
                    'customFieldPosition' => 1,
                ],
            ],
            [
                'name' => CF::PM_ORDER_VISITOR_ID,
                'type' => CustomFieldTypes::TEXT,
                'config' => [
                    'label' => [
                        'en-GB' => 'Profitmetrics Visitor ID',
                        'da-DK' => 'Profitmetrics besøgende-id',
                        'de-DE' => 'Profitmetrics Besucher-ID',
                    ],
                    'componentName' => 'sw-field',
                    'customFieldPosition' => 2,
                ],
            ],
            [
                'name' => CF::PM_ORDER_SENT_DATE,
                'type' => CustomFieldTypes::DATETIME,
                'config' => [
                    'label' => [
                        'en-GB' => 'Export date',
                        'da-DK' => 'Exportdato',
                        'de-DE' => 'Exportdatum',
                    ],
                    'componentName' => 'sw-field',
                    'customFieldPosition' => 3,
                ],
            ],
            [
                'name' => CF::PM_ORDER_RESPONSE,
                'type' => CustomFieldTypes::TEXT,
                'config' => [
                    'label' => [
                        'en-GB' => 'ProfitMetrics Response',
                        'da-DK' => 'ProfitMetrics Svar',
                        'de-DE' => 'ProfitMetrics Antwort',
                    ],
                    'componentName' => 'sw-field',
                    'customFieldPosition' => 6,
                ],
            ],
        ];

        // Add order tracking fields if they don't exist
        foreach ($orderCustomFields as $customField) {
            if (!$this->customFieldExists($orderSet->getId(), $customField['name'], $context)) {
                $this->customFieldRepository->create([
                    [
                        'name' => $customField['name'],
                        'type' => $customField['type'],
                        'config' => $customField['config'],
                        'customFieldSetId' => $orderSet->getId(),
                    ],
                ], $context);
            }
        }
    }

    /**
     * Remove all ProfitMetrics custom fields and related data.
     *
     * Cleans up custom fields, field sets, and the visitor tracking table
     * during plugin uninstallation.
     *
     * @param Context $context Shopware context for database operations
     *
     * @throws Exception
     */
    public function removeCustomFields(Context $context): void
    {
        // First delete our specific custom fields
        $customFieldsToDelete = [
            CF::PM_ORDER_GOOGLE_ADS_SCRIPT,
            CF::PM_ORDER_SENT_DATE,
            CF::PM_ORDER_RESPONSE,
            CF::PM_ORDER_VISITOR_ID,
            CF::PM_PRODUCT_BUY_PRICE,
        ];

        foreach ($customFieldsToDelete as $fieldName) {
            $this->deleteCustomFieldByName($fieldName, $context);
        }

        // Delete our custom field sets
        $this->deleteCustomFieldSet(CF::PM_FIELD_SET_PRODUCT, $context);
        $this->deleteCustomFieldSet(CF::PM_FIELD_SET_ORDER, $context);

        // Drop visitor tracking table if it exists
        $this->connection->executeStatement('DROP TABLE IF EXISTS profitmetrics_visitor');
    }

    /**
     * Create a custom field with standard configuration.
     *
     * Sets up a new custom field with given properties and standard display configuration.
     *
     * @param string $name Technical name of the field
     * @param string $type Field data type from CustomFieldTypes
     * @param array<string, string> $labels Display labels in different languages
     * @param string $setId ID of the parent custom field set
     * @param int $position Display position in the field set
     * @param Context $context Shopware context for database operations
     */
    private function createCustomField(
        string $name,
        string $type,
        array $labels,
        string $setId,
        int $position,
        Context $context
    ): void {
        $this->customFieldRepository->create([
            [
                'name' => $name,
                'type' => $type,
                'config' => [
                    'label' => $labels,
                    'componentName' => 'sw-field',
                    'customFieldPosition' => $position,
                ],
                'customFieldSetId' => $setId,
            ],
        ], $context);
    }

    /**
     * Delete a custom field set by name.
     *
     * Removes the custom field set and all its fields from the system.
     *
     * @param string $name Technical name of the field set to delete
     * @param Context $context Shopware context for database operations
     */
    private function deleteCustomFieldSet(string $name, Context $context): void
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('name', $name));

        $id = $this->customFieldSetRepository->searchIds($criteria, $context)->firstId();
        if ($id !== null) {
            $this->customFieldSetRepository->delete([['id' => $id]], $context);
        }
    }

    /**
     * Check if a custom field exists within a specific custom field set.
     *
     * Verifies if a custom field with given name exists in the specified set.
     *
     * @param string $customFieldSetId ID of the custom field set to check
     * @param string $customFieldName Name of the custom field to look for
     * @param Context $context Shopware context for database operations
     *
     * @return bool True if field exists, false otherwise
     */
    private function customFieldExists(string $customFieldSetId, string $customFieldName, Context $context): bool
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('name', $customFieldName));
        $criteria->addFilter(new EqualsFilter('customFieldSetId', $customFieldSetId));

        return $this->customFieldRepository->search($criteria, $context)->count() > 0;
    }

    /**
     * Delete a custom field by its name.
     *
     * Removes a specific custom field from the system.
     *
     * @param string $customFieldName Name of the custom field to delete
     * @param Context $context Shopware context for database operations
     */
    private function deleteCustomFieldByName(string $customFieldName, Context $context): void
    {
        // Find the custom field that matches the name
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('name', $customFieldName));

        $result = $this->customFieldRepository->searchIds($criteria, $context);
        if ($result->firstId() !== null) {
            $this->customFieldRepository->delete([['id' => $result->firstId()]], $context);
        }
    }
}
