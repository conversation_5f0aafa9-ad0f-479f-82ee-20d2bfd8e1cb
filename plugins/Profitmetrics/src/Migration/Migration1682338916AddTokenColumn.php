<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1682338916AddTokenColumn extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1_682_338_916;
    }

    public function update(Connection $connection): void
    {
        /**
         * Allow null, to allow backwards compat. with session.
         */
        $schemaManager = $connection->createSchemaManager();
        $tableName = 'profitmetrics_visitor';
        $tableDetails = $schemaManager->listTableDetails($tableName);

        // 1. Check if the 'token' column exists
        if (!$tableDetails->hasColumn('token')) {
            // Add the 'token' column
            $connection->executeStatement("
            ALTER TABLE $tableName
            ADD token VARCHAR(255) NULL AFTER id;
        ");
        }

        // 2. Check if the 'order_id' column exists
        if (!$tableDetails->hasColumn('order_id')) {
            // Add the 'order_id' column
            $connection->executeStatement("
            ALTER TABLE $tableName
            ADD order_id BINARY(16) NULL AFTER token;
        ");
        }

        // 3. Check if the index already exists
        if (!$tableDetails->hasIndex('profitmetrics_visitor_token_order_id_index')) {
            // Create the index if it doesn't exist
            $connection->executeStatement("
            CREATE INDEX profitmetrics_visitor_token_order_id_index
            ON $tableName (token, order_id);
        ");
        }

        // 4. Check if the foreign key constraint exists
        if (!$tableDetails->hasForeignKey('profitmetrics_visitor___fk')) {
            // Add the foreign key constraint
            $connection->executeStatement("
            ALTER TABLE $tableName
            ADD CONSTRAINT profitmetrics_visitor___fk
            FOREIGN KEY (order_id) REFERENCES `order` (id)
            ON DELETE CASCADE;
        ");
        }
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}
