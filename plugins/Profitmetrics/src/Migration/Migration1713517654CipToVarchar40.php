<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration**********CipToVarchar40 extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return **********;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        // implement update
        $connection->executeStatement(<<<SQL
                    ALTER TABLE profitmetrics_visitor MODIFY COLUMN cip VARCHAR(40);
                    SQL);
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}
