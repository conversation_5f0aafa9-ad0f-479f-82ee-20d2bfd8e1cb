<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1684220998GA4 extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1_684_220_998;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        $tableName = 'profitmetrics_visitor';
        $schemaManager = $connection->getSchemaManager();
        $tableDetails = $schemaManager->listTableDetails($tableName);

        // 1. Check if the 'ga4_session_id' column exists
        if (!$tableDetails->hasColumn('ga4_session_id')) {
            // Add the 'ga4_session_id' column if it doesn't exist
            $connection->executeStatement("
            ALTER TABLE $tableName
            ADD ga4_session_id TEXT NULL AFTER cip;
        ");
        }

        // 2. Check if the 'ga4_session_count' column exists
        if (!$tableDetails->hasColumn('ga4_session_count')) {
            // Add the 'ga4_session_count' column if it doesn't exist
            $connection->executeStatement("
            ALTER TABLE $tableName
            ADD ga4_session_count TEXT NULL AFTER ga4_session_id;
        ");
        }
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}
