#!/bin/bash
set -e  # Exit on any error

function check_last_command() {
    if [ $? -ne 0 ]; then
        echo "Error: $1 failed"
        exit 1
    fi
}

echo "Testing Shopware 6.5 compatibility..."
cd ./tools/shopware65
composer install
check_last_command "Composer install for Shopware 6.5"
vendor/bin/rector process ../../src --dry-run
check_last_command "Rector check for Shopware 6.5"

echo "Running composer validation..."
cd ../..
composer validate

echo "Running static analysis..."
vendor/bin/phpstan analyse src
