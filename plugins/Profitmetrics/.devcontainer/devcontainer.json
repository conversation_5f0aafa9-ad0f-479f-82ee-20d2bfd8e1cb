{"name": "Shopware 6.6.x.x", "dockerComposeFile": ["docker-compose.yml"], "service": "shopware", "workspaceFolder": "/workspaces/profitmetrics", "shutdownAction": "stopCompose", "remoteUser": "dockware", "features": {"ghcr.io/devcontainers/features/github-cli": {}}, "postCreateCommand": "bash .devcontainer/post-create.sh", "postAttachCommand": {"ports": "gh codespace ports visibility 8000:public -c $CODESPACE_NAME", "displayName": "gh codespace edit -c $CODESPACE_NAME -d 'Shopware 6.6.x.x'"}, "waitFor": "postCreateCommand", "forwardPorts": [8000], "portsAttributes": {"8000": {"label": "Shopware 6.6.x.x", "onAutoForward": "openBrowserOnce"}}, "containerEnv": {"HOME": "/var/www/html", "WORKSPACE_FOLDER": "/workspaces/profitmetrics"}, "customizations": {"codespaces": {"openFiles": [".devcontainer/README.md"]}}}