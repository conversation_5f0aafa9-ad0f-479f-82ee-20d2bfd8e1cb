{% sw_extends '@TmmsProductCustomerInputs/storefront/page/product-detail/buy-widget-customer-input-fields.html.twig' %}


{% block customerinput_date %}
    {% set disableDates = config('BrainProductInputsExtension.config.brainDefaultDateDisable') %}
    
    <input
        type="text"
        class="form-control tmms-customer-input-value{% if customFields[tmmsCustomerInputRequired] %} tmms-customer-input-is-required{% if tmmsCustomerInputValue == "" %} tmms-customer-input-is-empty{% endif %}{% endif %}{% if not(config('TmmsProductCustomerInputs.config.customerInputEmptyRequiredFieldAreSavedInCheckout')) %} tmms-customer-input-do-not-save-empty-required-field{% endif %}{% if config('TmmsProductCustomerInputs.config.customerInputBlockEnterKeyForInputTypeText') %} block-enter-key{% endif %}"
        name="tmms-customer-input-value-{{ tmmsCustomerInputCount }}"
        id="tmms-customer-input-value-{{ prefix }}{{ productId }}-{{ tmmsCustomerInputCount }}"
        value="{{ tmmsCustomerInputValue }}"
        data-path="{{ path('frontend.savecustomerinputs.request') }}"
        {% if customFields[tmmsCustomerInputRequired] and (tmmsRequiredFieldIsPossible == 1 or tmmsRequiredFieldIsPossible == 2) %}
            form="{{ requiredFormName }}"
            data-form="{{ requiredFormName }}"
            required
        {% endif %}
        data-save-customer-input="true"
        data-date-picker="true"
        data-date-picker-options='{
            {% if config('TmmsProductCustomerInputs.config.customerInputCalendarBasedOnLocalization') %}"locale": "{{ app.request.locale }}",{% endif %}
            "dateFormat": "{% if config('TmmsProductCustomerInputs.config.customerInputCalendarBasedOnLocalization') %}{% if customFields[tmmsCustomerInputFieldtype] == "datetime" %}{{ dateTimeFormat }}{% elseif customFields[tmmsCustomerInputFieldtype] == "date" %}{{ dateFormat }}{% elseif customFields[tmmsCustomerInputFieldtype] == "time" %}H:i{% endif %}{% else %}{% if customFields[tmmsCustomerInputFieldtype] == "datetime" %}{{ config('TmmsProductCustomerInputs.config.customerInputDateTimeFormat') }}{% elseif customFields[tmmsCustomerInputFieldtype] == "date" %}{{ config('TmmsProductCustomerInputs.config.customerInputDateFormat') }}{% elseif customFields[tmmsCustomerInputFieldtype] == "time" %}H:i{% endif %}{% endif %}",
            "enableTime": "{% if customFields[tmmsCustomerInputFieldtype] == "datetime" or customFields[tmmsCustomerInputFieldtype] == "time" %}true{% else %}false{% endif %}",
            {% if customFields[tmmsCustomerInputFieldtype] == "time" %}"noCalendar": "true",{% endif %}
            {% if (config('TmmsProductCustomerInputs.config.customerInputShowWeeksNumbers') == false) %}"weekNumbers": "false",{% endif %}
            {% if (config('TmmsProductCustomerInputs.config.customerInputAllowManualInput') == false) %}{% if customFields[tmmsCustomerInputRequired] and (tmmsRequiredFieldIsPossible == 1 or tmmsRequiredFieldIsPossible == 2) %}{% else %}"allowInput": "false",{% endif %}{% endif %}
            {% if (defaultDateValue != "") %}
                "defaultDate": "{{ defaultDateValue }}",
            {% endif %}
            {% if (customFields[tmmsCustomerInputFieldtype] == "datetime" or customFields[tmmsCustomerInputFieldtype] == "date") and (minDateValue != "") %}
                "minDate": "{{ minDateValue }}",
            {% endif %}
            {% if (customFields[tmmsCustomerInputFieldtype] == "datetime" or customFields[tmmsCustomerInputFieldtype] == "date") and (maxDateValue != "") %}
                "maxDate": "{{ maxDateValue }}",
            {% endif %}
            {% if (customFields[tmmsCustomerInputFieldtype] == "datetime" or customFields[tmmsCustomerInputFieldtype] == "time") and (customFields[tmmsCustomerInputStarttime]|replace({'[': '', ']': ''}) != "") %}
                "minTime": "{{ customFields[tmmsCustomerInputStarttime]|replace({'[': '', ']': ''}) }}",
            {% endif %}
            {% if (customFields[tmmsCustomerInputFieldtype] == "datetime" or customFields[tmmsCustomerInputFieldtype] == "time") and (customFields[tmmsCustomerInputEndtime]|replace({'[': '', ']': ''}) != "") %}
                "maxTime": "{{ customFields[tmmsCustomerInputEndtime]|replace({'[': '', ']': ''}) }}",
            {% endif %}
            {% if (customFields[tmmsCustomerInputFieldtype] == "datetime" or customFields[tmmsCustomerInputFieldtype] == "date") and (customFields[tmmsCustomerInputDisableddates]|replace({'[': '', ']': ''}) != "") %}
                "disable": [{{ customFields[tmmsCustomerInputDisableddates]|replace({'[': '', ']': ''}) }}],
            {% endif %}
            {% if (customFields[tmmsCustomerInputFieldtype] == "datetime" or customFields[tmmsCustomerInputFieldtype] == "date") and (disableDates|replace({'[': '', ']': ''}) != "") %}
            "disable": [{{ disableDates|replace({'[': '', ']': ''}) }}],
            {% endif %}
            {% if customFields[tmmsCustomerInputDaterange] %}
                "mode": "range",
            {% endif %}
            "altInput": "false"
        }'
    />
{% endblock %}
