<?php declare(strict_types=1);

namespace Brain\ProductInputsExtension;

use Shopware\Core\Framework\App\Manifest\Xml\CustomFieldTypes\CustomFieldType;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\System\CustomField\CustomFieldTypes;

class BrainProductInputsExtension extends Plugin
{
    const CSTEXT = 'product_text_and_info';
    /**
     * @param InstallContext $installContext
     */
    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);

        $customFieldSetRepository = $this->container->get('custom_field_set.repository');

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('name', self::CSTEXT));

        /** @var CustomFieldSetEntity $customFieldSet */
        $customFieldSet = $customFieldSetRepository->search(
            $criteria,
            $installContext->getContext()
        )->first();

        if (! $customFieldSet) {
            $customFieldSetRepository->upsert([[
                'name' => self::CSTEXT,
                'customFields' => [
                    [
                        'name' => 'product_text_festtema',
                        'type' => CustomFieldTypes::TEXT,
                        'config' => [
                            'label' => [
                                'da-DK' => 'Text',
                                'en-GB' => 'Text',
                            ]
                        ]
                    ],
                    [
                        'name' => 'product_text_info_festtema',
                        'type' => CustomFieldTypes::TEXT,
                        'config' => [
                            'label' => [
                                'da-DK' => 'Text info',
                                'en-GB' => 'Text info',
                            ]
                        ]
                    ]
                ],
                'config' => [
                    'label' => [
                        'da-DK' => 'Product Text and Info',
                        'en-GB' => 'Product Text and Info',
                        'de-DE' => 'Product Text and Info',
                    ]
                ],
                'relations' => [
                    [
                        'entityName' => 'product',
                    ],
                ],
            ]], $installContext->getContext());
        }

    }
}