<?php declare(strict_types=1);

namespace Brain\OrderNotes\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1690265664CreateOrderNotes extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1690265664;
    }

    public function update(Connection $connection): void
    {
        $connection->executeUpdate('
            CREATE TABLE IF NOT EXISTS `order_notes`(
                `id` BINARY(16) NOT NULL,
                `order_id` BINARY(16) NOT NULL,
                `order_version_id` BINARY(16) NOT NULL,
                `note` TEXT NOT NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3) NULL,
                CONSTRAINT `fk.order_notes.order_id` FOREIGN KEY (`order_id`)
                REFERENCES `order` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `fk.order_notes.order_id__order_version_id`
                    FOREI<PERSON>N KEY (`order_id`, `order_version_id`) REFERENCES `order` (`id`, `version_id`) ON UPDATE CASCADE ON DELETE CASCADE
            )ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci;
        ');
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}
