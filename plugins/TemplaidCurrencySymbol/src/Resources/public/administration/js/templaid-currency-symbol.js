!function(e){var r={};function n(c){if(r[c])return r[c].exports;var o=r[c]={i:c,l:!1,exports:{}};return e[c].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=r,n.d=function(e,r,c){n.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:c})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,r){if(1&r&&(e=n(e)),8&r)return e;if(4&r&&"object"==typeof e&&e&&e.__esModule)return e;var c=Object.create(null);if(n.r(c),Object.defineProperty(c,"default",{enumerable:!0,value:e}),2&r&&"string"!=typeof e)for(var o in e)n.d(c,o,function(r){return e[r]}.bind(null,o));return c},n.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(r,"a",r),r},n.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},n.p=(window.__sw__.assetPath + '/bundles/templaidcurrencysymbol/'),n(n.s="mStc")}({TNom:function(e){e.exports=JSON.parse('{"templaidCurrencySymbol":{"useSymbolLabel":"Use currency symbol in storefront","placeBeforeLabel":"Place symbol before number","removeWhitespace":"Remove whitespace between symbol and number"}}')},YcDd:function(e){e.exports=JSON.parse('{"templaidCurrencySymbol":{"useSymbolLabel":"Währungssymbol im Schaufenster verwenden","placeBeforeLabel":"Symbol vor die Zahl setzen","removeWhitespace":"Leerzeichen zwischen Symbol und Zahl entfernen"}}')},a58n:function(e){e.exports=JSON.parse('{"templaidCurrencySymbol":{"useSymbolLabel":"Gebruik valutasymbool in winkelpui","placeBeforeLabel":"Plaats symbool voor nummer","removeWhitespace":"Verwijder spaties tussen symbool en getal"}}')},dLGU:function(e){e.exports=JSON.parse('{"templaidCurrencySymbol":{"useSymbolLabel":"Brug valutasymbol i butiksfront","placeBeforeLabel":"Vis valutasymbol før beløb","removeWhitespace":"Fjern mellemrum mellem valutasymbol og beløb"}}')},mStc:function(e,r,n){"use strict";n.r(r);var c=n("TNom"),o=n("dLGU"),t=n("YcDd"),l=n("a58n"),s=n("x6rv"),u=Shopware.Component,i=Shopware.Data.Criteria;u.override("sw-settings-currency-detail",{template:'{% block sw_settings_currency_detail_content_field_tax_free_from %}\n    {% parent %}\n\n    <sw-switch-field\n            v-if="currency.extensions.currencySymbol"\n            class="sw-settings-currency-symbol__use-symbol-checkbox"\n            :label="$tc(\'templaidCurrencySymbol.useSymbolLabel\')"\n            :disabled="!acl.can(\'currencies.editor\')"\n            v-model="currency.extensions.currencySymbol.useCurrencySymbol"\n            @change="onUseCurrencySymbolChange">\n    </sw-switch-field>\n    <sw-switch-field\n            v-if="useCurrencySymbol"\n            class="sw-settings-currency-symbol__place-before-price-checkbox"\n            :label="$tc(\'templaidCurrencySymbol.placeBeforeLabel\')"\n            :disabled="!acl.can(\'currencies.editor\')"\n            v-model="currency.extensions.currencySymbol.placeSymbolFirst">\n    </sw-switch-field>\n    <sw-switch-field\n            v-if="useCurrencySymbol"\n            class="sw-settings-currency-symbol__remove-whitespace-checkbox"\n            :label="$tc(\'templaidCurrencySymbol.removeWhitespace\')"\n            :disabled="!acl.can(\'currencies.editor\')"\n            v-model="currency.extensions.currencySymbol.removeWhitespace">\n    </sw-switch-field>\n{% endblock %}',snippets:{"en-GB":c,"da-DK":o,"de-DE":t,"nl-NL":l,"fr-FR":s},data:function(){return{currency:{},isLoading:!1,currencyCountryLoading:!1,isSaveSuccessful:!1,currentCurrencyCountry:null,currencyCountryRoundings:null,searchTerm:"",customFieldSets:null,useCurrencySymbol:!1}},computed:{currencySymbolRepository:function(){return this.repositoryFactory.create("templaid_currency_symbol")}},created:function(){var e=this;this.createdComponent().then((function(){e.currency.extensions={currencySymbol:{}},e.currency.extensions.currencySymbol||(e.currency.extensions.currencySymbol=e.currencySymbolRepository.create(),e.currency.extensions.currencySymbol.useCurrencySymbol=e.useCurrencySymbol,e.loadEntityData())}))},methods:{loadEntityData:function(){var e=this;this.isLoading=!0,this.currency.extensions={currencySymbol:{}};var r=new i;return r.addAssociation("currencySymbol"),this.currencyRepository.get(this.currencyId,Shopware.Context.api,r).then((function(r){return e.currency=r,e.currency.extensions.currencySymbol?e.useCurrencySymbol=e.currency.extensions.currencySymbol.useCurrencySymbol:(e.currency.extensions.currencySymbol=e.currencySymbolRepository.create(),e.currency.extensions.currencySymbol.useCurrencySymbol=e.useCurrencySymbol),e.loadCurrencyCountryRoundings().then((function(e){return[r,e]}))})).finally((function(){e.isLoading=!1}))},onUseCurrencySymbolChange:function(e){this.useCurrencySymbol=e,this.currencyId&&!this.currency.extensions.currencySymbol.hasOwnProperty("id")&&this.loadEntityData()}}})},x6rv:function(e){e.exports=JSON.parse('{"templaidCurrencySymbol":{"useSymbolLabel":"Utiliser le symbole de la monnaie dans la vitrine","placeBeforeLabel":"Placez le symbole avant le numéro","removeWhitespace":"Supprimez l\'espace entre le symbole et le nombre"}}')}});
//# sourceMappingURL=templaid-currency-symbol.js.map