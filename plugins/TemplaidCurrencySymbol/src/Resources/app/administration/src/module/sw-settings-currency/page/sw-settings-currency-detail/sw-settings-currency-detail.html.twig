{% block sw_settings_currency_detail_content_field_tax_free_from %}
    {% parent %}

    <sw-switch-field
            v-if="currency.extensions.currencySymbol"
            class="sw-settings-currency-symbol__use-symbol-checkbox"
            :label="$tc('templaidCurrencySymbol.useSymbolLabel')"
            :disabled="!acl.can('currencies.editor')"
            v-model="currency.extensions.currencySymbol.useCurrencySymbol"
            @change="onUseCurrencySymbolChange">
    </sw-switch-field>
    <sw-switch-field
            v-if="useCurrencySymbol"
            class="sw-settings-currency-symbol__place-before-price-checkbox"
            :label="$tc('templaidCurrencySymbol.placeBeforeLabel')"
            :disabled="!acl.can('currencies.editor')"
            v-model="currency.extensions.currencySymbol.placeSymbolFirst">
    </sw-switch-field>
    <sw-switch-field
            v-if="useCurrencySymbol"
            class="sw-settings-currency-symbol__remove-whitespace-checkbox"
            :label="$tc('templaidCurrencySymbol.removeWhitespace')"
            :disabled="!acl.can('currencies.editor')"
            v-model="currency.extensions.currencySymbol.removeWhitespace">
    </sw-switch-field>
{% endblock %}