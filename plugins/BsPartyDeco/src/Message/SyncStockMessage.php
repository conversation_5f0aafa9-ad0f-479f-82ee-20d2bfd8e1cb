<?php declare(strict_types=1);

namespace BsPartyDeco\Message;

use Shopware\Core\Framework\MessageQueue\AsyncMessageInterface;

class SyncStockMessage implements AsyncMessageInterface
{
    private array $productData;

    public function __construct(array $productData)
    {
        $this->productData = $productData;
    }

    public function getProductData(): array
    {
        return $this->productData;
    }

}
