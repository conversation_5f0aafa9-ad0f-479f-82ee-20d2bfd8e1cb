<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>

        <service id="BsPartyDeco\Command\SyncStockCommand">
            <argument type="service" id="BsPartyDeco\Service\SyncStockService" />
            <tag name="console.command"/>
        </service>

        <service id="BsPartyDeco\Service\SyncStockService">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="BsPartyDeco\Util\Logger" on-invalid="ignore"/>
            <argument type="service" id="http_client"/>
            <argument type="service" id="messenger.bus.shopware" />
        </service>

        <service id="BsPartyDeco\MessageHandler\SyncStockHandler">
            <argument type="service" id="product.repository"/>
            <argument type="service" id="BsPartyDeco\Util\Logger" on-invalid="ignore"/>
            <tag name="messenger.message_handler"/>
        </service>

        <service id="BsPartyDeco\Util\Logger" class="Monolog\Logger">
            <argument type="string">bs_party_deco_log</argument>
            <argument type="collection">
                <argument type="service" id="BsPartyDeco\Handler\RotatingFileHandler"/>
            </argument>
        </service>

        <service id="BsPartyDeco\Handler\RotatingFileHandler" class="Monolog\Handler\RotatingFileHandler">
            <argument type="string">%kernel.logs_dir%/bs_party_deco/logs.log</argument>
        </service>

    </services>
</container>