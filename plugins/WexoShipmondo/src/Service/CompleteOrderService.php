<?php

namespace Wexo\Shipmondo\Service;

use Exception;
use DateTime;
use Psr\Log\LoggerInterface;
use Shopware\Core\Checkout\Order\Aggregate\OrderDelivery\OrderDeliveryStates;
use Shopware\Core\Checkout\Order\Aggregate\OrderTransaction\OrderTransactionStates;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Checkout\Order\OrderStates;
use Shopware\Core\Checkout\Order\SalesChannel\OrderService;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\System\StateMachine\Aggregation\StateMachineTransition\StateMachineTransitionActions;
use Shopware\Core\System\StateMachine\Exception\IllegalTransitionException;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\Messenger\MessageBusInterface;
use Wexo\Shipmondo\Exception\ApiCredentialsMissingException;
use Wexo\Shipmondo\WexoShipmondo;

/**
 * Class FinishOrderService
 * @package Wexo\Shipmondo\Service
 */
class CompleteOrderService extends AbstractService
{
    /**
     * @var EntityRepository
     */
    protected $orderRepository;

    /**
     * @var OrderService
     */
    protected $orderService;

    /**
     * @var EntityRepository
     */
    protected $orderDeliveryRepository;

    /**
     * FinishOrderService constructor.
     * @param EntityRepository $logEntryRepository
     * @param SystemConfigService $config
     * @param LoggerInterface $logger
     * @param EntityRepository $scheduledTaskRepository
     * @param MessageBusInterface $bus
     * @param EntityRepository $orderRepository
     * @param OrderService $orderService
     * @param EntityRepository $orderDeliveryRepository
     */
    public function __construct(
        EntityRepository $logEntryRepository,
        SystemConfigService $config,
        LoggerInterface $logger,
        EntityRepository $scheduledTaskRepository,
        MessageBusInterface $bus,
        EntityRepository $orderRepository,
        OrderService $orderService,
        EntityRepository $orderDeliveryRepository
    ) {
        parent::__construct($logEntryRepository, $config, $logger, $scheduledTaskRepository, $bus);
        $this->orderRepository = $orderRepository;
        $this->orderService = $orderService;
        $this->orderDeliveryRepository = $orderDeliveryRepository;
    }

    /**
     * @param bool $fromCommand
     */
    public function completeOrders(bool $fromCommand = false): void
    {
        if (!$this->config->get(WexoShipmondo::CONFIG_PREFIX . 'active') && $fromCommand === false) {
            return;
        }

        $this->setScheduledTaskTimeInterval(WexoShipmondo::COMMAND_COMPLETE_ORDERS, 'scheduledTaskTimeComplete');

        $orders = $this->getOrders();

        // Loop through all shipmondo orders with the shopware order attached
        foreach ($orders as $order) {
            /** @var OrderEntity $shopwareOrder */
            $shopwareOrder = $order['shopware_order'];

            $delivery = $shopwareOrder->getDeliveries()->first();

            // Make sure the order has not already been shipped
            if ($delivery &&
                $delivery->getStateMachineState()->getTechnicalName() === OrderDeliveryStates::STATE_SHIPPED
            ) {
                continue;
            }

            try {
                $this->completeOrder($order);
            } catch (IllegalTransitionException $e) {
                $this->handleException($e, "Could not change state of order ({$shopwareOrder->getId()})", false);
            } catch (Exception $e) {
                $this->handleException($e, "Error completing order ({$shopwareOrder->getId()})");
            }
        }
    }

    /**
     * @return array
     */
    public function getOrders(): array
    {
        $validOrders = $this->getShipmondoOrders();
        if (!$validOrders) {
            return [];
        }

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsAnyFilter('orderNumber', array_column($validOrders, 'order_id')));

        $criteria->addAssociation('stateMachineState');
        $criteria->addAssociation('deliveries');
        $criteria->addAssociation('documents');
        $criteria->addAssociation('transactions');

        $orders = $this->orderRepository->search($criteria, $this->context);

        foreach ($validOrders as $key => &$shipmondoOrder) {
            $orderNumber = $shipmondoOrder['order_id'];
            try {
                $order = $orders->filterByProperty('orderNumber', $orderNumber)->first();

                if (!$order) {
                    unset($validOrders[$key]);
                    throw new Exception("Order with order_number ($orderNumber) not found");
                }

                $shipmondoOrder['shopware_order'] = $order;
            } catch (Exception $exception) {
                $this->handleException($exception, $exception->getMessage());
            }
        }

        return $validOrders;
    }

    /**
     * @param array $order
     * @return bool
     */
    public function validateOrder(array $order): bool
    {
        $finishedOrderStatus = $this->config->get(WexoShipmondo::CONFIG_PREFIX . 'finishedOrderStatus');

        $finishedFulfillment = $this->config->get(WexoShipmondo::CONFIG_PREFIX . 'finishedFulfillmentStatus');

        $finishedPaymentStatus = $this->config->get(WexoShipmondo::CONFIG_PREFIX . 'finishedPaymentStatus');

        return !(!in_array($order['order_status'], $finishedOrderStatus)
            || !in_array($order['fulfillment_status'], $finishedFulfillment)
            || !in_array($order['payment_status'], $finishedPaymentStatus));
    }

    /**
     * @return array
     */
    public function getShipmondoOrders(): array
    {
        $orders = [];

        try {
            $params = [
//                'updated_at_min' => (new DateTime('-24 hour'))->format('Y-m-d'),
//            'order_id' => '710028614'
                'order_id' => '710029048'
            ];

            list($output, $pagination) = array_values($this->getClient()->getSalesOrders($params));
dd($output, $pagination);
            $currentPage = $pagination['x-current-page'];
            $totalPages = $pagination['x-total-pages'];

            do {
                foreach ($output as $order) {
                    if ($this->validateOrder($order)) {
                        $orders[] = $order;
                    }
                }

                if ($currentPage == $totalPages) {
                    break;
                }

                $params['page'] = ++$currentPage;
                try {
                    $output = $this->getClient()->getSalesOrders($params)['output'];
                } catch (Exception $exception) {
                    $output = [];
                    $this->handleException($exception, 'Error when fetching orders from shipmondo');
                }
            } while (!empty($output));
        } catch (ApiCredentialsMissingException $exception) {
            // Failed to getClient might be wrong credentials.
            return [];
        } catch (\Exception $exception) {
            $this->handleException($exception, 'Failed to retrieve shipped orders from Shipmondo', false);
        }

        return $orders;
    }

    /**
     * @param array $order
     */
    public function completeOrder(array $order): void
    {
        $parameterBag = new ParameterBag();

        /** @var OrderEntity $shopwareOrder */
        $shopwareOrder = $order['shopware_order'];

        // Add documents
        $parameterBag->add([
            'documentIds' => $shopwareOrder->getDocuments()->getIds()
        ]);

        // Update order data
        $this->updateOrder($order);

        // Set order status
        $this->setOrderStatus($shopwareOrder, $parameterBag);

        // Set payment status
        $this->setPaymentStatus($shopwareOrder, $parameterBag);

        // Set shipment status
        $this->setDeliveryStatus($shopwareOrder, $parameterBag);
    }

    /**
     * @param array $order
     */
    protected function updateOrder(array $order): void
    {
        /** @var OrderEntity $shopwareOrder */
        $shopwareOrder = $order['shopware_order'];

        try {
            $this->applyTrackingCodes($order, $shopwareOrder);
        } catch (Exception $e) {
            $this->handleException($e, "Could not update order ({$shopwareOrder->getId()})");
        }
    }

    /**
     * @param $order
     * @param OrderEntity $shopwareOrder
     * @return void
     */
    protected function applyTrackingCodes($order, OrderEntity $shopwareOrder): void
    {
        $trackingCodes = [];

        // Get all shipments to add tracking codes
        foreach ($order['order_fulfillments'] as $fulfillment) {
            if (!isset($fulfillment['shipment_id'])) {
                continue;
            }

            $shipmentId = $fulfillment['shipment_id'];

            try {
                $output = $this->getClient()->getShipment($shipmentId)['output'];

                $trackingCodes[] = $output['pkg_no'];
            } catch (ApiCredentialsMissingException $exception) {
                // Failed to getClient might be wrong credentials.
                return;
            } catch (Exception $exception) {
                $this->handleException(
                    $exception,
                    "Error when getting tracking codes from shipment ($shipmentId) from shipmondo"
                );
            }
        }

        if (!empty($trackingCodes) && $delivery = $shopwareOrder->getDeliveries()->first()) {
            $this->orderDeliveryRepository->update([
                [
                    'id' => $delivery->getId(),
                    'trackingCodes' => $trackingCodes
                ]
            ], $this->context);
        }
    }

    /**
     * @param OrderEntity $shopwareOrder
     * @param ParameterBag $parameterBag
     */
    protected function setOrderStatus(OrderEntity $shopwareOrder, ParameterBag $parameterBag): void
    {
        $transaction = $shopwareOrder->getTransactions()->first();

        // Only set order status to completed if order is payment_state === 'paid'
        if ($transaction &&
            $transaction->getStateMachineState()->getTechnicalName() !== OrderTransactionStates::STATE_PAID
        ) {
            return;
        }

        $orderId = $shopwareOrder->getId();

        if ($shopwareOrder->getStateMachineState()->getTechnicalName() !== OrderStates::STATE_COMPLETED) {
            if ($shopwareOrder->getStateMachineState()->getTechnicalName() === OrderStates::STATE_OPEN) {
                $this->orderService->orderStateTransition(
                    $orderId,
                    StateMachineTransitionActions::ACTION_PROCESS,
                    $parameterBag,
                    $this->context
                );
            }

            $this->orderService->orderStateTransition(
                $orderId,
                StateMachineTransitionActions::ACTION_COMPLETE,
                $parameterBag,
                $this->context
            );
        }
    }

    /**
     * @param OrderEntity $shopwareOrder
     * @param ParameterBag $parameterBag
     */
    protected function setPaymentStatus(OrderEntity $shopwareOrder, ParameterBag $parameterBag)
    {
        $option = $this->config->getBool(
            WexoShipmondo::CONFIG_PREFIX . 'collectPaymentAutomatically',
            $shopwareOrder->getSalesChannelId()
        );

        // Set payment_status 'paid' if this option is enabled
        if ($option) {
            $transaction = $shopwareOrder->getTransactions()->first();

            if ($transaction &&
                $transaction->getStateMachineState()->getTechnicalName() !== OrderTransactionStates::STATE_PAID
            ) {
                $this->orderService->orderTransactionStateTransition(
                    $transaction->getId(),
                    StateMachineTransitionActions::ACTION_PAID,
                    $parameterBag,
                    $this->context
                );
            }
        }
    }

    /**
     * @param OrderEntity $shopwareOrder
     * @param ParameterBag $parameterBag
     */
    protected function setDeliveryStatus(OrderEntity $shopwareOrder, ParameterBag $parameterBag): void
    {
        $orderDelivery = $shopwareOrder->getDeliveries()->first();

        if ($orderDelivery &&
            $orderDelivery->getStateMachineState()->getTechnicalName() != OrderDeliveryStates::STATE_SHIPPED
        ) {
            $deliveryId = $orderDelivery->getId();

            $this->orderService->orderDeliveryStateTransition(
                $deliveryId,
                StateMachineTransitionActions::ACTION_SHIP,
                $parameterBag,
                $this->context
            );
        }
    }
}
