{% sw_extends '@Storefront/storefront/component/line-item/type/product.html.twig' %}

{% block component_line_item_type_product_order_number %}
    {{ parent() }}

    {# page/account/order-history/order-detail-list.htnl.twig, page/account/order/index.html.twig and page/checkout/finish/index.html.twig #}
    {% if (not(showRemoveButton) and (displayMode === 'order')) or (not(showRemoveButton) and (controllerAction|lower === "finishpage")) %}
        {% for tmmsCustomerInputCount in 1..page.tmmsCustomerInputCountValue %}
            {% set tmmsCustomerInputCssClass = 'line-item-customer-input-fields-no-edit' %}

            {% sw_include '@TmmsProductCustomerInputs/storefront/page/line-item-customer-input-fields-no-edit.html.twig' %}
        {% endfor %}
    {% endif %}
{% endblock %}

{% block component_line_item_type_product %}
    {{ parent() }}

    {# offcanvas and checkout #}
    {% if displayMode === 'offcanvas' %}
        {% set prefix = 'offcanvas-' %}
        {% set tmmsPageType = 'offcanvas' %}
        {% set tmmsPageTypeCapitalize = 'Offcanvas' %}
        {% set tmmsRequiredFieldIsPossible = 0 %}
        {% set tmmsRequiredFormName = 'offcanvasForm' %}
    {% else %}
        {% set prefix = '' %}
        {% set tmmsPageType = 'checkout' %}
        {% set tmmsPageTypeCapitalize = '' %}
        {% set tmmsRequiredFieldIsPossible = 2 %}
        {% set tmmsRequiredFormName = 'confirmOrderForm' %}
    {% endif %}

    {% sw_include '@TmmsProductCustomerInputs/storefront/component/line-item/type/line-item-customer-input-fields.html.twig' with {
        prefix: prefix,
        tmmsPageType: tmmsPageType,
        tmmsPageTypeCapitalize: tmmsPageTypeCapitalize,
        tmmsRequiredFieldIsPossible: tmmsRequiredFieldIsPossible,
        tmmsRequiredFormName: tmmsRequiredFormName
    } %}
{% endblock %}
