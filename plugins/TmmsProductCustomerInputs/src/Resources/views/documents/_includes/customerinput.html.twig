{% if level > 0 %}
    {% for i in 1..level %}
        <span class="wrapper-wrapper">
            <span class="label-wrapper level-{{ i }}"></span>
        </span>
    {% endfor %}
{% endif %}

<span class="line-item-label level-{{ level }}">{{ lineItem.label }}</span>
{% if lineItem.payload.options|length >= 1 %}
    <br/>
    {% for option in lineItem.payload.options %}
        {{ option.group }}: {{ option.option }}
        {% if lineItem.payload.options|last != option %}
            {{ " | " }}
        {% endif %}
    {% endfor %}
{% endif %}

{% if lineItem.payload.features|length >=1  %}
    <br/>
    {% for feature in lineItem.payload.features %}
        {% if feature.type == 'referencePrice' %}
            {{ feature.value.purchaseUnit|sw_sanitize }} {{ feature.value.unitName|sw_sanitize }}
            ({{ feature.value.price|currency(currencyIsoCode, languageId) }}{{ "general.star"|trans }} / {{ feature.value.referenceUnit|sw_sanitize }} {{ feature.value.unitName|sw_sanitize }})
            {% if lineItem.payload.features|last != feature %}
                {{ " | " }}
            {% endif %}
        {% endif %}
    {% endfor %}
{% endif %}

{% block document_line_item_table_column_label_customerinput %}
    {% set tmmsCustomerInputCountValue = 5 %}

    {% for tmmsCustomerInputCount in 1..tmmsCustomerInputCountValue %}
        {% set customFields = lineItem.customFields %}
        {% set tmmsCustomerInputValue = 'tmms_customer_input_' ~ tmmsCustomerInputCount ~ '_value' %}
        {% set tmmsCustomerInputFieldtype = 'tmms_customer_input_' ~ tmmsCustomerInputCount ~ '_fieldtype' %}
        {% set tmmsCustomerInputLabel = 'tmms_customer_input_' ~ tmmsCustomerInputCount ~ '_label' %}
        {% set tmmsCustomerInputPlaceholder = 'tmms_customer_input_' ~ tmmsCustomerInputCount ~ '_placeholder' %}

        {% if customFields[tmmsCustomerInputValue] != "" %}
            {% block document_line_item_table_row_label_customerinput_inner %}
                <br/>
                {% if customFields[tmmsCustomerInputLabel] != "" %}
                    {{ customFields[tmmsCustomerInputLabel] }}
                {% endif %}
                {% if customFields[tmmsCustomerInputFieldtype] == "boolean" %}
                    <i>"{{ customFields[tmmsCustomerInputPlaceholder] }}"</i> ({{ customFields[tmmsCustomerInputValue] }})
                {% else %}
                    <i>"{{ customFields[tmmsCustomerInputValue] }}"</i>
                {% endif %}
            {% endblock %}
        {% endif %}
    {% endfor %}
{% endblock %}
