"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["tmms-product-customer-inputs"],{1874:(t,e,s)=>{var i=s(6285),r=s(207),u=s(8254);function n(t,e,s){t.addEventListener?t.addEventListener(e,s,!1):t.attachEvent&&t.attachEvent("on"+e,s)}class o extends i.Z{init(){this.$emitter.publish("beforeInitSaveCustomerInput"),n(this.el,"change",(function(){this.client=new u.Z;let t=this.closest("form"),e=!0,s=!1;this.hasAttribute("required")&&(s=!0,"checkbox"===this.getAttribute("type")?this.checked?this.removeAttribute("form"):this.hasAttribute("form")||this.setAttribute("form",this.getAttribute("data-form")):""!==this.value?this.removeAttribute("form"):this.hasAttribute("form")||this.setAttribute("form",this.getAttribute("data-form"))),this.classList.contains("tmms-customer-input-is-required")&&("checkbox"===this.getAttribute("type")?this.checked?(this.classList.remove("tmms-customer-input-is-empty"),t.classList.remove("tmms-customer-input-form-input-is-empty")):(this.classList.add("tmms-customer-input-is-empty"),t.classList.add("tmms-customer-input-form-input-is-empty"),this.classList.contains("tmms-customer-input-do-not-save-empty-required-field")&&(e=!1)):""!==this.value?(this.classList.remove("tmms-customer-input-is-empty"),t.classList.remove("tmms-customer-input-form-input-is-empty")):(this.classList.add("tmms-customer-input-is-empty"),t.classList.add("tmms-customer-input-form-input-is-empty"),this.classList.contains("tmms-customer-input-do-not-save-empty-required-field")&&(e=!1)));const i=this.closest("form"),n=this.getAttribute("data-path"),o=r.Z.serialize(i);if("number"===this.getAttribute("type")){let t=this.value,i=this.getAttribute("min"),r=this.getAttribute("max"),u=this.getAttribute("step"),n=0,o=0,a=0,m=0,h=!1,p=0,c="",l=0,d=0;u&&(h=!0),!0===h?-1!==u.indexOf(".")?(c=u.substring(u.indexOf(".")+1),l=c.length,d=Math.pow(10,l),n=parseFloat(t)*d,o=parseFloat(i)*d,a=parseFloat(r)*d,m=u*d):(p=1,m=parseInt(u,10)):p=1,1===p&&(n=parseInt(t,10),o=parseInt(i,10),a=parseInt(r,10)),n>=o&&n<=a?!0===h&&((n-o).toFixed(l)%m!=0&&0==p||(n-o)%m!=0&&1==p)&&(e=!1):e=!1,s?""===t&&(e=!1):""===t&&(e=!0)}e&&(this.$emitter.publish("beforeSaveCustomerInputSendPostRequest",o),this.client.post(n.toLowerCase(),o,(t=>{this.$emitter.publish("afterSaveCustomerInputSendPostRequest")})))})),n(this.el,"keydown",(t=>{let e=this.el;if(("text"===e.getAttribute("type")||"number"===e.getAttribute("type"))&&e.classList.contains("block-enter-key")&&(13===t.which||13===t.keyCode))return t.preventDefault(),!1}))}}var a=s(3206);class m extends i.Z{init(){this.$emitter.publish("beforeInitRepeatOrderSaveCustomerInput");const t=window.PluginManager.getPluginInstances("AddToCart");t&&t.forEach((t=>{t.$emitter.subscribe("beforeFormSubmit",(t=>{this.client=new u.Z;let e=t.target.getAttribute("id").replace("orderDetailForm-",""),s=a.Z.querySelectorAll(document,".order-item-tmms-customer-input-form[data-orderId='"+e+"']",!1);s.length>0&&s.forEach((t=>{let e=t.getAttribute("action"),s=r.Z.serialize(t);this.$emitter.publish("beforeRepeatOrderSaveCustomerInputSendPostRequest",s),this.client.post(e.toLowerCase(),s,(t=>{this.$emitter.publish("afterRepeatOrderSaveCustomerInputSendPostRequest")}))}))}))}))}}const h=window.PluginManager;h.register("SaveCustomerInputPlugin",o,'[data-save-customer-input="true"]'),h.register("RepeatOrderSaveCustomerInputPlugin",m)}},t=>{t.O(0,["vendor-node","vendor-shared"],(()=>{return e=1874,t(t.s=e);var e}));t.O()}]);