{"name": "tmms/tmmsproductcustomerinputs", "description": "Customer inputs or additional fields at order line items (under restrictions on products)", "version": "1.2.2", "type": "shopware-platform-plugin", "license": "proprietary", "authors": [{"name": "Deutsche Telekom MMS GmbH", "role": "Manufacturer"}], "require": {"shopware/core": "~6.5.0", "shopware/storefront": "~6.5.0"}, "extra": {"shopware-plugin-class": "Tmms\\ProductCustomerInputs\\TmmsProductCustomerInputs", "plugin-icon": "src/Resources/config/plugin.png", "copyright": "Deutsche Telekom MMS GmbH", "label": {"de-DE": "Kundeneingaben oder zusätzliche Felder an Bestellpositionen (unter Einschränkungen bei Produkten)", "en-GB": "Customer inputs or additional fields at order line items (under restrictions on products)"}, "description": {"de-DE": "Die Erweiterung ermöglicht unter anderem bis zu 5 verschiedene Kundeneingaben an Bestellpositionen und unter Einschränkungen bei Produkten. <PERSON><PERSON>r jedes Produkt kann man je <PERSON>feld - Set bei den Zusatzfeldern folgendes festlegen: ob eine Eingabe möglich sein soll, den Feldtyp der Eingabe (ein ein- oder mehrzeiliges Eingabefeld, ein Num<PERSON>feld, ein Checkboxfeld, ein Datums- und Uhrzeitfeld, ein Da<PERSON>feld, ein Uhrzeitfeld oder ein Auswahlfeld), die Beschriftung vor der Eingabe, den Platzhalter für die Eingabe und ob es sich um ein Pflichtfeld handelt. Die Beschriftung und der Platzhalter für die Eingabe können auch global für alle Produkte in den Textbausteinen festgelegt werden. Für das Datums- und/oder Uhrzeitfeld kann zusätzlich noch ein Startdatum, ein Enddatum, auszuschließende Daten oder eine Start- und Endzeit gesetzt werden. Die Werte für das Auswahlfeld werden kommasepariert im entsprechenden Feld hinterlegt. <PERSON><PERSON> kann zusätzlich ein Mindestwert, ein <PERSON>alwert und die Schrittweite festgelegt werden. Beim einzeiligen und mehrzeiligen Eingabefeld kann zusätzlich die maximale Anzahl an Zeichen festgelegt werden. Darüber hinaus kann innerhalb der Konfiguration ausgewählt werden, ob die Eingabe im Warenkorb, auf der Bestellabschlussseite oder auf der Produkt - Detailseite angezeigt werden und ob die Eingabe im Checkout änderbar sein soll. Des Weiteren kann festgelegt werden, ob leere Eingaben im Checkout angezeigt, nicht ausgewählte Checkboxfelder als Wert übertragen oder Pflichtfeldmarkierung farblich hervorgehoben werden sollen. Für das Datums- und Uhrzeitfeld kann ausgewählt werden, ob manuelle Eingaben erlaubt sind und die Kalenderwochen angezeigt werden sollen und es kann das Datumsformat festgelegt werden. Die Eingaben werden je Bestellposition sowohl auf der Bestellbestätigungsseite und im Kunden-Account im Frontend als auch bei den Bestellpositionen in der Administration und auf den Dokumenten angezeigt. Es wird dabei neben der Beschriftung die eigentliche Eingabe und bei einem Checkboxfeld zusätzlich noch der Platzhalter für jede Bestellposition mit übernommen. Bei einem Checkboxfeld wird der Text nach der Checkbox über den Platzhalter bestimmt. Das Abfangen des Absendens des Formulars, insofern die Eingabe ein Pflichtfeld ist, ist nur auf der Produkt - Detailseite und auf der Bestellabschlussseite möglich, da nur auf diesen Seiten ein entsprechendes Formular vorhanden ist und der Warenkorb beispielsweise durch den Kunden mit einem Kundenkonto übersprungen werden kann und die QuickView von Shopware bereits alle Produkte mit dessen Formularen vorlädt. Sobald der Kunde eine Eingabe in das Feld vornimmt, wird die Änderung gespeichert. Die Eingabe wird dabei in der Session gespeichert, sodass die Eingabe für den Kunden solange verfügbar ist, bis der Kunde den Einkauf abschließt oder den Browser-Cache leert.", "en-GB": "The extension allows up to 5 different customer inputs at order line items and under restrictions on products. For each product you can set the following things in the custom fields area for each custom field set: whether a input should be possible, the field type of the input (a single or multi-line input field, a number field, a checkbox field, a date time field, a date field, a time field or a selection field), the label before the input, the placeholder for the input and whether it is a required field. The label and the placeholder for the input can also be defined globally within the snippets for all products. A start date, an end date, dates to be excluded or a start and end time can also be set for the date and / or time field. The values for the selection field are separated by commas and set in the corresponding field. At the number field a minimum value, a maximum value and the steps can also be specified. At the single-line and multi-line input field the maximum number of characters can also be specified. In addition, it can be selected within the configuration whether the input should be displayed in the cart, on the confirm page or the product detail page and whether the input could be editable in the checkout. In addition, you can specify whether empty inputs should be displayed in the checkout, unselected checkbox fields should be transferred as a value or a required field should be highlighted in color. For the date and time field, you can select whether manual inputs are allowed and the week numbers are to be displayed and you can set the date format. The inputs are displayed for each line item both on the finish page and in the customer account in the frontend as well as in the line items in the administration and on the documents. In addition to the label, the actual input and, in the case of a checkbox field, the placeholder for each line item is also adopted. In the case of a checkbox field, the text after the checkbox is set using the placeholder. Intercepting the sending of the form, insofar as the input is a required field, is only possible on the product detail page and on the confirm page, because a corresponding form is only available on these pages and the shopping cart can be skipped, for example by the customer with a customer account and the quickview from shopware already preloads all products with its forms. As soon as the customer makes an input in the field, the change is saved. The input is saved in the session so that the input is available to the customer until the customer completes the purchase or clears the browser cache."}, "manufacturerLink": {"de-DE": "https://www.telekom-mms.com", "en-GB": "https://www.telekom-mms.com"}}, "autoload": {"psr-4": {"Tmms\\ProductCustomerInputs\\": "src/"}}}