# 1.2.2
- it will now prevent a JavaScript warning message from being shown

# 1.2.1
- the descriptions have been updated
- You can now select if you want to show the repeat order button
- You can now select if the repeat order function should take over the customer input

# 1.2.0
- established the compatibility with Shopware from version *******

# 1.1.11
- established the compatibility with Shopware from version ********

# 1.1.10
- the annotations of the controller have been replaced with Route defaults

# 1.1.9
- You can now select if the enter key should be blocked for the field types single-line input field, number field, date and time field, date field and time field

# 1.1.8
- at the field type "number field" the custom field "steps for the field" now also supports steps with decimal points

# 1.1.7
- established the compatibility with Shopware from version ********

# 1.1.6
- You can now select if the product input should be shown on the offcanvas cart page
- You can now select if a dividing line should be shown between the product and the product input in the checkout
- You can now select if the product input should be shown in a foldable area in the large shopping cart and on the confirm page
- You can now select if required fields can be changed in the shopping cart (however, it is not possible to intercept the sending of the form)
- You can now select if required fields can be changed on the confirm page
- You can now select if empty required fields are saved in the checkout

# 1.1.5
- You can now select if a details button should be displayed in the navigation when a product input has been activated for a product
- You can now select if a message should be displayed under the product inputs, for example with a description of the behavior when the product is added to the shopping cart again
- You can now select if a language-dependent calendar should be used based on the localization

# 1.1.4
- There is now also the field type "number field"
- At the field type "number field" a minimum value, a maximum value and the steps can also be specified
- At the field type "single-line input field" and "multi-line input field" the maximum number of characters can now also be specified

# 1.1.3
- The saving of the custom fields now only consider product line items

# 1.1.2
- Adaptation of the own controller for Symfony 6.0

# 1.1.1
- Adaptation of the version of the require components

# 1.1.0
- Revision of the output of the customer inputs at the products for line items in the administration for Shopware *******

# 1.0.0
- Initial release of the app
