# 1.2.2
- <PERSON><PERSON> wird nun ver<PERSON><PERSON><PERSON>, dass eine JavaScript-Warnmeldung ausgegeben wird

# 1.2.1
- die Beschreibungen wurden aktualisiert
- Man kann nun auswählen, ob die Schaltfläche Bestellung wiederholen angezeigt werden soll
- Man kann nun auswählen, ob bei der Funktion Bestellung wiederholen die Kundeneingaben übernommen werden sollen

# 1.2.0
- Kompatibilität zu Shopware ab Version ******* hergestellt

# 1.1.11
- Kompatibilität zu Shopware ab Version ******** hergestellt

# 1.1.10
- Die Annotations beim Controller wurden durch Route defaults ersetzt

# 1.1.9
- Man kann nun auswählen, ob die Eingabetaste bei den Feldtypen einzeiliges Eingabefeld, Nummernfeld, Datums- und Uhrzeitfeld, Datumsfeld und Uhrzeitfeld blockiert werden soll

# 1.1.8
- be<PERSON><PERSON> "<PERSON>ummer<PERSON>feld" werden nun beim <PERSON> "Schrittweite für das Feld" auch Schrittweiten mit Kommazahlen unterstützt

# 1.1.7
- Kompatibilität zu Shopware ab Version ******** hergestellt

# 1.1.6
- Man kann nun auswählen, ob die Produkteingabe im OffCanvas-Warenkorb angezeigt werden soll
- Man kann nun auswählen, ob ein Trennstrich zwischen Produkt und Produkteingabe im Checkout angezeigt werden soll
- Man kann nun auswählen, ob die Produkteingabe in einem aufklappbaren Bereich im großen Warenkorb und auf der Bestellabschlussseite angezeigt werden soll
- Man kann nun auswählen, ob Pflichtfelder im Warenkorb änderbar sind (das Abfangen des Absendens des Formulars ist aber nicht möglich)
- Man kann nun auswählen, ob Pflichtfelder auf der Bestellabschlussseite änderbar sind
- Man kann nun auswählen, ob leere Pflichtfelder im Checkout gespeichert werden sollen

# 1.1.5
- Man kann nun auswählen, ob eine Details-Schaltfläche in den Kategorielisten anzeigt werden soll, wenn eine Produkteingabe bei einem Produkt aktiviert wurde
- Man kann nun auswählen, ob unter den Produkteingaben eine Hinweismeldung angezeigt werden soll, beispielsweise mit der Beschreibung des Verhaltens beim erneuten Legen des Produkts in den Warenkorb
- Man kann nun auswählen, ob ein sprachabhängiger Kalender anhand der Lokalisierung verwendet werden soll

# 1.1.4
- Es gibt nun auch den Feldtyp "Nummernfeld"
- Beim Feldtyp "Nummernfeld" kann zusätzlich ein Mindestwert, ein Maximalwert und die Schrittweite festgelegt werden
- Beim Feldtyp "einzeiliges Eingabefeld" und "mehrzeiliges Eingabefeld" kann nun zusätzlich die maximale Anzahl an Zeichen festgelegt werden

# 1.1.3
- Das Speichern der Custom Fields berücksichtigt jetzt nur noch Produkte

# 1.1.2
- Anpassung des eigenen Controllers für Symfony 6.0

# 1.1.1
- Anpassung der Version der require-Komponenten

# 1.1.0
- Überarbeitung der Ausgabe der Kundeneingaben bei den Produkten bei den Bestellpositionen in der Administration für Shopware 6.4.2.0

# 1.0.0
- Erstveröffentlichung der App
