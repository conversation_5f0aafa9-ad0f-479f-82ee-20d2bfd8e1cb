import './page/bs-export-data-index';
import './service/exportDataServiceApi';

import enGB from "./snippet/en-GB";

const { Module } = Shopware;

Module.register('bs-export-data-list', {
    type: 'plugin',
    name: 'bs-export-data',
    title: 'bs-export-data-list.general.mainMenuItem',
    description: 'bs-export-data-list.general.descriptionText',
    version: '1.0.0',
    targetVersion: '1.0.0',
    color: '#9AA8B5',
    icon: 'default-badge-help',

    snippets: {
        'en-GB': enGB
    },

    routes: {
        index: {
            component: 'bs-export-data-index',
            path: 'index',
            icon: 'default-badge-help',
            meta: {
                parentPath: 'sw.settings.index'
            }
        }
    },

    settingsItem: [
        {
            name:   'bs-export-data-list-index',
            to:     'bs.export.data.list.index',
            label:  'bs-export-data-list.general.mainMenuItem',
            group:  'plugins',
            icon:   'default-action-circle-download'
        }
    ]
});
