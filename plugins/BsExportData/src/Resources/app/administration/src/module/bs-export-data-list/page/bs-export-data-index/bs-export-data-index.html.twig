{% block bs_export_data_index %}
    <sw-page class="bs-export-data-index sw-settings-index">
    
        <template slot="smart-bar-header">
            <h2>Export Data</h2>
        </template>

        <template slot="content">
            <sw-card class="export-data-index-fields" :isLoading="isLoading" :isEditing="isEditing">
                <sw-entity-single-select
                    :label="'Saleschannel'"
                    size="medium"
                    entity="sales_channel"
                    v-model="salesChannelId"
                    >
                </sw-entity-single-select>
                <sw-button variant="primary" class="bs-download-btn" size="small" v-if="downloadLink" @click="downloadFile()">
                    Download
                </sw-button>
            </sw-card>
        </template>

        <template slot="smart-bar-actions">
            <sw-button variant="primary" @click="exportData()" :isLoading="isLoading" :isEditing="isEditing">
                Export
            </sw-button>
        </template>
    </sw-page>
{% endblock %}
