<?php declare(strict_types=1);

namespace Acris\CustomerGroupAvailableProduct\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\InheritanceUpdaterTrait;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1655270945 extends MigrationStep
{
    use InheritanceUpdaterTrait;

    public function getCreationTimestamp(): int
    {
        return 1655270945;
    }

    public function update(Connection $connection): void
    {
        try {
            $connection->executeStatement('ALTER TABLE `product` DROP COLUMN `customerGroup`');
        } catch (\Throwable $e) {}

        $this->updateCustomInheritance($connection, 'product', 'acrisReleaseCustomerGroup');
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }

    protected function updateCustomInheritance(Connection $connection, string $entity, string $propertyName): void
    {
        $sql = str_replace(
            ['#table#', '#column#'],
            [$entity, $propertyName],
            'ALTER TABLE `#table#` ADD COLUMN `#column#` binary(16) NULL'
        );

        $connection->executeStatement($sql);
    }
}
