{% block sw_product_detail_base_basic_info_card %}
    {% parent %}

    {% block acris_customer_group_available_product_customer_group_card %}
        <sw-card :isLoading="isLoading" v-if="product && product.extensions && !hasParent"
                 :title="$tc('acris-product-customer-group.availableProductsCard')"
                 class="acris-product-customer-group">
            {% block acris_customer_group_available_product_many_to_many_select %}

{#                <acris-entity-many-to-many-select#}
{#                    :localMode="true"#}
{#                    v-model="product.extensions.acrisReleaseCustomerGroup"#}
{#                    :label="$tc('acris-product-customer-group.fieldLabelAvailableProducts')"#}
{#                    :placeholder="$tc('acris-product-customer-group.fieldPlaceholderAvailableProducts')"#}
{#                    :helpText="$tc('acris-product-customer-group.fieldHelpTextAvailableProducts')">#}
{#                </acris-entity-many-to-many-select>#}

                <sw-inherit-wrapper v-model="product.extensions.acrisReleaseCustomerGroup"
                                    :hasParent="hasParent"
                                    isAssociation>
                    <template #content="{ currentValue, isInherited, updateCurrentValue }">
                        <acris-entity-many-to-many-select
                            :localMode="true"
                            :entityCollection="currentValue"
                            :disabled="isInherited"
                            :key="isInherited"
                            :label="$tc('acris-product-customer-group.fieldLabelAvailableProducts')"
                            :placeholder="$tc('acris-product-customer-group.fieldPlaceholderAvailableProducts')"
                            @change="updateCurrentValue"
                            :helpText="$tc('acris-product-customer-group.fieldHelpTextAvailableProducts')">
                        </acris-entity-many-to-many-select>
                    </template>
                </sw-inherit-wrapper>
            {% endblock %}

            {% block acris_customer_group_available_product_exclude_sitemap_exclude %}
                <sw-checkbox-field :label="$tc('acris-product-customer-group.fieldLabelExcludeSitemap')"
                                   @change="onChangeExcludeSitemapCheckbox"
                                   v-model="acris_customer_group_available_product_exclude_sitemap">
                </sw-checkbox-field>
            {% endblock %}
        </sw-card>
    {% endblock %}
{% endblock %}
