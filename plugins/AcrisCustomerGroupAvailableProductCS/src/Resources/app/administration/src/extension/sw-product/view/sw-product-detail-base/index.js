import template from './sw-product-detail-base.html.twig';

const { Mixin } = Shopware;
const { Component } = Shopware;

Component.override('sw-product-detail-base', {
    template,

    data() {
        return {
            acris_customer_group_available_product_exclude_sitemap: false
        };
    },

    mixins: [
        Mixin.getByName('notification'),
        Mixin.getByName('placeholder')
    ],

    updated() {
        this.refreshSitemapCheckboxValue();
    },

    computed: {
        hasParent() {
            return !!this.parentProduct.id;
        }
    },

    methods: {
        createdComponent() {
            this.$super('createdComponent');
            this.refreshSitemapCheckboxValue();
        },

        refreshSitemapCheckboxValue() {
            if(this.product.customFields != null) {
                this.acris_customer_group_available_product_exclude_sitemap = this.product.customFields.acris_customer_group_available_product_exclude_sitemap;
            } else {
                this.acris_customer_group_available_product_exclude_sitemap = false;
            }
        },

        onChangeExcludeSitemapCheckbox() {
            this.createDefaultCustomFieldsIfNotExists();
            this.product.customFields = {
                acris_customer_group_available_product_exclude_sitemap: this.acris_customer_group_available_product_exclude_sitemap
            };
        },

        createDefaultCustomFieldsIfNotExists() {
            if(this.product.customFields == null) {
                this.product.customFields = {
                    acris_customer_group_available_product_exclude_sitemap: false
                };
            }
        }
    }
});
