<?php declare(strict_types=1);

namespace Acris\CustomerGroupAvailableProduct\Core\Content\Product\SalesChannel;

use Acris\CustomerGroupAvailableProduct\Components\DisplayAllowedProductService;
use Shopware\Core\Framework\DataAbstractionLayer\Exception\InconsistentCriteriaIdsException;
use Shopware\Core\Framework\DataAbstractionLayer\Search\AggregationResult\AggregationResultCollection;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;

class SalesChannelProductRepository extends SalesChannelRepository
{
    private SalesChannelRepository $parent;

    private DisplayAllowedProductService $displayProductService;

    private SystemConfigService $configService;

    public function __construct(
        SalesChannelRepository $parent,
        DisplayAllowedProductService $displayProductService,
        SystemConfigService $configService
    ) {
        $this->parent = $parent;
        $this->displayProductService = $displayProductService;
        $this->configService = $configService;
    }

    /**
     * @throws InconsistentCriteriaIdsException
     */
    public function search(Criteria $criteria, SalesChannelContext $salesChannelContext): EntitySearchResult
    {
        $this->releaseProductsForCustomerGroup($criteria, $salesChannelContext);

        return $this->parent->search($criteria, $salesChannelContext);
    }

    public function aggregate(Criteria $criteria, SalesChannelContext $salesChannelContext): AggregationResultCollection
    {
        return $this->parent->aggregate($criteria, $salesChannelContext);
    }

    public function searchIds(Criteria $criteria, SalesChannelContext $salesChannelContext): IdSearchResult
    {
        $this->releaseProductsForCustomerGroup($criteria, $salesChannelContext);

        return $this->parent->searchIds($criteria, $salesChannelContext);
    }

    private function releaseProductsForCustomerGroup(Criteria $criteria, SalesChannelContext $salesChannelContext): void
    {
        $displayProductIds = $this->displayProductService->getDisplayProductIdsForCustomerGroupId($salesChannelContext->getCurrentCustomerGroup()->getId(), $salesChannelContext->getContext());
        $filters = [];

        if (empty($displayProductIds) === true) {
            $filter = new EqualsFilter('id', Uuid::randomHex());
        }  else {
            $filter = new MultiFilter(MultiFilter::CONNECTION_OR, [
                new MultiFilter(MultiFilter::CONNECTION_AND, [
                    new EqualsAnyFilter('id', $displayProductIds),
                    new EqualsFilter('parentId', null),
                ]),
                new MultiFilter(MultiFilter::CONNECTION_AND, [
                    new EqualsAnyFilter('id', $displayProductIds),
                    new EqualsAnyFilter('parentId', $displayProductIds),
                ])
            ]);
        }

        $filters[] = $filter;

        if($this->configService->get('AcrisCustomerGroupAvailableProductCS.config.releaseProductsIfNoCustomerGroupAssigned', $salesChannelContext->getSalesChannel()->getId()) === DisplayAllowedProductService::DEFAULT_PLUGIN_CONFIG_RELEASE_PRODUCT_IF_NO_CUSTOMER_GROUPS_ASSIGNED) {
            $criteria->addAssociation('product.acrisReleaseCustomerGroup');
            $filters[] = new EqualsFilter('product.acrisReleaseCustomerGroup.id', null);
        }

        $criteria->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, $filters));
    }
}
