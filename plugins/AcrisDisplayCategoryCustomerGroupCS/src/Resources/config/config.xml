<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>Basic Configuration</title>
        <title lang="de-DE">Grundeinstellungen</title>

        <input-field type="bool">
            <name>releaseAssignedProductsForOtherCategories</name>
            <copyable>true</copyable>
            <label>Release only products in listing if they are assigned to a released category</label>
            <label lang="de-DE">Nur Produkte in der Auflistung freigeben, wenn sie einer freigegebenen Kategorie zugeordnet sind</label>
            <helpText>If products are assigned to a released category, they will be displayed at category product listing.<![CDATA[<br><br><strong>Note: </strong>If this option is not active then the all products will be displayed in the listing. (Shopware default)]]></helpText>
            <helpText lang="de-DE">Wenn Produkte mindestens einer freigegebenen Kategorie zugeordnet sind, werden sie auch in allen anderen freigegebenen Kategorien berücksichtigt.<![CDATA[<br><br><strong>Hinweis: </strong>Wenn diese Option nicht aktiv ist, werden alle Produkte in der Liste angezeigt. (Shopware-Standard)]]></helpText>
            <defaultValue>true</defaultValue>
        </input-field>

        <input-field type="bool">
            <name>releaseAssignedProductsForSearch</name>
            <copyable>true</copyable>
            <label>Release only products in search if they are assigned to a released category</label>
            <label lang="de-DE">Nur Produkte in der Suche freigeben, wenn sie einer freigegebenen Kategorie zugeordnet sind</label>
            <helpText>If products are assigned to at least one released category, they are included in the search.<![CDATA[<br><br><strong>Note: </strong>If this option is not active then the all products will be displayed in the search. (Shopware default)]]></helpText>
            <helpText lang="de-DE">Wenn Produkte mindestens einer freigegebenen Kategorie zugeordnet sind, werden sie bei der Suche berücksichtigt.<![CDATA[<br><br><strong>Hinweis: </strong>Wenn diese Option nicht aktiviert ist, werden alle Produkte in der Suche angezeigt. (Shopware-Standard)]]></helpText>
            <defaultValue>true</defaultValue>
        </input-field>

        <input-field type="single-select">
            <name>releaseCategoriesIfNoCustomerGroupAssigned</name>
            <label>Behaviour when no client group has been selected for release</label>
            <label lang="de-DE">Verhalten wenn keine Kundengruppe zur Freigabe ausgewählt wurde</label>
            <helpText>If there are no customer groups assigned for the category, then the category will be released in Storefront.</helpText>
            <helpText lang="de-DE">Wenn der Kategorie keine Kundengruppen zugeordnet sind, wird die Kategorie in Storefront freigegeben.</helpText>
            <defaultValue>default</defaultValue>
            <options>
                <option>
                    <id>default</id>
                    <name>ACRIS Standard (release of categories only if customer group is entered in designated field)</name>
                    <name lang="de-DE">ACRIS Standard (Freigabe der Kategorien nur wenn Kundengruppe in vorgesehenem Feld eingetragen ist)</name>
                </option>
                <option>
                    <id>unlockIfEmpty</id>
                    <name>Release without customer group assignment (If no category is entered in the intended field, then automatically release for ALL customer groups)</name>
                    <name lang="de-DE">Freigabe ohne Kundengruppenzuordnung (Wenn keine Kategorie in vorgesehenem Feld eingetragen ist, dann für ALLE Kundengruppen automatisch freigeben)</name>
                </option>
            </options>
        </input-field>
    </card>
</config>
