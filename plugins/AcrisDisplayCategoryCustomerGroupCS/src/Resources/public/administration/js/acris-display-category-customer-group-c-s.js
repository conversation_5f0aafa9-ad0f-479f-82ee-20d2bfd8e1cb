!function(e){var t={};function r(o){if(t[o])return t[o].exports;var a=t[o]={i:o,l:!1,exports:{}};return e[o].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.m=e,r.c=t,r.d=function(e,t,o){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(o,a,function(t){return e[t]}.bind(null,a));return o},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/bundles/acrisdisplaycategorycustomergroupcs/",r(r.s="WDjd")}({"+lGi":function(e,t){Shopware.Component.extend("acris-entity-many-to-many-select","sw-entity-many-to-many-select",{updated:function(){this.updatedComponent()},methods:{updatedComponent:function(){this.initData()}}})},UtUC:function(e,t){Shopware.Component.override("sw-category-detail",{computed:{categoryCriteria:function(){var e=this.$super("categoryCriteria");return e.addAssociation("acrisDisplayCategoryCustomerGroupCustomerGroup"),e}}})},WDjd:function(e,t,r){"use strict";r.r(t);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){c(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function c(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var n=Shopware.Component.getComponentHelper().mapState,i=Shopware.Mixin;Shopware.Component.override("sw-category-detail-base",{template:'{% block sw_category_detail_menu %}\n    {% parent %}\n\n    {% block acris_display_category_customer_group_customer_group_card %}\n        <sw-card :isLoading="isLoading" v-if="category && (category.type === \'page\' || category.type === \'folder\')"\n                 :title="$tc(\'acris-display-category-customer-group.cardCustomerGroup\')"\n                 class="acris-display-category-customer-group">\n\n            {% block acris_display_category_customer_group_many_to_many_select %}\n            <acris-entity-many-to-many-select\n                :localMode="true"\n                :placeholder="$tc(\'acris-display-category-customer-group.fieldPlaceholderCustomerGroup\')"\n                v-model="category.extensions.acrisDisplayCategoryCustomerGroupCustomerGroup"\n                :valueLimit="50">\n            </acris-entity-many-to-many-select>\n            {% endblock %}\n\n            {% block acris_display_category_customer_group_exclude_sitemap_exclude %}\n            <sw-checkbox-field :label="$tc(\'acris-display-category-customer-group.fieldLabelExcludeSitemap\')"\n                               @change="onChangeExcludeSitemapCheckbox"\n                               v-model="acris_display_category_customer_group_exclude_sitemap">\n            </sw-checkbox-field>\n            {% endblock %}\n        </sw-card>\n    {% endblock %}\n{% endblock %}\n\n{% block sw_category_detail_link %}\n    {% parent %}\n\n    {% block acris_display_category_customer_group_customer_group_card_link %}\n        <sw-card :isLoading="isLoading" v-if="category && category.type === \'link\'"\n                 :title="$tc(\'acris-display-category-customer-group.cardCustomerGroup\')"\n                 class="acris-display-category-customer-group">\n\n            {% block acris_display_category_customer_group_many_to_many_select_link %}\n                <acris-entity-many-to-many-select\n                    :localMode="true"\n                    :placeholder="$tc(\'acris-display-category-customer-group.fieldPlaceholderCustomerGroup\')"\n                    v-model="category.extensions.acrisDisplayCategoryCustomerGroupCustomerGroup"\n                :valueLimit="50">\n                </acris-entity-many-to-many-select>\n            {% endblock %}\n\n            {% block acris_display_category_customer_group_exclude_sitemap_exclude_link %}\n                <sw-checkbox-field :label="$tc(\'acris-display-category-customer-group.fieldLabelExcludeSitemap\')"\n                                   @change="onChangeExcludeSitemapCheckbox"\n                                   v-model="acris_display_category_customer_group_exclude_sitemap">\n                </sw-checkbox-field>\n            {% endblock %}\n        </sw-card>\n    {% endblock %}\n{% endblock %}\n',inject:["repositoryFactory","context"],data:function(){return{acris_display_category_customer_group_exclude_sitemap:!1}},mixins:[i.getByName("notification"),i.getByName("placeholder")],computed:a(a({},n("swCategoryDetail",["category"])),{},{category:function(){return Shopware.State.get("swCategoryDetail").category},categoryRepository:function(){return this.repositoryFactory.create("category")},identifier:function(){return this.placeholder(this.item,"id")}}),metaInfo:function(){return{title:this.$createTitle(this.identifier)}},created:function(){this.refreshSitemapCheckboxValue()},updated:function(){this.refreshSitemapCheckboxValue()},methods:{refreshSitemapCheckboxValue:function(){null!=this.category.customFields?this.acris_display_category_customer_group_exclude_sitemap=this.category.customFields.acris_display_category_customer_group_exclude_sitemap:this.acris_display_category_customer_group_exclude_sitemap=null},onChangeExcludeSitemapCheckbox:function(){this.createDefaultCustomFieldsIfNotExists(),this.category.customFields={acris_display_category_customer_group_exclude_sitemap:this.acris_display_category_customer_group_exclude_sitemap}},createDefaultCustomFieldsIfNotExists:function(){null==this.category.customFields&&(this.category.customFields={acris_display_category_customer_group_exclude_sitemap:null})}}});r("UtUC"),r("+lGi");var s=Shopware.Component,l=Shopware.Data.Criteria;s.override("sw-settings-customer-group-detail",{template:'{% block sw_settings_customer_group_detail_content_card %}\r\n    {% parent %}\r\n\r\n    {% block acris_display_category_customer_group_detail_content_card %}\r\n        <sw-card :isLoading="isLoading" v-if="customerGroup && customerGroup.extensions"\r\n                 :title="$tc(\'acris-available-category-customer-group.availableCategoriesCard\')">\r\n\r\n            {% block acris_customer_group_available_product_many_to_many_select %}\r\n                <acris-entity-many-to-many-select\r\n                    :localMode="true"\r\n                    :label="$tc(\'acris-available-category-customer-group.fieldLabelAvailableCategories\')"\r\n                    :placeholder="$tc(\'acris-available-category-customer-group.fieldPlaceholderAvailableCategories\')"\r\n                    :helpText="$tc(\'acris-available-category-customer-group.fieldHelpTextAvailableCategories\')"\r\n                    v-model="customerGroup.extensions.acrisDisplayCategoryCustomerGroupCategory"\r\n                :valueLimit="50">\r\n                </acris-entity-many-to-many-select>\r\n            {% endblock %}\r\n        </sw-card>\r\n    {% endblock %}\r\n{% endblock %}\r\n',methods:{createdComponent:function(){this.parentCreatedComponent()},parentCreatedComponent:function(){var e=this;if(this.isLoading=!0,this.customerGroupId){this.loadSeoUrls(),this.loadCustomFieldSets();var t=new l;return t.addAssociation("registrationSalesChannels"),t.addAssociation("acrisDisplayCategoryCustomerGroupCategory"),void this.customerGroupRepository.get(this.customerGroupId,Shopware.Context.api,t).then((function(t){e.customerGroup=t,e.isLoading=!1}))}Shopware.State.commit("context/resetLanguageToDefault"),this.customerGroup=this.customerGroupRepository.create(),this.isLoading=!1}}})}});