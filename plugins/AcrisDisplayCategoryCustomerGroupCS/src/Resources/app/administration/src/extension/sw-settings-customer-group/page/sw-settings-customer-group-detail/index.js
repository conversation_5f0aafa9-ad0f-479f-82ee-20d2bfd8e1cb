import template from './acris-customer-group-detail.html.twig';

const { Component } = Shopware;
const { Criteria } = Shopware.Data;

Component.override('sw-settings-customer-group-detail', {
    template,

    methods: {
        createdComponent() {
            this.parentCreatedComponent();
        },

        parentCreatedComponent() {
            this.isLoading = true;
            if (this.customerGroupId) {
                this.loadSeoUrls();
                this.loadCustomFieldSets();
                const criteria = new Criteria();
                criteria.addAssociation('registrationSalesChannels');
                criteria.addAssociation('acrisDisplayCategoryCustomerGroupCategory');

                this.customerGroupRepository.get(this.customerGroupId, Shopware.Context.api, criteria)
                    .then((customerGroup) => {
                        this.customerGroup = customerGroup;
                        this.isLoading = false;
                    });
                return;
            }

            Shopware.State.commit('context/resetLanguageToDefault');
            this.customerGroup = this.customerGroupRepository.create();
            this.isLoading = false;
        }
    }
});
