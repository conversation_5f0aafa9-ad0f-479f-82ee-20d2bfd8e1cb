{% block sw_settings_customer_group_detail_content_card %}
    {% parent %}

    {% block acris_display_category_customer_group_detail_content_card %}
        <sw-card :isLoading="isLoading" v-if="customerGroup && customerGroup.extensions"
                 :title="$tc('acris-available-category-customer-group.availableCategoriesCard')">

            {% block acris_customer_group_available_product_many_to_many_select %}
                <acris-entity-many-to-many-select
                    :localMode="true"
                    :label="$tc('acris-available-category-customer-group.fieldLabelAvailableCategories')"
                    :placeholder="$tc('acris-available-category-customer-group.fieldPlaceholderAvailableCategories')"
                    :helpText="$tc('acris-available-category-customer-group.fieldHelpTextAvailableCategories')"
                    v-model="customerGroup.extensions.acrisDisplayCategoryCustomerGroupCategory"
                :valueLimit="50">
                </acris-entity-many-to-many-select>
            {% endblock %}
        </sw-card>
    {% endblock %}
{% endblock %}
