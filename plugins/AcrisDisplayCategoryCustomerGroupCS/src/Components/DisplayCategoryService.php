<?php declare(strict_types=1);

namespace Acris\DisplayCategoryCustomerGroup\Components;

use Shopware\Core\Content\Category\Tree\TreeItem;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NotFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;

class DisplayCategoryService
{
    public const DEFAULT_PLUGIN_CONFIG_RELEASE_CATEGORY_IF_NO_CUSTOMER_GROUPS_ASSIGNED = 'unlockIfEmpty';
    /**
     * @var EntityRepository
     */
    private $categoryCustomerGroupRepository;

    /**
     * @var array
     */
    private $displayedCategoryIds;

    /**
     * @var array
     */
    private $categoryIdsAnyOtherCustomerGroupAssigned;

    /**
     * @var null|bool
     */
    private $checkNoCustomerGroupAssigned;

    /**
     * @var SystemConfigService
     */
    private $configService;

    public function __construct(EntityRepository $categoryCustomerGroupRepository, SystemConfigService $configService)
    {
        $this->categoryCustomerGroupRepository = $categoryCustomerGroupRepository;
        $this->displayedCategoryIds = [];
        $this->categoryIdsAnyOtherCustomerGroupAssigned = [];
        $this->checkNoCustomerGroupAssigned = null;
        $this->configService = $configService;
    }

    public function isCategoryAllowedForCustomerGroup(string $categoryId, SalesChannelContext $salesChannelContext)
    {
        $customerGroupId = $salesChannelContext->getCurrentCustomerGroup()->getId();
        $allowedCategoryIds = $this->getDisplayedCategoryIdsForCustomerGroupId($customerGroupId, $salesChannelContext->getContext());

        $checkNoCustomerGroupAssigned = $this->isCheckNoCustomerGroupAssigned($salesChannelContext->getSalesChannel()->getId());
        $categoryIdsAnyOtherCustomerGroup = [];
        if($checkNoCustomerGroupAssigned === true) {
            $categoryIdsAnyOtherCustomerGroup = $this->getCategoryIdsAnyOtherCustomerGroupAssigned($customerGroupId, $salesChannelContext->getContext());
        }

        return in_array($categoryId, $allowedCategoryIds) === true
            || ($checkNoCustomerGroupAssigned === true && in_array($categoryId, $categoryIdsAnyOtherCustomerGroup) === false);
    }


    public function getDisplayedCategoryIdsForCustomerGroupId(?string $customerGroupId, Context $context): array
    {
        if(empty($customerGroupId)) {
            return [];
        }

        if(array_key_exists($customerGroupId, $this->displayedCategoryIds) && $this->displayedCategoryIds[$customerGroupId]) {
            return $this->displayedCategoryIds[$customerGroupId];
        }

        /** @var IdSearchResult $categoryIdSearch */
        $displayedCategoryIdsResult = $this->categoryCustomerGroupRepository->searchIds((new Criteria())->addFilter(new EqualsFilter('customerGroupId', $customerGroupId)), $context);

        if(empty($displayedCategoryIdsResult->getIds())) {
            return [];
        }

        $displayedCategoryIds = [];
        foreach ($displayedCategoryIdsResult->getIds() as $customerGroupCategory) {
            array_push($displayedCategoryIds, $customerGroupCategory['categoryId']);
        }

        $this->displayedCategoryIds[$customerGroupId] = $displayedCategoryIds;
        return $displayedCategoryIds;
    }

    public function getCategoryIdsAnyOtherCustomerGroupAssigned(?string $customerGroupId, Context $context): array
    {
        if(empty($customerGroupId)) {
            return [];
        }

        if(array_key_exists($customerGroupId, $this->categoryIdsAnyOtherCustomerGroupAssigned) && $this->categoryIdsAnyOtherCustomerGroupAssigned[$customerGroupId]) {
            return $this->categoryIdsAnyOtherCustomerGroupAssigned[$customerGroupId];
        }

        /** @var IdSearchResult $categoryIdsAnyOtherCustomerGroupResult */
        $categoryIdsAnyOtherCustomerGroupResult = $this->categoryCustomerGroupRepository->searchIds((new Criteria())->addFilter(new NotFilter(NotFilter::CONNECTION_AND, [new EqualsFilter('customerGroupId', $customerGroupId)])), $context);

        if(empty($categoryIdsAnyOtherCustomerGroupResult->getIds())) {
            return [];
        }

        $categoryIdsAnyOtherCustomerGroup = [];
        foreach ($categoryIdsAnyOtherCustomerGroupResult->getIds() as $customerGroupCategory) {
            array_push($categoryIdsAnyOtherCustomerGroup, $customerGroupCategory['categoryId']);
        }

        $this->categoryIdsAnyOtherCustomerGroupAssigned[$customerGroupId] = $categoryIdsAnyOtherCustomerGroup;
        return $categoryIdsAnyOtherCustomerGroup;
    }

    public function displayCategory(TreeItem $treeItem, SalesChannelContext $salesChannelContext)
    {
        if($this->isCategoryAllowedForCustomerGroup($treeItem->getCategory()->getId(), $salesChannelContext) === false) {
            return false;
        }

        $convertedTreeItems = [];
        foreach ($treeItem->getChildren() as $childTreeItem) {
            $childTreeItem = $this->displayCategory($childTreeItem, $salesChannelContext);
            if($childTreeItem === false) {
                continue;
            }
            array_push($convertedTreeItems, $childTreeItem);
        }
        $treeItem->setChildren($convertedTreeItems);
        return $treeItem;
    }

    private function isCheckNoCustomerGroupAssigned(string $salesChannelId): bool
    {
        if(is_bool($this->checkNoCustomerGroupAssigned)) {
            return $this->checkNoCustomerGroupAssigned;
        }
        return $this->configService->get('AcrisDisplayCategoryCustomerGroupCS.config.releaseCategoriesIfNoCustomerGroupAssigned', $salesChannelId) === DisplayCategoryService::DEFAULT_PLUGIN_CONFIG_RELEASE_CATEGORY_IF_NO_CUSTOMER_GROUPS_ASSIGNED;
    }
}
