<?php declare(strict_types=1);

namespace Acris\DisplayCategoryCustomerGroup\Storefront\Subscriber;

use Shopware\Core\Content\Product\Events\ProductSearchCriteriaEvent;
use Shopware\Core\Content\Product\Events\ProductSuggestCriteriaEvent;
use Shopware\Core\Framework\Struct\ArrayEntity;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ProductListingFeaturesSubscriber implements EventSubscriberInterface
{
    public const HIDE_SEARCH_AND_SUGGEST = 'acrisDisplayCategoryCustomerGroupSearchAndSuggest';

    public static function getSubscribedEvents()
    {
        return [
            ProductSuggestCriteriaEvent::class => 'onProductSuggestCriteria',
            ProductSearchCriteriaEvent::class => 'onProductSearchCriteria'
        ];
    }

    public function onProductSuggestCriteria(ProductSuggestCriteriaEvent $event): void
    {
        $event->getSalesChannelContext()->addExtension(self::HIDE_SEARCH_AND_SUGGEST, new ArrayEntity([]));
    }

    public function onProductSearchCriteria(ProductSearchCriteriaEvent $event): void
    {
        $event->getSalesChannelContext()->addExtension(self::HIDE_SEARCH_AND_SUGGEST, new ArrayEntity([]));
    }
}
