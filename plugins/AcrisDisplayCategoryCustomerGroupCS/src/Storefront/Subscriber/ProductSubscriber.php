<?php declare(strict_types=1);

namespace Acris\DisplayCategoryCustomerGroup\Storefront\Subscriber;

use A<PERSON>ris\DisplayCategoryCustomerGroup\Components\DisplayCategoryService;
use Shopware\Core\Content\Category\Event\CategoryRouteCacheKeyEvent;
use Shopware\Core\Content\Category\Event\NavigationRouteCacheKeyEvent;
use Shopware\Core\Content\Product\Events\ProductListingRouteCacheKeyEvent;
use Shopware\Core\Framework\Adapter\Cache\StoreApiRouteCacheKeyEvent;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Page\Product\ProductPageCriteriaEvent;
use Shopware\Storefront\Page\Product\ProductPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ProductSubscriber implements EventSubscriberInterface
{
    /**
     * @var DisplayCategoryService
     */
    private $displayCategoryService;

    /**
     * @var SystemConfigService
     */
    private $configService;

    public function __construct(DisplayCategoryService $displayCategoryService, SystemConfigService $configService)
    {
        $this->displayCategoryService = $displayCategoryService;
        $this->configService = $configService;
    }

    public static function getSubscribedEvents(): array
    {
        return[
            ProductPageLoadedEvent::class => 'onProductLoaded',
            ProductPageCriteriaEvent::class => 'onProductLoaderCriteria',
            ProductListingRouteCacheKeyEvent::class => 'onStoreApiRouteCacheKey',
            NavigationRouteCacheKeyEvent::class => 'onStoreApiRouteCacheKey',
            CategoryRouteCacheKeyEvent::class => 'onStoreApiRouteCacheKey'
        ];
    }

    public function onProductLoaderCriteria(ProductPageCriteriaEvent $event): void
    {
        $event->getCriteria()->addAssociation('categories');
    }

    public function onProductLoaded(ProductPageLoadedEvent $event): void
    {
        if(!$this->configService->get('AcrisDisplayCategoryCustomerGroupCS.config.releaseAssignedProductsForOtherCategories', $event->getSalesChannelContext()->getSalesChannel()->getId())) {
            return;
        }

        $product = $event->getPage()->getProduct();
        if(empty($product)) {
            return;
        }

        if (!empty($product->getCategories()) && $product->getCategories()->count() > 0) {
            $categoryReleased = false;
            foreach ($product->getCategories()->getElements() as $key => $category) {
                if($this->displayCategoryService->isCategoryAllowedForCustomerGroup($category->getId(), $event->getSalesChannelContext()) === true) {
                    $categoryReleased = true;
                }
            }
            if ($categoryReleased === false) {
                throw new NotFoundHttpException();
            }
        } else {
            throw new NotFoundHttpException();
        }
    }

    public function onStoreApiRouteCacheKey(StoreApiRouteCacheKeyEvent $event): void
    {
        if (empty($event->getContext()) || empty($event->getContext()->getCurrentCustomerGroup()) || empty($event->getContext()->getCurrentCustomerGroup()->getId())) return;

        $event->addPart($event->getContext()->getCurrentCustomerGroup()->getId());
    }
}
