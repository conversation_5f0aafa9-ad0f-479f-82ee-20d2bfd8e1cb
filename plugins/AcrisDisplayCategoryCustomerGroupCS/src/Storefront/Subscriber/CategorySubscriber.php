<?php declare(strict_types=1);

namespace Acris\DisplayCategoryCustomerGroup\Storefront\Subscriber;

use A<PERSON>ris\DisplayCategoryCustomerGroup\Components\DisplayCategoryService;
use Shopware\Core\Content\Category\Event\NavigationLoadedEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Page\Navigation\NavigationPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CategorySubscriber implements EventSubscriberInterface
{
    public const ACRIS_SEARCH_CATEGORY_SEARCH_CRITERIA_EVENT = 'A<PERSON><PERSON>\Search\Components\ContentSearch\Events\CategorySearchCriteriaEvent';

    /**
     * @var DisplayCategoryService
     */
    private $displayCategoryService;

    /**
     * @var SystemConfigService
     */
    private $configService;

    public function __construct(DisplayCategoryService $displayCategoryService, SystemConfigService $configService)
    {
        $this->displayCategoryService = $displayCategoryService;
        $this->configService = $configService;
    }

    public static function getSubscribedEvents(): array
    {
        return[
            NavigationPageLoadedEvent::class => 'onNavigationPageLoaded',
            NavigationLoadedEvent::class => 'onNavigationLoaded',
            self::ACRIS_SEARCH_CATEGORY_SEARCH_CRITERIA_EVENT => 'onCategorySearchCriteria'
        ];
    }

    public function onNavigationPageLoaded(NavigationPageLoadedEvent $event)
    {
        $navigationId = $event->getRequest()->get('navigationId', $event->getSalesChannelContext()->getSalesChannel()->getNavigationCategoryId());
        if(empty($navigationId)) {
            return;
        }

        if(!$this->displayCategoryService->isCategoryAllowedForCustomerGroup($navigationId, $event->getSalesChannelContext())) {
            throw new NotFoundHttpException();
        }
    }

    public function onNavigationLoaded(NavigationLoadedEvent $event): void
    {
        $navigation = $event->getNavigation();
        $convertedTreeItems = [];

        foreach ($navigation->getTree() as $key => $treeItem) {
            if($this->displayCategoryService->displayCategory($treeItem, $event->getSalesChannelContext()) === false) {
                continue;
            }
            $convertedTreeItems[$key] = $treeItem;
        }
        $navigation->setTree($convertedTreeItems);
    }

    public function onCategorySearchCriteria($event): void
    {
        $displayedCategoryIds = $this->displayCategoryService->getDisplayedCategoryIdsForCustomerGroupId($event->getSalesChannelContext()->getCurrentCustomerGroup()->getId(), $event->getContext());

        $filters = [];

        if($this->configService->get('AcrisDisplayCategoryCustomerGroupCS.config.releaseCategoriesIfNoCustomerGroupAssigned', $event->getSalesChannelContext()->getSalesChannel()->getId()) === DisplayCategoryService::DEFAULT_PLUGIN_CONFIG_RELEASE_CATEGORY_IF_NO_CUSTOMER_GROUPS_ASSIGNED) {
            $event->getCriteria()->addAssociation('acrisDisplayCategoryCustomerGroupCustomerGroup');
            $filters[] = new EqualsFilter('acrisDisplayCategoryCustomerGroupCustomerGroup.id', null);
        }

        if ($displayedCategoryIds) {
            $filters[] = new EqualsAnyFilter('id', $displayedCategoryIds);
        }

        if (!empty($filters)) {
            $event->getCriteria()->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, $filters));
        }
    }
}
