<?php declare(strict_types=1);

namespace Acris\DisplayCategoryCustomerGroup\Core\Content\Product\SalesChannel;

use A<PERSON>ris\DisplayCategoryCustomerGroup\Components\DisplayCategoryService;
use A<PERSON>ris\DisplayCategoryCustomerGroup\Storefront\Subscriber\ProductListingFeaturesSubscriber;
use Shopware\Core\Framework\DataAbstractionLayer\Exception\InconsistentCriteriaIdsException;
use Shopware\Core\Framework\DataAbstractionLayer\Search\AggregationResult\AggregationResultCollection;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;

class SalesChannelProductRepository extends SalesChannelRepository
{
    private SalesChannelRepository $parent;

    private DisplayCategoryService $displayCategoryService;

    private SystemConfigService $configService;

    public function __construct(
        SalesChannelRepository $parent,
        DisplayCategoryService $displayCategoryService,
        SystemConfigService $configService
    ) {
        $this->parent = $parent;
        $this->displayCategoryService = $displayCategoryService;
        $this->configService = $configService;
    }

    /**
     * @throws InconsistentCriteriaIdsException
     */
    public function search(Criteria $criteria, SalesChannelContext $salesChannelContext): EntitySearchResult
    {
        $this->releaseProductsForCustomerGroup($criteria, $salesChannelContext);

        return $this->parent->search($criteria, $salesChannelContext);
    }

    public function aggregate(Criteria $criteria, SalesChannelContext $salesChannelContext): AggregationResultCollection
    {
        return $this->parent->aggregate($criteria, $salesChannelContext);
    }

    public function searchIds(Criteria $criteria, SalesChannelContext $salesChannelContext): IdSearchResult
    {
        $this->releaseProductsForCustomerGroup($criteria, $salesChannelContext);

        return $this->parent->searchIds($criteria, $salesChannelContext);
    }

    private function releaseProductsForCustomerGroup(Criteria $criteria, SalesChannelContext $salesChannelContext): void
    {
        if (!$this->hideProducts($salesChannelContext)) return;

        $releasedCategoryIds = $this->displayCategoryService->getDisplayedCategoryIdsForCustomerGroupId($salesChannelContext->getCurrentCustomerGroup()->getId(), $salesChannelContext->getContext());
        $filters = [];

        if(empty($releasedCategoryIds) === true) {
            $releasedCategoryIds = [Uuid::randomHex()];
        }

        if($this->configService->get('AcrisDisplayCategoryCustomerGroupCS.config.releaseCategoriesIfNoCustomerGroupAssigned', $salesChannelContext->getSalesChannel()->getId()) === DisplayCategoryService::DEFAULT_PLUGIN_CONFIG_RELEASE_CATEGORY_IF_NO_CUSTOMER_GROUPS_ASSIGNED) {
            $criteria->addAssociation('categories.acrisDisplayCategoryCustomerGroupCustomerGroup');

            $filters[] = new EqualsFilter('product.categories.acrisDisplayCategoryCustomerGroupCustomerGroup.id', null);
        }

        $filters[] = new EqualsAnyFilter('product.categories.id', $releasedCategoryIds);
        $criteria->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, $filters));
    }

    private function hideProducts(SalesChannelContext $salesChannelContext): bool
    {
        if ($salesChannelContext->hasExtension(ProductListingFeaturesSubscriber::HIDE_SEARCH_AND_SUGGEST)) {
            return $this->configService->get('AcrisDisplayCategoryCustomerGroupCS.config.releaseAssignedProductsForSearch', $salesChannelContext->getSalesChannel()->getId());
        }

        return $this->configService->get('AcrisDisplayCategoryCustomerGroupCS.config.releaseAssignedProductsForOtherCategories', $salesChannelContext->getSalesChannel()->getId());
    }
}
