# 5.1.1
- Improved compatibility with Shopware >= 6.5.

# 5.1.0
- Fixes a problem where the setting "Release without customer group assignment" did not work correctly with subcategories.
- Performance optimisations.
- Adds display category for the customer group field to the product elasticsearch mapping.

# 5.0.0
- Compatibility with Shopware 6.5.

# 4.0.2
- Improves plugin compatibility.

# 4.0.1
- Change of the plugin name and the manufacturer links.

# 4.0.0
- Optimizes displaying of the products which are assigned to the released category.

# 3.1.4
- Fixes a problem within the offcanvas navigation.

# 3.1.3
- Optimizes releasing of the products assigned to released categories for the cached product listing.

# 3.1.2
- Optimizes releasing of the products assigned to released categories in the Storefront.

# 3.1.1
- Improved compatibility with Elasticsearch.

# 3.1.0
- Adds plugin configuration for releasing a category in Storefront if there are no customer groups assigned for the category.

# 3.0.2
- Fixes problem on loading of the products in Cms element resolver.

# 3.0.1
- Increased customer group selection limit at category detail page in Administration.

# 3.0.0
- Improves plugin compatibility.
- Optimizes loading of the customer groups at category detail page in Administration.

# 2.0.2
- Fixes problem on loading of the landing pages in Administration.

# 2.0.1
- Optimize excluding from sitemap at category url provider.
- Display the customer group card at category link pages.

# 2.0.0
- Improved compatibility with Shopware 6.4*.

# 1.1.1
- Add validation on displaying products for released categories based on plugin configuration.

# 1.1.0
- Added configuration for displaying products of assigned categories.

# 1.0.0
- Release.
