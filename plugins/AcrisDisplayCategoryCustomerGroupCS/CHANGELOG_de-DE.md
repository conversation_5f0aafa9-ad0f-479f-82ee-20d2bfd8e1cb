# 5.1.1
- Verbesserte Kompatibilität mit Shopware >= 6.5.

# 5.1.0
- Behebt ein Problem bei dem die Einstellung "Freigabe ohne Kundengruppenzuordnung" mit Subkategorien nicht korrekt funktionierte.
- Performance Optimierungen.
- Fügt die Anzeigekategorie für das Kundengruppenfeld zur Produkt-Elasticsearch-Zuordnung hinzu.

# 5.0.0
- Kompatibilität mit Shopware 6.5.

# 4.0.2
- Verbessert die Plugin-Kompatibilität.

# 4.0.1
- Änderung des Pluginnamens und der Hersteller Links.

# 4.0.0
- Optimiert die Anzeige der Produkte, die der freigegebenen Kategorie zugeordnet sind.

# 3.1.4
- Behebt ein Problem innerhalb der Offcanvas-Navigation. 

# 3.1.3
- Optimiert die Freigabe der Produkte, die freigegebenen Kategorien zugeordnet sind, für die Produktliste im Cache.

# 3.1.2
- Optimiert die Freigabe der Produkte, die den freigegebenen Kategorien in der Storefront zugeordnet sind.

# 3.1.1
- Verbesserte Kompatibilität mit Elasticsearch.

# 3.1.0
- Fügt Plugin-Konfiguration für die Freigabe einer Kategorie in Storefront hinzu, wenn keine Kundengruppen für die Kategorie zugewiesen sind.

# 3.0.2
- Behebt das Problem beim Laden der Produkte im Cms Element Resolver.

# 3.0.1
- Das Limit für die Auswahl von Kundengruppen auf der Kategorie-Detailseite in der Verwaltung wurde erhöht.

# 3.0.0
- Verbessert die Plugin-Kompatibilität.
- Optimiert das Laden der Kundengruppen auf der Kategorie-Detailseite in der Administration.

# 2.0.2
- Behebt ein Problem beim Laden der Landing Pages in der Administration.

# 2.0.1
- Optimieren Sie das Ausschließen von Sitemaps bei Kategorie-URL-Anbietern.
- Anzeige der Kundengruppenkarte auf Kategorie-Link-Seiten.

# 2.0.0
- Kompatibilität mit Shopware 6.4* hergestellt.

# 1.1.1
- Hinzufügen einer Validierung bei der Anzeige von Produkten für freigegebene Kategorien basierend auf der Plugin-Konfiguration.

# 1.1.0
- Konfiguration für die Anzeige von Produkten der zugewiesenen Kategorien hinzugefügt.

# 1.0.0
- Veröffentlichung.
