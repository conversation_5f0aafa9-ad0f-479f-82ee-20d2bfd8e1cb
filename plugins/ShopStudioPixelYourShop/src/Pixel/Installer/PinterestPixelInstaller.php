<?php declare(strict_types=1);

namespace ShopStudio\PixelYourShop\Pixel\Installer;

use ShopStudio\PixelYourShop\Pixel\PixelEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Exception\InconsistentCriteriaIdsException;
use Shopware\Core\Framework\Uuid\Uuid;

/**
 * @since 1.0.0
 */
class PinterestPixelInstaller extends AbstractPixelInstaller
{
    /**
     * @since 1.0.0
     *
     * @inheritDoc
     *
     * @throws InconsistentCriteriaIdsException
     */
    public function install(string $salesChannelId, string $languageId): ?string
    {
        if ($this->isPixelInstalled(PixelEntity::NAME_PINTEREST, $salesChannelId, $languageId)) {
            return null;
        }

        $pixelId = Uuid::randomHex();

        $this->pixelRepository->create([
            [
                'id' => $pixelId,
                'name' => PixelEntity::NAME_PINTEREST,
                'salesChannelId' => $salesChannelId,
                'languageId' => $languageId,
                'version' => 1,
                'config' => [
                    'pinterestTagIds' => [],
                ],
                'optInActive' => false,
                'optInCode' => $this->pixelCodeReader->read(PixelEntity::NAME_PINTEREST, 'opt-in.html.twig'),
                'useCustomOptInCode' => false,
                'customOptInCode' => $this->pixelCodeReader->read(PixelEntity::NAME_PINTEREST, 'opt-in.html.twig'),
                'optInNoScriptCode' => $this->pixelCodeReader->read(PixelEntity::NAME_PINTEREST, 'opt-in-no-script.html.twig'),
                'useCustomOptInNoScriptCode' => false,
                'customOptInNoScriptCode' => $this->pixelCodeReader->read(PixelEntity::NAME_PINTEREST, 'opt-in-no-script.html.twig'),
            ],
        ], Context::createDefaultContext());

        $this->pixelEventRepository->create($this->buildPixelEvents(
            $pixelId,
            PixelEntity::NAME_PINTEREST,
            [
                ['login', 1],
                ['register', 1],
                ['view-product', 1],
                ['view-product-list', 1],
                ['search', 1],
                ['view-product-search', 1],
                ['add-to-cart', 1],
                ['purchase', 1],
                ['contact', 1],
            ]
        ), Context::createDefaultContext());

        return $pixelId;
    }
}
