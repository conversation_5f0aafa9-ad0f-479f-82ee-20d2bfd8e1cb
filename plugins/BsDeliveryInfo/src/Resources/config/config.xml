<?xml version="1.0" encoding="UTF-8"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/shopware/trunk/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>Minimal configuration</title>

        <input-field type="colorpicker">
            <name>localBgColor</name>
            <label>Local background color</label>
            <defaultValue>#d8f4db</defaultValue>
        </input-field>

        <input-field type="colorpicker">
            <name>localFontColor</name>
            <label>Local text font color</label>
            <defaultValue>#13bd42</defaultValue>
        </input-field>

        <input-field type="colorpicker">
            <name>remoteBgColor</name>
            <label>Remote background color</label>
            <defaultValue>#f6f5c0</defaultValue>
        </input-field>

        <input-field type="colorpicker">
            <name>remoteFontColor</name>
            <label>Remote text font color</label>
            <defaultValue>#f4aa4d</defaultValue>
        </input-field>
    </card>

</config>
