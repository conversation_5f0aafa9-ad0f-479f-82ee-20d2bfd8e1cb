<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartTranslation;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void                           add(SizeChartTranslationEntity $entity)
 * @method void                           set(string $key, SizeChartTranslationEntity $entity)
 * @method SizeChartTranslationEntity[]    getIterator()
 * @method SizeChartTranslationEntity[]    getElements()
 * @method SizeChartTranslationEntity|null get(string $key)
 * @method SizeChartTranslationEntity|null first()
 * @method SizeChartTranslationEntity|null last()
 */
class SizeChartTranslationCollection extends EntityCollection
{
    public function getSizeChartIds(): array
    {
        return $this->fmap(function (SizeChartTranslationEntity $sizeChartTranslation) {
            return $sizeChartTranslation->getSizeChartId();
        });
    }

    public function filterBySizeChartId(string $id): self
    {
        return $this->filter(function (SizeChartTranslationEntity $sizeChartTranslation) use ($id) {
            return $sizeChartTranslation->getSizeChartId() === $id;
        });
    }

    public function getLanguageIds(): array
    {
        return $this->fmap(function (SizeChartTranslationEntity $sizeChartTranslation) {
            return $sizeChartTranslation->getLanguageId();
        });
    }

    public function filterByLanguageId(string $id): self
    {
        return $this->filter(function (SizeChartTranslationEntity $sizeChartTranslation) use ($id) {
            return $sizeChartTranslation->getLanguageId() === $id;
        });
    }

    public function getApiAlias(): string
    {
        return 'flink_size_chart_translation_collection';
    }

    protected function getExpectedClass(): string
    {
        return SizeChartTranslationEntity::class;
    }
}
