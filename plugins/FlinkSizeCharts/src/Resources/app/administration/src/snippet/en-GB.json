{"flink-size-chart": {"general": {"mainMenuItemGeneral": "Size charts", "copy": "Copy", "pleaseSelect": "Please select"}, "buttons": {"addSizeChart": "Add size chart", "cancelButtonText": "Cancel", "saveButtonText": "Save"}, "list": {"columnName": "Name", "duplicate": "Duplicate"}, "detail": {"cardGeneral": "General", "cardSizeChart": "Size Chart", "cardTableLayout": "Chart layout", "cardText": "Text and image content", "cardPropertyGroup": "Filter by property", "addChartTab": "Add tab", "addChartRow": "Add row", "addChartColumn": "Add column", "deleteChartTab": "Delete tab", "deleteChartRow": "Delete row", "deleteChartColumn": "Delete column", "moveChartTabUp": "Move tab to the left", "moveChartTabDown": "Move tab to the right", "sortChartTabs": "Sort tabs alphabetically", "cardAssignment": "Size Chart Assignment", "assignViaCatetgories": "Assign by Category", "assignViaCatetgoryManufacturers": "Assign by Category and Manufacturer", "assignViaCatetgoriesHelp": "Size chart is displayed for products assigned to categories. Direct assignment to the product and assignment by category and manufacturer have higher weight for determining the chart to display.", "assignViaCatetgoryManufacturersHelp": "Size chart is displayed for products assigned to both the category and the manufacturer. Direct assignment to the product has higher weight for determining the chart to display.", "addCategoryManufacturer": "Add another line", "saveBeforeAssignment": "Size chart has to be saved before assignment.", "matchPropertyOptionHelp": "If a characteristic is selected here, the size table is only displayed for products that have this characteristic (e.g. for display for only one gender)", "assignViaProductStream": "Assign via dynamic product groups", "assignViaProductStreamHelp": "Size table is displayed for products that belong to at least one of the dynamic product groups. Direct assignment to the product has a higher weighting for the selection of the table to be displayed.", "assignViaProductStreamPlaceholder": "Select dynamic product groups"}, "properties": {"name": "Name", "displayName": "Display name", "tabName": "Tab name", "textBefore": "Text above chart", "textAfter": "Text below chart", "imageBefore": "Image before size chart (e.g: size explanation)", "sizeChart": "Size chart", "expandedTabs": "Display all tables one below the other in the frontend instead of as tabs", "tableHeadBackground": "Table head background color", "tableHeadColor": "Table head text color", "expandedTabTitleBackground": "Table title background color", "expandedTabTitleColor": "Table title text color", "inputItemHtml": "Formatierter Tabelleneintrag", "theadDark": "Dark table head", "tableDark": "Dark table", "tableSm": "Condensed table rows", "tableBordered": "Bordered table", "tableStriped": "Striped table", "tableHover": "Hover state on table rows", "tableStickyFirstColumn": "First table column sticky on scroll", "theadClasses": "Layout options for table head", "tableClasses": "Layout options for table", "assignedCategories": "Assigned categories", "category": "Category", "manufacturer": "Manufacturer", "mediaBefore": "Image above size table", "mediaAfter": "Image below size table", "matchPropertyOption": "Choose property option"}, "placeholder": {"expandedTabTitleBackground": "Choose color", "expandedTabTitleColor": "Choose color", "tableHeadBackground": "Choose color", "tableHeadColor": "Choose color"}, "validation": {"required": "This field is required."}, "notification": {"successTitle": "Success", "successText": "Size chart saved!", "errorTitle": "Error", "errorText": "Please fill in all required fields"}, "cms": {"sidebarItemLabel": "Größentabellen", "blocks": {"flinkSizeChart": {"label": "Größentabelle"}}, "elements": {"flinkSizeChart": {"label": "Größentabelle", "config": {"label": {"sizeChart": "Größentabelle"}, "placeholder": {"sizeChart": "Bitte auswählen"}, "help": {"sizeChart": "Die Größentabelle, welche angezeigt werden soll"}}}}}, "extension": {"product": {"customizeSizeChart": "Customize size chart", "sizeChartCustomized": "Size chart has been customized", "restoreTemplate": "Restore original size chart"}}}}