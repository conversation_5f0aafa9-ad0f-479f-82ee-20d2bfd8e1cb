{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/blocks/faq/component/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/blocks/faq/component/sw-cms-block-faq.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/blocks/faq/preview/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/blocks/faq/preview/sw-cms-preview-faq.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/blocks/faq/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/component/sw-cms-el-faqconfig.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/component/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/config/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/config/sw-cms-el-config-faqconfig.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/preview/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/preview/sw-cms-el-preview-faqconfig.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/main.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/preview/sw-cms-el-preview-faqconfig.scss", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/blocks/faq/preview/sw-cms-preview-faq.scss", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/blocks/faq/component/sw-cms-block-faq.scss", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/component/sw-cms-el-faqconfig.scss", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/mart1MwFaq/src/Resources/app/administration/src/module/sw-cms/elements/faqconfig/config/sw-cms-el-config-faqconfig.scss"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Shopware", "Component", "register", "template", "Service", "registerCmsBlock", "label", "category", "component", "previewComponent", "defaultConfig", "marginBottom", "marginTop", "marginLeft", "marginRight", "sizingMode", "slots", "content", "_Shopware", "Mixin", "mixins", "getByName", "computed", "customCatImageWidth", "this", "element", "config", "setCatImageWidth", "imageMode", "imageDisplay", "imageWidth", "customCatHeight", "setCustomCatHeight", "styles", "displayMode", "minHeight", "verticalAlign", "mediaStyles", "mediaUrl", "context", "Context", "api", "elemData", "data", "media", "source", "demoMedia", "getDemoValue", "url", "id", "concat", "assetsPath", "watch", "cmsPageState", "deep", "handler", "$forceUpdate", "created", "createdComponent", "methods", "initElementConfig", "initElementData", "_regeneratorRuntime", "Op", "hasOwn", "obj", "desc", "$Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "define", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "makeInvokeMethod", "tryCatch", "fn", "arg", "type", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "_typeof", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "state", "Error", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "undefined", "return", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "length", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "inject", "mediaModalIsOpen", "initialFolderId", "mediaRepository", "repositoryFactory", "uploadTag", "previewSource", "onImageUpload", "_ref", "_this", "_callee", "targetId", "mediaEntity", "_context", "updateElementData", "$emit", "args", "arguments", "apply", "onImageRemove", "onCloseModal", "onSelectionChanges", "$set", "onOpenMediaModal", "onChangeMinHeight", "onChangeDisplayMode", "registerCmsElement", "configComponent", "sorting", "mediaIdCategory", "IconOrImage", "visible", "entity", "showImage", "showSearch", "searchTerm", "showCategories", "showSearchHeadline", "textSearchHeadline", "showFaqHeadline", "textFaqHeadline", "numberCategories", "numberQuestions", "iconCategories", "headlineCategories", "descriptionCategories", "questionCategory", "questions", "answers", "imageAlt", "iconColor", "TabBackgroundColor", "TabFontColor", "TabBackgroundColorHover", "TabFontColorHover", "Locale", "extend", "deDE", "enGB", "default", "locals", "add", "listToStyles", "parentId", "list", "newStyles", "item", "part", "css", "sourceMap", "parts", "hasDocument", "document", "DEBUG", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "append<PERSON><PERSON><PERSON>", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,uBAIjBlC,EAAoBA,EAAoBmC,EAAI,Q,uDC/ErDC,SAASC,UAAUC,SAAS,mBAAoB,CAC5CC,SCJW,gM,UCGfH,SAASC,UAAUC,SAAS,qBAAsB,CAC9CC,SCJW,6LCGfH,SAASI,QAAQ,cAAcC,iBAAiB,CAC5C9B,KAAM,MACN+B,MAAO,qCACPC,SAAU,aACVC,UAAW,mBACXC,iBAAkB,qBAClBC,cAAe,CACXC,aAAc,OACdC,UAAW,OACXC,WAAY,OACZC,YAAa,OACbC,WAAY,SAEhBC,MAAO,CACHC,QAAS,e,UCjBF,ICGfC,EAA6BlB,SAArBC,EAASiB,EAATjB,UAAWkB,EAAKD,EAALC,MAEnBlB,EAAUC,SAAS,sBAAuB,CACtCC,SDNW,0xlCCQXiB,OAAQ,CACJD,EAAME,UAAU,gBAEpBC,SAAU,CACNC,oBAAmB,WACf,IAAItC,EAAQuC,KAAKC,QAAQC,OAAOH,oBAAoBtC,MAEpD,OAAsB,GADDuC,KAAKC,QAAQC,OAAOC,iBAAiB1C,MAE/C,SAAWA,EAAQ,gCAEnB,CACH,aAAcuC,KAAKC,QAAQC,OAAOE,UAAU3C,MAC5C,QAAWuC,KAAKC,QAAQC,OAAOG,aAAa5C,MAC5C,MAAUuC,KAAKC,QAAQC,OAAOI,WAAW7C,QAKrD8C,gBAAe,WACX,IAAqD,IAAjDP,KAAKC,QAAQC,OAAOM,mBAAmB/C,MACvC,MAAO,cAAgBuC,KAAKC,QAAQC,OAAOK,gBAAgB9C,MAAQ,MAG3EgD,OAAM,WACF,MAAO,CACH,aAAwD,UAA1CT,KAAKC,QAAQC,OAAOQ,YAAYjD,OACN,IAAxCuC,KAAKC,QAAQC,OAAOS,UAAUlD,MAAcuC,KAAKC,QAAQC,OAAOS,UAAUlD,MAAQ,QAClF,aAAeuC,KAAKC,QAAQC,OAAOU,cAAcnD,MAAeuC,KAAKC,QAAQC,OAAOU,cAAcnD,MAAzC,OAGjEoD,YAAW,WACP,MAAO,CACH,aAAcb,KAAKC,QAAQC,OAAOE,UAAU3C,MAC5C,QAAWuC,KAAKC,QAAQC,OAAOG,aAAa5C,MAC5C,MAAUuC,KAAKC,QAAQC,OAAOI,WAAW7C,QAGjDqD,SAAQ,WACJ,IAAMC,EAAUvC,SAASwC,QAAQC,IAC3BC,EAAWlB,KAAKC,QAAQkB,KAAKC,MAGlC,GAAoB,WAFDpB,KAAKC,QAAQC,OAAOkB,MAAMC,OAEf,CAC3B,IAAMC,EAAYtB,KAAKuB,aAAavB,KAAKC,QAAQC,OAAOkB,MAAM3D,OAE9D,GAAI6D,GAAaA,EAAUE,IACvB,OAAOF,EAAUE,IAIzB,OAAIN,GAAYA,EAASO,GACdzB,KAAKC,QAAQkB,KAAKC,MAAMI,IAG/BN,GAAYA,EAASM,IACf,GAANE,OAAUX,EAAQY,YAAUD,OAAGR,EAASM,KAGtC,GAANE,OAAUX,EAAQY,WAAU,+DAGpCC,MAAO,CACHC,aAAc,CACVC,MAAM,EACNC,QAAO,WACH/B,KAAKgC,kBAIjBC,QAAO,WACHjC,KAAKkC,oBAETC,QAAS,CACLD,iBAAgB,WACZlC,KAAKoC,kBAAkB,aACvBpC,KAAKqC,gBAAgB,iB,4PClFjCC,EAAA,kBAAAhG,GAAA,IAAAA,EAAA,GAAAiG,EAAArF,OAAAkB,UAAAoE,EAAAD,EAAAlE,eAAAlB,EAAAD,OAAAC,gBAAA,SAAAsF,EAAA1E,EAAA2E,GAAAD,EAAA1E,GAAA2E,EAAAjF,OAAAkF,EAAA,mBAAApF,cAAA,GAAAqF,EAAAD,EAAAE,UAAA,aAAAC,EAAAH,EAAAI,eAAA,kBAAAC,EAAAL,EAAAnF,aAAA,yBAAAyF,EAAAR,EAAA1E,EAAAN,GAAA,OAAAP,OAAAC,eAAAsF,EAAA1E,EAAA,CAAAN,QAAAL,YAAA,EAAA8F,cAAA,EAAAC,UAAA,IAAAV,EAAA1E,GAAA,IAAAkF,EAAA,aAAAG,GAAAH,EAAA,SAAAR,EAAA1E,EAAAN,GAAA,OAAAgF,EAAA1E,GAAAN,GAAA,SAAA4F,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAH,KAAAnF,qBAAAuF,EAAAJ,EAAAI,EAAAC,EAAA1G,OAAAY,OAAA4F,EAAAtF,WAAA2C,EAAA,IAAAC,EAAAyC,GAAA,WAAAtG,EAAAyG,EAAA,WAAAnG,MAAAoG,EAAAP,EAAAE,EAAAzC,KAAA6C,EAAA,SAAAE,EAAAC,EAAAtB,EAAAuB,GAAA,WAAAC,KAAA,SAAAD,IAAAD,EAAApH,KAAA8F,EAAAuB,IAAA,MAAAZ,GAAA,OAAAa,KAAA,QAAAD,IAAAZ,IAAA9G,EAAA+G,OAAA,IAAAa,EAAA,YAAAP,KAAA,SAAAQ,KAAA,SAAAC,KAAA,IAAAC,EAAA,GAAApB,EAAAoB,EAAAzB,GAAA,8BAAA0B,EAAApH,OAAAqH,eAAAC,EAAAF,OAAAG,EAAA,MAAAD,OAAAjC,GAAAC,EAAA7F,KAAA6H,EAAA5B,KAAAyB,EAAAG,GAAA,IAAAE,EAAAN,EAAAhG,UAAAuF,EAAAvF,UAAAlB,OAAAY,OAAAuG,GAAA,SAAAM,EAAAvG,GAAA,0BAAAwG,SAAA,SAAAC,GAAA5B,EAAA7E,EAAAyG,GAAA,SAAAb,GAAA,YAAAc,QAAAD,EAAAb,SAAA,SAAAe,EAAAnB,EAAAoB,GAAA,SAAAC,EAAAJ,EAAAb,EAAAkB,EAAAC,GAAA,IAAAC,EAAAtB,EAAAF,EAAAiB,GAAAjB,EAAAI,GAAA,aAAAoB,EAAAnB,KAAA,KAAAoB,EAAAD,EAAApB,IAAAvG,EAAA4H,EAAA5H,MAAA,OAAAA,GAAA,UAAA6H,EAAA7H,IAAA+E,EAAA7F,KAAAc,EAAA,WAAAuH,EAAAE,QAAAzH,EAAA8H,SAAAC,MAAA,SAAA/H,GAAAwH,EAAA,OAAAxH,EAAAyH,EAAAC,MAAA,SAAA/B,GAAA6B,EAAA,QAAA7B,EAAA8B,EAAAC,MAAAH,EAAAE,QAAAzH,GAAA+H,MAAA,SAAAC,GAAAJ,EAAA5H,MAAAgI,EAAAP,EAAAG,MAAA,SAAAK,GAAA,OAAAT,EAAA,QAAAS,EAAAR,EAAAC,QAAAC,EAAApB,KAAA,IAAA2B,EAAAxI,EAAA,gBAAAM,MAAA,SAAAoH,EAAAb,GAAA,SAAA4B,IAAA,WAAAZ,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAb,EAAAkB,EAAAC,MAAA,OAAAQ,MAAAH,KAAAI,YAAA,SAAA/B,EAAAP,EAAAE,EAAAzC,GAAA,IAAA8E,EAAA,iCAAAhB,EAAAb,GAAA,iBAAA6B,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAhB,EAAA,MAAAb,EAAA,OAAA+B,IAAA,IAAAhF,EAAA8D,SAAA9D,EAAAiD,QAAA,KAAAgC,EAAAjF,EAAAiF,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAjF,GAAA,GAAAkF,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,GAAA,YAAAlF,EAAA8D,OAAA9D,EAAAoF,KAAApF,EAAAqF,MAAArF,EAAAiD,SAAA,aAAAjD,EAAA8D,OAAA,uBAAAgB,EAAA,MAAAA,EAAA,YAAA9E,EAAAiD,IAAAjD,EAAAsF,kBAAAtF,EAAAiD,SAAA,WAAAjD,EAAA8D,QAAA9D,EAAAuF,OAAA,SAAAvF,EAAAiD,KAAA6B,EAAA,gBAAAT,EAAAtB,EAAAR,EAAAE,EAAAzC,GAAA,cAAAqE,EAAAnB,KAAA,IAAA4B,EAAA9E,EAAAwF,KAAA,6BAAAnB,EAAApB,MAAAE,EAAA,gBAAAzG,MAAA2H,EAAApB,IAAAuC,KAAAxF,EAAAwF,MAAA,UAAAnB,EAAAnB,OAAA4B,EAAA,YAAA9E,EAAA8D,OAAA,QAAA9D,EAAAiD,IAAAoB,EAAApB,OAAA,SAAAkC,EAAAF,EAAAjF,GAAA,IAAAyF,EAAAzF,EAAA8D,SAAAmB,EAAAnD,SAAA2D,GAAA,QAAAC,IAAA5B,EAAA,OAAA9D,EAAAiF,SAAA,eAAAQ,GAAAR,EAAAnD,SAAA6D,SAAA3F,EAAA8D,OAAA,SAAA9D,EAAAiD,SAAAyC,EAAAP,EAAAF,EAAAjF,GAAA,UAAAA,EAAA8D,SAAA,WAAA2B,IAAAzF,EAAA8D,OAAA,QAAA9D,EAAAiD,IAAA,IAAA2C,UAAA,oCAAAH,EAAA,aAAAtC,EAAA,IAAAkB,EAAAtB,EAAAe,EAAAmB,EAAAnD,SAAA9B,EAAAiD,KAAA,aAAAoB,EAAAnB,KAAA,OAAAlD,EAAA8D,OAAA,QAAA9D,EAAAiD,IAAAoB,EAAApB,IAAAjD,EAAAiF,SAAA,KAAA9B,EAAA,IAAA0C,EAAAxB,EAAApB,IAAA,OAAA4C,IAAAL,MAAAxF,EAAAiF,EAAAa,YAAAD,EAAAnJ,MAAAsD,EAAA+F,KAAAd,EAAAe,QAAA,WAAAhG,EAAA8D,SAAA9D,EAAA8D,OAAA,OAAA9D,EAAAiD,SAAAyC,GAAA1F,EAAAiF,SAAA,KAAA9B,GAAA0C,GAAA7F,EAAA8D,OAAA,QAAA9D,EAAAiD,IAAA,IAAA2C,UAAA,oCAAA5F,EAAAiF,SAAA,KAAA9B,GAAA,SAAA8C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,GAAA,SAAAO,EAAAP,GAAA,IAAA9B,EAAA8B,EAAAQ,YAAA,GAAAtC,EAAAnB,KAAA,gBAAAmB,EAAApB,IAAAkD,EAAAQ,WAAAtC,EAAA,SAAApE,EAAAyC,GAAA,KAAA8D,WAAA,EAAAJ,OAAA,SAAA1D,EAAAmB,QAAAoC,EAAA,WAAAW,OAAA,YAAAlD,EAAAmD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAAhF,GAAA,GAAAiF,EAAA,OAAAA,EAAAlL,KAAAiL,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAvL,GAAA,EAAAsK,EAAA,SAAAA,IAAA,OAAAtK,EAAAoL,EAAAG,QAAA,GAAAvF,EAAA7F,KAAAiL,EAAApL,GAAA,OAAAsK,EAAArJ,MAAAmK,EAAApL,GAAAsK,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAArJ,WAAAgJ,EAAAK,EAAAP,MAAA,EAAAO,GAAA,OAAAA,UAAA,OAAAA,KAAAf,GAAA,SAAAA,IAAA,OAAAtI,WAAAgJ,EAAAF,MAAA,UAAApC,EAAA/F,UAAAgG,EAAAjH,EAAAuH,EAAA,eAAAjH,MAAA2G,EAAAlB,cAAA,IAAA/F,EAAAiH,EAAA,eAAA3G,MAAA0G,EAAAjB,cAAA,IAAAiB,EAAA6D,YAAA/E,EAAAmB,EAAApB,EAAA,qBAAA1G,EAAA2L,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,KAAAE,YAAA,QAAAD,QAAAhE,GAAA,uBAAAgE,EAAAH,aAAAG,EAAApL,QAAAT,EAAA+L,KAAA,SAAAH,GAAA,OAAAhL,OAAAoL,eAAApL,OAAAoL,eAAAJ,EAAA9D,IAAA8D,EAAAK,UAAAnE,EAAAnB,EAAAiF,EAAAlF,EAAA,sBAAAkF,EAAA9J,UAAAlB,OAAAY,OAAA4G,GAAAwD,GAAA5L,EAAAkM,MAAA,SAAAxE,GAAA,OAAAuB,QAAAvB,IAAAW,EAAAI,EAAA3G,WAAA6E,EAAA8B,EAAA3G,UAAA0E,GAAA,0BAAAxG,EAAAyI,gBAAAzI,EAAAmM,MAAA,SAAAnF,EAAAC,EAAAC,EAAAC,EAAAuB,QAAA,IAAAA,MAAA0D,SAAA,IAAAC,EAAA,IAAA5D,EAAA1B,EAAAC,EAAAC,EAAAC,EAAAC,GAAAuB,GAAA,OAAA1I,EAAA2L,oBAAA1E,GAAAoF,IAAA7B,OAAAtB,MAAA,SAAAH,GAAA,OAAAA,EAAAkB,KAAAlB,EAAA5H,MAAAkL,EAAA7B,WAAAnC,EAAAD,GAAAzB,EAAAyB,EAAA1B,EAAA,aAAAC,EAAAyB,EAAA9B,GAAA,0BAAAK,EAAAyB,EAAA,qDAAApI,EAAAsM,KAAA,SAAAC,GAAA,IAAA3K,EAAAhB,OAAA2L,GAAAD,EAAA,WAAA7K,KAAAG,EAAA0K,EAAApB,KAAAzJ,GAAA,OAAA6K,EAAAE,UAAA,SAAAhC,IAAA,KAAA8B,EAAAb,QAAA,KAAAhK,EAAA6K,EAAAG,MAAA,GAAAhL,KAAAG,EAAA,OAAA4I,EAAArJ,MAAAM,EAAA+I,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAP,MAAA,EAAAO,IAAAxK,EAAAmI,SAAAzD,EAAA5C,UAAA,CAAAgK,YAAApH,EAAA2G,MAAA,SAAAqB,GAAA,QAAAC,KAAA,OAAAnC,KAAA,OAAAX,KAAA,KAAAC,WAAAK,EAAA,KAAAF,MAAA,OAAAP,SAAA,UAAAnB,OAAA,YAAAb,SAAAyC,EAAA,KAAAc,WAAA3C,QAAA6C,IAAAuB,EAAA,QAAAjM,KAAA,WAAAA,EAAAmM,OAAA,IAAA1G,EAAA7F,KAAA,KAAAI,KAAA+K,OAAA/K,EAAAoM,MAAA,WAAApM,QAAA0J,IAAA2C,KAAA,gBAAA7C,MAAA,MAAA8C,EAAA,KAAA9B,WAAA,GAAAG,WAAA,aAAA2B,EAAApF,KAAA,MAAAoF,EAAArF,IAAA,YAAAsF,MAAAjD,kBAAA,SAAAkD,GAAA,QAAAhD,KAAA,MAAAgD,EAAA,IAAAxI,EAAA,cAAAyI,EAAAC,EAAAC,GAAA,OAAAtE,EAAAnB,KAAA,QAAAmB,EAAApB,IAAAuF,EAAAxI,EAAA+F,KAAA2C,EAAAC,IAAA3I,EAAA8D,OAAA,OAAA9D,EAAAiD,SAAAyC,KAAAiD,EAAA,QAAAlN,EAAA,KAAA+K,WAAAQ,OAAA,EAAAvL,GAAA,IAAAA,EAAA,KAAA0K,EAAA,KAAAK,WAAA/K,GAAA4I,EAAA8B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAqC,EAAA,UAAAtC,EAAAC,QAAA,KAAA8B,KAAA,KAAAU,EAAAnH,EAAA7F,KAAAuK,EAAA,YAAA0C,EAAApH,EAAA7F,KAAAuK,EAAA,iBAAAyC,GAAAC,EAAA,SAAAX,KAAA/B,EAAAE,SAAA,OAAAoC,EAAAtC,EAAAE,UAAA,WAAA6B,KAAA/B,EAAAG,WAAA,OAAAmC,EAAAtC,EAAAG,iBAAA,GAAAsC,GAAA,QAAAV,KAAA/B,EAAAE,SAAA,OAAAoC,EAAAtC,EAAAE,UAAA,YAAAwC,EAAA,UAAA9D,MAAA,kDAAAmD,KAAA/B,EAAAG,WAAA,OAAAmC,EAAAtC,EAAAG,gBAAAf,OAAA,SAAArC,EAAAD,GAAA,QAAAxH,EAAA,KAAA+K,WAAAQ,OAAA,EAAAvL,GAAA,IAAAA,EAAA,KAAA0K,EAAA,KAAAK,WAAA/K,GAAA,GAAA0K,EAAAC,QAAA,KAAA8B,MAAAzG,EAAA7F,KAAAuK,EAAA,oBAAA+B,KAAA/B,EAAAG,WAAA,KAAAwC,EAAA3C,EAAA,OAAA2C,IAAA,UAAA5F,GAAA,aAAAA,IAAA4F,EAAA1C,QAAAnD,MAAA6F,EAAAxC,aAAAwC,EAAA,UAAAzE,EAAAyE,IAAAnC,WAAA,UAAAtC,EAAAnB,OAAAmB,EAAApB,MAAA6F,GAAA,KAAAhF,OAAA,YAAAiC,KAAA+C,EAAAxC,WAAAnD,GAAA,KAAA4F,SAAA1E,IAAA0E,SAAA,SAAA1E,EAAAkC,GAAA,aAAAlC,EAAAnB,KAAA,MAAAmB,EAAApB,IAAA,gBAAAoB,EAAAnB,MAAA,aAAAmB,EAAAnB,KAAA,KAAA6C,KAAA1B,EAAApB,IAAA,WAAAoB,EAAAnB,MAAA,KAAAqF,KAAA,KAAAtF,IAAAoB,EAAApB,IAAA,KAAAa,OAAA,cAAAiC,KAAA,kBAAA1B,EAAAnB,MAAAqD,IAAA,KAAAR,KAAAQ,GAAApD,GAAA6F,OAAA,SAAA1C,GAAA,QAAA7K,EAAA,KAAA+K,WAAAQ,OAAA,EAAAvL,GAAA,IAAAA,EAAA,KAAA0K,EAAA,KAAAK,WAAA/K,GAAA,GAAA0K,EAAAG,eAAA,YAAAyC,SAAA5C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAAhD,IAAA8F,MAAA,SAAA7C,GAAA,QAAA3K,EAAA,KAAA+K,WAAAQ,OAAA,EAAAvL,GAAA,IAAAA,EAAA,KAAA0K,EAAA,KAAAK,WAAA/K,GAAA,GAAA0K,EAAAC,WAAA,KAAA/B,EAAA8B,EAAAQ,WAAA,aAAAtC,EAAAnB,KAAA,KAAAgG,EAAA7E,EAAApB,IAAAyD,EAAAP,GAAA,OAAA+C,GAAA,UAAAnE,MAAA,0BAAAoE,cAAA,SAAAtC,EAAAf,EAAAE,GAAA,YAAAf,SAAA,CAAAnD,SAAA4B,EAAAmD,GAAAf,aAAAE,WAAA,cAAAlC,SAAA,KAAAb,SAAAyC,GAAAvC,IAAA5H,EAAA,SAAA6N,EAAAC,EAAAlF,EAAAC,EAAAkF,EAAAC,EAAAvM,EAAAiG,GAAA,QAAA4C,EAAAwD,EAAArM,GAAAiG,GAAAvG,EAAAmJ,EAAAnJ,MAAA,MAAAiI,GAAA,YAAAP,EAAAO,GAAAkB,EAAAL,KAAArB,EAAAzH,GAAAiL,QAAAxD,QAAAzH,GAAA+H,KAAA6E,EAAAC,GAEA,IAAA5K,EAA6BlB,SAArBC,EAASiB,EAATjB,UAAWkB,EAAKD,EAALC,MAEnBlB,EAAUC,SAAS,6BAA8B,CAC7CC,SCNW,movDDQXiB,OAAQ,CACJD,EAAME,UAAU,gBAEpB0K,OAAQ,CAAC,qBAETpJ,KAAI,WACA,MAAO,CACHqJ,kBAAkB,EAClBC,gBAAiB,OAIzB3K,SAAU,CACN4K,gBAAe,WACX,OAAO1K,KAAK2K,kBAAkB7M,OAAO,UAGzC8M,UAAS,WACL,MAAM,4BAANlJ,OAAmC1B,KAAKC,QAAQwB,KAGpDoJ,cAAa,WACT,OAAI7K,KAAKC,QAAQkB,MAAQnB,KAAKC,QAAQkB,KAAKC,OAASpB,KAAKC,QAAQkB,KAAKC,MAAMK,GACjEzB,KAAKC,QAAQkB,KAAKC,MAGtBpB,KAAKC,QAAQC,OAAOkB,MAAM3D,QAIzCwE,QAAO,WACHjC,KAAKkC,oBAGTC,QAAS,CACLD,iBAAgB,WACZlC,KAAKoC,kBAAkB,cAGrB0I,cAAa,SAAAC,GAAgB,IA9C3ChH,EA8C0CiH,EAAA,YA9C1CjH,EA8C0CzB,IAAA+F,MAAA,SAAA4C,IAAA,IAAAC,EAAAC,EAAA,OAAA7I,IAAAe,MAAA,SAAA+H,GAAA,cAAAA,EAAAnC,KAAAmC,EAAAtE,MAAA,OAAJ,OAARoE,EAAQH,EAARG,SAAQE,EAAAtE,KAAA,EACAkE,EAAKN,gBAAgBrN,IAAI6N,EAAU1M,SAASwC,QAAQC,KAAK,KAAD,EAA5EkK,EAAWC,EAAAjF,KAEjB6E,EAAK/K,QAAQC,OAAOkB,MAAM3D,MAAQ0N,EAAY1J,GAE9CuJ,EAAKK,kBAAkBF,GAEvBH,EAAKM,MAAM,iBAAkBN,EAAK/K,SAAS,wBAAAmL,EAAAhC,UAAA6B,MArDvD,eAAAzH,EAAA,KAAA+H,EAAAC,UAAA,WAAA9C,SAAA,SAAAxD,EAAAC,GAAA,IAAAiF,EAAArG,EAAA0H,MAAAjI,EAAA+H,GAAA,SAAAlB,EAAA5M,GAAA0M,EAAAC,EAAAlF,EAAAC,EAAAkF,EAAAC,EAAA,OAAA7M,GAAA,SAAA6M,EAAAlH,GAAA+G,EAAAC,EAAAlF,EAAAC,EAAAkF,EAAAC,EAAA,QAAAlH,GAAAiH,OAAA5D,WAwDQiF,cAAa,WACT1L,KAAKC,QAAQC,OAAOkB,MAAM3D,MAAQ,KAElCuC,KAAKqL,oBAELrL,KAAKsL,MAAM,iBAAkBtL,KAAKC,UAGtC0L,aAAY,WACR3L,KAAKwK,kBAAmB,GAG5BoB,mBAAkB,SAACT,GACf,IAAM/J,EAAQ+J,EAAY,GAC1BnL,KAAKC,QAAQC,OAAOkB,MAAM3D,MAAQ2D,EAAMK,GAExCzB,KAAKqL,kBAAkBjK,GAEvBpB,KAAKsL,MAAM,iBAAkBtL,KAAKC,UAGtCoL,kBAAiB,WAAgB,IAAfjK,EAAKoK,UAAAzD,OAAA,QAAAtB,IAAA+E,UAAA,GAAAA,UAAA,GAAG,KACtBxL,KAAK6L,KAAK7L,KAAKC,QAAQkB,KAAM,UAAqB,OAAVC,EAAiB,KAAOA,EAAMK,IACtEzB,KAAK6L,KAAK7L,KAAKC,QAAQkB,KAAM,QAASC,IAG1C0K,iBAAgB,WACZ9L,KAAKwK,kBAAmB,GAG5BuB,kBAAiB,SAACtO,GACduC,KAAKC,QAAQC,OAAOS,UAAUlD,MAAkB,OAAVA,EAAiB,GAAKA,EAE5DuC,KAAKsL,MAAM,iBAAkBtL,KAAKC,UAGtC+L,oBAAmB,SAACvO,GACF,UAAVA,EACAuC,KAAKC,QAAQC,OAAOU,cAAcnD,MAAQ,KAE1CuC,KAAKC,QAAQC,OAAOS,UAAUlD,MAAQ,GAG1CuC,KAAKsL,MAAM,iBAAkBtL,KAAKC,a,UEjG9CzB,SAASC,UAAUC,SAAS,8BAA+B,CACvDC,SCJW,2LCIfH,SAASI,QAAQ,cAAcqN,mBAAmB,CACjDlP,KAAM,YACN+B,MAAO,qCACPE,UAAW,sBACXkN,gBAAiB,6BACjBjN,iBAAkB,8BAClBC,cAAe,CACdiN,QAAS,CACR9K,OAAQ,SACR5D,MAAO,IAER2O,gBAAiB,CAChB/K,OAAQ,SACR5D,MAAO,IAER4O,YAAa,CACZhL,OAAQ,SACR5D,MAAO,QAER6O,QAAS,CACRjL,OAAQ,SACR5D,OAAO,GAER2D,MAAO,CACNC,OAAQ,SACR5D,MAAO,KACP8O,OAAQ,CACPxP,KAAM,UAGRyP,UAAW,CACVnL,OAAQ,SACR5D,OAAO,GAERgP,WAAY,CACXpL,OAAQ,SACR5D,OAAO,GAERiP,WAAY,CACXrL,OAAQ,SACR5D,MAAO,IAERkP,eAAgB,CACftL,OAAQ,SACR5D,OAAO,GAERmP,mBAAoB,CACnBvL,OAAQ,SACR5D,OAAO,GAERoP,mBAAoB,CACnBxL,OAAQ,SACR5D,MAAO,IAERqP,gBAAiB,CAChBzL,OAAQ,SACR5D,OAAO,GAERsP,gBAAiB,CAChB1L,OAAQ,SACR5D,MAAO,IAERuP,iBAAkB,CACjB3L,OAAQ,SACR5D,MAAO,GAERwP,gBAAiB,CAChB5L,OAAQ,SACR5D,MAAO,IAERyP,eAAgB,CACf7L,OAAQ,SACR5D,MAAO,IAER0P,mBAAoB,CACnB9L,OAAQ,SACR5D,MAAO,IAER2P,sBAAuB,CACtB/L,OAAQ,SACR5D,MAAO,IAER4P,iBAAkB,CACjBhM,OAAQ,SACR5D,MAAO,IAER6P,UAAW,CACVjM,OAAQ,SACR5D,MAAQ,IAET8P,QAAS,CACRlM,OAAQ,SACR5D,MAAO,IAER2C,UAAW,CACViB,OAAQ,SACR5D,MAAO,WAER+P,SAAU,CACTnM,OAAQ,SACR5D,MAAO,IAER6C,WAAY,CACXe,OAAQ,SACR5D,MAAO,QAER4C,aAAc,CACbgB,OAAQ,SACR5D,MAAO,gBAERgQ,UAAW,CACVpM,OAAQ,SACR5D,MAAO,WAERiQ,mBAAoB,CACnBrM,OAAQ,SACR5D,MAAO,WAERkQ,aAAc,CACbtM,OAAQ,SACR5D,MAAO,WAERmQ,wBAAyB,CACxBvM,OAAQ,SACR5D,MAAO,WAERoQ,kBAAmB,CAClBxM,OAAQ,SACR5D,MAAO,WAER0C,iBAAkB,CACjBkB,OAAQ,SACR5D,OAAO,GAERsC,oBAAqB,CACpBsB,OAAQ,SACR5D,MAAO,GAER+C,mBAAoB,CACnBa,OAAQ,SACR5D,OAAO,GAER8C,gBAAiB,CAChBc,OAAQ,SACR5D,MAAO,Q,4BC9IVe,SAASsP,OAAOC,OAAO,QAASC,GAChCxP,SAASsP,OAAOC,OAAO,QAASE,I,uBCJhC,IAAIxO,EAAU,EAAQ,QACnBA,EAAQ7B,aAAY6B,EAAUA,EAAQyO,SACnB,iBAAZzO,IAAsBA,EAAU,CAAC,CAAClD,EAAOC,EAAIiD,EAAS,MAC7DA,EAAQ0O,SAAQ5R,EAAOD,QAAUmD,EAAQ0O,SAG/BC,EADH,EAAQ,QAA4JF,SAC7J,WAAYzO,GAAS,EAAM,K,ylHCL7B,SAAS4O,EAAcC,EAAUC,GAG9C,IAFA,IAAI9N,EAAS,GACT+N,EAAY,GACPhS,EAAI,EAAGA,EAAI+R,EAAKxG,OAAQvL,IAAK,CACpC,IAAIiS,EAAOF,EAAK/R,GACZiF,EAAKgN,EAAK,GAIVC,EAAO,CACTjN,GAAI6M,EAAW,IAAM9R,EACrBmS,IALQF,EAAK,GAMbrN,MALUqN,EAAK,GAMfG,UALcH,EAAK,IAOhBD,EAAU/M,GAGb+M,EAAU/M,GAAIoN,MAAMrH,KAAKkH,GAFzBjO,EAAO+G,KAAKgH,EAAU/M,GAAM,CAAEA,GAAIA,EAAIoN,MAAO,CAACH,KAKlD,OAAOjO,E,+CCjBT,IAAIqO,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIhJ,MACV,2JAkBJ,IAAImJ,EAAc,GAQdC,EAAOJ,IAAgBC,SAASG,MAAQH,SAASI,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiBzB,EAAUC,EAAMyB,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAIxP,EAAS4N,EAAaC,EAAUC,GAGpC,OAFA2B,EAAezP,GAER,SAAiB0P,GAEtB,IADA,IAAIC,EAAY,GACP5T,EAAI,EAAGA,EAAIiE,EAAOsH,OAAQvL,IAAK,CACtC,IAAIiS,EAAOhO,EAAOjE,IACd6T,EAAWpB,EAAYR,EAAKhN,KACvB6O,OACTF,EAAU5I,KAAK6I,GAEbF,EAEFD,EADAzP,EAAS4N,EAAaC,EAAU6B,IAGhC1P,EAAS,GAEX,IAASjE,EAAI,EAAGA,EAAI4T,EAAUrI,OAAQvL,IAAK,CACzC,IAAI6T,EACJ,GAAsB,KADlBA,EAAWD,EAAU5T,IACZ8T,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASxB,MAAM9G,OAAQwI,IACzCF,EAASxB,MAAM0B,YAEVtB,EAAYoB,EAAS5O,OAMpC,SAASyO,EAAgBzP,GACvB,IAAK,IAAIjE,EAAI,EAAGA,EAAIiE,EAAOsH,OAAQvL,IAAK,CACtC,IAAIiS,EAAOhO,EAAOjE,GACd6T,EAAWpB,EAAYR,EAAKhN,IAChC,GAAI4O,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASxB,MAAM9G,OAAQwI,IACzCF,EAASxB,MAAM0B,GAAG9B,EAAKI,MAAM0B,IAE/B,KAAOA,EAAI9B,EAAKI,MAAM9G,OAAQwI,IAC5BF,EAASxB,MAAMrH,KAAKgJ,EAAS/B,EAAKI,MAAM0B,KAEtCF,EAASxB,MAAM9G,OAAS0G,EAAKI,MAAM9G,SACrCsI,EAASxB,MAAM9G,OAAS0G,EAAKI,MAAM9G,YAEhC,CACL,IAAI8G,EAAQ,GACZ,IAAS0B,EAAI,EAAGA,EAAI9B,EAAKI,MAAM9G,OAAQwI,IACrC1B,EAAMrH,KAAKgJ,EAAS/B,EAAKI,MAAM0B,KAEjCtB,EAAYR,EAAKhN,IAAM,CAAEA,GAAIgN,EAAKhN,GAAI6O,KAAM,EAAGzB,MAAOA,KAK5D,SAAS4B,IACP,IAAIC,EAAe3B,SAAS4B,cAAc,SAG1C,OAFAD,EAAazM,KAAO,WACpBiL,EAAK0B,YAAYF,GACVA,EAGT,SAASF,EAAU/N,GACjB,IAAIoO,EAAQC,EACRJ,EAAe3B,SAASgC,cAAc,SAAWtB,EAAW,MAAQhN,EAAIhB,GAAK,MAEjF,GAAIiP,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaM,WAAWC,YAAYP,GAIxC,GAAIhB,EAAS,CAEX,IAAIwB,EAAa7B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDI,EAASM,EAAoBnT,KAAK,KAAM0S,EAAcQ,GAAY,GAClEJ,EAASK,EAAoBnT,KAAK,KAAM0S,EAAcQ,GAAY,QAGlER,EAAeD,IACfI,EAASO,EAAWpT,KAAK,KAAM0S,GAC/BI,EAAS,WACPJ,EAAaM,WAAWC,YAAYP,IAMxC,OAFAG,EAAOpO,GAEA,SAAsB4O,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO1C,MAAQlM,EAAIkM,KACnB0C,EAAOjQ,QAAUqB,EAAIrB,OACrBiQ,EAAOzC,YAAcnM,EAAImM,UAC3B,OAEFiC,EAAOpO,EAAM4O,QAEbP,KAKN,IACMQ,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,QAI1C,SAAST,EAAqBT,EAAcc,EAAOV,EAAQrO,GACzD,IAAIkM,EAAMmC,EAAS,GAAKrO,EAAIkM,IAE5B,GAAI+B,EAAamB,WACfnB,EAAamB,WAAWC,QAAUP,EAAYC,EAAO7C,OAChD,CACL,IAAIoD,EAAUhD,SAASiD,eAAerD,GAClCsD,EAAavB,EAAauB,WAC1BA,EAAWT,IAAQd,EAAaO,YAAYgB,EAAWT,IACvDS,EAAWlK,OACb2I,EAAawB,aAAaH,EAASE,EAAWT,IAE9Cd,EAAaE,YAAYmB,IAK/B,SAASX,EAAYV,EAAcjO,GACjC,IAAIkM,EAAMlM,EAAIkM,IACVvN,EAAQqB,EAAIrB,MACZwN,EAAYnM,EAAImM,UAiBpB,GAfIxN,GACFsP,EAAayB,aAAa,QAAS/Q,GAEjCoO,EAAQ4C,OACV1B,EAAayB,aAAa1C,EAAUhN,EAAIhB,IAGtCmN,IAGFD,GAAO,mBAAqBC,EAAUyD,QAAQ,GAAK,MAEnD1D,GAAO,uDAAyD2D,KAAKC,SAASC,mBAAmBC,KAAKC,UAAU9D,MAAgB,OAG9H8B,EAAamB,WACfnB,EAAamB,WAAWC,QAAUnD,MAC7B,CACL,KAAO+B,EAAaiC,YAClBjC,EAAaO,YAAYP,EAAaiC,YAExCjC,EAAaE,YAAY7B,SAASiD,eAAerD,O,69GCxNrD,IAAIlP,EAAU,EAAQ,QACnBA,EAAQ7B,aAAY6B,EAAUA,EAAQyO,SACnB,iBAAZzO,IAAsBA,EAAU,CAAC,CAAClD,EAAOC,EAAIiD,EAAS,MAC7DA,EAAQ0O,SAAQ5R,EAAOD,QAAUmD,EAAQ0O,SAG/BC,EADH,EAAQ,QAA4JF,SAC7J,WAAYzO,GAAS,EAAM,K,qBCN5C,IAAIA,EAAU,EAAQ,QACnBA,EAAQ7B,aAAY6B,EAAUA,EAAQyO,SACnB,iBAAZzO,IAAsBA,EAAU,CAAC,CAAClD,EAAOC,EAAIiD,EAAS,MAC7DA,EAAQ0O,SAAQ5R,EAAOD,QAAUmD,EAAQ0O,SAG/BC,EADH,EAAQ,QAA4JF,SAC7J,WAAYzO,GAAS,EAAM,K,4CCN5C,IAAIA,EAAU,EAAQ,QACnBA,EAAQ7B,aAAY6B,EAAUA,EAAQyO,SACnB,iBAAZzO,IAAsBA,EAAU,CAAC,CAAClD,EAAOC,EAAIiD,EAAS,MAC7DA,EAAQ0O,SAAQ5R,EAAOD,QAAUmD,EAAQ0O,SAG/BC,EADH,EAAQ,QAA4JF,SAC7J,WAAYzO,GAAS,EAAM,K,4CCN5C,IAAIA,EAAU,EAAQ,QACnBA,EAAQ7B,aAAY6B,EAAUA,EAAQyO,SACnB,iBAAZzO,IAAsBA,EAAU,CAAC,CAAClD,EAAOC,EAAIiD,EAAS,MAC7DA,EAAQ0O,SAAQ5R,EAAOD,QAAUmD,EAAQ0O,SAG/BC,EADH,EAAQ,QAA4JF,SAC7J,WAAYzO,GAAS,EAAM,K", "file": "static/js/mart1-mw-faq.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/bundles/mart1mwfaq/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"4bKV\");\n", "import template from './sw-cms-block-faq.html.twig';\nimport './sw-cms-block-faq.scss';\n\nShopware.Component.register('sw-cms-block-faq', {\n    template\n});", "export default \"{% block sw_cms_block_faq %}\\n    <div class=\\\"sw-cms-block-faq\\\">\\n        <slot name=\\\"content\\\">{% block sw_cms_block_faq_slot_content %}{% endblock %}</slot>\\n    </div>\\n{% endblock %}\\n\";", "import template from './sw-cms-preview-faq.html.twig';\nimport './sw-cms-preview-faq.scss';\n\nShopware.Component.register('sw-cms-preview-faq', {\n    template\n});", "export default \"{% block sw_cms_block_faq_preview %}\\n    <div class=\\\"sw-cms-preview-faq\\\">\\n        <img :src=\\\"'/mart1mwfaq/static/img/cms/mwfaq-vorschau.png' | asset\\\">\\n    </div>\\n{% endblock %}\\n\";", "import './component';\nimport './preview';\n\nShopware.Service('cmsService').registerCmsBlock({\n    name: 'faq',\n    label: 'sw-cms.blocks.text-image.faq.label',\n    category: 'text-image',\n    component: 'sw-cms-block-faq',\n    previewComponent: 'sw-cms-preview-faq',\n    defaultConfig: {\n        marginBottom: '20px',\n        marginTop: '20px',\n        marginLeft: '20px',\n        marginRight: '20px',\n        sizingMode: 'boxed'\n    },\n    slots: {\n        content: 'faqconfig'\n    }\n});\n", "export default \"{% block sw_cms_element_faq %}\\n<div class=\\\"cms-element-faqconfig\\\" style=\\\"height: 100%; width: 100%\\\">\\n\\n    <div v-if=\\\"element.config.showImage.value === true && element.config.media.value !== null\\\" class=\\\"mw-faq-image\\\">\\n        <img :src=\\\"mediaUrl\\\" :alt=\\\"element.config.imageAlt.value\\\" :title=\\\"element.config.imageAlt.value\\\" :style=\\\"mediaStyles\\\">\\n    </div>\\n\\n    <div v-if=\\\"element.config.showImage.value === true && element.config.media.value == null\\\" class=\\\"mw-faq-image\\\">\\n        <img :src=\\\"'/mart1mwfaq/static/img/cms/faq-image.jpg' | asset\\\" :alt=\\\"element.config.imageAlt.value\\\" :title=\\\"element.config.imageAlt.value\\\" :style=\\\"mediaStyles\\\">\\n    </div>\\n\\n    <div v-if=\\\"element.config.showSearchHeadline.value === true\\\" class=\\\"mw-faq-cms-headline\\\">\\n        <h2>{{ element.config.textSearchHeadline.value }}</h2>\\n            <hr>\\n    </div>\\n\\n    <div v-if=\\\"element.config.showCategories.value === true\\\" class=\\\"mw-faq-categories\\\">\\n        <ul>\\n        <li v-for=\\\"n in element.config.numberCategories.value\\\" class=\\\"mw-faq-category\\\" :class=\\\"'mw-faq-category-elements-' + element\\n        .config.numberCategories.value.toString()\\\" :style=\\\"customCatHeight\\\">\\n            <input type=\\\"checkbox\\\" :id=\\\"'faq-category-' + n\\\" :name=\\\"'faq-category-' + n\\\">\\n            <label :for=\\\"'faq-category-' + n\\\">\\n            <div class=\\\"mw-faq-category-image\\\" v-if=\\\"element.config.IconOrImage.value == 'image'\\\">\\n                <img :src=\\\"'/mart1mwfaq/static/img/cms/demo-category-image.jpg' | asset\\\" :style=\\\"customCatImageWidth\\\">\\n            </div>\\n            <div class=\\\"mw-faq-category-icon\\\" v-if=\\\"element.config.IconOrImage.value == 'icon'\\\">\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-package-open'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M13 3.6995v3.7463l2.4181 1.5113L18.941 7 13 3.6995zm.4203 6.3675L12 9.1792l-1.4203.8878L12 10.856l1.4203-.789zM8.582 8.957 11 7.4459V3.6995L5.0591 7 8.582 8.957zm13.4126-2.063 1.934 4.835a1 1 0 0 1-.4429 1.2455L22 13.7999V17a1 1 0 0 1-.5144.8742l-9 5a1 1 0 0 1-.9712 0l-9-5A1 1 0 0 1 2 17v-3.2001l-1.4856-.8254a1 1 0 0 1-.4429-1.2455l1.934-4.835c.0318-.305.2015-.5974.5089-.7682l9-5a1 1 0 0 1 .9712 0l9 5c.3074.1708.477.4632.5089.7682zM20 14.911l-5.5144 3.0635c-.5265.2925-1.1904.0565-1.414-.5028L12 14.793l-1.0715 2.6788c-.2237.5593-.8876.7953-1.4141.5028L4 14.911v1.5006l8 4.4444 8-4.4444V14.911zm-9.2556-2.4646L3.5068 8.4255l-1.2512 3.128 7.2376 4.021 1.2512-3.128zm2.5112 0 1.2512 3.128 7.2376-4.0208-1.2512-3.128-7.2376 4.0208z\\\" id=\\\"icons-default-package-open\\\"></path></defs><use xlink:href=\\\"#icons-default-package-open\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-avatar-single'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\\\" fill=\\\"#758CA3\\\" /><path d=\\\"M0 0h24v24H0z\\\" fill=\\\"none\\\"/></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-action-settings'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M12 16c-2.2091 0-4-1.7909-4-4 0-2.2091 1.7909-4 4-4 2.2091 0 4 1.7909 4 4 0 2.2091-1.7909 4-4 4zm0-2c1.1046 0 2-.8954 2-2s-.8954-2-2-2-2 .8954-2 2 .8954 2 2 2zm8.2988-5.2848A.6474.6474 0 0 0 20.83 9H21c1.6569 0 3 1.3431 3 3s-1.3431 3-3 3h-.086a.65.65 0 0 0-.5949.394l-.0042.0098c-.1073.243-.0558.527.1222.709l.0596.0597a3 3 0 0 1 .0008 4.2442 3 3 0 0 1-4.2446.0004l-.0522-.0522c-.19-.1858-.4738-.2373-.7268-.1258-.238.1021-.3929.3358-.3939.5909V21c0 1.6569-1.3431 3-3 3s-3-1.3431-3-3l.0003-.0666c-.0063-.2668-.175-.5027-.484-.6185-.2432-.1073-.527-.0558-.7092.1222l-.0596.0596a3 3 0 0 1-4.2442.0008 3 3 0 0 1-.0004-4.2446l.0522-.0522c.1858-.19.2373-.4738.1258-.7268-.1021-.238-.3358-.3929-.5909-.3939H3c-1.6569 0-3-1.3431-3-3s1.3431-3 3-3l.0666.0003c.2668-.0063.5027-.175.6185-.484.1073-.2432.0558-.527-.1222-.7092l-.0596-.0596a3 3 0 0 1-.0008-4.2442 3 3 0 0 1 4.2446-.0004l.0522.0522a.65.65 0 0 0 .717.13 1 1 0 0 1 .1989-.0639A.6474.6474 0 0 0 9 3.17V3c0-1.6569 1.3431-3 3-3s3 1.3431 3 3v.086c.001.259.1558.4928.4038.5991.243.1073.527.0558.709-.1222l.0597-.0596a3 3 0 0 1 4.2442-.0008 3 3 0 0 1 .0004 4.2446l-.0522.0522a.65.65 0 0 0-.13.717 1 1 0 0 1 .0639.1989zM20.91 13H21c.5523 0 1-.4477 1-1s-.4477-1-1-1h-.174a2.65 2.65 0 0 1-2.4251-1.606 1 1 0 0 1-.0724-.2642c-.3313-.945-.1015-2.0052.6144-2.737l.0604-.0603a1 1 0 0 0-.0008-1.4158 1 1 0 0 0-1.4154.0004l-.0678.0678c-.7745.7575-1.932.9674-2.9132.5342C13.6353 5.1031 13.0042 4.1502 13 3.09V3c0-.5523-.4477-1-1-1s-1 .4477-1 1v.174a2.65 2.65 0 0 1-1.606 2.425 1 1 0 0 1-.2642.0724c-.945.3313-2.0052.1015-2.737-.6144l-.0603-.0604a1 1 0 0 0-1.4158.0008 1 1 0 0 0 .0004 1.4154l.0678.0678c.7575.7745.9674 1.932.5536 2.8645-.3756 1.0212-1.3373 1.709-2.4485 1.7348H3c-.5523 0-1 .4477-1 1s.4477 1 1 1h.174c1.0562.0042 2.009.6353 2.4209 1.5962.4374.9911.2275 2.1486-.5378 2.931l-.0604.0603a1 1 0 0 0 .0008 1.4158 1 1 0 0 0 1.4154-.0004l.0678-.0678c.7745-.7575 1.932-.9674 2.8645-.5536 1.0212.3756 1.709 1.3373 1.7348 2.4485V21c0 .5523.4477 1 1 1s1-.4477 1-1v-.174c.0042-1.0562.6353-2.009 1.5962-2.4209.9911-.4374 2.1486-.2275 2.931.5378l.0603.0604a1 1 0 0 0 1.4158-.0008 1 1 0 0 0-.0004-1.4154l-.0678-.0678a2.65 2.65 0 0 1-.5321-2.9182c.4172-.968 1.3686-1.5969 2.427-1.6011z\\\" id=\\\"icons-default-settings\\\"></path></defs><use xlink:href=\\\"#icons-default-settings\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-badge-warning'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m13.7744 1.4124 9.7058 18.6649c.5096.98.1283 2.1875-.8517 2.6971a2 2 0 0 1-.9227.2256H2.2942c-1.1045 0-2-.8954-2-2a2 2 0 0 1 .2256-.9227l9.7058-18.665c.5096-.98 1.7171-1.3613 2.6971-.8517a2 2 0 0 1 .8517.8518zM2.2942 21h19.4116L12 2.335 2.2942 21zM12 17c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zm1-2c0 .5523-.4477 1-1 1s-1-.4477-1-1v-5c0-.5523.4477-1 1-1s1 .4477 1 1v5z\\\" id=\\\"icons-default-warning\\\"></path></defs><use xlink:href=\\\"#icons-default-warning\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-badge-help'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M24 12c0 6.6274-5.3726 12-12 12S0 18.6274 0 12 5.3726 0 12 0s12 5.3726 12 12zM12 2C6.4772 2 2 6.4772 2 12s4.4772 10 10 10 10-4.4772 10-10S17.5228 2 12 2zm0 13c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zm-1-5c0 .5523-.4477 1-1 1s-1-.4477-1-1c0-1.8856 1.1144-3 3-3s3 1.1144 3 3c0 1.1817-.434 1.7713-1.3587 2.3496l-.1078.0674c-.4464.2817-.5335.4154-.5335 1.083 0 .5523-.4477 1-1 1s-1-.4477-1-1c0-1.4161.4788-2.1515 1.4665-2.7745l.1142-.0716C12.9784 10.4052 13 10.376 13 10c0-.781-.219-1-1-1s-1 .219-1 1z\\\" id=\\\"icons-default-help\\\"></path></defs><use xlink:href=\\\"#icons-default-help\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-device-headset'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M2 11v4h2v-4H2zm20-2c1.1046 0 2 .8954 2 2v4c0 1.1046-.8954 2-2 2v3c0 1.6569-1.3431 3-3 3h-1c0 .5523-.4477 1-1 1h-2c-.5523 0-1-.4477-1-1v-2c0-.5523.4477-1 1-1h2c.5523 0 1 .4477 1 1h1c.5523 0 1-.4477 1-1v-3c-1.1046 0-2-.8954-2-2v-4c0-1.1046.8954-2 2-2 0-3.866-3.134-7-7-7h-2C7.134 2 4 5.134 4 9c1.1046 0 2 .8954 2 2v4c0 1.1046-.8954 2-2 2H2c-1.1046 0-2-.8954-2-2v-4c0-1.1046.8954-2 2-2 0-4.9706 4.0294-9 9-9h2c4.9706 0 9 4.0294 9 9zm-2 2v4h2v-4h-2z\\\" id=\\\"icons-default-headset\\\"></path></defs><use xlink:href=\\\"#icons-default-headset\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-communication-speech-bubbles'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M8 14H6.3576l-3.0894 2.5333c-.8542.7004-2.1144.5758-2.8147-.2784A2 2 0 0 1 0 14.9868V6c0-2.7614 2.2386-5 5-5h6c2.7614 0 5 2.2386 5 5v1h3c2.7614 0 5 2.2386 5 5v8.9868a2 2 0 0 1-.4535 1.2681c-.7003.8542-1.9605.9788-2.8147.2784L17.6424 20H13c-2.7614 0-5-2.2386-5-5v-1zm0-2c0-2.7614 2.2386-5 5-5h1V6c0-1.6569-1.3431-3-3-3H5C3.3431 3 2 4.3431 2 6v8.9868L5.6424 12H8zm6-3h-1c-1.6569 0-3 1.3431-3 3v3c0 1.6569 1.3431 3 3 3h5.3576L22 20.9868V12c0-1.6569-1.3431-3-3-3h-5z\\\" id=\\\"icons-default-speech-bubbles\\\"></path></defs><use xlink:href=\\\"#icons-default-speech-bubbles\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-communication-envelope'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m3.7438 5 7.1093 4.9765a2 2 0 0 0 2.2938 0L20.2562 5H3.7438zM22 6.2207l-7.7062 5.3943a4 4 0 0 1-4.5876 0L2 6.2207V18c0 .5523.4477 1 1 1h18c.5523 0 1-.4477 1-1V6.2207zM3 3h18c1.6569 0 3 1.3431 3 3v12c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3V6c0-1.6569 1.3431-3 3-3z\\\" id=\\\"icons-default-envelope\\\"></path></defs><use xlink:href=\\\"#icons-default-envelope\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-bell-bell'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M11.602 2C8.5082 2 6 4.5081 6 7.602v4.162a5.6827 5.6827 0 0 1-2.4271 4.6577l-.7706.5386A1.8786 1.8786 0 0 0 2 18.5a.5.5 0 0 0 .5.5h19a.5.5 0 0 0 .5-.5 1.9531 1.9531 0 0 0-.8011-1.5772l-.7887-.576A5.876 5.876 0 0 1 18 11.6016V7.5392C18 4.48 15.52 2 12.4608 2h-.8587zm4.3626 19c-.2427 1.6961-1.7014 3-3.4646 3-1.7632 0-3.222-1.3039-3.4646-3H2.5C1.1193 21 0 19.8807 0 18.5a3.8786 3.8786 0 0 1 1.6565-3.179l.7706-.5386A3.6827 3.6827 0 0 0 4 11.764V7.602C4 3.4037 7.4036 0 11.602 0h.8588C16.6246 0 20 3.3754 20 7.5392v4.0625a3.876 3.876 0 0 0 1.5898 3.13l.7887.576A3.9531 3.9531 0 0 1 24 18.5c0 1.3807-1.1193 2.5-2.5 2.5h-5.5354zm-2.05 0h-2.8292c.2059.5826.7615 1 1.4146 1 .6531 0 1.2087-.4174 1.4146-1z\\\" id=\\\"icons-default-bell-bell\\\"></path></defs><use xlink:href=\\\"#icons-default-bell-bell\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-device-mobile'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M12 19c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zM7 0h10c1.6569 0 3 1.3431 3 3v18c0 1.6569-1.3431 3-3 3H7c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v18c0 .5523.4477 1 1 1h10c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1H7z\\\" id=\\\"icons-default-mobile\\\"></path></defs><use xlink:href=\\\"#icons-default-mobile\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-lock-closed'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M8 9h8.0554V4c0-1.1046-.8954-2-2-2H10c-1.1046 0-2 .8954-2 2v5zm10.0554.1909C19.1916 9.6179 20 10.7146 20 12v9c0 1.6569-1.3431 3-3 3H7c-1.6569 0-3-1.3431-3-3v-9c0-1.3062.8348-2.4175 2-2.8293V4c0-2.2091 1.7909-4 4-4h4.0554c2.2091 0 4 1.7909 4 4v5.1909zM7 11c-.5523 0-1 .4477-1 1v9c0 .5523.4477 1 1 1h10c.5523 0 1-.4477 1-1v-9c0-.5523-.4477-1-1-1H7zm4 4c0-.5523.4477-1 1-1s1 .4477 1 1v3c0 .5523-.4477 1-1 1s-1-.4477-1-1v-3z\\\" id=\\\"icons-default-lock-closed\\\"></path></defs><use xlink:href=\\\"#icons-default-lock-closed\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-location-flag'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M5 0h13.967c1.1045 0 2 .8954 2 2a2 2 0 0 1-.1618.7878L19 7l1.8052 4.2122c.4351 1.0152-.0352 2.191-1.0504 2.626a2 2 0 0 1-.7879.1618H6v9c0 .5523-.4477 1-1 1s-1-.4477-1-1V1c0-.5523.4477-1 1-1zm1 2v10h12.967l-2.143-5 2.143-5H6z\\\" id=\\\"icons-default-flag\\\"></path></defs><use xlink:href=\\\"#icons-default-flag\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-money-cash'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M12.993 7.1194C14.2703 7.4534 15 8.4743 15 10c0 .5523-.4477 1-1 1s-1-.4477-1-1c0-.781-.219-1-1-1s-1 .219-1 1c0 .2811.3025.5332 1.4472 1.1056C14.3025 12.0332 15 12.6145 15 14c0 1.5257-.7296 2.5466-2.007 2.8806.0046.0392.007.079.007.1194 0 .5523-.4477 1-1 1s-1-.4477-1-1c0-.0404.0024-.0802.007-.1194C9.7297 16.5466 9 15.5257 9 14c0-.5523.4477-1 1-1s1 .4477 1 1c0 .781.219 1 1 1s1-.219 1-1c0-.2811-.3025-.5332-1.4472-1.1056C9.6975 11.9668 9 11.3855 9 10c0-1.5257.7296-2.5466 2.007-2.8806A1.0103 1.0103 0 0 1 11 7c0-.5523.4477-1 1-1s1 .4477 1 1c0 .0404-.0024.0802-.007.1194zM2 3h20c1.1046 0 2 .8954 2 2v14c0 1.1046-.8954 2-2 2H2c-1.1046 0-2-.8954-2-2V5c0-1.1046.8954-2 2-2zm0 2v14h20V5H2z\\\" id=\\\"icons-default-money-cash\\\"></path></defs><use xlink:href=\\\"#icons-default-money-cash\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-tools-tools'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7 7.6499V16.35l2.4061 1.0694a1 1 0 0 1 .5939.9138V21c0 .5523-.4477 1-1 1s-1-.4477-1-1v-2.0168l-2-.8889-2 .889V21c0 .5523-.4477 1-1 1s-1-.4477-1-1v-2.6667a1 1 0 0 1 .5939-.9138L5 16.3501V7.65L2.5939 6.5805A1 1 0 0 1 2 5.6667V3c0-.5523.4477-1 1-1s1 .4477 1 1v2.0168l2 .8889 2-.889V3c0-.5523.4477-1 1-1s1 .4477 1 1v2.6667a1 1 0 0 1-.5939.9138L7 7.6499zM18 9v12c0 .5523-.4477 1-1 1s-1-.4477-1-1V9h-2.523c-1.1045 0-2-.8954-2-2a2 2 0 0 1 .143-.7428l1.2-3A2 2 0 0 1 14.677 2H20c1.1046 0 2 .8954 2 2v3c0 1.1046-.8954 2-2 2h-2zm-4.523-2H20V4h-5.323l-1.2 3z\\\" id=\\\"icons-default-tools\\\"></path></defs><use xlink:href=\\\"#icons-default-tools\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-web-dashboard'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M3 2c-.5523 0-1 .4477-1 1v3c0 .5523.4477 1 1 1h3c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1H3zm0-2h3c1.6569 0 3 1.3431 3 3v3c0 1.6569-1.3431 3-3 3H3C1.3431 9 0 7.6569 0 6V3c0-1.6569 1.3431-3 3-3zm11 16h7c1.6569 0 3 1.3431 3 3v2c0 1.6569-1.3431 3-3 3h-7c-1.6569 0-3-1.3431-3-3v-2c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v2c0 .5523.4477 1 1 1h7c.5523 0 1-.4477 1-1v-2c0-.5523-.4477-1-1-1h-7zM3 11h3c1.6569 0 3 1.3431 3 3v7c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3v-7c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v7c0 .5523.4477 1 1 1h3c.5523 0 1-.4477 1-1v-7c0-.5523-.4477-1-1-1H3zM21 0c1.6569 0 3 1.3431 3 3v8c0 1.6569-1.3431 3-3 3h-7c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3h7zm-8 3v8c0 .5523.4477 1 1 1h7c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1h-7c-.5523 0-1 .4477-1 1z\\\" id=\\\"icons-default-dashboard\\\"></path></defs><use xlink:href=\\\"#icons-default-dashboard\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-shopping-cart'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7.8341 20.9863C7.4261 22.1586 6.3113 23 5 23c-1.6569 0-3-1.3431-3-3 0-1.397.9549-2.571 2.2475-2.9048l.4429-1.3286c-1.008-.4238-1.7408-1.3832-1.8295-2.5365l-.7046-9.1593A1.1598 1.1598 0 0 0 1 3c-.5523 0-1-.4477-1-1s.4477-1 1-1c1.651 0 3.0238 1.2712 3.1504 2.9174L23 3.9446c.6306 0 1.1038.5766.9808 1.195l-1.6798 8.4456C22.0218 14.989 20.7899 16 19.3586 16H6.7208l-.4304 1.291a3.0095 3.0095 0 0 1 1.5437 1.7227C7.8881 19.0047 7.9435 19 8 19h8.1707c.4118-1.1652 1.523-2 2.8293-2 1.6569 0 3 1.3431 3 3s-1.3431 3-3 3c-1.3062 0-2.4175-.8348-2.8293-2H8c-.0565 0-.112-.0047-.1659-.0137zm-2.8506-1.9862C4.439 19.009 4 19.4532 4 20c0 .5523.4477 1 1 1s1-.4477 1-1c0-.5467-.4388-.991-.9834-.9999a.9923.9923 0 0 1-.033 0zM6.0231 14h13.3355a1 1 0 0 0 .9808-.805l1.4421-7.2504H4.3064l.5486 7.1321A1 1 0 0 0 5.852 14h.1247a.9921.9921 0 0 1 .0464 0zM19 21c.5523 0 1-.4477 1-1s-.4477-1-1-1-1 .4477-1 1 .4477 1 1 1z\\\" id=\\\"icons-default-cart\\\"></path></defs><use xlink:href=\\\"#icons-default-cart\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-web-bug'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M11 20.8939V14c0-.5523.4477-1 1-1s1 .4477 1 1v6.8939c2.7816-.595 5-3.6253 5-7.3939C18 9.289 15.2303 6 12 6s-6 3.289-6 7.5c0 3.7686 2.2184 6.7989 5 7.3939zM4.841 9.2552l-1.548-1.548c-.3905-.3906-.3905-1.0238 0-1.4143.3905-.3905 1.0237-.3905 1.4142 0l1.138 1.138c.6112-.873 1.3515-1.614 2.1863-2.1814A1.0018 1.0018 0 0 1 8 5c0-2.2091 1.7909-4 4-4 2.2091 0 4 1.7909 4 4 0 .0861-.0109.1697-.0314.2495.8348.5674 1.5751 1.3084 2.1863 2.1814l1.138-1.138c.3905-.3905 1.0237-.3905 1.4142 0 .3905.3905.3905 1.0237 0 1.4142L19.159 9.2552c.479 1.1371.7716 2.4042.8301 3.7449L20 13h2c.5523 0 1 .4477 1 1s-.4477 1-1 1h-2a1.012 1.012 0 0 1-.0985-.0048c-.1296.9736-.3838 1.898-.7425 2.7496l1.5481 1.548c.3905.3906.3905 1.0238 0 1.4143-.3905.3905-1.0237.3905-1.4142 0l-1.138-1.138C16.6874 21.6654 14.4749 23 12 23c-2.475 0-4.6874-1.3346-6.1549-3.4309l-1.138 1.138c-.3905.3905-1.0237.3905-1.4142 0-.3905-.3905-.3905-1.0237 0-1.4142l1.5481-1.5481c-.3587-.8515-.6129-1.776-.7425-2.7496A1.012 1.012 0 0 1 4 15H2c-.5523 0-1-.4477-1-1s.4477-1 1-1h2.0109c.0585-1.3406.3511-2.6077.8301-3.7448zm9.0166-4.9978C13.5628 3.5205 12.8422 3 12 3c-.8422 0-1.5628.5205-1.8576 1.2574A6.8207 6.8207 0 0 1 12 4c.6395 0 1.2615.0891 1.8576.2574z\\\" id=\\\"icons-default-bug\\\"></path></defs><use xlink:href=\\\"#icons-default-bug\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-action-bookmark'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M6 2v18.47l4.3346-2.893a3 3 0 0 1 3.3308 0L18 20.47V2H6zM5 0h14c.5523 0 1 .4477 1 1v21.3398c0 .5523-.4477 1-1 1a1 1 0 0 1-.5551-.1683l-5.8898-3.931a1 1 0 0 0-1.1102 0l-5.8898 3.931c-.4593.3066-1.0803.1828-1.3869-.2766A1 1 0 0 1 4 22.3398V1c0-.5523.4477-1 1-1z\\\" id=\\\"icons-default-bookmark\\\"></path></defs><use xlink:href=\\\"#icons-default-bookmark\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-action-document-view'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m18.6064 17.1922 4.515 4.515c.3904.3904.3904 1.0236 0 1.4141-.3906.3905-1.0238.3905-1.4143 0l-4.515-4.515C16.0237 19.4817 14.5724 20 13 20c-3.866 0-7-3.134-7-7s3.134-7 7-7 7 3.134 7 7c0 1.5723-.5184 3.0236-1.3936 4.1922zM7 2v4c0 .5523-.4477 1-1 1H2v14c0 .5523.4477 1 1 1h11c.5523 0 1 .4477 1 1s-.4477 1-1 1H3c-1.6569 0-3-1.3431-3-3V6c0-3.3137 2.6863-6 6-6h9c1.6569 0 3 1.3431 3 3v1c0 .5523-.4477 1-1 1s-1-.4477-1-1V3c0-.5523-.4477-1-1-1H7zM2.126 5H5V2.126C3.5944 2.4878 2.4878 3.5944 2.126 5zM13 18c2.7614 0 5-2.2386 5-5s-2.2386-5-5-5-5 2.2386-5 5 2.2386 5 5 5z\\\" id=\\\"icons-default-document-view\\\"></path></defs><use xlink:href=\\\"#icons-default-document-view\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-action-save'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7 2H4c-.5523 0-1 .4477-1 1v18c0 .5523.4477 1 1 1h16c.5523 0 1-.4477 1-1V5.7016L18.0388 2H17v6c0 .5523-.4477 1-1 1H8c-.5523 0-1-.4477-1-1V2zM4 0h15l4 5v16c0 1.6569-1.3431 3-3 3H4c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm5 7h6V2H9v5zm-1 8c-.5523 0-1-.4477-1-1s.4477-1 1-1h8c.5523 0 1 .4477 1 1s-.4477 1-1 1H8zm0 4c-.5523 0-1-.4477-1-1s.4477-1 1-1h8c.5523 0 1 .4477 1 1s-.4477 1-1 1H8z\\\" id=\\\"icons-default-save\\\"></path></defs><use xlink:href=\\\"#icons-default-save\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'regular-search'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></path><path d=\\\"M0 0h24v24H0z\\\" fill=\\\"none\\\"/></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-action-tray-up'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m13 14.5858 4.2929-4.293c.3905-.3904 1.0237-.3904 1.4142 0 .3905.3906.3905 1.0238 0 1.4143l-6 6c-.3905.3905-1.0237.3905-1.4142 0l-6-6c-.3905-.3905-.3905-1.0237 0-1.4142.3905-.3905 1.0237-.3905 1.4142 0L11 14.5858V1c0-.5523.4477-1 1-1s1 .4477 1 1v13.5858zM2 22.0416h20V17c0-.5523.4477-1 1-1s1 .4477 1 1v6.0416c0 .5523-.4477 1-1 1H1c-.5523 0-1-.4477-1-1v-5.9794c0-.5523.4477-1 1-1s1 .4477 1 1v4.9794z\\\" id=\\\"icons-default-tray-up\\\"></path></defs><use xlink:href=\\\"#icons-default-tray-up\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-basic-checkmark-circle'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M24 12c0 6.6274-5.3726 12-12 12S0 18.6274 0 12 5.3726 0 12 0s12 5.3726 12 12zM12 2C6.4772 2 2 6.4772 2 12s4.4772 10 10 10 10-4.4772 10-10S17.5228 2 12 2zM7.7071 12.2929 10 14.5858l6.2929-6.293c.3905-.3904 1.0237-.3904 1.4142 0 .3905.3906.3905 1.0238 0 1.4143l-7 7c-.3905.3905-1.0237.3905-1.4142 0l-3-3c-.3905-.3905-.3905-1.0237 0-1.4142.3905-.3905 1.0237-.3905 1.4142 0z\\\" id=\\\"icons-default-checkmark-circle\\\"></path></defs><use xlink:href=\\\"#icons-default-checkmark-circle\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-basic-plus-line'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M11 11V3c0-.5523.4477-1 1-1s1 .4477 1 1v8h8c.5523 0 1 .4477 1 1s-.4477 1-1 1h-8v8c0 .5523-.4477 1-1 1s-1-.4477-1-1v-8H3c-.5523 0-1-.4477-1-1s.4477-1 1-1h8z\\\" id=\\\"icons-default-plus\\\"></path></defs><use xlink:href=\\\"#icons-default-plus\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-web-layout'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7 9H2V7h20v2H9v13H7V9zM3 0h18c1.6569 0 3 1.3431 3 3v18c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v18c0 .5523.4477 1 1 1h18c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1H3z\\\" id=\\\"icons-default-layout\\\"></path></defs><use xlink:href=\\\"#icons-default-layout\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-object-image'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m2 20.4948 7.2526-8.1592c.3822-.43 1.0477-.4496 1.4545-.0427l2 2a1.066 1.066 0 0 1 .0427.0453l2.482-2.9784c.3764-.4517 1.0595-.4827 1.4753-.067L22 16.5859V3c0-.5523-.4477-1-1-1H3c-.5523 0-1 .4477-1 1v17.4948zM3.338 22H21c.5523 0 1-.4477 1-1v-1.5858l-5.9328-5.9328-4.299 5.1588c-.3535.4243-.9841.4816-1.4084.128-.4243-.3535-.4816-.9841-.128-1.4084l1.2524-1.5028a1.0023 1.0023 0 0 1-.1913-.1499l-1.25-1.25L3.3378 22zM3 0h18c1.6569 0 3 1.3431 3 3v18c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm4.5 11C5.567 11 4 9.433 4 7.5S5.567 4 7.5 4 11 5.567 11 7.5 9.433 11 7.5 11zm0-2C8.3284 9 9 8.3284 9 7.5S8.3284 6 7.5 6 6 6.6716 6 7.5 6.6716 9 7.5 9z\\\" id=\\\"icons-default-image\\\"></path></defs><use xlink:href=\\\"#icons-default-image\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-object-shield'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M4 12.8348A6 6 0 0 0 6.5592 17.75L12 21.5587l5.4408-3.8086A6 6 0 0 0 20 12.8348v-9.442a24.9264 24.9264 0 0 0-16 0v9.442zM22 2v10.8348a8 8 0 0 1-3.4123 6.5538l-6.0142 4.21a1 1 0 0 1-1.147 0l-6.0142-4.21A8 8 0 0 1 2 12.8348V2a26.9258 26.9258 0 0 1 20 0z\\\" id=\\\"icons-default-shield\\\"></path></defs><use xlink:href=\\\"#icons-default-shield\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-object-traffic-pawn'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m6.4692 12 2.182-8.7276C8.985 1.9369 10.1848 1 11.5615 1h.8768c1.3767 0 2.5766.9369 2.9105 2.2724L17.5308 12h2.667a2 2 0 0 1 1.9524 1.5661l1.5556 7a2 2 0 0 1 .0476.4339c0 1.1046-.8954 2-2 2H2.2466a2 2 0 0 1-.4338-.0476C.7345 22.7128.0546 21.6444.2942 20.566l1.5556-7A2 2 0 0 1 3.8022 12h2.667zm9.5 2-1-4H9.0308l-1 4h7.9384zm-10 0h-2.167l-1.5556 7h19.5068l-1.5556-7h-2.167l.9393 3.7575c.134.5358-.1918 1.0787-.7276 1.2126-.5358.134-1.0787-.1918-1.2126-.7276L16.4692 16H7.5308L6.97 18.2425c-.134.5358-.6768.8616-1.2126.7276-.5358-.134-.8616-.6768-.7276-1.2126L5.9692 14zm3.5616-6h4.9384l-1.0606-4.2425A1 1 0 0 0 12.4384 3h-.8768a1 1 0 0 0-.9702.7575L9.5308 8z\\\" id=\\\"icons-default-traffic-pawn\\\"></path></defs><use xlink:href=\\\"#icons-default-traffic-pawn\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-object-plug'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M19 16v1c0 1.1046-.8954 2-2 2H9c-2.2091 0-4-1.7909-4-4v-2H1c-.5523 0-1-.4477-1-1s.4477-1 1-1h4V9c0-2.2091 1.7909-4 4-4h8c1.1046 0 2 .8954 2 2v1h3c.5523 0 1 .4477 1 1s-.4477 1-1 1h-3v4h3c.5523 0 1 .4477 1 1s-.4477 1-1 1h-3zM9 7c-1.1046 0-2 .8954-2 2v6c0 1.1046.8954 2 2 2h8V7H9zm-1 4c0-.5523.4477-1 1-1s1 .4477 1 1v2c0 .5523-.4477 1-1 1s-1-.4477-1-1v-2z\\\" id=\\\"icons-default-plug\\\"></path></defs><use xlink:href=\\\"#icons-default-plug\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-object-beer'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M14 6h4.134a.9995.9995 0 0 1 .2654-.2994A1.496 1.496 0 0 0 19 4.5c0-.8284-.6716-1.5-1.5-1.5a1.492 1.492 0 0 0-1.0658.4445c-.4906.4954-1.3249.3492-1.6179-.2835A2.0002 2.0002 0 0 0 13 2a1.9996 1.9996 0 0 0-1.7821 1.0909c-.2946.5756-1.0498.7246-1.5409.3038C9.392 3.1506 8.968 3 8.5 3c-.7264 0-1.3134.3611-1.4636.7894-.2157.6148-.9523.8625-1.4956.5028C5.2616 4.1074 4.8957 4 4.5 4 3.6287 4 3 4.503 3 5v1.5C3 7.3284 3.6716 8 4.5 8c.7165 0 1.3295-.5067 1.47-1.199A1 1 0 0 1 6.95 6H14zm-3 2H7.6633C7.0971 9.1938 5.8816 10 4.5 10c-.1698 0-.3367-.012-.5-.0354V22h14V8h-7zm-9 .9495C1.3814 8.3182 1 7.4537 1 6.5V5c0-1.712 1.61-3 3.5-3 .4177 0 .826.0626 1.2088.1823C6.3605 1.4459 7.3887 1 8.5 1c.5432 0 1.0698.106 1.544.3045A3.9958 3.9958 0 0 1 13 0a3.997 3.997 0 0 1 2.9897 1.342A3.493 3.493 0 0 1 17.5 1C19.433 1 21 2.567 21 4.5c0 .9325-.368 1.8051-1 2.4497V9h1c1.6569 0 3 1.3431 3 3v5c0 1.6569-1.3431 3-3 3h-1v2c0 1.1046-.8954 2-2 2H4c-1.1046 0-2-.8954-2-2V8.9495zM20 11v7h1c.5523 0 1-.4477 1-1v-5c0-.5523-.4477-1-1-1h-1zM6 12c0-.5523.4477-1 1-1s1 .4477 1 1v6c0 .5523-.4477 1-1 1s-1-.4477-1-1v-6zm4 0c0-.5523.4477-1 1-1s1 .4477 1 1v6c0 .5523-.4477 1-1 1s-1-.4477-1-1v-6zm4 0c0-.5523.4477-1 1-1s1 .4477 1 1v6c0 .5523-.4477 1-1 1s-1-.4477-1-1v-6z\\\" id=\\\"icons-default-beer\\\"></path></defs><use xlink:href=\\\"#icons-default-beer\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-documentation-paperclip'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path id=\\\"icons-default-paperclip\\\" d=\\\"M8 16V5.5C8 3.567 9.567 2 11.5 2S15 3.567 15 5.5v14.5294c0 1.1045-.8954 2-2 2s-2-.8955-2-2V5.4618a.5001.5001 0 1 1 1.0002 0v10.5934c0 .5523.4477 1 1 1s1-.4477 1-1V5.4618c0-1.3808-1.1194-2.5001-2.5001-2.5001-1.3808 0-2.5002 1.1193-2.5002 2.5001v14.5676c0 2.2091 1.791 4 4 4 2.2092 0 4.0001-1.7909 4.0001-4V5.5C17 2.4624 14.5376 0 11.5 0S6 2.4624 6 5.5V16c0 .5523.4477 1 1 1s1-.4477 1-1z\\\"></path></defs><use xlink:href=\\\"#icons-default-paperclip\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-device-printer'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M5.1707 22H3c-1.6569 0-3-1.3431-3-3v-7c0-1.6569 1.3431-3 3-3h2V1c0-.5523.4477-1 1-1h12c.5523 0 1 .4477 1 1v8h2c1.6569 0 3 1.3431 3 3v7c0 1.6569-1.3431 3-3 3h-2.1707c-.4118 1.1652-1.523 2-2.8293 2H8c-1.3062 0-2.4175-.8348-2.8293-2zm0-2c.4118-1.1652 1.523-2 2.8293-2h8c1.3062 0 2.4175.8348 2.8293 2H21c.5523 0 1-.4477 1-1v-7c0-.5523-.4477-1-1-1H3c-.5523 0-1 .4477-1 1v7c0 .5523.4477 1 1 1h2.1707zM7 2v7h10V2H7zm1 18c-.5523 0-1 .4477-1 1s.4477 1 1 1h8c.5523 0 1-.4477 1-1s-.4477-1-1-1H8zm-3-7c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1z\\\" id=\\\"icons-default-printer\\\"></path></defs><use xlink:href=\\\"#icons-default-printer\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-device-harddisk'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M21.4257 15.03 19.323 3.8157A1 1 0 0 0 18.3401 3H5.66a1 1 0 0 0-.983.8157L2.5744 15.03c.139-.0198.2812-.03.4257-.03h18c.1445 0 .2866.0102.4257.03zm2.405 1.9738A2.9942 2.9942 0 0 1 24 18.02V20c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3v-1.98a2.9942 2.9942 0 0 1 .1694-1.0162L2.7112 3.4471C2.9772 2.0282 4.2162 1 5.66 1h12.68c1.4437 0 2.6826 1.0282 2.9487 2.4471l2.5418 13.5567zm-1.9295.5623A1 1 0 0 0 21 17H3a1 1 0 0 0-.9012.5661L2 18.0929V20c0 .5523.4477 1 1 1h18c.5523 0 1-.4477 1-1v-1.907l-.0988-.5269zM17 18c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zm3 0c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1z\\\" id=\\\"icons-default-harddisk\\\"></path></defs><use xlink:href=\\\"#icons-default-harddisk\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-chart-sales'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M18.5858 6H16c-.5523 0-1-.4477-1-1s.4477-1 1-1h5c.5523 0 1 .4477 1 1v5c0 .5523-.4477 1-1 1s-1-.4477-1-1V7.4142l-5.2929 5.293c-.3905.3904-1.0237.3904-1.4142 0L12 11.4141l-6.2929 6.293c-.3905.3904-1.0237.3904-1.4142 0-.3905-.3906-.3905-1.0238 0-1.4143l7-7c.3905-.3905 1.0237-.3905 1.4142 0L14 10.5858 18.5858 6zM0 1c0-.5523.4477-1 1-1s1 .4477 1 1v20c0 .5523.4477 1 1 1h20c.5523 0 1 .4477 1 1s-.4477 1-1 1H3c-1.6569 0-3-1.3431-3-3V1z\\\" id=\\\"icons-default-chart-sales\\\"></path></defs><use xlink:href=\\\"#icons-default-chart-sales\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                <svg v-if=\\\"element.config.iconCategories.value[n] == 'default-building-home'\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M13 21v-7c0-1.1046.8954-2 2-2h2c1.1046 0 2 .8954 2 2v7h2.0499v-8.9246L12 7.139l-9 4.909V21h10zm10.0499-8v8c0 1.1046-.8955 2-2 2H3c-1.1046 0-2-.8954-2-2v-7.9986C.4771 13.0008 0 12.5817 0 12V7a1 1 0 0 1 .5211-.8779l11-6a1 1 0 0 1 .9578 0l11 6A1 1 0 0 1 24 7v5c0 .5631-.4472.974-.9501 1zM2 10.3156l9.5211-5.1934a1 1 0 0 1 .9578 0L22 10.3155V7.5936L12 2.1391 2 7.5936v2.7219zM15 14v7h2v-7h-2zm-8-2h2c1.1046 0 2 .8954 2 2v2c0 1.1046-.8954 2-2 2H7c-1.1046 0-2-.8954-2-2v-2c0-1.1046.8954-2 2-2zm0 2v2h2v-2H7z\\\" id=\\\"icons-default-home\\\"></path></defs><use xlink:href=\\\"#icons-default-home\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n            </div>\\n            <div class=\\\"mw-faq-category-headline\\\">\\n                {{ element.config.headlineCategories.value[n] }}\\n            </div>\\n            <div class=\\\"mw-faq-category-description\\\" v-html=\\\"element.config.descriptionCategories.value[n]\\\">\\n                {{ element.config.descriptionCategories.value[n] }}\\n            </div>\\n            </label>\\n        </li>\\n        </ul>\\n    </div>\\n    <div class=\\\"clearer\\\"></div>\\n\\n    <div v-if=\\\"element.config.showFaqHeadline.value === true\\\" class=\\\"mw-faq-cms-headline\\\">\\n        <h2>{{ element.config.textFaqHeadline.value }}</h2>\\n            <hr>\\n    </div>\\n\\n    <div v-if=\\\"element.config.showSearch.value === true\\\" class=\\\"mw-faq-search\\\">\\n        <svg class=\\\"mw-faq-cms-search-svg-question-section\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></path><path d=\\\"M0 0h24v24H0z\\\" fill=\\\"none\\\"/></svg>\\n        <input type=\\\"text\\\" v-model=\\\"element.config.searchTerm.value\\\" :placeholder=\\\"$tc('sw-cms.elements.faqconfig.component.placeholder.searchTerm')\\\" />\\n    </div>\\n    <div class=\\\"mw-faq-question-section\\\">\\n        <div v-for=\\\"n in element.config.numberQuestions.value\\\" class=\\\"mw-faq-questions\\\" :style=\\\"'order:' + element.config.sorting.value[n]\\\">\\n            <div class=\\\"tab\\\" :class=\\\"element.config.questionCategory.value[n]\\\">\\n                <input type=\\\"checkbox\\\" :id=\\\"'chck' + n\\\">\\n                <label class=\\\"tab-label\\\" :for=\\\"'chck' + n\\\" :style=\\\"'background: ' + element.config.TabBackgroundColor.value + ';color: ' + element.config.TabFontColor.value\\\">\\n                    {{ element.config.questions.value[n] }}\\n                </label>\\n                <div class=\\\"tab-content\\\" v-html=\\\"element.config.answers.value[n]\\\">\\n                    {{ element.config.answers.value[n] }}\\n                </div>\\n            </div>\\n        </div>\\n    </div>\\n</div>\\n\\n{% endblock %}\\n\";", "import template from './sw-cms-el-faqconfig.html.twig';\nimport './sw-cms-el-faqconfig.scss';\n\nconst { Component, Mixin } = Shopware;\n\nComponent.register('sw-cms-el-faqconfig', {\n    template,\n\n    mixins: [\n        Mixin.getByName('cms-element')\n    ],\n    computed: {\n        customCatImageWidth(){\n            var value = this.element.config.customCatImageWidth.value;\n            var setCustomWidth = this.element.config.setCatImageWidth.value;\n            if (setCustomWidth == true){\n                return 'width:' + value + 'px;max-width:100%;height:auto';\n            } else {\n                return {\n                    'object-fit': this.element.config.imageMode.value,\n                    'display': this.element.config.imageDisplay.value,\n                    'width' : this.element.config.imageWidth.value\n                }\n            }\n            \n        },\n        customCatHeight(){\n            if (this.element.config.setCustomCatHeight.value === true){\n                return 'min-height:' + this.element.config.customCatHeight.value + 'px';\n            }\n        },\n        styles() {\n            return {\n                'min-height': this.element.config.displayMode.value === 'cover' &&\n                this.element.config.minHeight.value !== 0 ? this.element.config.minHeight.value : '340px',\n                'align-self': !this.element.config.verticalAlign.value ? null : this.element.config.verticalAlign.value\n            };\n        },\n        mediaStyles(){\n            return {\n                'object-fit': this.element.config.imageMode.value,\n                'display': this.element.config.imageDisplay.value,\n                'width' : this.element.config.imageWidth.value\n            }\n        },\n        mediaUrl() {\n            const context = Shopware.Context.api;\n            const elemData = this.element.data.media;\n            const mediaSource = this.element.config.media.source;\n\n             if (mediaSource === 'mapped') {\n                const demoMedia = this.getDemoValue(this.element.config.media.value);\n\n                if (demoMedia && demoMedia.url) {\n                    return demoMedia.url;\n                }\n            } \n\n            if (elemData && elemData.id) {\n                return this.element.data.media.url;\n            }\n\n            if (elemData && elemData.url) {\n                return `${context.assetsPath}${elemData.url}`;\n            }\n\n            return `${context.assetsPath}/administration/static/img/cms/preview_mountain_large.jpg`;\n        }\n    },\n    watch: {\n        cmsPageState: {\n            deep: true,\n            handler() {\n                this.$forceUpdate();\n            }\n        }\n    },\n    created() {\n        this.createdComponent();\n    },\n    methods: {\n        createdComponent() {\n            this.initElementConfig('faqconfig');\n            this.initElementData('faqconfig');\n        }\n    }\n});\n", "import template from './sw-cms-el-config-faqconfig.html.twig';\nimport './sw-cms-el-config-faqconfig.scss';\n\nconst { Component, Mixin } = Shopware;\n\nComponent.register('sw-cms-el-config-faqconfig', {\n    template,\n\n    mixins: [\n        Mixin.getByName('cms-element')\n    ],\n    inject: ['repositoryFactory'],\n\n    data() {\n        return {\n            mediaModalIsOpen: false,\n            initialFolderId: null,\n        };\n    },\n\n    computed: {\n        mediaRepository() {\n            return this.repositoryFactory.create('media');\n        },\n\n        uploadTag() {\n            return `cms-element-media-config-${this.element.id}`;\n        },\n\n        previewSource() {\n            if (this.element.data && this.element.data.media && this.element.data.media.id) {\n                return this.element.data.media;\n            }\n\n            return this.element.config.media.value;\n        }\n    },\n\n    created() {\n        this.createdComponent();\n    },\n\n    methods: {\n        createdComponent() {\n            this.initElementConfig('faqconfig');\n        },\n\n        async onImageUpload({ targetId }) {\n            const mediaEntity = await this.mediaRepository.get(targetId, Shopware.Context.api);\n\n            this.element.config.media.value = mediaEntity.id;\n\n            this.updateElementData(mediaEntity);\n\n            this.$emit('element-update', this.element);\n        },\n\n        onImageRemove() {\n            this.element.config.media.value = null;\n\n            this.updateElementData();\n\n            this.$emit('element-update', this.element);\n        },\n\n        onCloseModal() {\n            this.mediaModalIsOpen = false;\n        },\n\n        onSelectionChanges(mediaEntity) {\n            const media = mediaEntity[0];\n            this.element.config.media.value = media.id;\n\n            this.updateElementData(media);\n\n            this.$emit('element-update', this.element);\n        },\n\n        updateElementData(media = null) {\n            this.$set(this.element.data, 'mediaId', media === null ? null : media.id);\n            this.$set(this.element.data, 'media', media);\n        },\n\n        onOpenMediaModal() {\n            this.mediaModalIsOpen = true;\n        },\n\n        onChangeMinHeight(value) {\n            this.element.config.minHeight.value = value === null ? '' : value;\n\n            this.$emit('element-update', this.element);\n        },\n\n        onChangeDisplayMode(value) {\n            if (value === 'cover') {\n                this.element.config.verticalAlign.value = null;\n            } else {\n                this.element.config.minHeight.value = '';\n            }\n\n            this.$emit('element-update', this.element);\n        }\n    }\n});\n", "export default \"{% block sw_cms_element_faq_config %}\\n<div class=\\\"sw-cms-el-config-faqconfig\\\">\\n    {# Default Tab #}\\n    <sw-tabs defaultItem=\\\"settings\\\">\\n        {# All Tabs #}\\n        <template slot-scope=\\\"{ active }\\\">\\n            <sw-tabs-item name=\\\"settings\\\" :activeTab=\\\"active\\\">{{ $tc('sw-cms.elements.faqconfig.config.tab.settings') }}</sw-tabs-item>\\n            <sw-tabs-item name=\\\"content-categories\\\" :activeTab=\\\"active\\\">{{ $tc('sw-cms.elements.faqconfig.config.tab.contentCategories') }}\\n            </sw-tabs-item>\\n            <sw-tabs-item name=\\\"content-questions\\\" :activeTab=\\\"active\\\">{{ $tc('sw-cms.elements.faqconfig.config.tab.contentQuestions') }}\\n            </sw-tabs-item>\\n            <sw-tabs-item name=\\\"layout\\\" :activateTab=\\\"active\\\">{{ $tc('sw-cms.elements.faqconfig.config.tab.layout') }}</sw-tabs-item>\\n        </template>\\n        <template slot=\\\"content\\\" slot-scope=\\\"{ active }\\\">\\n            {# Tab Settings #}\\n            <sw-container v-if=\\\"active === 'settings'\\\" class=\\\"sw-cms-el-config-form__tab-content\\\">\\n                <div class=\\\"counter-wrapper\\\">\\n                    <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.displayElements') }}</span>\\n                    <div class=\\\"count-item\\\">\\n                        <sw-switch-field v-model=\\\"element.config.showImage.value\\\"\\n                                         :label=\\\"$tc('sw-cms.elements.faqconfig.config.label.imageFrontend')\\\"></sw-switch-field>\\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                        <div class=\\\"search-help-text\\\">\\n                            <sw-help-text :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.search')\\\" :width=\\\"300\\\" tooltipPosition=\\\"top\\\"></sw-help-text>\\n                        </div>\\n                        <sw-switch-field v-model=\\\"element.config.showSearch.value\\\"\\n                                         :label=\\\"$tc('sw-cms.elements.faqconfig.config.label.searchFrontend')\\\"></sw-switch-field>                \\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                        <sw-switch-field v-model=\\\"element.config.showCategories.value\\\"\\n                                         :label=\\\"$tc('sw-cms.elements.faqconfig.config.label.categoryFrontend')\\\"></sw-switch-field>\\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                        <sw-switch-field v-model=\\\"element.config.showSearchHeadline.value\\\"\\n                                         :label=\\\"$tc('sw-cms.elements.faqconfig.config.label.searchHeadlineFrontend')\\\"></sw-switch-field>\\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                        <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.searchHeadline') }}</span>\\n                        <sw-text-field v-model=\\\"element.config.textSearchHeadline.value\\\"\\n                                         :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.textSearchHeadline')\\\"></sw-text-field>\\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                        <sw-switch-field v-model=\\\"element.config.showFaqHeadline.value\\\"\\n                                         :label=\\\"$tc('sw-cms.elements.faqconfig.config.label.faqHeadlineFrontend')\\\"></sw-switch-field>\\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                        <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.faqHeadline') }}</span>\\n                        <sw-text-field v-model=\\\"element.config.textFaqHeadline.value\\\"\\n                                         :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.textFaqHeadline')\\\"></sw-text-field>\\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                        <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.numberCategory') }} <sw-help-text\\n                                    :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.numberCategory')\\\" :width=\\\"300\\\"\\n                                    tooltipPosition=\\\"top\\\"></sw-help-text></span>\\n                        <div class=\\\"counter-item-inner\\\">\\n                            <sw-number-field :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.numberCategory')\\\" numberType=\\\"int\\\"\\n                                             :step=\\\"1\\\" :min=\\\"1\\\" :max=\\\"8\\\" :value=\\\"null\\\"\\n                                             v-model=\\\"element.config.numberCategories.value\\\"></sw-number-field>\\n                        </div>\\n                    </div>\\n                    <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.label.iconorimage') }} <sw-help-text\\n                                    :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.mediaCategory')\\\" :width=\\\"300\\\"\\n                                    tooltipPosition=\\\"top\\\"></sw-help-text>\\n                            </span>\\n                        <sw-select-field v-model=\\\"element.config.IconOrImage.value\\\" :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.iconorimage')\\\">\\n                            <option value=\\\"icon\\\">{{ $tc('sw-cms.elements.faqconfig.config.option.icon') }}</option>\\n                            <option value=\\\"image\\\">{{ $tc('sw-cms.elements.faqconfig.config.option.image') }}</option>\\n                        </sw-select-field>\\n                    </div>\\n                    <span v-if=\\\"element.config.IconOrImage.value === 'image'\\\">\\n                        <sw-switch-field v-model=\\\"element.config.setCatImageWidth.value\\\" :label=\\\"$tc('sw-cms.elements.faqconfig.config.category.setCustomWidth')\\\"></sw-switch-field>\\n                    </span>\\n                    <span v-if=\\\"element.config.setCatImageWidth.value === true && element.config.IconOrImage.value === 'image'\\\">\\n                        <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.categoryImageWidth') }} <sw-help-text\\n                                    :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.categoryImageWidth')\\\" :width=\\\"300\\\"\\n                                    tooltipPosition=\\\"top\\\"></sw-help-text></span>\\n                        <sw-number-field numberType=\\\"int\\\"\\n                                             :step=\\\"1\\\" :value=\\\"null\\\"\\n                                             v-model=\\\"element.config.customCatImageWidth.value\\\"></sw-number-field>\\n                    </span>\\n\\n\\n                    <div class=\\\"count-item\\\">\\n                        <sw-switch-field v-model=\\\"element.config.setCustomCatHeight.value\\\"\\n                                         :label=\\\"$tc('sw-cms.elements.faqconfig.config.label.setCustomCatHeight')\\\"></sw-switch-field>\\n                    </div>\\n                    <div class=\\\"counter-item\\\" v-if=\\\"element.config.setCustomCatHeight.value === true\\\">\\n                        <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.customCatHeight') }}</span>\\n                        <sw-number-field numberType=\\\"int\\\"\\n                                            :step=\\\"1\\\" :min=\\\"1\\\" :value=\\\"null\\\"\\n                                            v-model=\\\"element.config.customCatHeight.value\\\"></sw-number-field>\\n                    </div>\\n\\n\\n\\n                    <div class=\\\"count-item\\\">\\n                        <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.numberQuestion') }} <sw-help-text\\n                                    :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.numberQuestion')\\\" :width=\\\"300\\\"\\n                                    tooltipPosition=\\\"top\\\"></sw-help-text></span>\\n                        <div class=\\\"counter-item-inner\\\">\\n                            <sw-number-field :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.numberQuestion')\\\" numberType=\\\"int\\\"\\n                                             :step=\\\"1\\\" :min=\\\"1\\\" :max=\\\"150\\\" :value=\\\"null\\\"\\n                                             v-model=\\\"element.config.numberQuestions.value\\\"></sw-number-field>\\n                        </div>\\n                    </div>\\n                </div>\\n            </sw-container>\\n            {# Tab Content - Categories #}\\n            <sw-container v-else-if=\\\"active === 'content-categories'\\\" class=\\\"sw-cms-el-config-form__tab-options\\\">\\n                <div v-for=\\\"n in element.config.numberCategories.value\\\" class=\\\"content-wrapper\\\">\\n                    <sw-card-view style=\\\"position: relative;\\\">\\n                        <span class=\\\"count-headline\\\">{{ $tc('sw-cms.elements.faqconfig.config.headline.settingCategory') }} {{ n }}</span>\\n                        <sw-card>\\n                            <div class=\\\"count-item\\\" v-if=\\\"element.config.IconOrImage.value == 'icon'\\\">\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.category.icon') }}</span>\\n                                <div class=\\\"radio-icons\\\">\\n                                <sw-radio-panel value=\\\"default-package-open\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M13 3.6995v3.7463l2.4181 1.5113L18.941 7 13 3.6995zm.4203 6.3675L12 9.1792l-1.4203.8878L12 10.856l1.4203-.789zM8.582 8.957 11 7.4459V3.6995L5.0591 7 8.582 8.957zm13.4126-2.063 1.934 4.835a1 1 0 0 1-.4429 1.2455L22 13.7999V17a1 1 0 0 1-.5144.8742l-9 5a1 1 0 0 1-.9712 0l-9-5A1 1 0 0 1 2 17v-3.2001l-1.4856-.8254a1 1 0 0 1-.4429-1.2455l1.934-4.835c.0318-.305.2015-.5974.5089-.7682l9-5a1 1 0 0 1 .9712 0l9 5c.3074.1708.477.4632.5089.7682zM20 14.911l-5.5144 3.0635c-.5265.2925-1.1904.0565-1.414-.5028L12 14.793l-1.0715 2.6788c-.2237.5593-.8876.7953-1.4141.5028L4 14.911v1.5006l8 4.4444 8-4.4444V14.911zm-9.2556-2.4646L3.5068 8.4255l-1.2512 3.128 7.2376 4.021 1.2512-3.128zm2.5112 0 1.2512 3.128 7.2376-4.0208-1.2512-3.128-7.2376 4.0208z\\\" id=\\\"icons-default-package-open\\\"></path></defs><use xlink:href=\\\"#icons-default-package-open\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-avatar-single\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\\\" fill=\\\"#758CA3\\\" /><path d=\\\"M0 0h24v24H0z\\\" fill=\\\"none\\\"/></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-action-settings\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M12 16c-2.2091 0-4-1.7909-4-4 0-2.2091 1.7909-4 4-4 2.2091 0 4 1.7909 4 4 0 2.2091-1.7909 4-4 4zm0-2c1.1046 0 2-.8954 2-2s-.8954-2-2-2-2 .8954-2 2 .8954 2 2 2zm8.2988-5.2848A.6474.6474 0 0 0 20.83 9H21c1.6569 0 3 1.3431 3 3s-1.3431 3-3 3h-.086a.65.65 0 0 0-.5949.394l-.0042.0098c-.1073.243-.0558.527.1222.709l.0596.0597a3 3 0 0 1 .0008 4.2442 3 3 0 0 1-4.2446.0004l-.0522-.0522c-.19-.1858-.4738-.2373-.7268-.1258-.238.1021-.3929.3358-.3939.5909V21c0 1.6569-1.3431 3-3 3s-3-1.3431-3-3l.0003-.0666c-.0063-.2668-.175-.5027-.484-.6185-.2432-.1073-.527-.0558-.7092.1222l-.0596.0596a3 3 0 0 1-4.2442.0008 3 3 0 0 1-.0004-4.2446l.0522-.0522c.1858-.19.2373-.4738.1258-.7268-.1021-.238-.3358-.3929-.5909-.3939H3c-1.6569 0-3-1.3431-3-3s1.3431-3 3-3l.0666.0003c.2668-.0063.5027-.175.6185-.484.1073-.2432.0558-.527-.1222-.7092l-.0596-.0596a3 3 0 0 1-.0008-4.2442 3 3 0 0 1 4.2446-.0004l.0522.0522a.65.65 0 0 0 .717.13 1 1 0 0 1 .1989-.0639A.6474.6474 0 0 0 9 3.17V3c0-1.6569 1.3431-3 3-3s3 1.3431 3 3v.086c.001.259.1558.4928.4038.5991.243.1073.527.0558.709-.1222l.0597-.0596a3 3 0 0 1 4.2442-.0008 3 3 0 0 1 .0004 4.2446l-.0522.0522a.65.65 0 0 0-.13.717 1 1 0 0 1 .0639.1989zM20.91 13H21c.5523 0 1-.4477 1-1s-.4477-1-1-1h-.174a2.65 2.65 0 0 1-2.4251-1.606 1 1 0 0 1-.0724-.2642c-.3313-.945-.1015-2.0052.6144-2.737l.0604-.0603a1 1 0 0 0-.0008-1.4158 1 1 0 0 0-1.4154.0004l-.0678.0678c-.7745.7575-1.932.9674-2.9132.5342C13.6353 5.1031 13.0042 4.1502 13 3.09V3c0-.5523-.4477-1-1-1s-1 .4477-1 1v.174a2.65 2.65 0 0 1-1.606 2.425 1 1 0 0 1-.2642.0724c-.945.3313-2.0052.1015-2.737-.6144l-.0603-.0604a1 1 0 0 0-1.4158.0008 1 1 0 0 0 .0004 1.4154l.0678.0678c.7575.7745.9674 1.932.5536 2.8645-.3756 1.0212-1.3373 1.709-2.4485 1.7348H3c-.5523 0-1 .4477-1 1s.4477 1 1 1h.174c1.0562.0042 2.009.6353 2.4209 1.5962.4374.9911.2275 2.1486-.5378 2.931l-.0604.0603a1 1 0 0 0 .0008 1.4158 1 1 0 0 0 1.4154-.0004l.0678-.0678c.7745-.7575 1.932-.9674 2.8645-.5536 1.0212.3756 1.709 1.3373 1.7348 2.4485V21c0 .5523.4477 1 1 1s1-.4477 1-1v-.174c.0042-1.0562.6353-2.009 1.5962-2.4209.9911-.4374 2.1486-.2275 2.931.5378l.0603.0604a1 1 0 0 0 1.4158-.0008 1 1 0 0 0-.0004-1.4154l-.0678-.0678a2.65 2.65 0 0 1-.5321-2.9182c.4172-.968 1.3686-1.5969 2.427-1.6011z\\\" id=\\\"icons-default-settings\\\"></path></defs><use xlink:href=\\\"#icons-default-settings\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-badge-warning\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m13.7744 1.4124 9.7058 18.6649c.5096.98.1283 2.1875-.8517 2.6971a2 2 0 0 1-.9227.2256H2.2942c-1.1045 0-2-.8954-2-2a2 2 0 0 1 .2256-.9227l9.7058-18.665c.5096-.98 1.7171-1.3613 2.6971-.8517a2 2 0 0 1 .8517.8518zM2.2942 21h19.4116L12 2.335 2.2942 21zM12 17c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zm1-2c0 .5523-.4477 1-1 1s-1-.4477-1-1v-5c0-.5523.4477-1 1-1s1 .4477 1 1v5z\\\" id=\\\"icons-default-warning\\\"></path></defs><use xlink:href=\\\"#icons-default-warning\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-badge-help\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M24 12c0 6.6274-5.3726 12-12 12S0 18.6274 0 12 5.3726 0 12 0s12 5.3726 12 12zM12 2C6.4772 2 2 6.4772 2 12s4.4772 10 10 10 10-4.4772 10-10S17.5228 2 12 2zm0 13c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zm-1-5c0 .5523-.4477 1-1 1s-1-.4477-1-1c0-1.8856 1.1144-3 3-3s3 1.1144 3 3c0 1.1817-.434 1.7713-1.3587 2.3496l-.1078.0674c-.4464.2817-.5335.4154-.5335 1.083 0 .5523-.4477 1-1 1s-1-.4477-1-1c0-1.4161.4788-2.1515 1.4665-2.7745l.1142-.0716C12.9784 10.4052 13 10.376 13 10c0-.781-.219-1-1-1s-1 .219-1 1z\\\" id=\\\"icons-default-help\\\"></path></defs><use xlink:href=\\\"#icons-default-help\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-device-headset\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M2 11v4h2v-4H2zm20-2c1.1046 0 2 .8954 2 2v4c0 1.1046-.8954 2-2 2v3c0 1.6569-1.3431 3-3 3h-1c0 .5523-.4477 1-1 1h-2c-.5523 0-1-.4477-1-1v-2c0-.5523.4477-1 1-1h2c.5523 0 1 .4477 1 1h1c.5523 0 1-.4477 1-1v-3c-1.1046 0-2-.8954-2-2v-4c0-1.1046.8954-2 2-2 0-3.866-3.134-7-7-7h-2C7.134 2 4 5.134 4 9c1.1046 0 2 .8954 2 2v4c0 1.1046-.8954 2-2 2H2c-1.1046 0-2-.8954-2-2v-4c0-1.1046.8954-2 2-2 0-4.9706 4.0294-9 9-9h2c4.9706 0 9 4.0294 9 9zm-2 2v4h2v-4h-2z\\\" id=\\\"icons-default-headset\\\"></path></defs><use xlink:href=\\\"#icons-default-headset\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-communication-speech-bubbles\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M8 14H6.3576l-3.0894 2.5333c-.8542.7004-2.1144.5758-2.8147-.2784A2 2 0 0 1 0 14.9868V6c0-2.7614 2.2386-5 5-5h6c2.7614 0 5 2.2386 5 5v1h3c2.7614 0 5 2.2386 5 5v8.9868a2 2 0 0 1-.4535 1.2681c-.7003.8542-1.9605.9788-2.8147.2784L17.6424 20H13c-2.7614 0-5-2.2386-5-5v-1zm0-2c0-2.7614 2.2386-5 5-5h1V6c0-1.6569-1.3431-3-3-3H5C3.3431 3 2 4.3431 2 6v8.9868L5.6424 12H8zm6-3h-1c-1.6569 0-3 1.3431-3 3v3c0 1.6569 1.3431 3 3 3h5.3576L22 20.9868V12c0-1.6569-1.3431-3-3-3h-5z\\\" id=\\\"icons-default-speech-bubbles\\\"></path></defs><use xlink:href=\\\"#icons-default-speech-bubbles\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-communication-envelope\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m3.7438 5 7.1093 4.9765a2 2 0 0 0 2.2938 0L20.2562 5H3.7438zM22 6.2207l-7.7062 5.3943a4 4 0 0 1-4.5876 0L2 6.2207V18c0 .5523.4477 1 1 1h18c.5523 0 1-.4477 1-1V6.2207zM3 3h18c1.6569 0 3 1.3431 3 3v12c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3V6c0-1.6569 1.3431-3 3-3z\\\" id=\\\"icons-default-envelope\\\"></path></defs><use xlink:href=\\\"#icons-default-envelope\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-bell-bell\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M11.602 2C8.5082 2 6 4.5081 6 7.602v4.162a5.6827 5.6827 0 0 1-2.4271 4.6577l-.7706.5386A1.8786 1.8786 0 0 0 2 18.5a.5.5 0 0 0 .5.5h19a.5.5 0 0 0 .5-.5 1.9531 1.9531 0 0 0-.8011-1.5772l-.7887-.576A5.876 5.876 0 0 1 18 11.6016V7.5392C18 4.48 15.52 2 12.4608 2h-.8587zm4.3626 19c-.2427 1.6961-1.7014 3-3.4646 3-1.7632 0-3.222-1.3039-3.4646-3H2.5C1.1193 21 0 19.8807 0 18.5a3.8786 3.8786 0 0 1 1.6565-3.179l.7706-.5386A3.6827 3.6827 0 0 0 4 11.764V7.602C4 3.4037 7.4036 0 11.602 0h.8588C16.6246 0 20 3.3754 20 7.5392v4.0625a3.876 3.876 0 0 0 1.5898 3.13l.7887.576A3.9531 3.9531 0 0 1 24 18.5c0 1.3807-1.1193 2.5-2.5 2.5h-5.5354zm-2.05 0h-2.8292c.2059.5826.7615 1 1.4146 1 .6531 0 1.2087-.4174 1.4146-1z\\\" id=\\\"icons-default-bell-bell\\\"></path></defs><use xlink:href=\\\"#icons-default-bell-bell\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-device-mobile\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M12 19c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zM7 0h10c1.6569 0 3 1.3431 3 3v18c0 1.6569-1.3431 3-3 3H7c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v18c0 .5523.4477 1 1 1h10c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1H7z\\\" id=\\\"icons-default-mobile\\\"></path></defs><use xlink:href=\\\"#icons-default-mobile\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-lock-closed\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M8 9h8.0554V4c0-1.1046-.8954-2-2-2H10c-1.1046 0-2 .8954-2 2v5zm10.0554.1909C19.1916 9.6179 20 10.7146 20 12v9c0 1.6569-1.3431 3-3 3H7c-1.6569 0-3-1.3431-3-3v-9c0-1.3062.8348-2.4175 2-2.8293V4c0-2.2091 1.7909-4 4-4h4.0554c2.2091 0 4 1.7909 4 4v5.1909zM7 11c-.5523 0-1 .4477-1 1v9c0 .5523.4477 1 1 1h10c.5523 0 1-.4477 1-1v-9c0-.5523-.4477-1-1-1H7zm4 4c0-.5523.4477-1 1-1s1 .4477 1 1v3c0 .5523-.4477 1-1 1s-1-.4477-1-1v-3z\\\" id=\\\"icons-default-lock-closed\\\"></path></defs><use xlink:href=\\\"#icons-default-lock-closed\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-location-flag\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M5 0h13.967c1.1045 0 2 .8954 2 2a2 2 0 0 1-.1618.7878L19 7l1.8052 4.2122c.4351 1.0152-.0352 2.191-1.0504 2.626a2 2 0 0 1-.7879.1618H6v9c0 .5523-.4477 1-1 1s-1-.4477-1-1V1c0-.5523.4477-1 1-1zm1 2v10h12.967l-2.143-5 2.143-5H6z\\\" id=\\\"icons-default-flag\\\"></path></defs><use xlink:href=\\\"#icons-default-flag\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-money-cash\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M12.993 7.1194C14.2703 7.4534 15 8.4743 15 10c0 .5523-.4477 1-1 1s-1-.4477-1-1c0-.781-.219-1-1-1s-1 .219-1 1c0 .2811.3025.5332 1.4472 1.1056C14.3025 12.0332 15 12.6145 15 14c0 1.5257-.7296 2.5466-2.007 2.8806.0046.0392.007.079.007.1194 0 .5523-.4477 1-1 1s-1-.4477-1-1c0-.0404.0024-.0802.007-.1194C9.7297 16.5466 9 15.5257 9 14c0-.5523.4477-1 1-1s1 .4477 1 1c0 .781.219 1 1 1s1-.219 1-1c0-.2811-.3025-.5332-1.4472-1.1056C9.6975 11.9668 9 11.3855 9 10c0-1.5257.7296-2.5466 2.007-2.8806A1.0103 1.0103 0 0 1 11 7c0-.5523.4477-1 1-1s1 .4477 1 1c0 .0404-.0024.0802-.007.1194zM2 3h20c1.1046 0 2 .8954 2 2v14c0 1.1046-.8954 2-2 2H2c-1.1046 0-2-.8954-2-2V5c0-1.1046.8954-2 2-2zm0 2v14h20V5H2z\\\" id=\\\"icons-default-money-cash\\\"></path></defs><use xlink:href=\\\"#icons-default-money-cash\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-tools-tools\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7 7.6499V16.35l2.4061 1.0694a1 1 0 0 1 .5939.9138V21c0 .5523-.4477 1-1 1s-1-.4477-1-1v-2.0168l-2-.8889-2 .889V21c0 .5523-.4477 1-1 1s-1-.4477-1-1v-2.6667a1 1 0 0 1 .5939-.9138L5 16.3501V7.65L2.5939 6.5805A1 1 0 0 1 2 5.6667V3c0-.5523.4477-1 1-1s1 .4477 1 1v2.0168l2 .8889 2-.889V3c0-.5523.4477-1 1-1s1 .4477 1 1v2.6667a1 1 0 0 1-.5939.9138L7 7.6499zM18 9v12c0 .5523-.4477 1-1 1s-1-.4477-1-1V9h-2.523c-1.1045 0-2-.8954-2-2a2 2 0 0 1 .143-.7428l1.2-3A2 2 0 0 1 14.677 2H20c1.1046 0 2 .8954 2 2v3c0 1.1046-.8954 2-2 2h-2zm-4.523-2H20V4h-5.323l-1.2 3z\\\" id=\\\"icons-default-tools\\\"></path></defs><use xlink:href=\\\"#icons-default-tools\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-web-dashboard\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M3 2c-.5523 0-1 .4477-1 1v3c0 .5523.4477 1 1 1h3c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1H3zm0-2h3c1.6569 0 3 1.3431 3 3v3c0 1.6569-1.3431 3-3 3H3C1.3431 9 0 7.6569 0 6V3c0-1.6569 1.3431-3 3-3zm11 16h7c1.6569 0 3 1.3431 3 3v2c0 1.6569-1.3431 3-3 3h-7c-1.6569 0-3-1.3431-3-3v-2c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v2c0 .5523.4477 1 1 1h7c.5523 0 1-.4477 1-1v-2c0-.5523-.4477-1-1-1h-7zM3 11h3c1.6569 0 3 1.3431 3 3v7c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3v-7c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v7c0 .5523.4477 1 1 1h3c.5523 0 1-.4477 1-1v-7c0-.5523-.4477-1-1-1H3zM21 0c1.6569 0 3 1.3431 3 3v8c0 1.6569-1.3431 3-3 3h-7c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3h7zm-8 3v8c0 .5523.4477 1 1 1h7c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1h-7c-.5523 0-1 .4477-1 1z\\\" id=\\\"icons-default-dashboard\\\"></path></defs><use xlink:href=\\\"#icons-default-dashboard\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-shopping-cart\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7.8341 20.9863C7.4261 22.1586 6.3113 23 5 23c-1.6569 0-3-1.3431-3-3 0-1.397.9549-2.571 2.2475-2.9048l.4429-1.3286c-1.008-.4238-1.7408-1.3832-1.8295-2.5365l-.7046-9.1593A1.1598 1.1598 0 0 0 1 3c-.5523 0-1-.4477-1-1s.4477-1 1-1c1.651 0 3.0238 1.2712 3.1504 2.9174L23 3.9446c.6306 0 1.1038.5766.9808 1.195l-1.6798 8.4456C22.0218 14.989 20.7899 16 19.3586 16H6.7208l-.4304 1.291a3.0095 3.0095 0 0 1 1.5437 1.7227C7.8881 19.0047 7.9435 19 8 19h8.1707c.4118-1.1652 1.523-2 2.8293-2 1.6569 0 3 1.3431 3 3s-1.3431 3-3 3c-1.3062 0-2.4175-.8348-2.8293-2H8c-.0565 0-.112-.0047-.1659-.0137zm-2.8506-1.9862C4.439 19.009 4 19.4532 4 20c0 .5523.4477 1 1 1s1-.4477 1-1c0-.5467-.4388-.991-.9834-.9999a.9923.9923 0 0 1-.033 0zM6.0231 14h13.3355a1 1 0 0 0 .9808-.805l1.4421-7.2504H4.3064l.5486 7.1321A1 1 0 0 0 5.852 14h.1247a.9921.9921 0 0 1 .0464 0zM19 21c.5523 0 1-.4477 1-1s-.4477-1-1-1-1 .4477-1 1 .4477 1 1 1z\\\" id=\\\"icons-default-cart\\\"></path></defs><use xlink:href=\\\"#icons-default-cart\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-web-bug\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M11 20.8939V14c0-.5523.4477-1 1-1s1 .4477 1 1v6.8939c2.7816-.595 5-3.6253 5-7.3939C18 9.289 15.2303 6 12 6s-6 3.289-6 7.5c0 3.7686 2.2184 6.7989 5 7.3939zM4.841 9.2552l-1.548-1.548c-.3905-.3906-.3905-1.0238 0-1.4143.3905-.3905 1.0237-.3905 1.4142 0l1.138 1.138c.6112-.873 1.3515-1.614 2.1863-2.1814A1.0018 1.0018 0 0 1 8 5c0-2.2091 1.7909-4 4-4 2.2091 0 4 1.7909 4 4 0 .0861-.0109.1697-.0314.2495.8348.5674 1.5751 1.3084 2.1863 2.1814l1.138-1.138c.3905-.3905 1.0237-.3905 1.4142 0 .3905.3905.3905 1.0237 0 1.4142L19.159 9.2552c.479 1.1371.7716 2.4042.8301 3.7449L20 13h2c.5523 0 1 .4477 1 1s-.4477 1-1 1h-2a1.012 1.012 0 0 1-.0985-.0048c-.1296.9736-.3838 1.898-.7425 2.7496l1.5481 1.548c.3905.3906.3905 1.0238 0 1.4143-.3905.3905-1.0237.3905-1.4142 0l-1.138-1.138C16.6874 21.6654 14.4749 23 12 23c-2.475 0-4.6874-1.3346-6.1549-3.4309l-1.138 1.138c-.3905.3905-1.0237.3905-1.4142 0-.3905-.3905-.3905-1.0237 0-1.4142l1.5481-1.5481c-.3587-.8515-.6129-1.776-.7425-2.7496A1.012 1.012 0 0 1 4 15H2c-.5523 0-1-.4477-1-1s.4477-1 1-1h2.0109c.0585-1.3406.3511-2.6077.8301-3.7448zm9.0166-4.9978C13.5628 3.5205 12.8422 3 12 3c-.8422 0-1.5628.5205-1.8576 1.2574A6.8207 6.8207 0 0 1 12 4c.6395 0 1.2615.0891 1.8576.2574z\\\" id=\\\"icons-default-bug\\\"></path></defs><use xlink:href=\\\"#icons-default-bug\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-action-bookmark\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M6 2v18.47l4.3346-2.893a3 3 0 0 1 3.3308 0L18 20.47V2H6zM5 0h14c.5523 0 1 .4477 1 1v21.3398c0 .5523-.4477 1-1 1a1 1 0 0 1-.5551-.1683l-5.8898-3.931a1 1 0 0 0-1.1102 0l-5.8898 3.931c-.4593.3066-1.0803.1828-1.3869-.2766A1 1 0 0 1 4 22.3398V1c0-.5523.4477-1 1-1z\\\" id=\\\"icons-default-bookmark\\\"></path></defs><use xlink:href=\\\"#icons-default-bookmark\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-action-document-view\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m18.6064 17.1922 4.515 4.515c.3904.3904.3904 1.0236 0 1.4141-.3906.3905-1.0238.3905-1.4143 0l-4.515-4.515C16.0237 19.4817 14.5724 20 13 20c-3.866 0-7-3.134-7-7s3.134-7 7-7 7 3.134 7 7c0 1.5723-.5184 3.0236-1.3936 4.1922zM7 2v4c0 .5523-.4477 1-1 1H2v14c0 .5523.4477 1 1 1h11c.5523 0 1 .4477 1 1s-.4477 1-1 1H3c-1.6569 0-3-1.3431-3-3V6c0-3.3137 2.6863-6 6-6h9c1.6569 0 3 1.3431 3 3v1c0 .5523-.4477 1-1 1s-1-.4477-1-1V3c0-.5523-.4477-1-1-1H7zM2.126 5H5V2.126C3.5944 2.4878 2.4878 3.5944 2.126 5zM13 18c2.7614 0 5-2.2386 5-5s-2.2386-5-5-5-5 2.2386-5 5 2.2386 5 5 5z\\\" id=\\\"icons-default-document-view\\\"></path></defs><use xlink:href=\\\"#icons-default-document-view\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-action-save\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7 2H4c-.5523 0-1 .4477-1 1v18c0 .5523.4477 1 1 1h16c.5523 0 1-.4477 1-1V5.7016L18.0388 2H17v6c0 .5523-.4477 1-1 1H8c-.5523 0-1-.4477-1-1V2zM4 0h15l4 5v16c0 1.6569-1.3431 3-3 3H4c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm5 7h6V2H9v5zm-1 8c-.5523 0-1-.4477-1-1s.4477-1 1-1h8c.5523 0 1 .4477 1 1s-.4477 1-1 1H8zm0 4c-.5523 0-1-.4477-1-1s.4477-1 1-1h8c.5523 0 1 .4477 1 1s-.4477 1-1 1H8z\\\" id=\\\"icons-default-save\\\"></path></defs><use xlink:href=\\\"#icons-default-save\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"regular-search\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></path><path d=\\\"M0 0h24v24H0z\\\" fill=\\\"none\\\"/></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-action-tray-up\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m13 14.5858 4.2929-4.293c.3905-.3904 1.0237-.3904 1.4142 0 .3905.3906.3905 1.0238 0 1.4143l-6 6c-.3905.3905-1.0237.3905-1.4142 0l-6-6c-.3905-.3905-.3905-1.0237 0-1.4142.3905-.3905 1.0237-.3905 1.4142 0L11 14.5858V1c0-.5523.4477-1 1-1s1 .4477 1 1v13.5858zM2 22.0416h20V17c0-.5523.4477-1 1-1s1 .4477 1 1v6.0416c0 .5523-.4477 1-1 1H1c-.5523 0-1-.4477-1-1v-5.9794c0-.5523.4477-1 1-1s1 .4477 1 1v4.9794z\\\" id=\\\"icons-default-tray-up\\\"></path></defs><use xlink:href=\\\"#icons-default-tray-up\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-basic-checkmark-circle\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M24 12c0 6.6274-5.3726 12-12 12S0 18.6274 0 12 5.3726 0 12 0s12 5.3726 12 12zM12 2C6.4772 2 2 6.4772 2 12s4.4772 10 10 10 10-4.4772 10-10S17.5228 2 12 2zM7.7071 12.2929 10 14.5858l6.2929-6.293c.3905-.3904 1.0237-.3904 1.4142 0 .3905.3906.3905 1.0238 0 1.4143l-7 7c-.3905.3905-1.0237.3905-1.4142 0l-3-3c-.3905-.3905-.3905-1.0237 0-1.4142.3905-.3905 1.0237-.3905 1.4142 0z\\\" id=\\\"icons-default-checkmark-circle\\\"></path></defs><use xlink:href=\\\"#icons-default-checkmark-circle\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-basic-plus-line\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M11 11V3c0-.5523.4477-1 1-1s1 .4477 1 1v8h8c.5523 0 1 .4477 1 1s-.4477 1-1 1h-8v8c0 .5523-.4477 1-1 1s-1-.4477-1-1v-8H3c-.5523 0-1-.4477-1-1s.4477-1 1-1h8z\\\" id=\\\"icons-default-plus\\\"></path></defs><use xlink:href=\\\"#icons-default-plus\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-web-layout\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M7 9H2V7h20v2H9v13H7V9zM3 0h18c1.6569 0 3 1.3431 3 3v18c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm0 2c-.5523 0-1 .4477-1 1v18c0 .5523.4477 1 1 1h18c.5523 0 1-.4477 1-1V3c0-.5523-.4477-1-1-1H3z\\\" id=\\\"icons-default-layout\\\"></path></defs><use xlink:href=\\\"#icons-default-layout\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-object-image\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m2 20.4948 7.2526-8.1592c.3822-.43 1.0477-.4496 1.4545-.0427l2 2a1.066 1.066 0 0 1 .0427.0453l2.482-2.9784c.3764-.4517 1.0595-.4827 1.4753-.067L22 16.5859V3c0-.5523-.4477-1-1-1H3c-.5523 0-1 .4477-1 1v17.4948zM3.338 22H21c.5523 0 1-.4477 1-1v-1.5858l-5.9328-5.9328-4.299 5.1588c-.3535.4243-.9841.4816-1.4084.128-.4243-.3535-.4816-.9841-.128-1.4084l1.2524-1.5028a1.0023 1.0023 0 0 1-.1913-.1499l-1.25-1.25L3.3378 22zM3 0h18c1.6569 0 3 1.3431 3 3v18c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3V3c0-1.6569 1.3431-3 3-3zm4.5 11C5.567 11 4 9.433 4 7.5S5.567 4 7.5 4 11 5.567 11 7.5 9.433 11 7.5 11zm0-2C8.3284 9 9 8.3284 9 7.5S8.3284 6 7.5 6 6 6.6716 6 7.5 6.6716 9 7.5 9z\\\" id=\\\"icons-default-image\\\"></path></defs><use xlink:href=\\\"#icons-default-image\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-object-shield\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M4 12.8348A6 6 0 0 0 6.5592 17.75L12 21.5587l5.4408-3.8086A6 6 0 0 0 20 12.8348v-9.442a24.9264 24.9264 0 0 0-16 0v9.442zM22 2v10.8348a8 8 0 0 1-3.4123 6.5538l-6.0142 4.21a1 1 0 0 1-1.147 0l-6.0142-4.21A8 8 0 0 1 2 12.8348V2a26.9258 26.9258 0 0 1 20 0z\\\" id=\\\"icons-default-shield\\\"></path></defs><use xlink:href=\\\"#icons-default-shield\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-object-traffic-pawn\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"m6.4692 12 2.182-8.7276C8.985 1.9369 10.1848 1 11.5615 1h.8768c1.3767 0 2.5766.9369 2.9105 2.2724L17.5308 12h2.667a2 2 0 0 1 1.9524 1.5661l1.5556 7a2 2 0 0 1 .0476.4339c0 1.1046-.8954 2-2 2H2.2466a2 2 0 0 1-.4338-.0476C.7345 22.7128.0546 21.6444.2942 20.566l1.5556-7A2 2 0 0 1 3.8022 12h2.667zm9.5 2-1-4H9.0308l-1 4h7.9384zm-10 0h-2.167l-1.5556 7h19.5068l-1.5556-7h-2.167l.9393 3.7575c.134.5358-.1918 1.0787-.7276 1.2126-.5358.134-1.0787-.1918-1.2126-.7276L16.4692 16H7.5308L6.97 18.2425c-.134.5358-.6768.8616-1.2126.7276-.5358-.134-.8616-.6768-.7276-1.2126L5.9692 14zm3.5616-6h4.9384l-1.0606-4.2425A1 1 0 0 0 12.4384 3h-.8768a1 1 0 0 0-.9702.7575L9.5308 8z\\\" id=\\\"icons-default-traffic-pawn\\\"></path></defs><use xlink:href=\\\"#icons-default-traffic-pawn\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-object-plug\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M19 16v1c0 1.1046-.8954 2-2 2H9c-2.2091 0-4-1.7909-4-4v-2H1c-.5523 0-1-.4477-1-1s.4477-1 1-1h4V9c0-2.2091 1.7909-4 4-4h8c1.1046 0 2 .8954 2 2v1h3c.5523 0 1 .4477 1 1s-.4477 1-1 1h-3v4h3c.5523 0 1 .4477 1 1s-.4477 1-1 1h-3zM9 7c-1.1046 0-2 .8954-2 2v6c0 1.1046.8954 2 2 2h8V7H9zm-1 4c0-.5523.4477-1 1-1s1 .4477 1 1v2c0 .5523-.4477 1-1 1s-1-.4477-1-1v-2z\\\" id=\\\"icons-default-plug\\\"></path></defs><use xlink:href=\\\"#icons-default-plug\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-object-beer\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M14 6h4.134a.9995.9995 0 0 1 .2654-.2994A1.496 1.496 0 0 0 19 4.5c0-.8284-.6716-1.5-1.5-1.5a1.492 1.492 0 0 0-1.0658.4445c-.4906.4954-1.3249.3492-1.6179-.2835A2.0002 2.0002 0 0 0 13 2a1.9996 1.9996 0 0 0-1.7821 1.0909c-.2946.5756-1.0498.7246-1.5409.3038C9.392 3.1506 8.968 3 8.5 3c-.7264 0-1.3134.3611-1.4636.7894-.2157.6148-.9523.8625-1.4956.5028C5.2616 4.1074 4.8957 4 4.5 4 3.6287 4 3 4.503 3 5v1.5C3 7.3284 3.6716 8 4.5 8c.7165 0 1.3295-.5067 1.47-1.199A1 1 0 0 1 6.95 6H14zm-3 2H7.6633C7.0971 9.1938 5.8816 10 4.5 10c-.1698 0-.3367-.012-.5-.0354V22h14V8h-7zm-9 .9495C1.3814 8.3182 1 7.4537 1 6.5V5c0-1.712 1.61-3 3.5-3 .4177 0 .826.0626 1.2088.1823C6.3605 1.4459 7.3887 1 8.5 1c.5432 0 1.0698.106 1.544.3045A3.9958 3.9958 0 0 1 13 0a3.997 3.997 0 0 1 2.9897 1.342A3.493 3.493 0 0 1 17.5 1C19.433 1 21 2.567 21 4.5c0 .9325-.368 1.8051-1 2.4497V9h1c1.6569 0 3 1.3431 3 3v5c0 1.6569-1.3431 3-3 3h-1v2c0 1.1046-.8954 2-2 2H4c-1.1046 0-2-.8954-2-2V8.9495zM20 11v7h1c.5523 0 1-.4477 1-1v-5c0-.5523-.4477-1-1-1h-1zM6 12c0-.5523.4477-1 1-1s1 .4477 1 1v6c0 .5523-.4477 1-1 1s-1-.4477-1-1v-6zm4 0c0-.5523.4477-1 1-1s1 .4477 1 1v6c0 .5523-.4477 1-1 1s-1-.4477-1-1v-6zm4 0c0-.5523.4477-1 1-1s1 .4477 1 1v6c0 .5523-.4477 1-1 1s-1-.4477-1-1v-6z\\\" id=\\\"icons-default-beer\\\"></path></defs><use xlink:href=\\\"#icons-default-beer\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-documentation-paperclip\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path id=\\\"icons-default-paperclip\\\" d=\\\"M8 16V5.5C8 3.567 9.567 2 11.5 2S15 3.567 15 5.5v14.5294c0 1.1045-.8954 2-2 2s-2-.8955-2-2V5.4618a.5001.5001 0 1 1 1.0002 0v10.5934c0 .5523.4477 1 1 1s1-.4477 1-1V5.4618c0-1.3808-1.1194-2.5001-2.5001-2.5001-1.3808 0-2.5002 1.1193-2.5002 2.5001v14.5676c0 2.2091 1.791 4 4 4 2.2092 0 4.0001-1.7909 4.0001-4V5.5C17 2.4624 14.5376 0 11.5 0S6 2.4624 6 5.5V16c0 .5523.4477 1 1 1s1-.4477 1-1z\\\"></path></defs><use xlink:href=\\\"#icons-default-paperclip\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-device-printer\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M5.1707 22H3c-1.6569 0-3-1.3431-3-3v-7c0-1.6569 1.3431-3 3-3h2V1c0-.5523.4477-1 1-1h12c.5523 0 1 .4477 1 1v8h2c1.6569 0 3 1.3431 3 3v7c0 1.6569-1.3431 3-3 3h-2.1707c-.4118 1.1652-1.523 2-2.8293 2H8c-1.3062 0-2.4175-.8348-2.8293-2zm0-2c.4118-1.1652 1.523-2 2.8293-2h8c1.3062 0 2.4175.8348 2.8293 2H21c.5523 0 1-.4477 1-1v-7c0-.5523-.4477-1-1-1H3c-.5523 0-1 .4477-1 1v7c0 .5523.4477 1 1 1h2.1707zM7 2v7h10V2H7zm1 18c-.5523 0-1 .4477-1 1s.4477 1 1 1h8c.5523 0 1-.4477 1-1s-.4477-1-1-1H8zm-3-7c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1z\\\" id=\\\"icons-default-printer\\\"></path></defs><use xlink:href=\\\"#icons-default-printer\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-device-harddisk\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M21.4257 15.03 19.323 3.8157A1 1 0 0 0 18.3401 3H5.66a1 1 0 0 0-.983.8157L2.5744 15.03c.139-.0198.2812-.03.4257-.03h18c.1445 0 .2866.0102.4257.03zm2.405 1.9738A2.9942 2.9942 0 0 1 24 18.02V20c0 1.6569-1.3431 3-3 3H3c-1.6569 0-3-1.3431-3-3v-1.98a2.9942 2.9942 0 0 1 .1694-1.0162L2.7112 3.4471C2.9772 2.0282 4.2162 1 5.66 1h12.68c1.4437 0 2.6826 1.0282 2.9487 2.4471l2.5418 13.5567zm-1.9295.5623A1 1 0 0 0 21 17H3a1 1 0 0 0-.9012.5661L2 18.0929V20c0 .5523.4477 1 1 1h18c.5523 0 1-.4477 1-1v-1.907l-.0988-.5269zM17 18c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1zm3 0c.5523 0 1 .4477 1 1s-.4477 1-1 1-1-.4477-1-1 .4477-1 1-1z\\\" id=\\\"icons-default-harddisk\\\"></path></defs><use xlink:href=\\\"#icons-default-harddisk\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-chart-sales\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M18.5858 6H16c-.5523 0-1-.4477-1-1s.4477-1 1-1h5c.5523 0 1 .4477 1 1v5c0 .5523-.4477 1-1 1s-1-.4477-1-1V7.4142l-5.2929 5.293c-.3905.3904-1.0237.3904-1.4142 0L12 11.4141l-6.2929 6.293c-.3905.3904-1.0237.3904-1.4142 0-.3905-.3906-.3905-1.0238 0-1.4143l7-7c.3905-.3905 1.0237-.3905 1.4142 0L14 10.5858 18.5858 6zM0 1c0-.5523.4477-1 1-1s1 .4477 1 1v20c0 .5523.4477 1 1 1h20c.5523 0 1 .4477 1 1s-.4477 1-1 1H3c-1.6569 0-3-1.3431-3-3V1z\\\" id=\\\"icons-default-chart-sales\\\"></path></defs><use xlink:href=\\\"#icons-default-chart-sales\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>\\n                                <sw-radio-panel value=\\\"default-building-home\\\" v-model=\\\"element.config.iconCategories.value[n]\\\">\\n                                    <svg xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"><defs><path d=\\\"M13 21v-7c0-1.1046.8954-2 2-2h2c1.1046 0 2 .8954 2 2v7h2.0499v-8.9246L12 7.139l-9 4.909V21h10zm10.0499-8v8c0 1.1046-.8955 2-2 2H3c-1.1046 0-2-.8954-2-2v-7.9986C.4771 13.0008 0 12.5817 0 12V7a1 1 0 0 1 .5211-.8779l11-6a1 1 0 0 1 .9578 0l11 6A1 1 0 0 1 24 7v5c0 .5631-.4472.974-.9501 1zM2 10.3156l9.5211-5.1934a1 1 0 0 1 .9578 0L22 10.3155V7.5936L12 2.1391 2 7.5936v2.7219zM15 14v7h2v-7h-2zm-8-2h2c1.1046 0 2 .8954 2 2v2c0 1.1046-.8954 2-2 2H7c-1.1046 0-2-.8954-2-2v-2c0-1.1046.8954-2 2-2zm0 2v2h2v-2H7z\\\" id=\\\"icons-default-home\\\"></path></defs><use xlink:href=\\\"#icons-default-home\\\" fill=\\\"#758CA3\\\" fill-rule=\\\"evenodd\\\"></use></svg>\\n                                </sw-radio-panel>              \\n                                </div>\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.headlineCategory') }} {{ n }}</span>\\n                                <sw-text-field v-model=\\\"element.config.headlineCategories.value[n]\\\"\\n                                               :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.headlineCategory')\\\"\\n                                               :copyable=\\\"false\\\"\\n                                               :copyableTooltip=\\\"false\\\"></sw-text-field>\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.descriptionCategory') }} {{ n }}</span>\\n                                <sw-text-editor v-model=\\\"element.config.descriptionCategories.value[n]\\\"></sw-text-editor>\\n                            </div>\\n                            <div class=\\\"count-item\\\" v-else-if=\\\"element.config.IconOrImage.value == 'image'\\\">\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.category.image') }}</span>\\n                                <sw-media-field v-model=\\\"element.config.mediaIdCategory.value[n]\\\"></sw-media-field>\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.headlineCategory') }} {{ n }}</span>\\n                                <sw-text-field v-model=\\\"element.config.headlineCategories.value[n]\\\"\\n                                            :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.headlineCategory')\\\"\\n                                            :copyable=\\\"false\\\"\\n                                            :copyableTooltip=\\\"false\\\"></sw-text-field>\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.descriptionCategory') }} {{ n }}</span>\\n                                <sw-text-editor v-model=\\\"element.config.descriptionCategories.value[n]\\\"></sw-text-editor>\\n                            </div>\\n                        </sw-card>\\n                    </sw-card-view>\\n                </div>\\n            </sw-container>\\n            {# Tab Content - Questions #}\\n            <sw-container v-else-if=\\\"active === 'content-questions'\\\" class=\\\"sw-cms-el-config-form__tab-options\\\">\\n                <div v-for=\\\"n in element.config.numberQuestions.value\\\" class=\\\"content-wrapper\\\">\\n                    <sw-card-view style=\\\"position: relative;\\\">\\n                        <span class=\\\"count-headline\\\">{{ $tc('sw-cms.elements.faqconfig.config.headline.faq') }} {{ n }}</span>\\n                        <sw-card>\\n                            <div class=\\\"count-item\\\">\\n                                <span class=\\\"config-headline\\\">{{ $tc('sw-cms.elements.faqconfig.config.headline.sorting') }}</span>\\n                                    <sw-number-field numberType=\\\"int\\\" :step=\\\"1\\\" :value=\\\"null\\\" v-model=\\\"element.config.sorting.value[n]\\\"></sw-number-field>\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.categoryFaq') }} {{ n }}</span>\\n                                    <sw-select-field v-model=\\\"element.config.questionCategory.value[n]\\\"\\n                                                    :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.questionCategory')\\\">\\n                                        <option v-for=\\\"n in element.config.numberCategories.value\\\"\\n                                                :value=\\\"element.config.headlineCategories.value[n]\\\">{{ element.config.headlineCategories.value[n]\\n                                            }}</option>\\n                                    </sw-select-field>\\n                                <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.questionFaq') }} {{ n }}</span>\\n                                    <sw-text-field v-model=\\\"element.config.questions.value[n]\\\"\\n                                                :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.question')\\\" :copyable=\\\"false\\\"\\n                                                :copyableTooltip=\\\"false\\\"></sw-text-field>\\n                                    <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.answerFaq') }} {{ n }}</span>\\n                                    <sw-text-editor v-model=\\\"element.config.answers.value[n]\\\"></sw-text-editor>\\n                            </div>\\n                        </sw-card>\\n                    </sw-card-view>\\n                </div>\\n            </sw-container>\\n            {# Tab Layoutsettings #}\\n            <sw-container v-else-if=\\\"active === 'layout'\\\" class=\\\"sw-cms-el-config-form__tab-options\\\">\\n                <sw-card-view title=\\\"Layouteinstellungen\\\" style=\\\"position: relative;\\\">\\n                    <sw-card>\\n                        <div class=\\\"count-item mediaupload\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.imageFaq') }}</span>\\n                            <sw-cms-mapping-field valueTypes=\\\"entity\\\" entity=\\\"media\\\" v-model=\\\"element.config.media\\\">\\n                                <sw-media-upload-v2 variant=\\\"regular\\\"\\n                                                    :uploadTag=\\\"uploadTag\\\"\\n                                                    :source=\\\"previewSource\\\"\\n                                                    :allowMultiSelect=\\\"false\\\"\\n                                                    :defaultFolder=\\\"cmsPageState.pageEntityName\\\"\\n                                                    :caption=\\\"$tc('sw-cms.elements.faqconfig.config.caption.mediaUpload')\\\"\\n                                                    @media-upload-sidebar-open=\\\"onOpenMediaModal\\\"\\n                                                    @media-upload-remove-image=\\\"onImageRemove\\\">\\n                                </sw-media-upload-v2>\\n\\n                                <div class=\\\"sw-cms-el-config-image__mapping-preview\\\" slot=\\\"preview\\\" slot-scope=\\\"{ demoValue }\\\">\\n                                    <img :src=\\\"demoValue.url\\\" v-if=\\\"demoValue.url\\\">\\n                                    <sw-alert class=\\\"sw-cms-el-config-image__preview-info\\\" variant=\\\"info\\\" v-else>\\n                                        {{ $tc('sw-cms.elements.faqconfig.config.detail.label.mappingEmptyPreview') }}\\n                                    </sw-alert>\\n                                </div>\\n                            </sw-cms-mapping-field>\\n                        </div>\\n                        <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.displayMode') }}</span>\\n                            <sw-select-field :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.displayMode')\\\" v-model=\\\"element.config\\n                            .imageMode.value\\\">\\n                                <option value=\\\"fill\\\">{{ $tc('sw-cms.elements.faqconfig.config.option.displayModeFill') }}</option>\\n                                <option value=\\\"contain\\\">{{ $tc('sw-cms.elements.faqconfig.config.option.displayModeContain') }}</option>\\n                                <option value=\\\"cover\\\">{{ $tc('sw-cms.elements.faqconfig.config.option.displayModeCover') }}</option>\\n                            </sw-select-field>\\n                        </div>\\n                        <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.imageAlt') }} <sw-help-text\\n                                        :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.imageAlt')\\\" :width=\\\"300\\\"\\n                                        tooltipPosition=\\\"top\\\"></sw-help-text></span>\\n                            <sw-text-field :copyable=\\\"false\\\" v-model=\\\"element.config.imageAlt.value\\\"\\n                                           :placeholder=\\\"$tc('sw-cms.elements.faqconfig.config.placeholder.imageAlt')\\\"></sw-text-field>\\n                        </div>\\n                    </sw-card>\\n                    <sw-upload-listener\\n                            :uploadTag=\\\"uploadTag\\\"\\n                            autoUpload\\n                            @media-upload-finish=\\\"onImageUpload\\\">\\n                    </sw-upload-listener>\\n\\n                    <sw-media-modal-v2\\n                            variant=\\\"regular\\\"\\n                            v-if=\\\"mediaModalIsOpen\\\"\\n                            :caption=\\\"$tc('sw-cms.elements.faqconfig.config.caption.mediaUpload')\\\"\\n                            :entityContext=\\\"cmsPageState.entityName\\\"\\n                            :allowMultiSelect=\\\"false\\\"\\n                            :initialFolderId=\\\"cmsPageState.defaultMediaFolderId\\\"\\n                            @media-upload-remove-image=\\\"onImageRemove\\\"\\n                            @media-modal-selection-change=\\\"onSelectionChanges\\\"\\n                            @modal-close=\\\"onCloseModal\\\">\\n                    </sw-media-modal-v2>\\n                </sw-card-view>\\n                {# Color Options #}\\n                <sw-card-view title=\\\"Farbeinstellungen\\\" style=\\\"position: relative;\\\">\\n                    <sw-card>\\n                        <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.headline.iconColor') }}</span>\\n                            <sw-colorpicker v-model=\\\"element.config.iconColor.value\\\" z-index=\\\"1000\\\" value=\\\"#44a74c\\\" colorOutput=\\\"auto\\\"\\n                                            :alpha=\\\"true\\\"></sw-colorpicker>\\n                        </div>\\n                        <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.faq.TabBackgroundColor') }}</span>\\n                            <sw-colorpicker v-model=\\\"element.config.TabBackgroundColor.value\\\" z-index=\\\"1000\\\" value=\\\"#FFFFFF\\\" colorOutput=\\\"auto\\\"\\n                                            :alpha=\\\"true\\\"></sw-colorpicker>\\n                        </div>\\n                        <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.faq.TabFontColor') }}</span>\\n                            <sw-colorpicker v-model=\\\"element.config.TabFontColor.value\\\" z-index=\\\"1000\\\" value=\\\"#000000\\\" colorOutput=\\\"auto\\\"\\n                                            :alpha=\\\"true\\\"></sw-colorpicker>\\n                        </div>\\n                        <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.faq.TabBackgroundColorHover') }} <sw-help-text\\n                                    :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.hoverColor')\\\" :width=\\\"300\\\"\\n                                    tooltipPosition=\\\"top\\\"></sw-help-text></span>\\n                            <sw-colorpicker v-model=\\\"element.config.TabBackgroundColorHover.value\\\" z-index=\\\"1000\\\" value=\\\"#44a74c\\\" colorOutput=\\\"auto\\\"\\n                                            :alpha=\\\"true\\\"></sw-colorpicker>\\n                        </div>\\n                        <div class=\\\"count-item\\\">\\n                            <span>{{ $tc('sw-cms.elements.faqconfig.config.faq.TabFontColorHover') }} <sw-help-text\\n                                    :text=\\\"$tc('sw-cms.elements.faqconfig.config.helpText.hoverColor')\\\" :width=\\\"300\\\"\\n                                    tooltipPosition=\\\"top\\\"></sw-help-text></span>\\n                            <sw-colorpicker v-model=\\\"element.config.TabFontColorHover.value\\\" z-index=\\\"1000\\\" value=\\\"#000000\\\" colorOutput=\\\"auto\\\"\\n                                            :alpha=\\\"true\\\"></sw-colorpicker>\\n                        </div>\\n                    </sw-card>\\n                </sw-card-view>\\n            </sw-container>\\n        </template>\\n    </sw-tabs>\\n</div>\\n</div>\\n</sw-container>\\n</template>\\n</sw-tabs>\\n</div>\\n{% endblock %}\\n\";", "import template from './sw-cms-el-preview-faqconfig.html.twig';\nimport './sw-cms-el-preview-faqconfig.scss';\n\nShopware.Component.register('sw-cms-el-preview-faqconfig', {\n    template\n});\n", "export default \"{% block sw_cms_element_faq_preview %}\\n    <div class=\\\"sw-cms-el-preview-faq\\\">\\n        <img :src=\\\"'/mart1mwfaq/static/img/cms/faq-image.jpg' | asset\\\">\\n    </div>\\n{% endblock %}\";", "import './component';\nimport './config';\nimport './preview';\n\nShopware.Service('cmsService').registerCmsElement({\n\tname: 'faqconfig',\n\tlabel: 'sw-cms.blocks.text-image.faq.label',\n\tcomponent: 'sw-cms-el-faqconfig',\n\tconfigComponent: 'sw-cms-el-config-faqconfig',\n\tpreviewComponent: 'sw-cms-el-preview-faqconfig',\n\tdefaultConfig: {\n\t\tsorting: {\n\t\t\tsource: 'static',\n\t\t\tvalue: []\n\t\t},\n\t\tmediaIdCategory: {\n\t\t\tsource: 'static',\n\t\t\tvalue: []\n\t\t},\n\t\tIconOrImage: {\n\t\t\tsource: 'static',\n\t\t\tvalue: 'icon'\n\t\t},\n\t\tvisible: {\n\t\t\tsource: 'static',\n\t\t\tvalue: true\n\t\t},\n\t\tmedia: {\n\t\t\tsource: 'static',\n\t\t\tvalue: null,\n\t\t\tentity: {\n\t\t\t\tname: 'media'\n\t\t\t}\n\t\t},\n\t\tshowImage: {\n\t\t\tsource: 'static',\n\t\t\tvalue: true\n\t\t},\n\t\tshowSearch: {\n\t\t\tsource: 'static',\n\t\t\tvalue: true\n\t\t},\n\t\tsearchTerm: {\n\t\t\tsource: 'static',\n\t\t\tvalue: ''\n\t\t},\n\t\tshowCategories: {\n\t\t\tsource: 'static',\n\t\t\tvalue: true\n\t\t},\n\t\tshowSearchHeadline: {\n\t\t\tsource: 'static',\n\t\t\tvalue: true\n\t\t},\n\t\ttextSearchHeadline: {\n\t\t\tsource: 'static',\n\t\t\tvalue: ''\n\t\t},\n\t\tshowFaqHeadline: {\n\t\t\tsource: 'static',\n\t\t\tvalue: true\n\t\t},\n\t\ttextFaqHeadline: {\n\t\t\tsource: 'static',\n\t\t\tvalue: ''\n\t\t},\n\t\tnumberCategories: {\n\t\t\tsource: 'static',\n\t\t\tvalue: 4\n\t\t},\n\t\tnumberQuestions: {\n\t\t\tsource: 'static',\n\t\t\tvalue: 10\n\t\t},\n\t\ticonCategories: {\n\t\t\tsource: 'static',\n\t\t\tvalue: []\n\t\t},\n\t\theadlineCategories: {\n\t\t\tsource: 'static',\n\t\t\tvalue: []\n\t\t},\n\t\tdescriptionCategories: {\n\t\t\tsource: 'static',\n\t\t\tvalue: []\n\t\t},\n\t\tquestionCategory: {\n\t\t\tsource: 'static',\n\t\t\tvalue: []\n\t\t},\n\t\tquestions: {\n\t\t\tsource: 'static',\n\t\t\tvalue : []\n\t\t},\n\t\tanswers: {\n\t\t\tsource: 'static',\n\t\t\tvalue: []\n\t\t},\n\t\timageMode: {\n\t\t\tsource: 'static',\n\t\t\tvalue: 'contain'\n\t\t},\n\t\timageAlt: {\n\t\t\tsource: 'static',\n\t\t\tvalue: ''\n\t\t},\n\t\timageWidth: {\n\t\t\tsource: 'static',\n\t\t\tvalue: '100%'\n\t\t},\n\t\timageDisplay: {\n\t\t\tsource: 'static',\n\t\t\tvalue: 'inline-block'\n\t\t},\n\t\ticonColor: {\n\t\t\tsource: 'static',\n\t\t\tvalue: '#44a74c'\n\t\t},\n\t\tTabBackgroundColor: {\n\t\t\tsource: 'static',\n\t\t\tvalue: '#FFFFFF'\n\t\t},\n\t\tTabFontColor: {\n\t\t\tsource: 'static',\n\t\t\tvalue: '#000000'\n\t\t},\n\t\tTabBackgroundColorHover: {\n\t\t\tsource: 'static',\n\t\t\tvalue: '#44a74c'\n\t\t},\n\t\tTabFontColorHover: {\n\t\t\tsource: 'static', \n\t\t\tvalue: '#000000'\n\t\t},\n\t\tsetCatImageWidth: {\n\t\t\tsource: 'static',\n\t\t\tvalue: false\n\t\t},\n\t\tcustomCatImageWidth: {\n\t\t\tsource: 'static',\n\t\t\tvalue: 0\n\t\t},\n\t\tsetCustomCatHeight: {\n\t\t\tsource: 'static',\n\t\t\tvalue: false\n\t\t},\n\t\tcustomCatHeight: {\n\t\t\tsource: 'static',\n\t\t\tvalue: 250\n\t\t}\n\t}\n});\n", "import './module/sw-cms/blocks/faq';\nimport './module/sw-cms/elements/faqconfig';\n\nimport deDE from './module/sw-cms/snippet/de-DE.json';\nimport enGB from './module/sw-cms/snippet/en-GB.json';\n\nShopware.Locale.extend('de-DE', deDE);\nShopware.Locale.extend('en-GB', enGB);", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-cms-el-preview-faqconfig.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"31cc5477\", content, true, {});", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-cms-preview-faq.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"5de73970\", content, true, {});", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-cms-block-faq.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"e1224bca\", content, true, {});", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-cms-el-faqconfig.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"57c9a9fc\", content, true, {});", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-cms-el-config-faqconfig.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../vendor/shopware/administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"a3890f06\", content, true, {});"], "sourceRoot": ""}