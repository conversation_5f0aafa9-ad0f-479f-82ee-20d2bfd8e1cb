/* Generel */
.clearer{
  clear:both;
}
.mw-faq-cms-search-svg-question-section {
  position: absolute;
  margin: 13px 0 0 10px;
}
.mw-faq-cms-headline{
  margin-top:20px;
}
.mw-faq-cms-headline hr {
  max-width: 160px;
  margin-left: auto;
  margin-right: auto;
  border: 2px solid #e9edf0;
}
.mw-faq-cms-headline h2{
  text-align: center;
}
/* <PERSON><PERSON>ien */
.mw-faq-categories ul{
  display:contents;
}
.mw-faq-categories ul li{
  cursor:pointer;
  display: inline-grid;
}
.mw-faq-category{
  padding: 20px;
  text-align: center;
  box-shadow: 0 0 1px 0 #777;
  transition:ease .3s;
  list-style: none;
}
.mw-faq-category input{
  display: none;
}
.mw-faq-category:hover{
  box-shadow: 0 0 3px 0 #777;
}
.mw-faq-category-elements-1{
  width:95%;
  margin: 20px 2.5%;
}
.mw-faq-category-elements-2{
  width:45%;
  margin: 20px 2.5%;
}
.mw-faq-category-elements-3,
.mw-faq-category-elements-6{
  width:31.333%;
  margin: 20px 1%;
}
.mw-faq-category-elements-4,
.mw-faq-category-elements-7,
.mw-faq-category-elements-8{
  width:22.5%;
  margin: 20px 1%;
}
.mw-faq-category-elements-5{
  width:18%;
  margin: 20px 0.5%;
}
.mw-faq-category-icon .sw-icon{
  width: 50px;
  height: 50px;
  border: 1px solid;
  border-radius: 50%;
  padding: 10px;
  box-shadow: 0px 1px 5px -3px #000;
}
.mw-faq-category-headline{
  font-size: 1.4rem;
  margin: 10px 0;
  font-weight: bold;
}
/* Suche */
.mw-faq-search{
  margin: 20px auto;
  width: 50%;
}
.mw-faq-search input{
  width: 100%;
  height: 50px;
  padding:15px;
  text-align: center;
}
.mw-faq-search .sw-icon{
  position: absolute;
  margin: 10px;
}
/* Fragen und Antworten */
.mw-faq-question-section{
  display: grid;
}
.mw-faq-questions {
  overflow: hidden;
  margin: 10px 0;
  transition: ease .3s;
  border-bottom: 1px solid #bbbbbb;
}
.mw-faq-questions:hover {
  box-shadow: 0 1px 0 0 rgba(0,0,0,.5);
}
.mw-faq-questions input {
  position: absolute;
  opacity: 0;
  z-index: -1;
}
.mw-faq-questions .tab {
  width: 100%;
  overflow: hidden;
}
.mw-faq-questions .tab-label {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  padding: 1em;
  font-weight: bold;
  cursor: pointer;
  transition: ease .3s;
  color:#000;
}
.mw-faq-questions .tab-label:hover {
  background: #44a74c;
}
.mw-faq-questions .tab-label::after {
  content: "\276F";
  width: 1em;
  height: 1em;
  text-align: center;
  -webkit-transition: ease .35s;
  transition: ease .35s;
}
.mw-faq-questions .tab-content {
  max-height: 0;
  padding: 0 1em;
  -webkit-transition: ease .35s;
  transition: ease .35s;
}
.mw-faq-questions input:checked + .tab-label {
  background: #44a74c;
}
.mw-faq-questions input:checked + .tab-label::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.mw-faq-questions input:checked ~ .tab-content {
  max-height: 100vh;
  padding: 1em;
}
/* Tablet and Mobile options */
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-1,
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-2,
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-3,
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-4,
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-5,
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-6,
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-7,
.sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-category-elements-8{
    width:47%;
  }
  .sw-cms-stage.is--tablet-landscape .sw-cms-section .mw-faq-search,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-search{
    width:100%;
  }
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-1,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-2,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-3,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-4,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-5,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-6,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-7,
  .sw-cms-stage.is--mobile .sw-cms-section .mw-faq-category-elements-8{
      width:98%;
    }  