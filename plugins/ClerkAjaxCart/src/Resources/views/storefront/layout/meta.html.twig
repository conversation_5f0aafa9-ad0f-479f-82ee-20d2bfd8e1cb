{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_javascript_tracking %}
    {{ parent() }}
    {% set options = {
        'addToCartUrl': '{{ path('frontend.checkout.line-item.add') }}',
        'showFlashMessages': true,
        'snippets': {
            'productAddedToCart': "clerkAjaxCart.messages.productAddedToCart"|trans|striptags,
            'failedToAddProduct': "clerkAjaxCart.messages.failedToAddProduct"|trans|striptags,
            'productIdNotFound': "clerkAjaxCart.messages.productIdNotFound"|trans|striptags,
            'errorUpdatingCartCounter': "clerkAjaxCart.messages.errorUpdatingCartCounter"|trans|striptags
        }
    } %}

    <script>
        window.clerkAjaxCartConfig = {{ options|json_encode|raw}};
    </script>
{% endblock %}