{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_javascript_tracking %}
    {{ parent() }}

    <script>
        window.clerkAjaxCartConfig = {
            addToCartUrl: '{{ path('frontend.checkout.line-item.add') }}',
            snippets: {
                productAddedToCart: '{{ "clerkAjaxCart.messages.productAddedToCart"|trans|striptags }}',
                failedToAddProduct: '{{ "clerkAjaxCart.messages.failedToAddProduct"|trans|striptags }}',
                productIdNotFound: '{{ "clerkAjaxCart.messages.productIdNotFound"|trans|striptags }}',
                errorUpdatingCartCounter: '{{ "clerkAjaxCart.messages.errorUpdatingCartCounter"|trans|striptags }}'
            }
        };
    </script>
{% endblock %}