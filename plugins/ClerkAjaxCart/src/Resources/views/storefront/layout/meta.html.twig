{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_javascript_tracking %}
    {{ parent() }}

    <script>
        window.clerkAjaxCartConfig = {
            addToCartUrl: '{{ path('frontend.checkout.line-item.add') }}',
            showFlashMessages: true,
            snippets: {
                productAddedToCart: {{ 'clerkAjaxCart.messages.productAddedToCart'|trans|json_encode|raw }},
                failedToAddProduct: {{ 'clerkAjaxCart.messages.failedToAddProduct'|trans|json_encode|raw }},
                productIdNotFound: {{ 'clerkAjaxCart.messages.productIdNotFound'|trans|json_encode|raw }},
                errorUpdatingCartCounter: {{ 'clerkAjaxCart.messages.errorUpdatingCartCounter'|trans|json_encode|raw }}
            }
        };
    </script>
{% endblock %}