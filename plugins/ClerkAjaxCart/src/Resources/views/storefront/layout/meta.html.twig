{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_javascript_tracking %}
    {{ parent() }}

    <script>
        window.clerkAjaxCartConfig = {
            addToCartUrl: '{{ path('frontend.checkout.line-item.add') }}',
            showFlashMessages: true,
            snippets: {
                {% set locale = app.request.locale %}
                {% if locale == 'de' or locale == 'de-DE' %}
                productAddedToCart: "Produkt wurde zum Warenkorb hinzugefügt!",
                failedToAddProduct: "Fehler beim Hinzufügen des Produkts zum Warenkorb",
                productIdNotFound: "Produkt-ID nicht am Button-Element gefunden",
                errorUpdatingCartCounter: "Fehler beim Aktualisieren des Warenkorb-Zählers:"
                {% else %}
                productAddedToCart: "Product added to cart!",
                failedToAddProduct: "Failed to add product to cart",
                productIdNotFound: "Product ID not found on button element",
                errorUpdatingCartCounter: "Error updating cart counter:"
                {% endif %}
            }
        };
    </script>
{% endblock %}