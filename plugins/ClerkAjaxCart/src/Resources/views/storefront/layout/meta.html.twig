{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_javascript_tracking %}
    {{ parent() }}

    <script>
        window.clerkAjaxCartConfig = {
            addToCartUrl: '{{ path('frontend.checkout.line-item.add') }}',
            showFlashMessages: true,
            snippets: {
                productAddedToCart: '{{ "clerkAjaxCart.messages.productAddedToCart"|trans|escape('js') }}',
                failedToAddProduct: '{{ "clerkAjaxCart.messages.failedToAddProduct"|trans|escape('js') }}',
                productIdNotFound: '{{ "clerkAjaxCart.messages.productIdNotFound"|trans|escape('js') }}',
                errorUpdatingCartCounter: '{{ "clerkAjaxCart.messages.errorUpdatingCartCounter"|trans|escape('js') }}'
            }
        };
    </script>
{% endblock %}