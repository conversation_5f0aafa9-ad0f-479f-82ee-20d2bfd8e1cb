{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_javascript_tracking %}
    {{ parent() }}

    {% set clerkAjaxCartOptions = {
        'addToCartUrl': path('frontend.checkout.line-item.add'),
        'showFlashMessages': true,
        'snippets': {
            'productAddedToCart': "clerkAjaxCart.productAddedToCart"|trans|sw_sanitize,
            'failedToAddProduct': "clerkAjaxCart.failedToAddProduct"|trans|sw_sanitize
        }
    } %}

    <script>
        window.clerkAjaxCartConfig = {{ clerkAjaxCartOptions|json_encode|raw }};
    </script>
{% endblock %}