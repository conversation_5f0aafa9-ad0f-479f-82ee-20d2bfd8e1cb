import Plugin from 'src/plugin-system/plugin.class';
import HttpClient from 'src/service/http-client.service';

export default class ClerkAjaxCartPlugin extends Plugin {

    static options = {
        loadingClass: 'is-loading',
        successClass: 'is-success',
        errorClass: 'is-error',
        showFlashMessages: true
    };

    init() {
        this._client = new HttpClient();

        // Override options with config from window object
        if (window.clerkAjaxCartConfig?.showFlashMessages !== undefined) {
            this.options.showFlashMessages = window.clerkAjaxCartConfig.showFlashMessages;
        }

        this._createGlobalFunction();
    }

    _getSnippet(key) {
        try {
            return window.clerkAjaxCartConfig?.snippets?.[key] || key;
        } catch (e) {
            console.warn('ClerkAjaxCart: Could not access snippets, using fallback key:', key);
            return key;
        }
    }

    _createGlobalFunction() {
        const self = this;

        // Create global function that can be called from Clerk.io rendered HTML
        window.clerkAddToCart = (productId, buttonElement = null) => {
            self._addToCart(buttonElement, productId, 1);
        };

        // Also create a function that accepts the button element directly
        window.clerkAddToCartFromButton = (buttonElement) => {
            const productId = buttonElement.getAttribute('data-clerk-product-id') ||
                buttonElement.dataset.productId;

            if (!productId) {
                console.error('Product not found.');
                return;
            }

            self._addToCart(buttonElement, productId, 1);
        };
    }

    _addToCart(button, productId, quantity) {
        // Show loading state if button element is provided
        if (button) {
            button.classList.add(this.options.loadingClass);
            button.disabled = true;
        }

        const formData = new FormData();
        formData.append('lineItems[' + productId + '][id]', productId);
        formData.append('lineItems[' + productId + '][type]', 'product');
        formData.append('lineItems[' + productId + '][quantity]', quantity);
        formData.append('lineItems[' + productId + '][referencedId]', productId);
        formData.append('lineItems[' + productId + '][stackable]', 1);
        formData.append('lineItems[' + productId + '][removable]', 1);
        // formData.append('_csrf_token', window.clerkAjaxCartConfig.csrfToken);

        this._client.post(
            window.clerkAjaxCartConfig.addToCartUrl,
            formData,
            (response) => this._onAddToCartSuccess(button, response),
            'application/json',
            false,
            (error) => this._onAddToCartError(button, error)
        );
    }

    _onAddToCartSuccess(button, response) {
        let parsedResponse;
        try {
            parsedResponse = typeof response === 'string' ? JSON.parse(response) : response;
        } catch (e) {
            parsedResponse = { success: true };
        }

        // Remove loading state if button element is provided
        if (button) {
            button.classList.remove(this.options.loadingClass);
            button.disabled = false;
            button.classList.add(this.options.successClass);

            // Show success feedback
            this._showSuccessMessage(button);

            // Remove success state after delay
            setTimeout(() => {
                button.classList.remove(this.options.successClass);
            }, 2000);
        }

        // Update cart counter
        this._updateCartCounter();

        // Also trigger Shopware's native cart update event
        this._triggerCartUpdate();

        // Trigger custom event
        const event = new CustomEvent('clerkAjaxCartSuccess', {
            detail: { button: button, response: parsedResponse }
        });
        document.dispatchEvent(event);
    }

    _onAddToCartError(button, error) {
        // Remove loading state if button element is provided
        if (button) {
            button.classList.remove(this.options.loadingClass);
            button.disabled = false;
            button.classList.add(this.options.errorClass);

            // Show error message
            this._showErrorMessage(button, error);

            // Remove error state after delay
            setTimeout(() => {
                button.classList.remove(this.options.errorClass);
            }, 2000);
        }

        // Trigger custom event
        const event = new CustomEvent('clerkAjaxCartError', {
            detail: { button: button, error: error }
        });
        document.dispatchEvent(event);
    }

    _showSuccessMessage(button) {
        if (this.options.showFlashMessages) {
            this._showFlashMessage(this._getSnippet('productAddedToCart'), 'success');
        }
    }

    _showErrorMessage(button, error) {
        if (this.options.showFlashMessages) {
            this._showFlashMessage(this._getSnippet('failedToAddProduct'), 'error');
        }
    }

    _showFlashMessage(text, type) {
        // Remove any existing clerk flash messages
        const existingMessages = document.querySelectorAll('.clerk-flash-message');
        existingMessages.forEach(msg => this._removeFlashMessage(msg));

        // Create flash message container
        const flashMessage = document.createElement('div');
        flashMessage.className = `clerk-flash-message clerk-flash-${type}`;

        // Create content
        const content = document.createElement('div');
        content.className = 'clerk-flash-content';

        const textSpan = document.createElement('span');
        textSpan.className = 'clerk-flash-text';
        textSpan.textContent = text;

        const closeButton = document.createElement('button');
        closeButton.className = 'clerk-flash-close';
        closeButton.innerHTML = '×';
        closeButton.setAttribute('aria-label', 'Close');
        closeButton.addEventListener('click', () => this._removeFlashMessage(flashMessage));

        content.appendChild(textSpan);
        content.appendChild(closeButton);
        flashMessage.appendChild(content);

        // Add to page
        document.body.appendChild(flashMessage);

        // Animate in
        requestAnimationFrame(() => {
            flashMessage.classList.add('show');
        });

        // Auto remove after delay
        setTimeout(() => {
            this._removeFlashMessage(flashMessage);
        }, type === 'error' ? 2000 : 3000);
    }

    _removeFlashMessage(flashMessage) {
        if (!flashMessage || !flashMessage.parentNode) return;

        flashMessage.classList.remove('show');
        setTimeout(() => {
            if (flashMessage.parentNode) {
                flashMessage.remove();
            }
        }, 300);
    }

    _updateCartCounter() {
        // Find cart counter elements with more comprehensive selectors
        const cartCounters = document.querySelectorAll(
            '.header-cart-count, .cart-counter, .cart-badge, .header-cart-badge, ' +
            '[data-cart-counter], .js-cart-counter, .cart-quantity, .header-cart-quantity'
        );

        // Make a request to get updated cart count
        this._client.get('/widgets/checkout/info', (response) => {
            try {
                const parser = new DOMParser();
                const doc = parser.parseFromString(response, 'text/html');

                // Try multiple selectors to find the counter in the response
                const newCounter = doc.querySelector(
                    '.header-cart-count, .cart-counter, .cart-badge, .header-cart-badge, ' +
                    '[data-cart-counter], .js-cart-counter, .cart-quantity, .header-cart-quantity'
                );

                if (newCounter && cartCounters.length > 0) {
                    const newCount = newCounter.textContent.trim();

                    cartCounters.forEach(counter => {
                        counter.textContent = newCount;
                        counter.classList.add('updated');
                        setTimeout(() => counter.classList.remove('updated'), 1000);
                    });
                } else {
                    console.warn('ClerkAjaxCart: Could not update cart counter - no counter found in response or on page');
                }
            } catch (e) {
                console.error('Error updating cart counter:', e);
            }
        }, 'text/html');
    }

    _triggerCartUpdate() {
        // Trigger Shopware's native cart update events
        const cartUpdateEvent = new CustomEvent('cart-widget-update');
        document.dispatchEvent(cartUpdateEvent);

        // Also try to trigger offcanvas cart update
        const offcanvasUpdateEvent = new CustomEvent('offcanvas-cart-update');
        document.dispatchEvent(offcanvasUpdateEvent);

        // Force reload of cart widget if it exists
        const cartWidget = document.querySelector('[data-cart-widget]');
        if (cartWidget && window.PluginManager) {
            const cartWidgetInstance = window.PluginManager.getPluginInstanceFromElement(cartWidget, 'CartWidget');
            if (cartWidgetInstance && typeof cartWidgetInstance.fetch === 'function') {
                cartWidgetInstance.fetch();
            }
        }
    }
}