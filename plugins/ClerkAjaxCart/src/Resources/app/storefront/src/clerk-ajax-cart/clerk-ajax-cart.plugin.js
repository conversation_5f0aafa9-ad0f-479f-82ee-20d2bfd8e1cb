import Plugin from 'src/plugin-system/plugin.class';
import HttpClient from 'src/service/http-client.service';

export default class ClerkAjaxCartPlugin extends Plugin {

    static options = {
        loadingClass: 'is-loading',
        successClass: 'is-success',
        errorClass: 'is-error'
    };

    init() {
        this._client = new HttpClient();
        this._createGlobalFunction();
    }

    _createGlobalFunction() {
        // Create global function that can be called from Clerk.io rendered HTML
        window.clerkAddToCart = (productId, buttonElement = null) => {
            this._addToCart(buttonElement, productId, 1);
        };

        // Also create a function that accepts the button element directly
        window.clerkAddToCartFromButton = (buttonElement) => {
            const productId = buttonElement.getAttribute('data-clerk-product-id') ||
                buttonElement.dataset.productId;

            console.log('productId', productId);

            if (!productId) {
                console.error('Product ID not found on button element');
                return;
            }

            this._addToCart(buttonElement, productId, 1);
        };
    }

    _addToCart(button, productId, quantity) {
        // Show loading state if button element is provided
        if (button) {
            button.classList.add(this.options.loadingClass);
            button.disabled = true;
        }

        const formData = new FormData();
        formData.append('lineItems[' + productId + '][id]', productId);
        formData.append('lineItems[' + productId + '][type]', 'product');
        formData.append('lineItems[' + productId + '][quantity]', quantity);
        formData.append('lineItems[' + productId + '][referencedId]', productId);
        formData.append('lineItems[' + productId + '][stackable]', 1);
        formData.append('lineItems[' + productId + '][removable]', 1);
        // formData.append('_csrf_token', window.clerkAjaxCartConfig.csrfToken);

        this._client.post(
            window.clerkAjaxCartConfig.addToCartUrl,
            formData,
            (response) => this._onAddToCartSuccess(button, response),
            'application/json',
            false,
            (error) => this._onAddToCartError(button, error)
        );
    }

    _onAddToCartSuccess(button, response) {
        // Remove loading state if button element is provided
        if (button) {
            button.classList.remove(this.options.loadingClass);
            button.disabled = false;
            button.classList.add(this.options.successClass);

            // Show success feedback
            this._showSuccessMessage(button);

            // Remove success state after delay
            setTimeout(() => {
                button.classList.remove(this.options.successClass);
            }, 2000);
        }

        // Update cart counter
        this._updateCartCounter();

        // Trigger custom event
        const event = new CustomEvent('clerkAjaxCartSuccess', {
            detail: { button: button, response: response }
        });
        document.dispatchEvent(event);

        console.log('Product added to cart successfully');
    }

    _onAddToCartError(button, error) {
        // Remove loading state if button element is provided
        if (button) {
            button.classList.remove(this.options.loadingClass);
            button.disabled = false;
            button.classList.add(this.options.errorClass);

            // Show error message
            this._showErrorMessage(button, error);

            // Remove error state after delay
            setTimeout(() => {
                button.classList.remove(this.options.errorClass);
            }, 3000);
        }

        // Trigger custom event
        const event = new CustomEvent('clerkAjaxCartError', {
            detail: { button: button, error: error }
        });
        document.dispatchEvent(event);

        console.error('Failed to add product to cart:', error);
    }

    _showSuccessMessage(button) {
        this._showFlashMessage('Product added to cart!', 'success');
    }

    _showErrorMessage(button, error) {
        this._showFlashMessage('Failed to add product to cart', 'error');
    }

    _showFlashMessage(text, type) {
        // Remove any existing flash messages
        const existingMessages = document.querySelectorAll('.clerk-flash-message');
        existingMessages.forEach(msg => msg.remove());

        // Create flash message container
        const flashMessage = document.createElement('div');
        flashMessage.className = `clerk-flash-message clerk-flash-${type}`;
        flashMessage.innerHTML = `
            <div class="clerk-flash-content">
                <span class="clerk-flash-text">${text}</span>
                <button class="clerk-flash-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add to top of page
        document.body.appendChild(flashMessage);

        // Animate in
        setTimeout(() => {
            flashMessage.classList.add('show');
        }, 10);

        // Auto remove after delay
        setTimeout(() => {
            if (flashMessage.parentNode) {
                flashMessage.classList.remove('show');
                setTimeout(() => {
                    if (flashMessage.parentNode) {
                        flashMessage.remove();
                    }
                }, 300);
            }
        }, type === 'error' ? 5000 : 3000);
    }

    _updateCartCounter() {
        // Find cart counter elements and update them
        const cartCounters = document.querySelectorAll('.header-cart-count, .cart-counter');

        // Make a request to get updated cart count
        this._client.get('/widgets/checkout/info', (response) => {
            try {
                const parser = new DOMParser();
                const doc = parser.parseFromString(response, 'text/html');
                const newCounter = doc.querySelector('.header-cart-count, .cart-counter');

                if (newCounter) {
                    cartCounters.forEach(counter => {
                        counter.textContent = newCounter.textContent;
                        counter.classList.add('updated');
                        setTimeout(() => counter.classList.remove('updated'), 1000);
                    });
                }
            } catch (e) {
                console.error('Error updating cart counter:', e);
            }
        });
    }
}