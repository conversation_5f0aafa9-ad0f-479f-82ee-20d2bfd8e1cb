.clerk-add-to-cart-btn {
  border-radius: 6px !important;
  font-size: 14px !important;
  line-height: 25px;
  background-color: #84196f;
  color: #fff;
  position: relative;
  transition: all 0.3s ease;

  &.is-loading {
    pointer-events: none;
    opacity: 0.7;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border: 2px solid #ccc;
      border-top: 2px solid #333;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  &.is-success {
    background-color: #28a745;
    color: white;
  }

  &.is-error {
    background-color: #dc3545;
    color: white;
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

// Flash Message Styles
.clerk-flash-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 500px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    transform: translateX(0);
  }

  .clerk-flash-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-weight: 500;
  }

  &.clerk-flash-success .clerk-flash-content {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.clerk-flash-error .clerk-flash-content {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .clerk-flash-text {
    flex: 1;
    margin-right: 10px;
  }

  .clerk-flash-close {
    background: none;
    border: none;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .clerk-flash-message {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }
}