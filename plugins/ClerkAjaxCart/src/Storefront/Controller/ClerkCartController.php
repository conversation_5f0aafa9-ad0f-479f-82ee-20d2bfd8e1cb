<?php declare(strict_types=1);

namespace ClerkAjaxCart\Storefront\Controller;

use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\CartCalculator;
use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Cart\SalesChannel\CartService;
use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ClerkCartController extends StorefrontController
{
    public function __construct(
        private readonly CartService $cartService,
        private readonly CartCalculator $cartCalculator
    ) {
    }

    #[Route(
        path: '/clerk/checkout/line-item/add',
        name: 'clerk.frontend.checkout.line-item.add',
        defaults: ['XmlHttpRequest' => true],
        methods: ['POST']
    )]
    public function addLineItem(Request $request, Cart $cart, SalesChannelContext $context): JsonResponse
    {
        try {
            $lineItems = $request->request->all('lineItems');
            
            if (empty($lineItems)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'No line items provided'
                ], 400);
            }

            foreach ($lineItems as $lineItemData) {
                $lineItem = new LineItem(
                    $lineItemData['id'],
                    $lineItemData['type'] ?? LineItem::PRODUCT_LINE_ITEM_TYPE,
                    $lineItemData['referencedId'] ?? $lineItemData['id'],
                    (int) ($lineItemData['quantity'] ?? 1)
                );

                if (isset($lineItemData['stackable'])) {
                    $lineItem->setStackable((bool) $lineItemData['stackable']);
                }

                if (isset($lineItemData['removable'])) {
                    $lineItem->setRemovable((bool) $lineItemData['removable']);
                }

                $cart = $this->cartService->add($cart, $lineItem, $context);
            }

            // Calculate cart totals
            $cart = $this->cartCalculator->calculate($cart, $context);

            return new JsonResponse([
                'success' => true,
                'message' => 'Product added to cart successfully',
                'cartCount' => $cart->getLineItems()->count(),
                'cartTotal' => $cart->getPrice()->getTotalPrice()
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to add product to cart: ' . $e->getMessage()
            ], 500);
        }
    }
}
