<?php declare(strict_types=1);

namespace BsOppoSuits\Command;

use BsOppoSuits\Service\SyncStockService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SyncStockCommand extends Command
{
    protected static $defaultName = 'bs-oppo-suits:sync-stock';

    public function __construct(SyncStockService $syncStockService)
    {
        parent::__construct();
        $this->syncStockService = $syncStockService;
    }

    protected function configure(): void
    {
        $this->setDescription('Sync oppo suits products stock');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('It works!');
        ($this->syncStockService)();

        return 0;
    }
}
