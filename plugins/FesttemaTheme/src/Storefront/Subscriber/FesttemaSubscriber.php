<?php declare(strict_types=1);

namespace FesttemaTheme\Storefront\Subscriber;

use Shopware\Core\Checkout\Cart\Event\CartBeforeSerializationEvent;
use Shopware\Core\Checkout\Customer\Event\CustomerWishlistProductListingResultEvent;
use Shopware\Core\Content\Product\Events\ProductListingResultEvent;
use Shopware\Core\Content\Product\Events\ProductSearchResultEvent;
use Shopware\Core\Content\Product\ProductEvents;
use Shopware\Core\Content\Product\SalesChannel\SalesChannelProductEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Aggregation\Metric\SumAggregation;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Storefront\Page\Product\ProductPageLoadedEvent;
use Shopware\Storefront\Page\Product\QuickView\MinimalQuickViewPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class FesttemaSubscriber implements EventSubscriberInterface
{

    private EntityRepository $productRepository;
    private RequestStack $request;
    private $dispatcher;

    public function __construct(EntityRepository $productRepository, 
    RequestStack $request, 
    EventDispatcherInterface $dispatcher)
    {
        $this->productRepository = $productRepository;
        $this->request = $request;
        $this->dispatcher = $dispatcher;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ProductPageLoadedEvent::class => 'onProductPageLoaded',
            MinimalQuickViewPageLoadedEvent::class => 'onMinimalQuickViewPageLoaded',
            ProductListingResultEvent::class => 'onProductListingResultEvent',
            ProductSearchResultEvent::class => 'onProductSearchResultEvent',
            CustomerWishlistProductListingResultEvent::class => 'onCustomerWishlistProductListingResultEvent',
            ProductEvents::PRODUCT_WRITTEN_EVENT => 'onProductStockWritten',
            CartBeforeSerializationEvent::class => 'onCartBeforeSerialization',
        ];
    }

    /**
     * check if product has parent and display product parent's details
     */
    public function onProductPageLoaded(ProductPageLoadedEvent $event): SalesChannelProductEntity
    {
        $product = $event->getPage()->getProduct();
        $parentId = $product->getParentId();
        if(!empty($parentId)){
            
            $customFields = $product->getCustomFields();
            $translatedArr = $product->getTranslated();

            if(isset($customFields['ft_parent_prod_name'])){
                $translatedArr['name'] = $customFields['ft_parent_prod_name'];
            }

            if(isset($customFields['ft_parent_prod_desc'])){
                $translatedArr['description'] = $customFields['ft_parent_prod_desc'];
            }

            $product->setTranslated($translatedArr);
        }
        return $product;
    }

    /**
     * product propup - display product's parent name/desc
     */

    public function onMinimalQuickViewPageLoaded(MinimalQuickViewPageLoadedEvent $event)
    {
        $page = $event->getPage();
        $product = $page->getProduct();

        $parentId = $product->getParentId();
        if(!empty($parentId)){
            $customFields = $product->getCustomFields();
            $translatedArr = $product->getTranslated();
            
            if(isset($customFields['ft_parent_prod_name'])){
                $translatedArr['name'] = $customFields['ft_parent_prod_name'];
            }

            if(isset($customFields['ft_parent_prod_desc'])){
                $translatedArr['description'] = $customFields['ft_parent_prod_desc'];
            }

            $product->setTranslated($translatedArr);
        }
        return $product;
    }

    /**
     * product listing - display product's parent name/desc
     */

    public function onProductListingResultEvent(ProductListingResultEvent $event): void{
        $elements = $event->getResult()->getElements();
        if(empty($elements)){
            $redirectTo = ($event->getSalesChannelContext()->getSalesChannel()->getDomains()->first()->getUrl()); 
            echo "<script>window.location.href='".$redirectTo."';</script>";
            //header('Location: '.$redirectTo);
        }
        foreach($elements AS $element){
            $parentId = $element->getParentId();
            if(!empty($parentId)){ 
                $customFields = $element->getCustomFields();
                $translatedArr = $element->getTranslated();
    
                if(isset($customFields['ft_parent_prod_name'])){
                    $translatedArr['name'] = $customFields['ft_parent_prod_name'];
                }
    
                if(isset($customFields['ft_parent_prod_desc'])){
                    $translatedArr['description'] = $customFields['ft_parent_prod_desc'];
                }
    
                $element->setTranslated($translatedArr);
            }
        }
    }

    /**
     * product search listing - display product's parent name/desc
     */

    public function onProductSearchResultEvent(ProductSearchResultEvent $event): void{
        $elements = $event->getResult()->getElements();
        foreach($elements AS $element){
            $parentId = $element->getParentId();
            if(!empty($parentId)){
                $customFields = $element->getCustomFields();
                $translatedArr = $element->getTranslated();
    
                if(isset($customFields['ft_parent_prod_name'])){
                    $translatedArr['name'] = $customFields['ft_parent_prod_name'];
                }
    
                if(isset($customFields['ft_parent_prod_desc'])){
                    $translatedArr['description'] = $customFields['ft_parent_prod_desc'];
                }
    
                $element->setTranslated($translatedArr);
            }
        }
    }

     /**
     * product whishlist - display product's parent name/desc
     */

    public function onCustomerWishlistProductListingResultEvent(CustomerWishlistProductListingResultEvent $event): void{
        $elements = $event->getResult()->getEntities()->getElements();
        foreach($elements AS $element){
            $parentId = $element->getParentId();
            if(!empty($parentId)){
                $customFields = $element->getCustomFields();
                $translatedArr = $element->getTranslated();
    
                if(isset($customFields['ft_parent_prod_name'])){
                    $translatedArr['name'] = $customFields['ft_parent_prod_name'];
                }
    
                if(isset($customFields['ft_parent_prod_desc'])){
                    $translatedArr['description'] = $customFields['ft_parent_prod_desc'];
                }
    
                $element->setTranslated($translatedArr);
            }
        }
    }

    /**
     * product stock check
     *
     * @param EntityWrittenEvent $event
     */
    public function onProductStockWritten(EntityWrittenEvent $event)
    {
        $items = $event->getWriteResults();
        $context = Context::createDefaultContext();

        foreach($items AS $item){
            $payload = $item->getPayload();
            $existence = $item->getExistence();
            $isChild = false;
            if($existence){
                $isChild = $existence->isChild()? $existence->isChild(): false;
                $parentId = isset($existence->getState()['parent_id'])? $existence->getState()['parent_id']: null;
            }

            if($isChild){

                if(!empty($parentId)){
                    $stock  = isset($payload['stock'])? $payload['stock']: 'N';
                    if($stock == 'N') continue;
                    
                    $criteria = new Criteria();
                    $criteria->addAggregation(
                        new SumAggregation('count', 'stock')
                    );
                    $criteria->addFilter(new EqualsFilter('parentId', bin2hex($parentId)));
                    $result = $this->productRepository->aggregate($criteria, $context);
                    /** @var CountResult $countResult */
                    $countResult = $result->get('count');
                    $stockResult = ($countResult->getSum() > 0)? round($countResult->getSum(), 0): 0;
                    $this->updateParentProductStock(bin2hex($parentId), $stockResult, $context);      
                }
            }
        }

    }

    /**
     * function to update parent product stock
     * @param string
     * @param int
     * @param Context
     */

    public function updateParentProductStock($id, $stock, Context $context){
        try {
            $this->productRepository->update([[
                'id' => $id,
                'stock' => (int)$stock
            ]], $context);
        } catch (\Exception $exception) {}
    }

    public function onCartBeforeSerialization(CartBeforeSerializationEvent $event): void
    {
        $allowed = $event->getCustomFieldAllowList();
        $allowed[] = 'ft_parent_prod_name';
        
        $event->setCustomFieldAllowList($allowed);
    }
}

