<?php declare(strict_types=1);

namespace FesttemaTheme\Storefront\Subscriber;

use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Content\Product\ProductEvents;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class FtProductNameSubscriber implements EventSubscriberInterface
{

    private EntityRepository $productRepository;
    private RequestStack $request;
    private $dispatcher;

    public function __construct(EntityRepository $productRepository, 
    RequestStack $request, 
    EventDispatcherInterface $dispatcher)
    {
        $this->productRepository = $productRepository;
        $this->request = $request;
        $this->dispatcher = $dispatcher;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ProductEvents::PRODUCT_WRITTEN_EVENT => 'onProductWritten',
        ];
    }

    /**
     * set parent product name, desc to child products
     *
     * @param EntityWrittenEvent $event
     */
    public function onProductWritten(EntityWrittenEvent $event): void
    {
        $context = $event->getContext();
        
        if($event->getEntityName() != 'product'){
            return;
        }

        if(!$this->request->getCurrentRequest()){
            return;
        }
        $currentRequest = $this->request->getCurrentRequest();
        $requests = $currentRequest->request;

        foreach($requests as $request){
            if(isset($request['entity']) && $request['entity'] == 'product'){
                foreach($request['payload'] as $data){
                    if(isset($data['name']) || isset($data['description']) || isset($data['productNumber']) && isset($data['id'])) {

                        //get product to check whether it's parent/not
                        $product = $this->productRepository->search(new Criteria([$data['id']]), $context)->first();
                        
                        //if product is parent product
                        if($product->getChildCount() > 0){
                            $payload = $this->generatePayload($product, $context);
                
                            if(!empty($payload)){                                
                                try {
                                    $this->dispatcher->removeSubscriber($this);
                                    $this->productRepository->update($payload, $context);
                                } catch (\Exception $exception) {}
                            }
                        }
                    }
                } 
                break;
            }
        }

        return;
    }

    /**
     * Generate payload
     * @param ProductEntity
     * @param Context
     * @return array
     */
    private function generatePayload(ProductEntity $product, Context $context): array
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('parentId', $product->getId()));
        $childProducts = $this->productRepository->search($criteria, $context);

        //set parent product's name, description, metadescription
        $customFields = [];
        $payload = [];

        if(isset($product->getTranslated()['name'])){
            $customFields['ft_parent_prod_name'] = $product->getTranslated()['name'];
        }

        if(isset($product->getTranslated()['description'])){
            $customFields['ft_parent_prod_desc'] = $product->getTranslated()['description'];
        }

        $customFields['ft_parent_sku'] = $product->getProductNumber();

        foreach($childProducts as $product){
            if(!empty($customFields)){
                $payload[] = [ 'id' => $product->getId(), 'customFields' => $customFields ];
            }
        }
        return $payload;
    }
}
