<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="FesttemaTheme\Storefront\Subscriber\FesttemaSubscriber">
            <argument id="product.repository" type="service" />
            <argument type="service" id="request_stack" />
            <argument type="service" id="Symfony\Component\EventDispatcher\EventDispatcherInterface" />
            <tag name="kernel.event_subscriber"/>
        </service>
        
        <service id="FesttemaTheme\Storefront\Controller\CheckoutController" public="true">
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Cart\CheckoutCartPageLoader"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoader"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Finish\CheckoutFinishPageLoader"/>
            <argument type="service" id="Shopware\Core\Checkout\Order\SalesChannel\OrderService"/>
            <argument type="service" id="Shopware\Core\Checkout\Payment\PaymentService"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Offcanvas\OffcanvasCartPageLoader"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\LogoutRoute"/>
            <argument type="service" id="sales_channel.product.repository" />
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <call method="setTwig">
                <argument type="service" id="twig"/>
            </call>
        </service>

        <service id="FesttemaTheme\Storefront\Controller\RegisterController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\Account\Login\AccountLoginPageLoader"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\RegisterRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\RegisterConfirmRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>
            <argument type="service" id="Shopware\Storefront\Page\Checkout\Register\CheckoutRegisterPageLoader"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="customer.repository"/>
            <argument type="service" id="Shopware\Storefront\Page\Account\CustomerGroupRegistration\CustomerGroupRegistrationPageLoader"/>
            <argument type="service" id="sales_channel_domain.repository"/>
            <argument type="service" id="sales_channel.product.repository" />
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="FesttemaTheme\Storefront\Controller\AccountOrderController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\Account\Order\AccountOrderPageLoader"/>
            <argument type="service" id="Shopware\Storefront\Page\Account\Order\AccountEditOrderPageLoader"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\SalesChannel\ContextSwitchRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Order\SalesChannel\CancelOrderRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Order\SalesChannel\SetPaymentOrderRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Payment\SalesChannel\HandlePaymentMethodRoute"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="Shopware\Storefront\Page\Account\Order\AccountOrderDetailPageLoader"/>
            <argument type="service" id="Shopware\Core\Checkout\Order\SalesChannel\OrderRoute"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextService"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\Checkout\Order\SalesChannel\OrderService"/>
            <argument type="service" id="sales_channel.product.repository" />
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <call method="setTwig">
                <argument type="service" id="twig"/>
            </call>
        </service>

        <!-- <service id="FesttemaTheme\Core\Content\Cms\DataResolver\Element\TextCmsElementResolver">
            <tag name="shopware.cms.data_resolver"/>
            <argument type="service" id="Shopware\Core\Framework\Util\HtmlSanitizer"/>
            <argument type="service" id="translator" />
        </service> -->

        <service id="Shopware\Core\Content\Product\SalesChannel\Detail\ProductDetailRoute" class="FesttemaTheme\Core\Content\Product\SalesChannel\Detail\ProductDetailRoute" public="true">
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Detail\ProductConfiguratorLoader"/>
            <argument type="service" id="Shopware\Core\Content\Category\Service\CategoryBreadcrumbBuilder"/>
            <argument type="service" id="Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\SalesChannelProductDefinition"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\ProductCloseoutFilterFactory"/>
            <argument type="service" id="event_dispatcher"/>
        </service>
        
        <service id="FesttemaTheme\Storefront\Subscriber\FtProductNameSubscriber">
            <argument id="product.repository" type="service" />
            <argument type="service" id="request_stack" />
            <argument type="service" id="Symfony\Component\EventDispatcher\EventDispatcherInterface" />
            <tag name="kernel.event_subscriber"/>
        </service>
        
    </services>
</container>
