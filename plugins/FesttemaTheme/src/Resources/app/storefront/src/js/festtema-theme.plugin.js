import Plugin from 'src/plugin-system/plugin.class';

export default class FesttemaThemePlugin extends Plugin {

    init() {
        let readmorelink = document.querySelector('.js-read-more-link');
        let readlesslink = document.querySelector('.js-read-less-link');

        if(readmorelink){
            readmorelink.addEventListener('click', this._showMoreText);
        }

        if(readlesslink){
            readlesslink.addEventListener('click', this._showLessText);
        }
    }
    _showLessText(e){
        let lessContent = document.querySelector('.js-less-content');
        let moreContent = document.querySelector('.js-more-content');

        moreContent.style.display = 'none';
        lessContent.style.display = 'block';
    }

    _showMoreText(e){
        let lessContent = document.querySelector('.js-less-content');
        let moreContent = document.querySelector('.js-more-content');

        lessContent.style.display = 'none';
        moreContent.style.display = 'block';
    }

}
