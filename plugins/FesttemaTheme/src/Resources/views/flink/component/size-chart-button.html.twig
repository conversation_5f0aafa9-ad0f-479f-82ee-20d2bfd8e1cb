{% set sizeChart = page.product.extensions.flinkSizeChart %}
{% if not sizeChart and page.product.parent.extensions.flinkSizeChart %}
    {% set sizeChart = page.product.parent.extensions.flinkSizeChart %}
{% endif %}

{% if not flinkQuickView %}
    {% if sizeChart %}
        <div class="flink-size-chart-button-container">

            {% set btnStyle = 'link' %}
            {% if config('FlinkSizeCharts.config.modalButtonStyle') %}
                {% set btnStyle = config('FlinkSizeCharts.config.modalButtonStyle') %}
            {% endif %}

            {% set btnSize = '' %}
            {% if config('FlinkSizeCharts.config.modalButtonSize') and config('FlinkSizeCharts.config.modalButtonSize') != 'default' %}
                {% set btnSize = config('FlinkSizeCharts.config.modalButtonSize') %}
            {% endif %}

            {% set isBtn = false %}
            {% if config('FlinkSizeCharts.config.modalButtonType') == 'button' %}
                {% set isBtn = true %}
            {% endif %}

            <p>
                <a href="#flink-size-chart-modal" data-bs-toggle="modal"  class="flink-size-chart-button {% if isBtn %}btn btn-{{ btnStyle }} {% if btnSize %}btn-{{ btnSize }}{% endif %}{% endif %}">
                    <img src="{{ asset('bundles/festtematheme/images/ruler.png', 'asset') }}" class="sizechart-icon" alt="size guide" title="size guide" height="17" width="20"> {{ "flink-size-chart.modalButtonText" | trans }}
                </a>
            </p>
        </div>
    {% endif %}
{% endif %}