{% block zeobv_bundle_products_components_bundle_product_info %}
    {% if products %}
        {{ "zeobv-bundle-products.detail.tableHeaderText"|trans|sw_sanitize }}

        {% set showAvailabilityColumn = config('ZeobvBundleProducts.config.showBundleProductItemAvailabilityColumn') %}
        {% set showThumbnail = config('ZeobvBundleProducts.config.showBundleProductItemThumbnailColumn') %}
        {% set showManufacturer = config('ZeobvBundleProducts.config.showBundleProductItemManufacturer') %}
        {% set showBundleProductNumber = config('ZeobvBundleProducts.config.showBundleProductNumber') %}
        {% set showBundleProductDetailInPopUp = config('ZeobvBundleProducts.config.showBundleProductDetailInPopUp') %}
        {% set showQuantityOfBundleItemsProductDetailPageTablePresentation = config('ZeobvBundleProducts.config.showQuantityOfBundleItemsProductDetailPageTablePresentation') %}

        <table class="table product-bundle-products-table">
            {% block page_product_detail_price_block_table_head %}
                <thead class="product-bundle-products-head">
                {% block page_product_detail_price_block_table_head_inner %}
                    <tr class="product-bundle-products-header-row">
                        {% block zeobv_bundle_products_components_bundle_product_info_table_header_cell_name %}
                            <th scope="col"
                                class="product-bundle-products-product-cell product-bundle-products-product-name-cell"
                                colspan="{{showThumbnail ? (showQuantityOfBundleItemsProductDetailPageTablePresentation ? "2" : 3) : (showQuantityOfBundleItemsProductDetailPageTablePresentation ? "2" : 3) }}">
                                {{ "zeobv-bundle-products.detail.dataColumnName" | trans | sw_sanitize }}
                            </th>
                        {% endblock %}
                        {% block zeobv_bundle_products_components_bundle_product_info_table_header_cell_quantity %}
                            {% if(showQuantityOfBundleItemsProductDetailPageTablePresentation == true ) %}
                                <th scope="col"
                                class="product-bundle-products-product-cell product-bundle-products-product-quantity-cell">
                                     {{ "zeobv-bundle-products.detail.dataColumnQuantity"|trans|sw_sanitize }}
                                </th>
                            {% endif %}
                        {% endblock %}
                        {% block zeobv_bundle_products_components_bundle_product_info_table_header_cell_available %}
                            {% if showAvailabilityColumn %}
                                <th scope="col"
                                    class="product-bundle-products-product-cell product-bundle-products-product-available-cell">
                                    {{ "zeobv-bundle-products.detail.dataColumnAvailable"|trans|sw_sanitize }}
                                </th>
                            {% endif %}
                        {% endblock %}
                        {% block zeobv_bundle_products_components_bundle_product_info_table_header_cell_price %}
                            {% if showPrices %}
                                <th scope="col"
                                    class="product-bundle-products-product-cell product-bundle-products-product-price-cell">
                                    {{ "zeobv-bundle-products.detail.dataColumnPrice"|trans|sw_sanitize }}
                                </th>
                            {% endif %}
                        {% endblock %}
                    </tr>
                {% endblock %}
                </thead>
            {% endblock %}

            {% block zeobv_bundle_products_components_bundle_product_info_table_body %}
                <tbody class="product-bundle-products-body">
                {% block zeobv_bundle_products_components_bundle_product_info_table_body_inner %}
                    {% set total = 0 %}

                    {% set mediaIds = products|map(product => product.cover ? product.cover.mediaId : null)|filter(mediaId => mediaId) %}
                    {% if mediaIds %}
                        {% set mediaCollection = searchMedia(mediaIds, context.context) %}
                    {% endif %}

                    {% for product in products|sort((a, b) => a.extensions.zeobvProductBundleConnection.position - b.extensions.zeobvProductBundleConnection.position) %}
                        {% set quantity = product.extensions.zeobvProductBundleConnection.quantity %}
                        {% set comment = product.extensions.zeobvProductBundleConnection.comment %}
                        {% set variant = product.options.elements %}

                        {% set price = product.calculatedPrice %}

                        {% if product.calculatedPrices|length > 0 %}
                            {% set price = product.calculatedPrices|filter((calculatedPrice) => calculatedPrice.quantity <= quantity)|last %}

                            {% if not price %}
                                {% for advancePrice in product.prices %}
                                    {% if advancePrice.quantityStart <= quantity and advancePrice.quantityEnd >= quantity %}
                                        {% set price = product.calculatedPrices|filter(
                                            (calculatedPrice) => calculatedPrice.quantity <= advancePrice.quantityEnd
                                            )|last %}
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                        {% endif %}

                        {% if quantity > 1 %}
                            {% set price = quantity * price.unitPrice %}
                        {% else %}
                            {% set price = price.unitPrice %}
                        {% endif %}

                        {% set total = total + price %}

                        <tr class="product-bundle-products-product-row">
                            {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_thumb %}
                                {% if showThumbnail %}
                                    <td scope="row"
                                        class="product-bundle-products-product-cell product-bundle-products-cell-thin product-bundle-products-product-cell-thumbnail">
                                        {% if product.cover.mediaId %}
                                            {% set productCover = mediaCollection.get(product.cover.mediaId) %}
                                            {% sw_thumbnails 'minimal-image-thumbnails' with {
                                                media: productCover,
                                                sizes: {
                                                    'default': '20px'
                                                },
                                                attributes: {
                                                    'class': 'img-fluid quickview-minimal-img',
                                                    'alt': ( product.cover.translated.alt ?: ''),
                                                    'title': (product.cover.translated.title ?: '')
                                                }
                                            } %}
                                        {% endif %}
                                    </td>
                                {% endif %}
                            {% endblock %}

                            {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_name %}
                                {% set showQuantity = showQuantityOfBundleItemsProductDetailPageTablePresentation %} {# Set this to true or false based on activation state #}

                                {% if not showThumbnail %}
                                    {% if showAvailabilityColumn and not showThumbnail and not showQuantity %}
                                        {% set colspanValue = 3 %}
                                    {% else %}
                                        {% set colspanValue = 1 %}
                                    {% endif %}
                                {% elseif showQuantity %}
                                    {% set colspanValue = 1 %}
                                {% elseif showThumbnail and showAvailabilityColumn %}
                                    {% set colspanValue = 2 %}
                                {% elseif not showThumbnail and showAvailabilityColumn %}
                                    {% set colspanValue = 3 %}
                                {% elseif showThumbnail and showQuantity and showAvailabilityColumn %}
                                    {% set colspanValue = 1 %}
                                {% elseif showThumbnail and showQuantity and not showAvailabilityColumn %}
                                    {% set colspanValue = 1 %}
                                {% elseif not showThumbnail and not showQuantity and showAvailabilityColumn %}
                                    {% set colspanValue = 3 %}
                                {% elseif showQuantity and not showThumbnail and not showAvailabilityColumn %}
                                    {% set colspanValue = 1 %}
                                {% endif %}

                                <td scope="row" class="product-bundle-products-product-cell product-bundle-products-cell-thin" colspan="{{ colspanValue }}">

                                    {% set visibilityResult = product.visibilities.elements |first %}
                                    {% if product.available and product.active and visibilityResult.visibility != "10" %}
                                        {% if(showBundleProductDetailInPopUp == true ) %}
                                           <a href="{{ seoUrl('frontend.detail.page', {productId: product.id}) }}"
                                              class="line-item-label"
                                              title="{{ "zeobv-bundle-products.detail.bundleProductQuickViewTitle"|trans|sw_sanitize }}"
                                               {% if controllerAction is same as('index') %}
                                                 data-ajax-modal="modal"
                                                 data-modal-class="quickview-modal"
                                                 data-url="{{ path('widgets.quickview.minimal', { 'productId': product.id }) }}"
                                               {% endif %}
                                           >
                                                {{ product.translated.name }} {% if comment %} | {{ comment }} {% endif %}
                                            </a>

                                        {% else %}

                                            <a href="{{ seoUrl('frontend.detail.page', {productId: product.id}) }}" title="{{ "zeobv-bundle-products.detail.bundleProductNameTitle"|trans|sw_sanitize }}">
                                                {{ product.translated.name }} {% if comment %} | {{ comment }} {% endif %}
                                            </a>

                                        {% endif %}

                                    {% else %}
                                        {{ product.translated.name }} {% if comment %} | {{ comment }} {% endif %}
                                    {% endif %}
                                    <div class="sub-text">
                                        {% if showManufacturer and product.manufacturer.name %}
                                            <span class="manufacturer-name">{{ product.manufacturer.name }}</span>
                                        {% endif %}
                                        {% if showBundleProductNumber and product.productNumber %}
                                            <span class="product-number">{{ product.productNumber }}</span>
                                        {% endif %}
                                    </div>
                                    {% if variant|length > 0 %}
                                        {% for option in variant %}
                                            {% block component_line_item_variant_characteristics_option_group %}
                                                <span class="product-bundle-details-characteristics-option-group">
                                                    {{ option.group.translated.name }}:
                                                </span>
                                            {% endblock %}
                                            {% block component_line_item_variant_characteristics_option %}
                                                <span class="product-bundle-details-characteristics-option">
                                                    {{ option.translated.name }}
                                                </span>
                                            {% endblock %}
                                            {% if product.options.elements|last != option %}
                                                {% block component_line_item_variant_characteristics_divider %}
                                                    {{ ' | ' }}
                                                {% endblock %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </td>
                            {% endblock %}

                            {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_quantity %}
                                {% if(showQuantityOfBundleItemsProductDetailPageTablePresentation == true ) %}
                                    <td class="product-bundle-products-product-cell product-bundle-products-cell-thin product-bundle-products-product-cell-qty"
                                        colspan="{{ showThumbnail ? "1" : 2}}"
                                    >
                                        {{ quantity }}
                                    </td>
                                {% endif %}
                            {% endblock %}

                            {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_available %}
                                {% if showAvailabilityColumn %}
                                    <td class="product-bundle-products-product-cell product-bundle-products-product-cell-available">
                                        {% if product.available %}
                                            {% sw_icon 'checkmark' style { 'size': 'sm' } %}
                                        {% else %}
                                            {% sw_icon 'x' style { 'size': 'sm' } %}
                                        {% endif %}
                                    </td>
                                {% endif %}
                            {% endblock %}

                            {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_price %}
                                {% if showPrices %}
                                    <td class="product-bundle-products-product-cell product-bundle-products-product-cell-price"
                                        colspan="{{ (showThumbnail ? (showQuantityOfBundleItemsProductDetailPageTablePresentation ? 2 : 3) : (showQuantityOfBundleItemsProductDetailPageTablePresentation ? 1 : 3)) }}">
                                        {{ price|currency }}
                                    </td>
                                {% endif %}
                            {% endblock %}
                        </tr>
                    {% endfor %}

                    {% block zeobv_bundle_products_components_bundle_product_info_table_footer %}
                        {% if showPrices and showTotalPrice %}
                            <tr class="product-bundle-products-total-row">
                                {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_total_label %}
                                    <th scope="row"
                                        colspan="{% if showQuantityOfBundleItemsProductDetailPageTablePresentation and showAvailabilityColumn %}4{% else %}{{ showThumbnail and showAvailabilityColumn ? 4 : (showThumbnail or showAvailabilityColumn ? 3 : 3) }}{% endif %}"
                                        class="product-bundle-products-product-cell product-bundle-total-cell">
                                    <span class="product-bundle-total-label">
                                        {{ "zeobv-bundle-products.detail.dataColumnTotal"|trans|sw_sanitize }}
                                    </span>
                                    </th>
                                {% endblock %}

                                {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_total_price %}
                                    <td class="product-bundle-products-product-cell product-bundle-products-product-cell-total">
                                        <span>{{ total|currency }}</span>
                                    </td>
                                {% endblock %}
                            </tr>
                        {% endif %}
                    {% endblock %}
                {% endblock %}
                </tbody>
            {% endblock %}
        </table>

        {% block product_table_mobile %}
            <table class="table product-bundle-products-table responsive-table-head">

                {% block zeobv_bundle_products_components_bundle_product_info_table_body_responsive %}
                    <tbody class="product-bundle-products-body">
                    {% block zeobv_bundle_products_components_bundle_product_info_table_body_inner_responsive %}
                        {% set total = 0 %}

                        {% set mediaIds = products|map(product => product.cover ? product.cover.mediaId : null)|filter(mediaId => mediaId) %}
                        {% if mediaIds %}
                            {% set mediaCollection = searchMedia(mediaIds, context.context) %}
                        {% endif %}
                        <div class="responsive-table-head">
                            {% block page_product_detail_price_block_table_head_responsive %}
                                    <div class="product-bundle-products-head">
                                    {% block page_product_detail_price_block_table_head_inner_responsive %}
                                        <div class="product-bundle-products-header-row">
                                            {% block zeobv_bundle_products_components_bundle_product_info_table_header_cell_name_responsive %}
                                                <div scope="col"
                                                    class="product-bundle-products-product-cell product-bundle-products-product-name-cell"
                                                    colspan="{{ showThumbnail ? "2" : 1 }}">
                                                    {{ "zeobv-bundle-products.detail.dataColumnName"|trans|sw_sanitize }}
                                                </div>
                                            {% endblock %}
                                        </div>
                                    {% endblock %}
                                    </div>
                            {% endblock %}

                            {% for product in products|sort((a, b) => a.extensions.zeobvProductBundleConnection.position - b.extensions.zeobvProductBundleConnection.position) %}
                                {% set quantity = product.extensions.zeobvProductBundleConnection.quantity %}
                                {% set comment = product.extensions.zeobvProductBundleConnection.comment %}

                                {% set price = product.calculatedPrice %}

                                {% if product.calculatedPrices|length > 0 %}
                                    {% set price = product.calculatedPrices|filter((calculatedPrice) => calculatedPrice.quantity <= quantity)|last %}

                                    {% if not price %}
                                        {% for advancePrice in product.prices %}
                                            {% if advancePrice.quantityStart <= quantity and advancePrice.quantityEnd >= quantity %}
                                                {% set price = product.calculatedPrices|filter(
                                                    (calculatedPrice) => calculatedPrice.quantity <= advancePrice.quantityEnd
                                                    )|last %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                {% endif %}

                                {% if quantity > 1 %}
                                    {% set price = quantity * price.unitPrice %}
                                {% else %}
                                    {% set price = price.unitPrice %}
                                {% endif %}

                                {% set total = total + price %}

                                <tr class="product-bundle-products-product-row">
                                    {% block main %}
                                        <div class="table product-bundle-products-table-responsive">
                                                {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_thumb_responsive %}
                                                    {% if showThumbnail and product.cover.mediaId and mediaCollection %}
                                                        <div class="image">
                                                            <div class="product-bundle-products-product-cell product-bundle-products-cell-thin product-bundle-products-product-cell-thumbnail">
                                                                {% set productCover = mediaCollection.get(product.cover.mediaId) %}
                                                                {% sw_thumbnails 'minimal-image-thumbnails' with {
                                                                    media: productCover,
                                                                    sizes: {
                                                                        'default': '20px'
                                                                    },
                                                                    attributes: {
                                                                        'class': 'img-fluid quickview-minimal-img',
                                                                        'alt': ( product.cover.translated.alt ?: ''),
                                                                        'title': (product.cover.translated.title ?: '')
                                                                    }
                                                                } %}
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                {% endblock %}

                                            <div class="bundle-child-product-detail">
                                                {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_name_responsive %}
                                                    <div class="product-bundle-products-product-cell product-bundle-products-cell-thin">
                                                        {% set visibilityResult = product.visibilities.elements |first %}
                                                        {% if product.available and product.active and visibilityResult.visibility != "10" %}
                                                            {% if(showBundleProductDetailInPopUp == true ) %}
                                                                <a href="{{ seoUrl('frontend.detail.page', {productId: product.id}) }}"
                                                                   class="line-item-label"
                                                                   title="{{ "zeobv-bundle-products.detail.bundleProductModalTitle"|trans|sw_sanitize }}"
                                                                        {% if controllerAction is same as('index') %}
                                                                            data-ajax-modal="modal"
                                                                            data-modal-class="quickview-modal"
                                                                            data-url="{{ path('widgets.quickview.minimal', { 'productId': product.id }) }}"
                                                                        {% endif %}
                                                                >
                                                                    {{ product.translated.name }} {% if comment %} | {{ comment }} {% endif %}
                                                                </a>

                                                            {% else %}

                                                                <a href="{{ seoUrl('frontend.detail.page', {productId: product.id}) }}" title="{{ "zeobv-bundle-products.detail.bundleProductNameTitle"|trans|sw_sanitize }}">
                                                                    {{ product.translated.name }} {% if comment %} | {{ comment }} {% endif %}
                                                                </a>

                                                            {% endif %}

                                                        {% else %}
                                                            {{ product.translated.name }} {% if comment %} | {{ comment }} {% endif %}
                                                        {% endif %}
                                                        <div class="sub-text">
                                                            {% if showManufacturer and product.manufacturer.name %}
                                                                <div class="sub-content"><span class="manufacturer-title product-label-title">{{ "zeobv-bundle-products.detail.manufacturerName"|trans|sw_sanitize }}</span>
                                                                <span class="manufacturer-name product-label-content">{{ product.manufacturer.name }}</span></div>
                                                            {% endif %}
                                                            {% if showBundleProductNumber and product.productNumber %}
                                                                <div class="sub-content"><span class="product-sku product-label-title">{{ "zeobv-bundle-products.detail.productSKU"|trans|sw_sanitize }}</span>
                                                                <span class="product-number product-label-content">{{ product.productNumber }}</span></div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                {% endblock %}


                                                {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_quantity_responsive %}
                                                    {% if(showQuantityOfBundleItemsProductDetailPageTablePresentation == true ) %}
                                                        <div class="sub-content"><span class="product-quantity product-label-title">
                                                            {{ "zeobv-bundle-products.detail.productQuantity"|trans|sw_sanitize }}
                                                            </span>
                                                            <div class="product-bundle-products-product-cell product-bundle-products-cell-thin product-bundle-products-product-cell-qty product-label-content">
                                                                {{ quantity }}
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                {% endblock %}

                                                {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_available_responsive %}
                                                    {% if showAvailabilityColumn %}
                                                        <div class="sub-content"><span class="product-available product-label-title">{{ "zeobv-bundle-products.detail.productAvailable"|trans|sw_sanitize }}</span>
                                                        <div class="product-bundle-products-product-cell product-bundle-products-product-cell-available product-label-content">
                                                            {% if product.available %}
                                                                {% sw_icon 'checkmark' style { 'size': 'sm' } %}
                                                            {% else %}
                                                                {% sw_icon 'x' style { 'size': 'sm' } %}
                                                            {% endif %}
                                                        </div>
                                                        </div>
                                                    {% endif %}
                                                {% endblock %}

                                                {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_price_responsive %}
                                                    {% if showPrices %}
                                                        <div class="sub-content"><span class="product-price product-label-title">{{ "zeobv-bundle-products.detail.productPrice"|trans|sw_sanitize }}</span>
                                                        <div class="product-bundle-products-product-cell product-bundle-products-product-cell-price product-label-content">
                                                            {{ price|currency }}
                                                        </div>
                                                        </div>
                                                    {% endif %}
                                                {% endblock %}
                                            </div>
                                        </div>
                                    {% endblock %}
                                </tr>
                            {% endfor %}
                        </div>

                        {% block zeobv_bundle_products_components_bundle_product_info_table_footer_responsive %}
                            {% if showPrices and showTotalPrice %}
                                <tr class="product-bundle-products-total-row">
                                    {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_total_label_responsive %}
                                        <th scope="row"
                                            colspan="{{ showThumbnail and showAvailabilityColumn ? 4 : (showThumbnail or showAvailabilityColumn ? 3 : 2) }}"
                                            class="product-bundle-products-product-cell product-bundle-total-cell">
                                    <span class="product-bundle-total-label">
                                        {{ "zeobv-bundle-products.detail.dataColumnTotal"|trans|sw_sanitize }}
                                    </span>
                                        </th>
                                    {% endblock %}

                                    {% block zeobv_bundle_products_components_bundle_product_info_table_body_cell_total_price_responsive %}
                                        <td class="product-bundle-products-product-cell product-bundle-products-product-cell-total">
                                            <span>{{ total|currency }}</span>
                                        </td>
                                    {% endblock %}
                                </tr>
                            {% endif %}
                        {% endblock %}
                    {% endblock %}
                    </tbody>
                {% endblock %}
            </table>
        {% endblock %}

    {% endif %}
{% endblock %}
