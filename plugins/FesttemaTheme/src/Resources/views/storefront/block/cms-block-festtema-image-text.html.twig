{% block block_festtema_image_text %}
    <div class="main-width">	
        <div class="row row-cols-3 featured-categories">
            {% block block_festtema_image_text_top_left_image %}
                {% set element = block.slots.getSlot('top-left-image') %}
                <div class="col-sm-6 col-6 col-md-4 pr-0 featured-box cms-element-image">
                    {% if element.translated.config.url.value %}
                        <a href="{{ element.translated.config.url.value }}"
                        {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                    {% endif %}
                        <div class="fea-images-one cms-image-container is-{{ element.translated.config.displayMode.value }}" {% if element.translated.config.minHeight.value and element.translated.config.displayMode.value == "cover" %} style="min-height: {{ element.translated.config.minHeight.value }};"{% endif %}>
                            {% block element_image_media_one %}
                                {% set attributes = {
                                    'class': 'cms-image fes-cat-img',
                                    'alt': (element.data.media.translated.alt ?: ''),
                                    'title': (element.data.media.translated.title ?: ''),
                                    'width': 380,
                                    'height': 270
                                } %}

                                {% if element.translated.config.displayMode.value == 'cover' or element.translated.config.displayMode.value == 'contain' %}
                                    {% set attributes = attributes|merge({ 'data-object-fit': element.translated.config.displayMode.value }) %}
                                {% endif %}

                                {% sw_thumbnails 'cms-image-thumbnails' with {
                                    media: element.data.media
                                } %}
                            {% endblock %}
                            {% set elementtext = block.slots.getSlot('top-left-text') %}
                            <div class="categories-names"><h3>{{ elementtext.data.content | raw}}</h3></div>
                        </div>	
                    {% if element.translated.config.url.value %}
                        </a>
                    {% endif %}
                </div>
            {% endblock %}

            {% block block_festtema_image_text_top_center_image %}
                {% set element = block.slots.getSlot('top-center-image') %}
                <div class="col-sm-6 col-6 col-md-4 pr-0 featured-box cms-element-image">
                    {% if element.translated.config.url.value %}
                        <a href="{{ element.translated.config.url.value }}"
                        {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                    {% endif %}
                        <div class="fea-images-two cms-image-container is-{{ element.translated.config.displayMode.value }}" {% if element.translated.config.minHeight.value and element.translated.config.displayMode.value == "cover" %} style="min-height: {{ element.translated.config.minHeight.value }};"{% endif %}>
                            {% block element_image_media_two %}
                                {% set attributes = {
                                    'class': 'cms-image fes-cat-img',
                                    'alt': (element.data.media.translated.alt ?: ''),
                                    'title': (element.data.media.translated.title ?: ''),
                                    'width': 380,
                                    'height': 270
                                } %}

                                {% if element.translated.config.displayMode.value == 'cover' or element.translated.config.displayMode.value == 'contain' %}
                                    {% set attributes = attributes|merge({ 'data-object-fit': element.translated.config.displayMode.value }) %}
                                {% endif %}

                                {% sw_thumbnails 'cms-image-thumbnails' with {
                                    media: element.data.media
                                } %}
                            {% endblock %}
                            {% set elementtext = block.slots.getSlot('top-center-text') %}
                            <div class="categories-names"><h3>{{ elementtext.data.content | raw}}</h3></div>
                        </div>	
                    {% if element.translated.config.url.value %}
                        </a>
                    {% endif %}
                </div>
            {% endblock %}
            
            {% block block_festtema_image_text_top_right_image %}
                {% set element = block.slots.getSlot('top-right-image') %}
                <div class="col-sm-6 col-6 col-md-4 pr-0 featured-box cms-element-image">
                    {% if element.translated.config.url.value %}
                        <a href="{{ element.translated.config.url.value }}"
                        {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                    {% endif %}
                        <div class="fea-images-three cms-image-container is-{{ element.translated.config.displayMode.value }}" {% if element.translated.config.minHeight.value and element.translated.config.displayMode.value == "cover" %} style="min-height: {{ element.translated.config.minHeight.value }};"{% endif %}>
                            {% block element_image_media_three %}
                                {% set attributes = {
                                    'class': 'cms-image fes-cat-img',
                                    'alt': (element.data.media.translated.alt ?: ''),
                                    'title': (element.data.media.translated.title ?: ''),
                                    'width': 380,
                                    'height': 270
                                } %}

                                {% if element.translated.config.displayMode.value == 'cover' or element.translated.config.displayMode.value == 'contain' %}
                                    {% set attributes = attributes|merge({ 'data-object-fit': element.translated.config.displayMode.value }) %}
                                {% endif %}

                                {% sw_thumbnails 'cms-image-thumbnails' with {
                                    media: element.data.media
                                } %}
                            {% endblock %}
                            {% set elementtext = block.slots.getSlot('top-right-text') %}
                            <div class="categories-names"><h3>{{ elementtext.data.content | raw}}</h3></div>
                        </div>	
                    {% if element.translated.config.url.value %}
                        </a>
                    {% endif %}
                </div>
            {% endblock %}

            {% block block_festtema_image_text_bottom_left_image %}
                {% set element = block.slots.getSlot('bottom-left-image') %}
                <div class="col-sm-6 col-6 col-md-4 pr-0 featured-box cms-element-image">
                    {% if element.translated.config.url.value %}
                        <a href="{{ element.translated.config.url.value }}"
                        {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                    {% endif %}
                        <div class="fea-images-four cms-image-container is-{{ element.translated.config.displayMode.value }}" {% if element.translated.config.minHeight.value and element.translated.config.displayMode.value == "cover" %} style="min-height: {{ element.translated.config.minHeight.value }};"{% endif %}>
                            {% block element_image_media_four %}
                                {% set attributes = {
                                    'class': 'cms-image fes-cat-img',
                                    'alt': (element.data.media.translated.alt ?: ''),
                                    'title': (element.data.media.translated.title ?: ''),
                                    'width': 380,
                                    'height': 270
                                } %}

                                {% if element.translated.config.displayMode.value == 'cover' or element.translated.config.displayMode.value == 'contain' %}
                                    {% set attributes = attributes|merge({ 'data-object-fit': element.translated.config.displayMode.value }) %}
                                {% endif %}

                                {% sw_thumbnails 'cms-image-thumbnails' with {
                                    media: element.data.media
                                } %}
                            {% endblock %}
                            {% set elementtext = block.slots.getSlot('bottom-left-text') %}
                            <div class="categories-names"><h3>{{ elementtext.data.content | raw}}</h3></div>
                        </div>	
                    {% if element.translated.config.url.value %}
                        </a>
                    {% endif %}
                </div>
            {% endblock %}

            {% block block_festtema_image_text_bottom_center_image %}
                {% set element = block.slots.getSlot('bottom-center-image') %}
                <div class="col-sm-6 col-6 col-md-4 pr-0 featured-box cms-element-image">
                    {% if element.translated.config.url.value %}
                        <a href="{{ element.translated.config.url.value }}"
                        {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                    {% endif %}
                        <div class="fea-images-five cms-image-container is-{{ element.translated.config.displayMode.value }}" {% if element.translated.config.minHeight.value and element.translated.config.displayMode.value == "cover" %} style="min-height: {{ element.translated.config.minHeight.value }};"{% endif %}>
                            {% block element_image_media_five %}
                                {% set attributes = {
                                    'class': 'cms-image fes-cat-img',
                                    'alt': (element.data.media.translated.alt ?: ''),
                                    'title': (element.data.media.translated.title ?: ''),
                                    'width': 380,
                                    'height': 270
                                } %}

                                {% if element.translated.config.displayMode.value == 'cover' or element.translated.config.displayMode.value == 'contain' %}
                                    {% set attributes = attributes|merge({ 'data-object-fit': element.translated.config.displayMode.value }) %}
                                {% endif %}

                                {% sw_thumbnails 'cms-image-thumbnails' with {
                                    media: element.data.media
                                } %}
                            {% endblock %}
                            {% set elementtext = block.slots.getSlot('bottom-center-text') %}
                            <div class="categories-names"><h3>{{ elementtext.data.content | raw}}</h3></div>
                        </div>	
                    {% if element.translated.config.url.value %}
                        </a>
                    {% endif %}
                </div>
            {% endblock %}

            {% block block_festtema_image_text_bottom_right_image %}
                {% set element = block.slots.getSlot('bottom-right-image') %}
                <div class="col-sm-6 col-6 col-md-4 pr-0 featured-box cms-element-image">
                    {% if element.translated.config.url.value %}
                        <a href="{{ element.translated.config.url.value }}"
                        {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                    {% endif %}
                        <div class="fea-images-six cms-image-container is-{{ element.translated.config.displayMode.value }}" {% if element.translated.config.minHeight.value and element.translated.config.displayMode.value == "cover" %} style="min-height: {{ element.translated.config.minHeight.value }};"{% endif %}>
                            {% block element_image_media_six %}
                                {% set attributes = {
                                    'class': 'cms-image fes-cat-img',
                                    'alt': (element.data.media.translated.alt ?: ''),
                                    'title': (element.data.media.translated.title ?: ''),
                                    'width': 380,
                                    'height': 270
                                } %}

                                {% if element.translated.config.displayMode.value == 'cover' or element.translated.config.displayMode.value == 'contain' %}
                                    {% set attributes = attributes|merge({ 'data-object-fit': element.translated.config.displayMode.value }) %}
                                {% endif %}

                                {% sw_thumbnails 'cms-image-thumbnails' with {
                                    media: element.data.media
                                } %}
                            {% endblock %}
                            {% set elementtext = block.slots.getSlot('bottom-right-text') %}
                            <div class="categories-names"><h3>{{ elementtext.data.content | raw}}</h3></div>
                        </div>	
                    {% if element.translated.config.url.value %}
                        </a>
                    {% endif %}
                </div>
            {% endblock %}
        </div>
    </div>
</div>
{% endblock %}