{% block block_festtema_trends %}
    <div class="trend-count col-12">
        <div class="container main-width">
            <div class="row trend-box-bg">
                <div class="col-12 col-md-6 col-sm-6 pr-0 pl-0 left-box">
                    {% block block_festtema_trends_top_left %}
                        {% set element = block.slots.getSlot('top-left') %}
                        {% block element_festtema_trends_image_media_top %}
                            {% if element.translated.config.url.value %}
                                    <a href="{{ element.translated.config.url.value }}"
                                {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                            {% endif %}
                                {% if 'framework/assets' in element.config.media.value %}
                                        <img src="/bundles/{{ element.config.media.value }}" class="img-fluid" alt="{{ element.data.media.translated.alt }}" height="352" width="585">
                                {% else %}
                                    {% set media = searchMedia([element.config.media.value], context.context) %}
                                    {# @var item \Shopware\Core\Content\Media\MediaEntity #}
                                    <img src="{{ media.first.url }}" class="img-fluid" alt="{{ element.data.media.translated.alt }}" height="352" width="585">
                                {% endif %}
                            {% if element.translated.config.url.value %}
                                </a>
                            {% endif %}
                        {% endblock %}
                    {% endblock %}
                </div>
                <div class="col-12 col-md-6 col-sm-6 trend-box-details">
                    {% block block_festtema_trends_top_right %}
                        {% set element = block.slots.getSlot('top-right') %}
                        {{ element.data.content | raw}}
                    {% endblock %}
                </div>						
            </div>
            
            <div class="row trend-box-bg costume-box-bg">
                
                <div class="mobile-view col-12 col-md-6 col-sm-6 pr-0 pl-0 right-box">
                    {% block block_festtema_trends_bottom_right_mobile %}
                        {% set element = block.slots.getSlot('bottom-left') %}
                        {% block element_festtema_trends_image_media_bottom_mobile %}
                            {% if 'framework/assets' in element.config.media.value %}
                                <img src="/bundles/{{ element.config.media.value }}" class="img-fluid" alt="{{ element.data.media.translated.alt }}" height="352" width="585">
                            {% else %}
                                {% set media = searchMedia([element.config.media.value], context.context) %}
                                {# @var item \Shopware\Core\Content\Media\MediaEntity #}
                                <img src="{{ media.first.url }}" class="img-fluid" alt="{{ element.data.media.translated.alt }}" height="352" width="585">
                            {% endif %}
                        {% endblock %}
                    {% endblock %}
                </div>
                
                <div class="col-12 col-md-6 col-sm-6 trend-box-details">
                    {% block block_festtema_trends_bottom_left %}
                        {% set element = block.slots.getSlot('bottom-right') %}
                        {{ element.data.content | raw}}
                    {% endblock %}
                </div>
                
                <div class="desk-view col-12 col-md-6 col-sm-6 pr-0 pl-0 right-box">
                    {% block block_festtema_trends_bottom_right %}
                        {% set element = block.slots.getSlot('bottom-left') %} 
                        {% block element_festtema_trends_image_media_bottom %}
                            {% if element.translated.config.url.value %}
                                    <a href="{{ element.translated.config.url.value }}"
                                {% if element.translated.config.newTab.value %}target="_blank" rel="noopener"{% endif %}>
                            {% endif %}
                                {% if 'framework/assets' in element.config.media.value %}
                                        <img src="/bundles/{{ element.config.media.value }}" class="img-fluid" alt="{{ element.data.media.translated.alt }}">
                                {% else %}
                                    {% set media = searchMedia([element.config.media.value], context.context) %}
                                    {# @var item \Shopware\Core\Content\Media\MediaEntity #}
                                    {% for item in media %}
                                            <img src="{{ item.url }}" class="img-fluid" alt="{{ element.data.media.translated.alt }}">
                                    {% endfor %} 
                                {% endif %}
                            {% if element.translated.config.url.value %}
                                </a>
                            {% endif %}
                        {% endblock %}
                    {% endblock %}
                </div>
                
            </div>
        </div>
    </div>
{% endblock %}
