{% sw_extends '@Storefront/storefront/page/content/index.html.twig' %}


{% block cms_breadcrumb %}
    <div class="main-width">
        <div class="breadcrumb cms-breadcrumb container">
            {% sw_include '@Storefront/storefront/layout/breadcrumb.html.twig' with {
                context: context,
                category: page.header.navigation.active
            } only %}
        </div>
    </div>
{% endblock %}

{% block cms_content %}
    {% set cmsPageClasses = ('cms-page ' ~ page.cmsPage.cssClass|striptags)|trim %}
    {# {% if controllerAction == 'home' %}
        {% if app.request.locale == 'sv-SE' %}
            {% sw_include "@Storefront/storefront/page/content/se.html.twig" %}
        {% endif %}
        {% if app.request.locale == 'nb-NO' %}
            {% sw_include "@Storefront/storefront/page/content/no.html.twig" %}
        {% endif %}
        {% if (app.request.schemeAndHttpHost in ['https://www.festtema.dk/', 'https://www.festtema.dk', 'http://shopware.local']) %}
            {% sw_include "@Storefront/storefront/page/content/dk.html.twig" %}
        {% endif %}
        {% if (app.request.schemeAndHttpHost in ['https://www.billigekostumer.dk', 'https://www.billigekostumer.dk/', 'https://billigekostumer.dk/', 'https://billigekostumer.dk'])%}
            {% sw_include "@Storefront/storefront/page/content/bk.html.twig" %}
        {% endif %}
    {% else %} #}
        <div class="{{ cmsPageClasses }}">
            {% block page_content_blocks %}
                {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with {'cmsPage': page.cmsPage} %}
            {% endblock %}
        </div>
    {# {% endif %} #}
{% endblock %}
