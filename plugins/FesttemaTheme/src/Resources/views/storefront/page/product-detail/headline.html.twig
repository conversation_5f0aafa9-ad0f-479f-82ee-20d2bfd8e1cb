{% sw_extends '@Storefront/storefront/page/product-detail/headline.html.twig' %}

{% block page_product_detail_name_container %}
    {% block page_product_detail_name %}
        <h1 class="product-detail-name"
            itemprop="name">
            {{ page.product.translated.name }}
        </h1>
    {% endblock %}

    {% block page_product_detail_reviews_io %}
        {% if config('REVIEWSio.config.showRatingSnippet') %}
            <div class='ruk_rating_snippet' data-sku='{{page.product.productNumber}}'></div>
        {% endif %}
    {% endblock %}

    <div class="float-end product-review-wishlist-block">
        {% set remoteClickOptions = {
            selector: "#review-tab",
            scrollToElement: true
        } %}

        {% block page_product_detail_reviews %}
            {% if page.reviews.totalReviews > 0 and config('core.listing.showReview') %}
                <div class="product-detail-reviews">

                    {% sw_include '@Storefront/storefront/component/review/rating.html.twig' with {
                        points: page.product.ratingAverage,
                        style: 'text-primary'
                    } %}
                    <a {{ dataBsToggleAttr }}="tab"
                        class="product-detail-reviews-link"
                        data-offcanvas-tabs="true"
                        data-remote-click="true"
                        data-remote-click-options='{{ remoteClickOptions|json_encode }}'
                        href="#review-tab-pane"
                        aria-controls="review-tab-pane">
                        {{ page.reviews.totalReviews }}
                        {{ "detail.reviewLinkText"|trans({'%count%': page.reviews.totalReviews})|sw_sanitize }}
                    </a>
                </div>
            {% endif %}
        {% endblock %}
        {% if config('core.cart.wishlistEnabled') %}
            <div class="fs-wishlist desktop-view">
                {% block page_product_detail_wishlist %}
                    {% sw_include '@Storefront/storefront/component/product/card/wishlist.html.twig' with {
                        showText: false,
                        size: 'lg',
                        productId: page.product.id
                    } %}
                {% endblock %}
            </div>
        {% endif %}
    </div>
    {% block product_indholder_block %}
    {% endblock %}
{% endblock %}

{% block page_product_detail_manufacturer %}
{% endblock %}
