{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_meta_tags_opengraph %}
      {{ parent() }}
      <style>@charset "UTF-8";*,::before,::after{box-sizing:border-box}html{font-family:sans-serif;line-height:1.15}header,main,nav{display:block}body{margin:0;font-family:Montserrat,sans-serif;font-size:.875rem;font-weight:400;line-height:1.5;color:rgb(0,0,0);text-align:left;background-color:rgb(255,255,255)}ol,ul{margin-top:0;margin-bottom:1rem}a{color:rgb(132,25,111);text-decoration:none;background-color:transparent}img{vertical-align:middle;border-style:none}svg{overflow:hidden;vertical-align:middle}button{border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:0}input,button{margin:0;font-family:inherit;font-size:inherit;line-height:inherit}button,input{overflow:visible}button{text-transform:none}button,[type="button"]{-webkit-appearance:button}*::-webkit-file-upload-button{font-family:inherit;font-size:inherit;font-style:inherit;font-variant:inherit;font-weight:inherit;line-height:inherit;-webkit-appearance:button}template{display:none}.list-unstyled{padding-left:0;list-style:none}.list-inline{padding-left:0;list-style:none}.list-inline-item{display:inline-block}.list-inline-item:not(:last-child){margin-right:.5rem}.img-fluid{max-width:100%;height:auto}.container{width:100%;padding-right:20px;padding-left:20px;margin-right:auto;margin-left:auto}@media (min-width:576px){.container{max-width:540px}}.row{display:-webkit-flex;margin-right:-20px;margin-left:-20px}.no-gutters{margin-right:0;margin-left:0}.no-gutters>.col,.no-gutters>[class*="col-"]{padding-right:0;padding-left:0}.col-12,.col,.col-auto,.col-sm-12,.col-sm,.col-sm-auto,.col-md-6,.col-md-auto,.col-lg-6,.col-lg-auto{position:relative;width:100%;padding-right:20px;padding-left:20px}.col{max-width:100%}.col-auto{width:auto;max-width:100%}.col-12{max-width:100%}@media (min-width:576px){.col-sm{max-width:100%}.col-sm-auto{width:auto;max-width:100%}.col-sm-12{max-width:100%}}.btn{display:inline-block;font-weight:400;color:rgb(0,0,0);text-align:center;vertical-align:middle;background-color:transparent;border:1px solid transparent;padding:2px 12px;font-size:.875rem;line-height:34px;border-top-left-radius:3px;border-top-right-radius:3px;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.btn-primary{color:rgb(255,255,255);background-color:rgb(132,25,111);border-color:rgb(132,25,111)}.btn-light{color:rgb(33,37,41);background-color:rgb(249,249,249);border-color:rgb(249,249,249)}.btn-block{display:block;width:100%}.dropdown{position:relative}.dropdown-menu{position:absolute;top:100%;left:0;z-index:1000;display:none;float:left;min-width:10rem;padding:.5rem 0;margin:.125rem 0 0;font-size:.875rem;color:rgb(0,0,0);text-align:left;list-style:none;background-color:rgb(255,255,255);-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid rgb(188,193,199);border-top-left-radius:3px;border-top-right-radius:3px;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.dropdown-menu-right{right:0;left:auto}.dropdown-header{display:block;padding:.5rem 1.5rem;margin-bottom:0;font-size:.75rem;color:rgb(121,132,144);white-space:nowrap}.nav{display:-webkit-flex;padding-left:0;margin-bottom:0;list-style:none}.nav-link{display:block;padding:.5rem 1rem}.navbar{position:relative;display:-webkit-flex;padding:.5rem 1rem}.navbar-toggler{padding:.25rem .75rem;font-size:1rem;line-height:1;background-color:transparent;border:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.navbar-toggler-icon{display:inline-block;width:1.5em;height:1.5em;vertical-align:middle;content:'';background-size:100%;background-position:50% 50%;background-repeat:no-repeat no-repeat}.navbar-light .navbar-toggler{color:rgba(0,0,0,.498039);border-color:rgba(0,0,0,.0980392)}.navbar-light .navbar-toggler-icon{background-image:url('data:image/svg+xml,%3csvg viewBox=\'0 0 30 30\' xmlns=\'http://www.w3.org/2000/svg\'%3e%3cpath stroke=\'rgba(0, 0, 0, 0.5)\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-miterlimit=\'10\' d=\'M4 7h22M4 15h22M4 23h22\'/%3e%3c/svg%3e')}.card{position:relative;display:-webkit-flex;min-width:0;word-wrap:break-word;background-color:transparent;-webkit-background-clip:border-box;background-clip:border-box;border:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.card>.list-group:first-child .list-group-item:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.card>.list-group:last-child .list-group-item:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.breadcrumb{display:-webkit-flex;padding:.75rem 1rem;margin-bottom:1rem;list-style:none;background-color:transparent;border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.badge{display:inline-block;padding:.25em .4em;font-size:.75rem;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-top-left-radius:3px;border-top-right-radius:3px;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.badge-primary{color:rgb(255,255,255);background-color:rgb(132,25,111)}.list-group{display:-webkit-flex;padding-left:0;margin-bottom:0}.list-group-item-action{width:100%;color:rgb(73,80,87);text-align:inherit}.list-group-item{position:relative;display:block;padding:.75rem 1.25rem;margin-bottom:-1px;background-color:rgb(255,255,255);border:1px solid rgba(0,0,0,.121569)}.list-group-item:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.list-group-item:last-child{margin-bottom:0;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.list-group-flush .list-group-item{border-right-width:0;border-left-width:0;border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.list-group-flush .list-group-item:last-child{margin-bottom:-1px}.list-group-flush:first-child .list-group-item:first-child{border-top-width:0}.list-group-flush:last-child .list-group-item:last-child{margin-bottom:0;border-bottom-width:0}.d-none{display:none!important}@media (min-width:576px){.d-sm-none{display:none!important}.d-sm-block{display:block!important}}.p-0{padding:0px!important}html::before{position:absolute;top:-100%;font-size:0}@media (max-width:575.98px){html::before{content:xs}}@media (max-width:767.98px) and (min-width:576px){html::before{content:sm}}@media (max-width:991.98px) and (min-width:768px){html::before{content:md}}@media (max-width:1199.98px) and (min-width:992px){html::before{content:lg}}.base-slider{position:relative;overflow:hidden}.gallery-slider{width:99.9%}.gallery-slider-row{margin-bottom:1rem}.gallery-slider-row.is-loading{height:430px;overflow:hidden}.gallery-slider-container{position:relative;height:100%}.gallery-slider-container .gallery-slider-image{display:block}.gallery-slider-item{position:relative;-webkit-backface-visibility:hidden;display:block;height:100%;max-width:100%}.gallery-slider-item.is-contain{height:100%}.gallery-slider-item.is-contain .gallery-slider-image{font-family:'object-fit: cover;';position:absolute;top:0;right:0;bottom:0;left:0;margin:0 auto}.gallery-slider-item.is-contain .gallery-slider-image{margin:auto;font-family:'object-fit: contain;'}.gallery-slider-image{max-height:100%;max-width:100%}@media (max-width:575.98px){.gallery-slider-item.is-contain{min-height:225px!important}}.icon{width:22px;height:22px;display:-webkit-inline-flex;font-size:inherit;overflow:visible;color:rgb(135,135,135)}.icon>svg{width:100%;height:100%;top:.25em;position:relative;fill:currentColor}.icon>svg path,.icon>svg use{fill:currentColor}.icon-fluid{width:100%;height:100%}.icon-sm{width:19.25px;height:19.25px}.btn-light .icon{color:rgb(33,37,41)}.btn .icon>svg{top:6px}.navigation-offcanvas-link-icon .icon>svg{top:0}.container{max-width:1400px}.container-main{padding:20px 0;min-height:200px}.header-row{padding:.5rem 0}.header-logo-col{margin:1rem 0}.header-logo-main{text-align:center;display:block}.header-logo-main-link{width:100%}.header-logo-picture{display:block;max-width:300px;margin:auto}.header-search{margin:.5rem 0}.header-search-form{position:relative}.header-cart-total{display:none}.header-wishlist .header-wishlist-badge{position:absolute;right:-.25rem}@media (min-width:576px){.header-row{padding-bottom:1rem}.header-logo-col{padding-bottom:.5rem}.header-search{max-width:400px;margin:auto}.header-cart-total{display:inline-block;margin-left:.5rem}}.navigation-offcanvas-container{position:relative}.navigation-offcanvas-link-icon{display:-webkit-inline-flex}.navigation-offcanvas-link{display:-webkit-flex;padding:12px 1rem}.navigation-offcanvas-link.is-back-link .navigation-offcanvas-link-icon{margin-right:.5rem}.account-menu-header{border-bottom-width:1px;border-bottom-style:solid;border-bottom-color:rgb(188,193,199);font-size:.875rem;font-weight:700;padding:.5rem 0;color:rgb(0,0,0)}.account-menu-register{text-align:center}.account-menu-login{padding:1rem 0;border-bottom-width:1px;border-bottom-style:solid;border-bottom-color:rgb(188,193,199);margin-bottom:.25rem}.account-menu-login-button{width:100%;margin-bottom:.5rem}.account-menu-dropdown{padding:.5rem 1rem;min-width:200px}.account-menu-dropdown .account-menu-inner{border:0}.account-menu-dropdown .account-aside-item{padding:.5rem 0}@media (min-width:576px){.account-menu .offcanvas-close{display:none}}body{-webkit-font-feature-settings:'cv02' 1,'cv03' 1,'cv04' 1}body{-webkit-font-smoothing:antialiased;text-rendering:optimizelegibility;display:-webkit-flex;min-height:100vh}.badge{border:0;border-top-left-radius:50px;border-top-right-radius:50px;border-bottom-right-radius:50px;border-bottom-left-radius:50px;box-sizing:content-box;height:20px;line-height:20px;margin:0 5px;min-width:10px;padding:0 5px}.breadcrumb{padding:.75rem 0}.breadcrumb svg{height:.875rem;width:auto;top:3px}.breadcrumb .breadcrumb-placeholder{margin:0 .5rem}.breadcrumb a{color:rgb(0,0,0);font-size:.875rem}.breadcrumb a.is-active{color:rgb(132,25,111);font-weight:bolder}.btn{font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}</style>
{% endblock %}


