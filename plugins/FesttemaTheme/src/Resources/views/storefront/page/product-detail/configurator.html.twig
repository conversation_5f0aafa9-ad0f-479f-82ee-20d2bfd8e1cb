{% sw_extends '@Storefront/storefront/page/product-detail/configurator.html.twig' %}

{% block page_product_detail_configurator_group_title %}
    <div class="product-detail-configurator-group-title">
        {% block page_product_detail_configurator_group_title_text %}
            {# {{ group.translated.name }} #}
        {% endblock %}
    </div>
{% endblock %}

{% block page_product_detail_configurator_option_radio_label %}
    <label class="product-detail-configurator-option-label{% if isCombinableCls %} {{ isCombinableCls }}{% else %} option-strikethrough {% endif %} is-display-{{ displayType }}"
            {% if displayType == 'color' and option.colorHexCode %}
            style="background-color: {{ option.colorHexCode }}"
            {% endif %}
            title="{{ option.translated.name }}"
            for="{{ optionIdentifier }}">

        {% if displayType == 'media' and media %}
            {% block page_product_detail_configurator_option_radio_label_media %}
                {% sw_thumbnails 'configurator-option-img-thumbnails' with {
                    media: media,
                    sizes: {
                        'default': '52px'
                    },
                    attributes: {
                        'class': 'product-detail-configurator-option-image',
                        'alt': option.translated.name,
                        'title': option.translated.name
                    }
                } %}
            {% endblock %}
        {% elseif displayType == 'text' or
                    (displayType == 'media' and not media) or
                    (displayType == 'color' and not option.colorHexCode) %}
            {% block page_product_detail_configurator_option_radio_label_text %}
                {{ option.translated.name }}
            {% endblock %}
        {% endif %}
    </label>
{% endblock %}