{% block bs_smiffy_manufacturer_stock %}
    <div class="alert {% if localStock > 0 %}alert-success{% else %}alert-danger{% endif %} alert-has-icon">
        {% sw_icon 'clock' %}
        <div class="alert-content-container">
            <div class="alert-content">
                <b>
                    {{ "bs-smiffy-stock.bs-stock-local"|trans({'%stock%': localStock| bs_format_stock})|sw_sanitize }}
                </b>
            </div>
        </div>
    </div>

    <div class="alert alert-warning alert-has-icon">
        {% sw_icon 'clock' %}
        <div class="alert-content-container">
            <div class="alert-content">
                <b>{{ "bs-smiffy-stock.bs-stock-manufacturer"|trans({'%stock%': manufacturerStock| bs_format_stock})|sw_sanitize }}</b>
            </div>
        </div>
    </div>
{% endblock %}