{% sw_extends '@Storefront/storefront/utilities/offcanvas.html.twig' %}

{% block utilities_offcanvas_content %}
    {% block page_product_detail_description_container %}
        <div class="product-detail-description tab-pane-container">

            {% block page_product_detail_description_content %}
                {% block page_product_detail_description_content_text %}
                    <div class="product-detail-description-text"
                         itemprop="description">
                        {{ page.product.translated.description|raw }}
                    </div>
                {% endblock %}
            {% endblock %}
        </div>
    {% endblock %}

    {% block clerk_io_custom_block %}
        <span class="clerk clerk1" data-template="@product-page-alternatives" data-products='["{{ page.product.id }}"]'></span>
        <span class="clerk clerk2" data-exclude-from=".clerk1" data-template="@product-page-others-also-bought" data-products='["{{ page.product.id }}"]'></span>
    {% endblock %}
{% endblock %}
