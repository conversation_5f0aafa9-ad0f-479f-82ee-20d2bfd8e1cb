{% sw_extends '@Storefront/storefront/page/product-detail/properties.html.twig' %}

{% block page_product_detail_properties_inner %}
    <div class="product-detail-properties  tab-pane-container">
        {% block page_product_detail_properties_container %}
            <div class="row product-detail-properties-container">
                <div class="col-md-10 col-lg-6">
                    {% block page_product_detail_properties_table %}
                        <table class="table table-striped product-detail-properties-table">
                            <tbody>
                            <tr class="properties-row">
                                <th class="properties-label">{{ "festtemaTheme.productProperties.itemnumber"|trans|sw_sanitize }}:</th>
                                <td class="properties-value">{{ page.product.productNumber }}</td>
                            </tr>
                            {% if page.product.ean %}
                                <tr class="properties-row">
                                    <th class="properties-label">{{ "festtemaTheme.productProperties.ean"|trans|sw_sanitize }}:</th>
                                    <td class="properties-value">{{ page.product.ean }}</td>
                                </tr>
                            {% endif %}
                            {% if page.product.manufacturer %}
                                <tr class="properties-row d-none">
                                    <th class="properties-label">{{ "festtemaTheme.productProperties.manufacturer"|trans|sw_sanitize }}:</th>
                                    <td class="properties-value">
                                        <a href="{{ page.product.manufacturer.link }}"
                                            class="product-detail-manufacturer-link"
                                            rel="noreferrer noopener"
                                            target="_blank"
                                            title="{{ page.product.manufacturer.translated.name }}">
                                                {% if page.product.manufacturer.media %}
                                                    {% block page_product_detail_manufacturer_logo_fs %}
                                                        <img src="{{ page.product.manufacturer.media|sw_encode_media_url }}"
                                                            class="product-detail-manufacturer-logo"
                                                            alt="{{ page.product.manufacturer.translated.name }}"/>
                                                    {% endblock %}
                                                {% else %}
                                                    {% block page_product_detail_manufacturer_text_fs %}
                                                        {{ page.product.manufacturer.translated.name }}
                                                    {% endblock %}
                                                {% endif %}
                                            </a>
                                    </td>
                                </tr>
                            {% endif %}
                            {% for group in page.product.sortedProperties %}
                                {% block page_product_detail_properties_table_row %}
                                    {{ parent() }}
                                {% endblock %}
                            {% endfor %}
                            {% if page.product.customFields.migration_attribute_23_c2c_contents_265 is not empty %}
                                <tr class="properties-row">
                                    <th class="properties-label">{{ "festtemaTheme.customField.indholderLabel"|trans|sw_sanitize }}</th>
                                    <td class="properties-value">
                                        {{ page.product.customFields.migration_attribute_23_c2c_contents_265|sw_sanitize }}
                                    </td>
                                </tr>
                            {% endif %}
                            </tbody>
                        </table>
                    {% endblock %}
                </div>
            </div>
        {% endblock %}
    </div>
{% endblock %}
