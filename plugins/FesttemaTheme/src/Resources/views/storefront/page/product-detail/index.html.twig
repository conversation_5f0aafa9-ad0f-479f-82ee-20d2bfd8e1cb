{% sw_extends '@Storefront/storefront/page/product-detail/index.html.twig' %}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/product-detail/meta.html.twig' %}
{% endblock %}

{% block page_product_detail_inner %}
    <div class="main-width">
        <div class="row products-inner">
            {% block page_product_detail_content %}
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-7">
                    {% set mediaItems = page.product.media.media %}
                    {% sw_include '@Storefront/storefront/element/cms-element-image-gallery.html.twig' with {
                        'mediaItems': mediaItems,
                        'zoom': false,
                        'zoomModal': true,
                        'displayMode': 'contain',
                        'gutter': 5,
                        'minHeight': '430px',
                        'navigationArrows': 'inside',
                        'navigationDots': 'inside',
                        'galleryPosition': 'left',
                        'isProduct': true,
                        'fallbackImageTitle': page.product.translated.name,
                        'startIndexThumbnails': 1,
                        'startIndexSlider': 1,
                        'keepAspectRatioOnZoom': false,
                        'festtemaslider': true
                    } %}
                    {% if config('core.cart.wishlistEnabled') %}
                        <div class="fs-wishlist-mobile mobile-view">
                            {% block page_product_detail_wishlist %}
                                {% sw_include '@Storefront/storefront/component/product/card/wishlist.html.twig' with {
                                    showText: false,
                                    size: 'lg',
                                    productId: page.product.id
                                } %}
                            {% endblock %}
                        </div>
                    {% endif %}
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-5 media-discription">
                    {% block page_product_detail_headline %}
                        {% sw_include '@Storefront/storefront/page/product-detail/headline.html.twig' %}
                    {% endblock %}

                    {% block page_product_detail_buy %} 
                        {% sw_include '@Storefront/storefront/page/product-detail/buy-widget.html.twig' %}
                    {% endblock %}
                </div>
            {% endblock %}
        </div>
    </div>

    <div class="check-out-new fes-categories new-arrivals">
        <div class="container main-width product-tabs product-detail-tabs">
            {% block page_product_detail_tabs %}
                {% sw_include '@Storefront/storefront/page/product-detail/tabs.html.twig' %}
            {% endblock %}
        </div>
    </div>
    <div class="main-width">
        {% block page_product_detail_cross_selling %}
            {% if page.crossSellings.elements is defined and page.crossSellings.elements|filter(item => item.total > 0)|length > 0 %}
                <div class="product-detail-tabs product-detail-cross-selling">
                    {% sw_include '@Storefront/storefront/page/product-detail/cross-selling/tabs.html.twig' with {
                        crossSellings: page.crossSellings
                    } %}
                </div>
            {% endif %}
        {% endblock %}

        {% block block_product_recommendations %}
        {% endblock %}
    </div>
{% endblock %}   

