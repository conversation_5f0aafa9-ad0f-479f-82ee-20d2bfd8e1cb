{% block page_product_detail_tabs_inner %}
    <div class="card card-tabs">
        {% block page_product_detail_tabs_navigation %}
            <div class="card-header product-detail-tab-navigation">
                {% block page_product_detail_tabs_navigation_container %}
                    <ul class="nav nav-tabs product-detail-tab-navigation-list"
                        id="product-detail-tabs"
                        role="tablist">
                        {% block page_product_detail_tabs_navigation_description %}
                            <li class="nav-item">
                                <a
                                    class="nav-link {% if (ratingSuccess != 1) and (ratingSuccess != -1) %}active{% endif %} product-detail-tab-navigation-link"
                                    id="description-tab"
                                    data-bs-toggle="tab"
                                    {# @deprecated tag:v6.6.0 - Registering plugin on selector "data-offcanvas-tabs" is deprecated. Use "data-off-canvas-tabs" instead #}
                                    {% if feature('v6.6.0.0') %}
                                        data-off-canvas-tabs="true"
                                    {% else %}
                                        data-offcanvas-tabs="true"
                                    {% endif %}
                                    href="#description-tab-pane"
                                    role="tab"
                                    aria-controls="description-tab-pane"
                                    aria-selected="true"
                                >
                                    <span>{{ "detail.tabsDescription"|trans|sw_sanitize }}</span>
                                    <span class="product-detail-tab-navigation-icon">
                                        {% sw_icon 'arrow-medium-right' style {'pack':'solid'} %}
                                    </span>
                                    {% if page.product.translated.description|length > 0 %}
                                        <span class="product-detail-tab-preview">
                                            {{ page.product.translated.description|raw|striptags|sw_sanitize|u.truncate(125, '…') }}
                                            {# truncate always cuts down the length to 125 characters.
                                               So it will only shorten the string if it exceeds 125 chars.
                                               Therefor, only show the button when the length of the text is
                                               greater or equal then 126 characters. #}
                                            {# {% if page.product.translated.description|length >= 126 %}
                                                <span class="product-detail-tab-preview-more">{{ "detail.tabsPreviewMore"|trans|sw_sanitize }}</span>
                                            {% endif %} #}
                                        </span>
                                    {% endif %}
                                </a>
                            </li>
                        {% endblock %}
                        {% block page_product_detail_tabs_navigation_specification %}
                            <li class="nav-item">
                                <a
                                    class="nav-link product-detail-tab-navigation-link"
                                    id="specification-tab"
                                    data-bs-toggle="tab"
                                    data-offcanvas-tabs="true"
                                    href="#specification"
                                    role="tab"
                                    aria-controls="specification"
                                    aria-selected="false"
                                >
                                    {{ "festtemaTheme.productTabs.specification"|trans|sw_sanitize }}
                                    <span class="product-detail-tab-navigation-icon">
                                        {% sw_icon 'arrow-medium-right' style {'pack':'solid'} %}
                                    </span>
                                </a>
                            </li>
                        {% endblock %}
                        {% block page_product_detail_tabs_navigation_review %}
                            {% if config('core.listing.showReview') %}
                                <li class="nav-item">
                                    <a
                                        class="nav-link {% if (ratingSuccess == 1) or (ratingSuccess == -1) %}active{% endif %} product-detail-tab-navigation-link"
                                        id="review-tab"
                                        data-bs-toggle="tab"
                                        data-offcanvas-tabs="false"
                                        href="#review-tab-pane"
                                        role="tab"
                                        aria-controls="review-tab-pane"
                                        aria-selected="true"
                                    >
                                        {{ "detail.tabsReview"|trans|sw_sanitize }}
                                        <span class="product-detail-tab-navigation-icon">
                                            {% sw_icon 'arrow-medium-right' style {'pack':'solid'} %}
                                        </span>
                                    </a>
                                </li>
                            {% endif %}
                        {% endblock %}
                    </ul>
                {% endblock %}
            </div>
        {% endblock %}

        {% block page_product_detail_tabs_content %}
            <div class="product-detail-tabs-content card-body">
                {% block page_product_detail_tabs_content_container %}
                    <div class="tab-content">
                        {% block page_product_detail_tabs_content_description %}
                            <div class="tab-pane fade show {% if (ratingSuccess != 1) and (ratingSuccess != -1) %}active{% endif %}"
                                 id="description-tab-pane"
                                 role="tabpanel"
                                 aria-labelledby="description-tab">
                                {% sw_include '@Storefront/storefront/page/product-detail/description.html.twig' %}
                            </div>
                        {% endblock %}
                        
                        {% block page_product_detail_tabs_content_specification %}
                            <div class="tab-pane fade show"
                                    id="specification"
                                    role="tabpanel"
                                    aria-labelledby="specification-tab">
                                {% sw_include '@Storefront/storefront/page/product-detail/specification.html.twig' %}
                            </div>
                        {% endblock %}

                        {% block page_product_detail_tabs_content_review %}
                            {% if config('core.listing.showReview') %}
                                <div class="tab-pane fade show {% if (ratingSuccess == 1) or (ratingSuccess == -1) %}active{% endif %}"
                                     id="review-tab-pane"
                                     role="tabpanel"
                                     aria-labelledby="review-tab">
                                    {% sw_include '@Storefront/storefront/page/product-detail/review/review.html.twig' with {'reviews': page.reviews} %}
                                </div>
                            {% endif %}
                        {% endblock %}
                    </div>
                {% endblock %}
            </div>
        {% endblock %}
    </div>
{% endblock %}
