{% sw_extends '@Storefront/storefront/page/product-detail/review/review.html.twig' %}

{% block page_product_detail_review_container %}

    {# TODO NEXT-16993 - replace items per list config value #}
    {% set reviewsPerListPage = 10 %}

    {# TODO NEXT-16994 - replace current list page value #}
    {% set currentListPage = 1 %}

    {% set productReviewCount = reviews.totalReviews %}

    {% if productReviewCount > 0 %}
        {% set productAvgRating = reviews.matrix.averageRating|round(2, 'common')  %}
    {% endif %}

    {# TODO NEXT-16994 - replace language flag #}
    {% set foreignReviewsCount = 150 %}

    <div class="product-detail-review tab-pane-container">
        {% if config('REVIEWSio.config.showProductWidget') %}
            <div id="ReviewsWidget"></div>
            <script src="https://widget.reviews.co.uk/polaris/build.js"></script>
            <script>
            var loadReviewsioProductWidget = function() {

                new ReviewsWidget('#ReviewsWidget', {
                //Your REVIEWS.io account ID and widget type:
                store: 'www.festtema.dk',
                widget: 'polaris',
                //Content settings (store_review,product_review,questions). Choose what to display in this widget:
                options: {
                    types: 'product_review,questions',
                    lang: 'en',
                    //Possible layout options: bordered, large and reverse.
                    layout: 'bordered',
                    //How many reviews & questions to show per page?
                    per_page: 15,
                    //Product specific settings. Provide product SKU for which reviews should be displayed:
                    product_review:{
                        //Display product reviews - include multiple product SKUs seperated by Semi-Colons (Main Identifier in your product catalog )
                        sku: '{{page.product.productNumber}}',
                        hide_if_no_results: false,
                        structured_data: {
                            category: "{{page.product.seoCategory.breadcrumb | join(' > ')}}",
                            gtin: "{{page.product.ean}}",
                            url: window.location.href,
                            image: "{{page.product.cover.media.url}}",
                            description: {{page.product.translated.metaDescription | striptags | json_encode | raw}},
                            offers: [{
                            type: "Offer",
                            price: "{{page.product.calculatedPrice.totalPrice}}",
                            priceCurrency: "{{context.currency.isoCode}}",
                            url: window.location.href,
                            availability: "http://schema.org/{% if page.product.available == true %}InStock{% else %}OutOfStock{% endif %}",
                            priceValidUntil: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
                            }],
                            brand: {
                            type: "Brand",
                            name: "{{page.product.manufacturer.name}}",
                            }
                        },
                    },
                    //Questions settings:
                    questions:{
                        hide_if_no_results: false,
                        enable_ask_question: true,
                        show_dates: true,
                        grouping: '{% for keys in page.product.cheapestPriceContainer.value|keys %}{{keys}};{% endfor %}',
                    },

                    //Header settings:
                    header:{
                        enable_summary: true, //Show overall rating & review count
                        enable_ratings: true,
                        enable_attributes: true,
                        enable_header_logo: false,
                        enable_image_gallery: true, //Show photo & video gallery
                        enable_percent_recommended: false, //Show what percentage of reviewers recommend it
                        enable_write_review: {% if config('REVIEWSio.config.disableWriteReview') is not empty %}false{% else %}true{% endif %}, //Show "Write Review" button
                        enable_ask_question: true, //Show "Ask Question" button
                        enable_sub_header: true, //Show subheader
                    },

                    //Filtering settings:
                    filtering:{
                        enable: true, //Show filtering options
                        enable_text_search: true, //Show search field
                        enable_sorting: true, //Show sorting options (most recent, most popular)
                        enable_overall_rating_filter: true, //Show overall rating breakdown filter
                        enable_ratings_filters: true, //Show product attributes filter
                        enable_attributes_filters: true, //Show author attributes filter
                    },

                    //Review settings:
                    reviews:{
                        enable_avatar: true, //Show author avatar
                        enable_reviewer_name:  true, //Show author name
                        enable_reviewer_address:  true, //Show author location
                        reviewer_address_format: 'city, country', //Author location display format
                        enable_verified_badge: true, //Show "Verified Customer" badge
                        enable_reviewer_recommends: true, //Show "I recommend it" badge
                        enable_attributes: true, //Show author attributes
                        enable_product_name: false, //Show display product name
                        enable_review_title: undefined, //Show review title
                        enable_replies: {% if config('REVIEWSio.config.enableReplies') is not empty %}true{% else %}false{% endif %}, //Show review replies
                        enable_images: true, //Show display review photos
                        enable_ratings: true, //Show product attributes (additional ratings)
                        enable_share: true, //Show share buttons
                        enable_helpful_vote: true, //Show "was this helpful?" section
                        enable_helpful_display: true, //Show how many times times review upvoted
                        enable_report: true, //Show report button
                        enable_date: true, //Show when review was published
                    },
                },
                //Style settings:
                {% if config('REVIEWSio.config.productReviewsWidgetStyles') is not empty %}
                    {{ config('REVIEWSio.config.productReviewsWidgetStyles') | raw }}
                {% else %}
                    styles: {
                        //Base font size is a reference size for all text elements. When base value gets changed, all TextHeading and TexBody elements get proportionally adjusted.
                        '--base-font-size': '16px',

                        //Button styles (shared between buttons):
                        '--common-button-font-family': 'inherit',
                        '--common-button-font-size':'16px',
                        '--common-button-font-weight':'500',
                        '--common-button-letter-spacing':'0',
                        '--common-button-text-transform':'none',
                        '--common-button-vertical-padding':'10px',
                        '--common-button-horizontal-padding':'20px',
                        '--common-button-border-width':'2px',
                        '--common-button-border-radius':'0px',

                        //Primary button styles:
                        '--primary-button-bg-color': '#0E1311',
                        '--primary-button-border-color': '#0E1311',
                        '--primary-button-text-color': '#ffffff',

                        //Secondary button styles:
                        '--secondary-button-bg-color': 'transparent',
                        '--secondary-button-border-color': '#0E1311',
                        '--secondary-button-text-color': '#0E1311',

                        //Star styles:
                        '--common-star-color': '{% if config('REVIEWSio.config.starColour') is empty %}#f47e27{% else %}{{config('REVIEWSio.config.starColour')}}{% endif %}',
                        '--common-star-disabled-color': 'rgba(0,0,0,0.25)',
                        '--medium-star-size': '22px',
                        '--small-star-size': '19px',

                        //Heading styles:
                        '--heading-text-color': '#0E1311',
                        '--heading-text-font-weight': '600',
                        '--heading-text-font-family': 'inherit',
                        '--heading-text-line-height': '1.4',
                        '--heading-text-letter-spacing': '0',
                        '--heading-text-transform': 'none',

                        //Body text styles:
                        '--body-text-color': '#0E1311',
                        '--body-text-font-weight': '400',
                        '--body-text-font-family': 'inherit',
                        '--body-text-line-height': '1.4',
                        '--body-text-letter-spacing': '0',
                        '--body-text-transform': 'none',

                        //Input field styles:
                        '--inputfield-text-font-family': 'inherit',
                        '--input-text-font-size': '14px',
                        '--inputfield-text-font-weight': '400',
                        '--inputfield-text-color': '#0E1311',
                        '--inputfield-border-color': 'rgba(0,0,0,0.2)',
                        '--inputfield-background-color': 'transparent',
                        '--inputfield-border-width': '1px',
                        '--inputfield-border-radius': '0px',

                        '--common-border-color': 'rgba(0,0,0,0.15)',
                        '--common-border-width': '1px',
                        '--common-sidebar-width': '190px',

                        //Slider indicator (for attributes) styles:
                        '--slider-indicator-bg-color': 'rgba(0,0,0,0.1)',
                        '--slider-indicator-button-color': '#0E1311',
                        '--slider-indicator-width': '190px',

                        //Badge styles:
                        '--badge-icon-color': '#0E1311',
                        '--badge-icon-font-size': 'inherit',
                        '--badge-text-color': '#0E1311',
                        '--badge-text-font-size': 'inherit',
                        '--badge-text-letter-spacing': 'inherit',
                        '--badge-text-transform': 'inherit',

                        //Author styles:
                        '--author-font-size': 'inherit',
                        '--author-text-transform': 'none',

                        //Author avatar styles:
                        '--avatar-thumbnail-size': '60px',
                        '--avatar-thumbnail-border-radius': '100px',
                        '--avatar-thumbnail-text-color': '#0E1311',
                        '--avatar-thumbnail-bg-color': 'rgba(0,0,0,0.1)',

                        //Product photo or review photo styles:
                        '--photo-video-thumbnail-size': '80px',
                        '--photo-video-thumbnail-border-radius': '0px',

                        //Media (photo & video) slider styles:
                        '--mediaslider-scroll-button-icon-color': '#0E1311',
                        '--mediaslider-scroll-button-bg-color': 'rgba(255, 255, 255, 0.85)',
                        '--mediaslider-overlay-text-color': '#ffffff',
                        '--mediaslider-overlay-bg-color': 'rgba(0, 0, 0, 0.8))',
                        '--mediaslider-item-size': '110px',

                        //Pagination & tabs styles (normal):
                        '--pagination-tab-text-color': '#0E1311',
                        '--pagination-tab-text-transform': 'none',
                        '--pagination-tab-text-letter-spacing': '0',
                        '--pagination-tab-text-font-size': '16px',
                        '--pagination-tab-text-font-weight': '600',

                        //Pagination & tabs styles (active):
                        '--pagination-tab-active-text-color': '#0E1311',
                        '--pagination-tab-active-text-font-weight': '600',
                        '--pagination-tab-active-border-color': '#0E1311',
                        '--pagination-tab-border-width': '3px',
                    },
                {% endif %}
                });

                snippet = document.querySelector(".ruk_rating_snippet");
                polaris = document.querySelector("#ReviewsWidget");
                reviewTab = document.querySelector("#review-tab");
                if(polaris && snippet) {
                    snippet.onclick = function(event) {
                    if(reviewTab) {
                        reviewTab.click();
                    }
                    setTimeout(function() {
                        polaris.scrollIntoView({ block: 'start',  behavior: 'smooth' });
                    }, 500);
                    }
                }
            }
            </script>

    {% else %}
        {% block page_product_detail_review_tab_pane %}
            <div class="row product-detail-review-content js-review-container">
                {% block page_product_detail_review_aside %}
                    <div class="col-sm-4">
                        {% block page_product_detail_review_widget_container %}
                            {% sw_include '@Storefront/storefront/page/product-detail/review/review-widget.html.twig' %}
                        {% endblock %}
                    </div>
                {% endblock %}

                {% block page_product_detail_review_main %}
                    <div class="col product-detail-review-main js-review-content">
                        {% block page_product_detail_review_alert %}
                            {% if ratingSuccess == 1 %}
                                {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                                    type: "success",
                                    content: "detail.reviewFormSuccessAlert"|trans|sw_sanitize
                                } %}
                            {% elseif ratingSuccess == 2 %}
                                {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                                    type: "success",
                                    content: "detail.reviewFormSuccessUpdateAlert"|trans|sw_sanitize
                                } %}
                            {% elseif ratingSuccess == -1 %}
                                {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                                    type: "danger",
                                    content: "detail.reviewFormErrorAlert"|trans|sw_sanitize
                                } %}
                            {% endif %}
                        {% endblock %}

                        {% block page_product_detail_review_form_container %}
                            <div class="collapse multi-collapse{% if ratingSuccess == -1 %} show{% endif %}"
                                    id="review-form">
                                {% if context.customer and not context.customer.guest %}
                                    <div class="product-detail-review-form">
                                        {% sw_include '@Storefront/storefront/page/product-detail/review/review-form.html.twig' %}
                                    </div>
                                {% else %}
                                    <div class="product-detail-review-login">
                                        {% sw_include '@Storefront/storefront/page/product-detail/review/review-login.html.twig' %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endblock %}

                        {% block page_product_detail_review_list_container %}
                            <div id="review-list"
                                    class="collapse multi-collapse product-detail-review-list{% if ratingSuccess != -1 %} show{% endif %}">

                                {% block page_product_detail_review_list %}
                                    {% block page_product_detail_review_list_actions %}
                                        <div class="row align-items-center product-detail-review-actions">
                                            {% set formAjaxSubmitOptions = {
                                                replaceSelectors: [".js-review-container"],
                                                submitOnChange: true
                                            } %}

                                            {% block page_product_detail_review_list_action_language %}
                                                <div class="col product-detail-review-language">
                                                    {% if foreignReviewsCount > 0 %}
                                                        <form class="product-detail-review-language-form"
                                                                action="{{ path('frontend.product.reviews', { productId: reviews.productId, parentId: reviews.parentId }) }}"
                                                                method="post"
                                                                data-form-ajax-submit="true"
                                                                data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>

                                                            {% if app.request.get('limit') %}
                                                                <input type="hidden" name="limit" value="{{ app.request.get('limit') }}">
                                                            {% endif %}

                                                            {% if app.request.get('sort') %}
                                                                <input type="hidden" name="sort" value="{{ app.request.get('sort') }}">
                                                            {% endif %}

                                                            <div class="form-check form-switch">
                                                                {# TODO NEXT-16994 - set checked and disabled state #}
                                                                <input type="checkbox"
                                                                        class="form-check-input"
                                                                        id="showForeignReviews"
                                                                        value="filter-language"
                                                                        name="language"
                                                                        {% if app.request.get('language') %}checked="checked"{% endif %}>
                                                                <label class="custom-control-label form-label"
                                                                        for="showForeignReviews">
                                                                    <small>{{ "detail.reviewLanguageFilterLabel"|trans|sw_sanitize }}</small>
                                                                </label>
                                                            </div>
                                                        </form>
                                                    {% endif %}
                                                </div>
                                            {% endblock %}

                                            {% block page_product_detail_review_list_action_sortby %}
                                                {% if productReviewCount > 0 %}
                                                    <div class="col-12 col-md-auto product-detail-review-sortby">
                                                        {% set formAjaxSubmitOptions = {
                                                            replaceSelectors: [
                                                                ".js-review-info",
                                                                ".js-review-teaser",
                                                                ".js-review-content"
                                                            ],
                                                            submitOnChange: true
                                                        } %}

                                                        {% block page_product_detail_review_list_action_sortby_form %}
                                                            <form class="form-inline product-detail-review-sortby-form"
                                                                    action="{{ path('frontend.product.reviews', { productId: reviews.productId, parentId: reviews.parentId }) }}"
                                                                    method="post"
                                                                    data-form-ajax-submit="true"
                                                                    data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>
                                                                {% if app.request.get('limit') %}
                                                                    <input type="hidden" name="limit" value="{{ app.request.get('limit') }}">
                                                                {% endif %}

                                                                {% if app.request.get('language') %}
                                                                    <input type="hidden" name="language" value="{{ app.request.get('language') }}">
                                                                {% endif %}

                                                                {% if app.request.get('points') %}
                                                                    {% for points in app.request.get('points') %}
                                                                        <input type="hidden" name="points[]" value="{{ points }}">
                                                                    {% endfor %}
                                                                {% endif %}

                                                                {% block page_product_detail_review_list_action_sortby_label %}
                                                                    <label class="form-label product-detail-review-sortby-label"
                                                                            for="reviewsSortby">
                                                                        <small>{{ "detail.reviewSortLabel"|trans|sw_sanitize }}</small>
                                                                    </label>
                                                                {% endblock %}

                                                                {% block page_product_detail_review_list_action_sortby_select %}
                                                                    <select class="form-select form-select-sm"
                                                                            name="sort"
                                                                            id="reviewsSortby">
                                                                        <option value="createdAt"{% if reviews.criteria.sorting.0.field == 'createdAt' %} selected{% endif %}>
                                                                            {{ "detail.reviewSortNewLabel"|trans|sw_sanitize }}
                                                                        </option>
                                                                        <option value="points"{% if reviews.criteria.sorting.0.field == 'points' %} selected{% endif %}>
                                                                            {{ "detail.reviewSortTopRatedLabel"|trans|sw_sanitize }}
                                                                        </option>
                                                                    </select>
                                                                {% endblock %}
                                                            </form>
                                                        {% endblock %}
                                                    </div>
                                                {% endif %}
                                            {% endblock %}
                                        </div>

                                        <hr/>

                                        {# TODO NEXT-16994 - calculate reviews in current language in list #}
                                        {% set listReviewsCount = productReviewCount - foreignReviewsCount %}
                                        {# TODO NEXT-16994 - fix if reviews in foreign language are more than in customer language #}
                                        {% if listReviewsCount < 0 %}
                                            {% set listReviewsCount = 0 %}
                                        {% endif %}

                                        <p class="text-right product-detail-review-counter">
                                            {% if (listReviewsCount > 1 and listReviewsCount > reviewsPerListPage) %}
                                                <small><strong>{{ currentListPage }}</strong> - <strong>{{ reviewsPerListPage }}</strong> {{ "detail.reviewCountBefore"|trans|sw_sanitize }} <strong>{{ listReviewsCount }}</strong> {{ "detail.reviewCountAfter"|trans({'%count%': listReviewsCount })|sw_sanitize }}</small>
                                            {% elseif listReviewsCount > 0 %} {# TODO (NEXT-16994) fix detail.reviewCountAfter snippet for listReviewsCount = 0 #}
                                                <small><strong>{{ listReviewsCount }}</strong> {{ "detail.reviewCountAfter"|trans({'%count%': listReviewsCount })|sw_sanitize }}</small>
                                            {% endif %}
                                        </p>
                                    {% endblock %}

                                    {% block page_product_detail_review_list_content %}
                                        {% for review in reviews %}
                                            <div class="product-detail-review-list-content">
                                                {% sw_include '@Storefront/storefront/page/product-detail/review/review-item.html.twig' %}
                                            </div>
                                        {% endfor %}
                                    {% endblock %}

                                    {% block page_product_detail_review_list_paging %}
                                        {% set criteria = reviews.criteria %}
                                        {% set totalPages = (productReviewCount/criteria.limit)|round(0,'ceil') %}

                                        {% if totalPages > 1 %}
                                            {% set formAjaxSubmitOptions = {
                                                replaceSelectors: ".js-review-container",
                                                submitOnChange: true
                                            } %}

                                            {% block page_product_detail_review_list_paging_form %}
                                                <div class="product-detail-review-pagination">
                                                    <form class="product-detail-review-pagination-form"
                                                            action="{{ path('frontend.product.reviews', { productId: reviews.productId, parentId: reviews.parentId }) }}"
                                                            method="post"
                                                            data-form-ajax-submit="true"
                                                            data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>
                                                        {% if app.request.get('limit') %}
                                                            <input type="hidden" name="limit" value="{{ app.request.get('limit') }}">
                                                        {% endif %}

                                                        {% if app.request.get('language') %}
                                                            <input type="hidden" name="language" value="{{ app.request.get('language') }}">
                                                        {% endif %}

                                                        {% if app.request.get('sort') %}
                                                            <input type="hidden" name="sort" value="{{ app.request.get('sort') }}">
                                                        {% endif %}

                                                        {% if app.request.get('points') %}
                                                            {% for points in app.request.get('points') %}
                                                                <input type="hidden" name="points[]" value="{{ points }}">
                                                            {% endfor %}
                                                        {% endif %}

                                                        {% sw_include '@Storefront/storefront/component/pagination.html.twig' with {
                                                            entities: reviews,
                                                            criteria: criteria,
                                                            total: productReviewCount,
                                                        } %}
                                                    </form>
                                                </div>
                                            {% endblock %}
                                        {% endif %}
                                    {% endblock %}
                                {% endblock %}

                                {% if productReviewCount <= 0 %}
                                    {% block page_product_detail_review_list_empty %}
                                        {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                                            type: "info",
                                            content: "detail.reviewListEmpty"|trans|sw_sanitize
                                        } %}
                                    {% endblock %}
                                {% endif %}
                            </div>
                        {% endblock %}
                    </div>
                {% endblock %}
            </div>
        {% endblock %}
    {% endif %}
    </div>
{% endblock %}
