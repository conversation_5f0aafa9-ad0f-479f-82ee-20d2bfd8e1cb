{% sw_extends '@Storefront/storefront/page/checkout/_page.html.twig' %}

{% block page_checkout_opc_step_1_container %}
    <div class="checkout-main">
        <div class="{% if not premsOnePageCheckout.useBorders %}border{% endif %} step-container">
            {% block page_checkout_opc_headline_step1 %}
                <h2 class="checkout-step-headline"><span class="step-icon">1</span> {{ "prems-one-page-checkout.steps.step1-headline"|trans }}</h2>
            {% endblock %}
            {% block page_checkout_main_content %}{% endblock %}
        </div>
        {% if theme_config('sw-topbar-emarket') %}
            <div class="e-market-checkout-desktop desktop-view">
                <a href="{{ "festtemaTheme.header.eMarketLink"|trans|sw_sanitize }}" target="_new">
                    <img src="{{ asset('bundles/festtematheme/images/Reviews-mobile-version.png', 'asset') }}" alt="e-market" class="img-fluid">
                </a>
            </div>
            <div class="e-market-checkout-mobile mobile-view">
                <a href="{{ "festtemaTheme.header.eMarketLink"|trans|sw_sanitize }}" target="_new">
                    <img src="{{ asset('bundles/festtematheme/images/Reviews-mobile-version.png', 'asset') }}" alt="e-market" class="img-fluid">
                </a>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block page_checkout_aside_container %}
    {{ parent() }}
    {% if page.hasExtension(constant('Ngs\\AchieveFreeShipping\\Struct\\PluginConfig::PLUGIN_CONFIG_EXTENSION_NAME')) %}
        {% set NgsAchieveFreeShippingPluginConfig = page.getExtension(constant('Ngs\\AchieveFreeShipping\\Struct\\PluginConfig::PLUGIN_CONFIG_EXTENSION_NAME')) %}
        {% sw_include '@Storefront/storefront/component/achieve-free-shipping/free-delivery-box-checkout.html.twig' with {
            freeDeliveryPrices: NgsAchieveFreeShippingPluginConfig.freeDeliveryPrices,
            shippingMethod: page.cart.deliveries.elements[0].shippingMethod
        } %}
    {% endif %}

    {% block checkout_offcanvas_product_recomendations %}
    {% endblock %}
{% endblock %}