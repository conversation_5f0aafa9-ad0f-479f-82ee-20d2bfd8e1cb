{% sw_extends '@Storefront/storefront/page/checkout/summary/summary-position.html.twig' %}

{% block page_checkout_summary_position_value %}
    {% set surchargeAmount = 0 %}
    {% for lineItem in summary.lineItems %}
        {% if lineItem.type === 'BS_SURCHARGE' || lineItem.type === 'BS_PAYMENT_SURCHARGE' %}
                {% set surchargeAmount = surchargeAmount + lineItem.price.totalPrice %}
        {% endif %}
    {% endfor %}
    <dd class="col-5 checkout-aside-summary-value">
        {% set positionPrice = summary.price.positionPrice - surchargeAmount %}
        {{ positionPrice|currency }}
    </dd>
{% endblock %}
