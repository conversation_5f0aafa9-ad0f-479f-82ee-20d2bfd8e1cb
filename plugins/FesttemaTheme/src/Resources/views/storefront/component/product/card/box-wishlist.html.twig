{% sw_extends '@Storefront/storefront/component/product/card/box-wishlist.html.twig' %}

{% block component_product_box_content %}
    <div class="box-fes-bg">
        <div class="hovereffect">
            <a href="{{ seoUrl('frontend.detail.page', {'productId': id}) }}"  title="{{ name }}">
                {% if cover.url %}
                    {% set attributes = {
                        'class': 'product-image is-'~displayMode,
                        'alt': (cover.translated.alt ?: name),
                        'title': (cover.translated.title ?: name)
                    } %}

                    {% if displayMode == 'cover' or displayMode == 'contain' %}
                        {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
                    {% endif %}

                    {% sw_thumbnails 'product-image-thumbnails' with {
                        media: cover
                    } %}
                {% else %}
                    <div class="product-image-placeholder">
                        {% sw_icon 'placeholder' style {
                            'size': 'fluid'
                        } %}
                    </div>
                {% endif %}
            </a>
            <div class="overlay">
                <div class="icon-links">
                    {% if config('FlinkQuickView') %}
                        <button class="bg-unset-icon flink-quickview-btn flink-quickview-btn__"
                                data-toggle="modal"
                                data-url="{{ path('widgets.quickview.minimal', { 'productId': id }) }}"
                                data-modal-class="quickview">
                            <i class="bi bi-box-arrow-up-right"></i></a>
                        </button>
                    {% endif %}
                    {% if config('core.cart.wishlistEnabled') %}
                        {% block component_product_box_wishlist_action %}
                            {% sw_include '@Storefront/storefront/component/product/card/wishlist.html.twig' with {
                                productId: id
                            } %}
                        {% endblock %}
                    {% endif %}
                    {% block component_product_box_action %}
                        {% sw_include '@Storefront/storefront/component/product/card/action.html.twig' %}
                    {% endblock %}
                </div>
            </div>
        </div>
    </div>
    <div class="categ-in-title text-center">
        <h3>
            <a href="{{ seoUrl('frontend.detail.page', {'productId': id}) }}"
                        class="product-name"
                        title="{{ name }}">{{ name }}</a>
        </h3>
        {% block component_product_box_price %}
            {% sw_include '@Storefront/storefront/component/product/card/price-unit.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}