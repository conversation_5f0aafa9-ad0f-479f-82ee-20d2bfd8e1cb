{% sw_extends '@Storefront/storefront/component/product/card/wishlist.html.twig' %}

{% block component_product_wishlist %}
    {% set addToWishlistOptions = {
        productId: productId,
        router: {
            add: {
                afterLoginPath: path('frontend.wishlist.add.after.login', { productId: productId }),
                path: path('frontend.wishlist.product.add', { productId: productId }),
                
            },
            remove: {
                path: path('frontend.wishlist.product.remove', { productId: productId }),
                
            }
        }
    } %}

    {% set size = size ?? 'md' %}

        {% block component_product_wishlist_button %}
            <button
                class="bg-unset-icon product-wishlist-{{ productId }} product-wishlist-action{% if appearance == 'circle' %}-circle{% endif %} product-wishlist-not-added product-wishlist-loading festtema-wishlist"
                title="{{ "listing.toggleWishlist"|trans|sw_sanitize }}"
                data-add-to-wishlist="true"
                data-add-to-wishlist-options="{{ addToWishlistOptions|json_encode }}"
            >
                {% block component_product_wishlist_icon %}
                    {% if not hideText %}
                        <i class="bi bi-suit-heart-fill icon-wishlist icon-wishlist-added"></i>
                        <i class="bi bi-suit-heart icon-wishlist icon-wishlist-not-added"></i>
                    {% endif %}
                    {% if showText %}
                        <span class="product-wishlist-btn-content text-wishlist-not-added product-wishlist-btn-content-{{ size }}">
                            {{ "listing.addToWishlist"|trans|sw_sanitize }}
                        </span>
                        <span class="product-wishlist-btn-content text-wishlist-remove product-wishlist-btn-content-{{ size }}">
                            {{ "listing.removeFromWishlist"|trans|sw_sanitize }}
                        </span>

                    {% endif %}
                {% endblock %}
            </button>
        {% endblock %}
    
{% endblock %}
