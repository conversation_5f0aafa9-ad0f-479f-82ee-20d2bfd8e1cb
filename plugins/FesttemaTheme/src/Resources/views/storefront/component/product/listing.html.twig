{% sw_extends '@Storefront/storefront/component/product/listing.html.twig' %}

{% block element_product_listing_row %}
    {% set currentCat = page.header.navigation.active %}
    {% set blockContent = currentCat.customFields.festtema_product_content %}
    {% set blockEnable = currentCat.customFields.festtema_product_content_onoff %}
    {% set blockBgColor = (currentCat.customFields.festtema_product_content_bg_color is not empty)? currentCat.customFields.festtema_product_content_bg_color: '#84196f' %}
    {% set globalBlockContent = context.salesChannel.customFields.custom_global_product_info_content %}
    {% set globalBlockEnable = context.salesChannel.customFields.custom_global_product_info_onoff %}
    {% set globalBlockBgColor = (context.salesChannel.customFields.custom_global_product_info_bgcolor is not empty)? context.salesChannel.customFields.custom_global_product_info_bgcolor: '#84196f' %}
    {% set i = 0 %}
    {% set flag = (listingColumns == 'col-sm-6 col-lg-4 col-xl-3')? 4: 3 %}

    <div class="row cms-listing-row js-listing-wrapper">
        {% if searchResult.total > 0 %}
            {% block element_product_listing_col %}
                {% for product in searchResult %} 
                    {% if searchResult.page == 1 %}
                        {% if (i == flag) and (blockEnable is true) %}
                            {% if (blockContent is not empty) %}
                                <div class="col col-12 product-tip-info-block" style="background: {{ blockBgColor }};">
                                    {{ blockContent | raw }}
                                </div>
                            {% elseif (globalBlockContent is not empty) and (globalBlockEnable is true) %}
                                <div class="col col-12 product-tip-info-block" style="background: {{ globalBlockBgColor }};">
                                    {{ globalBlockContent | raw }}
                                </div>
                            {% endif %}
                        {% endif %}
                    {% endif %}
                    <div class="cms-listing-col col-6 {{ listingColumns }}">
                        {% block element_product_listing_box %}
                            {% sw_include '@Storefront/storefront/component/product/card/box.html.twig' with {
                                'layout': boxLayout,
                                'displayMode': displayMode
                            } %}
                        {% endblock %}
                    </div>
                    {% set i = i + 1 %}
                {% endfor %}
            {% endblock %}
        {% else %}
            {% block element_product_listing_col_empty %}
                <div class="cms-listing-col col-12">
                    {% block element_product_listing_col_empty_alert %}
                        {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                            type: 'info',
                            content: 'listing.emptyResultMessage'|trans|sw_sanitize
                        } %}
                    {% endblock %}
                </div>
            {% endblock %}
        {% endif %}
    </div>
{% endblock %}
{% block element_product_listing_pagination_nav_actions %}
    <div class="cms-element-product-listing-actions row justify-content-between">
        <div class="col-md-auto">
        </div>
        <div class="col-md-auto">
            {% block element_product_listing_sorting %}
                {% sw_include '@Storefront/storefront/component/sorting.html.twig' with {
                    current: searchResult.sorting,
                    sortings: searchResult.availableSortings
                } %}
            {% endblock %}
        </div>
    </div>
{% endblock %}