{% sw_extends '@Storefront/storefront/component/account/register.html.twig' %}


{% block component_account_register_personal_account_fields %}
    <div class="row g-2">
        <div class="form-group col-sm-6">
            {{ formGroupMail }}
        </div>
        {% block component_address_form_phone_number %}
            {# <div class="form-row"> #}
                {% if config('core.loginRegistration.showPhoneNumberField') %}
                    <div class="form-group col-md-6">
                        {% if formViolations.getViolations("/phoneNumber") is not empty %}
                            {% set violationPath = "/phoneNumber" %}
                        {% elseif formViolations.getViolations("/#billingAddress/phoneNumber") is not empty %}
                            {% set violationPath = "/#billingAddress/phoneNumber" %}
                        {% endif %}

                        {% block component_address_form_phone_number_label %}
                            <label class="form-label"
                                for="billingAddressAddressPhoneNumber">
                                {{ "address.phoneNumberLabel"|trans|sw_sanitize }}{{ config('core.loginRegistration.phoneNumberFieldRequired') ? "general.required"|trans|sw_sanitize }}
                            </label>
                        {% endblock %}

                        {% block component_address_form_phone_number_input %}
                            <input type="text"
                                class="form-control"
                                id="billingAddressAddressPhoneNumber"
                                placeholder="{{ "address.phoneNumberPlaceholder"|trans|striptags }}"
                                name="billingAddress[phoneNumber]"
                                value="{{ data.get('phoneNumber') }}"
                                {% if config('BsValidation.config.isMaxLengthPhone') %}
                                    data-numeric-input
                                    minlength="{{ config('BsValidation.config.minLengthPhone') }}"
                                    maxlength="{{ config('BsValidation.config.maxLengthPhone') }}"
                                    data-max-length="{{ config('BsValidation.config.maxLengthPhone') }}"
                                    data-min-length="{{ config('BsValidation.config.minLengthPhone') }}"
                                    data-max-length-message="{{ "BsValidation.maxLengthPhone"|trans({
                                                                '%maxLength%': config('BsValidation.config.maxLengthPhone')
                                                                })|sw_sanitize }}"
                                    data-min-length-message="{{ "BsValidation.minLengthPhone"|trans({
                                                            '%minLength%': config('BsValidation.config.minLengthPhone')
                                                            })|sw_sanitize }}"
                                {% endif %}
                                {{ config('core.loginRegistration.phoneNumberFieldRequired') ? 'required="true"' }}>
                        {% endblock %}

                        {% block component_address_form_phone_error %}
                            {% if violationPath %}
                                {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                            {% endif %}
                        {% endblock %}
                    </div>
                {% endif %}
            {# </div> #}
        {% endblock %}
       
        <div class="form-group col-sm-6">
            {% if config('core.loginRegistration.requireEmailConfirmation') %}
                {{ formGroupMailConfirmation }}
            {% elseif not config('core.loginRegistration.requirePasswordConfirmation') %}
                {{ formGroupPassword }}
            {% endif %}
        </div>
        <div class="form-group col-sm-6">
            {% if config('core.loginRegistration.requireEmailConfirmation') or config('core.loginRegistration.requirePasswordConfirmation') %}
                {{ formGroupPassword }}
            {% endif %}
        </div>
        <div class="form-group col-sm-6">
            {% if config('core.loginRegistration.requirePasswordConfirmation') %}
                {{ formGroupPasswordConfirmation }}
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block component_account_register_submit %}
    <div class="register-submit d-grid col-md-4">
        <button type="submit"
                class="btn btn-primary btn-lg">
            {{ "account.registerSubmit"|trans|sw_sanitize }}
        </button>
    </div>
{% endblock %}

{% block component_account_register_privacy %}
    {% if (app.request.attributes.get('sw-original-request-uri')) == '/account/login'  %}
        {% sw_include '@Storefront/storefront/component/privacy-notice.html.twig' %}
    {% endif %}
{% endblock %}