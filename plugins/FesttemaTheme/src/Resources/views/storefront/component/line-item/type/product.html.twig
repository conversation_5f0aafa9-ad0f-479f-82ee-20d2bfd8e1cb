{% sw_extends '@Storefront/storefront/component/line-item/type/product.html.twig' %}

{% set hideBundleCartAndCheckout = config('ZeobvBundleProducts.config.hideBundleInformationInShoppingCartAndCheckout') %}

{% block component_line_item_type_product %}
    {% if allDiscounts %}
        {{ parent() }}

        {% block page_checkout_item_discounts %}
            {% for item in allDiscounts %}
                {% set lineItem = item[0] %}
                {% block page_checkout_item_discount %}
                    {% if lineItem and lineItem.payload.discountDisplay is same as ("positionBased") %}
                        {% set discountIncludeFile = '@Storefront/storefront/component/line-item/type/discount.html.twig' %}
                        {% block page_checkout_item_discount_include %}
                            {% sw_include discountIncludeFile with {
                                'lineItem': lineItem,
                                'isAcrisDiscountPositionBased': true,
                                'priceDiscount': item.discount
                            } %}
                        {% endblock %}
                    {% endif %}
                {% endblock %}
            {% endfor %}
        {% endblock %}
    {% else %}
        <div class="{{ lineItemClasses }}">
            <div class="row line-item-row">

                {% block component_line_item_type_product_col_info %}
                    {{ parent() }}
                    {% block component_line_item_type_product_order_number %}
                    {%  endblock %}
                    {% block component_line_item_type_product_wishlist %}
                    {% endblock %}
                {% endblock %}

                {% block component_line_item_type_product_col_quantity %}
                    {{ parent() }}
                {% endblock %}

                {% if showTaxPrice %}
                    {% block component_line_item_type_product_col_tax_price %}
                        {{ parent() }}
                    {% endblock %}
                {% else %}
                    {% block component_line_item_type_product_col_unit_price %}
                        {{ parent() }}
                    {% endblock %}
                {% endif %}

                {% block component_line_item_type_product_col_total_price %}
                    {{ parent() }}
                {% endblock %}

                {% if showRemoveButton %}
                    {% block component_line_item_type_product_col_remove %}
                        {{ parent() }}
                    {% endblock %}
                {% endif %}

                {% if hideBundleCartAndCheckout == false %}
                    {% if lineItem.children.count > 0 %}
                        {% block component_line_item_type_container_children %}
                            {% sw_include '@Storefront/storefront/component/line-item/element/children-wrapper.html.twig' %}
                        {% endblock %}
                    {% endif %}
                {% endif %}
            </div>

            {% if displayMode === 'order' %}
                {% block component_line_item_type_product_downloads_table %}
                    {{ parent() }}
                {% endblock %}
            {% endif %}
        </div>
    {% endif %}

{% endblock %}