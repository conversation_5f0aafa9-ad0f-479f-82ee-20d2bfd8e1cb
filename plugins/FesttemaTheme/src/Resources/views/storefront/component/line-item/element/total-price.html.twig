{% sw_extends '@Storefront/storefront/component/line-item/element/total-price.html.twig' %}

{% block component_line_item_total_price_value %}
        <div class="line-item-total-price-value">
            {# Shipping costs discounts always have a price of 0, which might be confusing, therefore we do not show those #}
            {% if lineItem.payload.discountScope != 'delivery' %}
                {{ lineItem.price.totalPrice|currency }}
            {% endif %}

            {% set referencePrice = lineItem.price.referencePrice %}
            {% if referencePrice is not null and displayMode == 'offcanvas' %}
                <br>
                <small class="line-item-reference-price">
                    ({{ referencePrice.price|currency }} / {{ referencePrice.referenceUnit }}&nbsp;{{ referencePrice.unitName }})
                </small>
            {% endif %}
        </div>
    {% endblock %}
