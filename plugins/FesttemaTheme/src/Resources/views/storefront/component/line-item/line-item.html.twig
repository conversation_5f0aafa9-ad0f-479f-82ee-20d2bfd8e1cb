{% sw_extends '@Storefront/storefront/component/line-item/line-item.html.twig' %}

{% block component_line_item %}
    {% set PRODUCT_LINE_ITEM_TYPE = constant('Shopware\\Core\\Checkout\\Cart\\LineItem\\LineItem::PRODUCT_LINE_ITEM_TYPE') %}
    {% set DISCOUNT_LINE_ITEM_TYPE = constant('Shopware\\Core\\Checkout\\Cart\\LineItem\\LineItem::DISCOUNT_LINE_ITEM') %}
    {% set CONTAINER_LINE_ITEM_TYPE = constant('Shopware\\Core\\Checkout\\Cart\\LineItem\\LineItem::CONTAINER_LINE_ITEM') %}
    {% set SWKWEB_PRODUCT_SET_LINE_ITEM_TYPE = constant('Swkweb\\ProductSet\\Core\\Content\\ProductSet\\Cart\\ProductSetCartProcessor::TYPE') %}
    {% set isDiscount = (not lineItem.good and lineItem.price.totalPrice <= 0) || lineItem.type == DISCOUNT_LINE_ITEM_TYPE %}
    {% set isProduct = lineItem.type == PRODUCT_LINE_ITEM_TYPE %}
    {% set isContainer = lineItem.type == CONTAINER_LINE_ITEM_TYPE %}

    {% block component_line_item_type_include %}
        {% set allDiscounts = [] %}

        {% set lineItems = [] %}
        {% if page.cart.lineItems is defined %}
            {% set lineItems = page.cart.lineItems %}
        {% elseif page.order.lineItems is defined %}
            {% set lineItems = page.order.lineItems %}
        {% elseif order.lineItems is defined %}
            {% set lineItems = order.lineItems %}
        {% endif %}

        {% if lineItems %}
            {% for item in lineItems %}
                {% if item.type is same as ("promotion") and item.payload.discountDisplay is same as ("positionBased") %}
                    {% if item.payload.composition is defined %}
                        {% for discount in item.payload.composition %}
                            {% if (lineItem.identifier and discount.id == lineItem.identifier) or discount.id == lineItem.id %}
                                {% set allDiscounts = allDiscounts|merge([discount|merge([item])]) %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% endif %}
        {% if isProduct %}
            {% sw_include '@Storefront/storefront/component/line-item/type/product.html.twig' %}
        {% elseif isDiscount %}
            {% sw_include '@Storefront/storefront/component/line-item/type/discount.html.twig' %}
        {% elseif isContainer %}
            {% sw_include '@Storefront/storefront/component/line-item/type/container.html.twig' %}
        {% elseif lineItem.type == 'bundle_product_item' %}
            {% sw_include '@Storefront/storefront/component/line-item/type/bundle-product.html.twig' %}
        {% elseif lineItem.type == SWKWEB_PRODUCT_SET_LINE_ITEM_TYPE %}
            {% sw_include '@Storefront/storefront/component/swkweb-product-set-configurator/line-item/type/set.html.twig' %}
        {% elseif lineItem.type == 'BS_SURCHARGE' %}
            {% sw_include '@Storefront/storefront/component/line-item/type/bs-surcharge.html.twig' %}
        {% elseif lineItem.type == 'BS_PAYMENT_SURCHARGE' %}
            {% sw_include '@Storefront/storefront/component/line-item/type/bs-payment-surcharge.html.twig' %}
        {% else %}
            {% sw_include '@Storefront/storefront/component/line-item/type/generic.html.twig' %}
        {% endif %}
    {% endblock %}
{% endblock %}
