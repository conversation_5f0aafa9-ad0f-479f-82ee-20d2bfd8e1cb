{% set showManufacturer = config('ZeobvBundleProducts.config.showBundleProductItemManufacturer') %}
{% set showQuantityCartAndCheckout = config('ZeobvBundleProducts.config.showQuantityOfBundleItemsInCartAndCheckout') %}
{% set showProductNumber = config('ZeobvBundleProducts.config.showBundleProductNumber') %}
{% set showItemPrices = mainLineItems.payload.zeobvBundleProductsShowItemPricesOnStorefront %}

{% block component_line_item_type_bundle_product %}
    <div class="{{ lineItemClasses }}">
        <div class="row line-item-row">
            {% block component_line_item_type_bundle_product_col_info %}
                <div class="line-item-info">
                    <div class="row line-item-row">
                        {% if nestingLevel < 1 %}
                            {% block component_line_item_type_bundle_product_image %}
                                <div class="col-auto line-item-info-img">
                                    <div class="line-item-img-container">
                                        {% block component_line_item_type_bundle_product_image_inner %}
                                            {% sw_include '@Storefront/storefront/component/line-item/element/image.html.twig' %}
                                        {% endblock %}
                                    </div>
                                </div>
                            {% endblock %}
                        {% endif %}

                        {% block component_line_item_type_bundle_product_details %}
                            <div class="line-item-details">
                                <div class="line-item-details-container">
                                    {% block component_line_item_type_bundle_product_label %}
                                        {% sw_include '@Storefront/storefront/component/line-item/element/label.html.twig' %}
                                        {% if showManufacturer and lineItem.payload.manufacturer.name %}
                                            <span class="manufacturer-name">{{ lineItem.payload.manufacturer.name }}</span>
                                        {% endif %}
                                    {% endblock %}

                                    {% if lineItem.payload.options is not empty %}
                                        {% block component_line_item_type_bundle_product_variant_characteristics %}
                                            {% sw_include '@Storefront/storefront/component/line-item/element/variant-characteristics.html.twig' %}
                                        {% endblock %}
                                    {% endif %}

                                    {% if lineItem.payload.features is not empty %}
                                        {% block component_line_item_type_bundle_product_features %}
                                            {% sw_include '@Storefront/storefront/component/product/feature/list.html.twig' with {
                                                'features': lineItem.payload.features
                                            } %}
                                        {% endblock %}
                                    {% endif %}

                                    {% if showProductNumber %}
                                        {% if lineItem.payload.productNumber %}
                                            {% block component_line_item_type_bundle_product_order_number %}
                                                <div class="line-item-ordernumber">
                                                    {{ "checkout.cartItemInfoId"|trans|sw_sanitize }} {{ lineItem.payload.productNumber }}
                                                </div>
                                            {% endblock %}
                                        {% endif %}
                                    {% endif %}

                                    {% if config('core.cart.showDeliveryTime') %}
                                        {% block component_line_item_type_bundle_product_delivery_date %}
                                            {% sw_include '@Storefront/storefront/component/line-item/element/delivery-date.html.twig' %}
                                        {% endblock %}
                                    {% endif %}

                                    {% if config('core.cart.wishlistEnabled') %}
                                        {% block component_line_item_type_bundle_product_wishlist %}
                                            {% sw_include '@Storefront/storefront/component/product/card/wishlist.html.twig' with {
                                                showText: true,
                                                size: 'sm',
                                                productId: lineItem.referencedId
                                            } %}
                                        {% endblock %}
                                    {% endif %}
                                </div>
                            </div>
                        {% endblock %}
                    </div>
                </div>
            {% endblock %}

            {% block component_line_item_type_bundle_product_col_quantity %}
                <div class="line-item-quantity">
                {% if showQuantityCartAndCheckout %}
                    {% sw_include '@Storefront/storefront/component/line-item/element/quantity.html.twig' %}
                {% endif %}
                </div>
            {% endblock %}

            {% if showItemPrices %}
                {% if showTaxPrice %}
                    {% block component_line_item_type_bundle_product_col_tax_price %}
                        <div class="line-item-tax-price">
                            {% if not nestedLineItem.type is same as("bundle_product_item") or not nestedLineItem.payload.zeobvHideBundleProductPricesInStorefront %}
                                {% if context.salesChannel.taxCalculationType == 'horizontal' %}
                                    {% sw_include '@Storefront/storefront/component/line-item/element/tax-price.html.twig' %}
                                {% endif %}
                            {% endif %}
                        </div>
                    {% endblock %}
                {% else %}
                    {% block component_line_item_type_bundle_product_col_unit_price %}
                        {% if not nestedLineItem.type is same as("bundle_product_item") or not nestedLineItem.payload.zeobvHideBundleProductPricesInStorefront %}
                            <div class="line-item-unit-price">
                                {% sw_include '@Storefront/storefront/component/line-item/element/unit-price.html.twig' %}
                            </div>
                        {% endif %}
                    {% endblock %}
                {% endif %}

                {% block component_line_item_type_bundle_product_col_total_price %}
                    <div class="line-item-total-price">
                        {% sw_include '@Storefront/storefront/component/line-item/element/total-price.html.twig' %}
                    </div>
                {% endblock %}
            {% endif %}
        </div>
    </div>
{% endblock %}
