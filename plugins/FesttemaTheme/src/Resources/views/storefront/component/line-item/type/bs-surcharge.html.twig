{% set label = lineItem.label|trans|sw_sanitize %}
{% set label = label !== '' ? label : lineItem.label %}

{% if nestingLevel is not defined %}
    {% set nestingLevel = 0 %}
{% endif %}

{% if displayMode is not defined %}
    {% set displayMode = 'default' %}
{% endif %}

{% if showRemoveButton is not defined %}
    {% set showRemoveButton = true %}
{% endif %}

{% set lineItemClasses = 'line-item line-item-' ~ lineItem.type ~ ' is-' ~ displayMode %}

{% if displayMode === 'offcanvas' %}
    {% set lineItemClasses = lineItemClasses ~ ' js-cart-item' %}
{% endif %}

{% if not showRemoveButton %}
    {% set lineItemClasses = lineItemClasses ~ ' no-remove-button' %}
{% endif %}

{% block component_line_item_type_generic %}
    <div class="{{ lineItemClasses }}">
        <div class="row line-item-row">

            {% block component_line_item_type_generic_col_info %}
                <div class="line-item-info">
                    <div class="row line-item-row">

                        {% block component_line_item_type_generic_details %}
                            <div class="line-item-details">
                                <div class="line-item-details-container">
                                    {% block component_line_item_type_generic_label %}
                                        {% sw_include '@Storefront/storefront/component/line-item/element/label.html.twig' %}
                                    {% endblock %}

                                    {% block component_line_item_type_generic_quantity %}
                                        <div class="bs-minimum-qty-surcharge">{{ "bs-surcharge.bs-minimum-order-info"|trans({'%orderValue%': config('BsSurcharge.config.surchargeApplyAmount')|currency})|sw_sanitize }}</div>
                                    {% endblock %}

                                </div>
                            </div>
                        {% endblock %}
                    </div>
                </div>
            {% endblock %}

            {% block component_line_item_type_generic_col_quantity %}
                <div class="line-item-quantity">
                </div>
            {% endblock %}

            {% if showTaxPrice %}
                {% block component_line_item_type_product_col_tax_price %}
                    <div class="line-item-tax-price">
{#                        {% if config('BsSurcharge.config.surchargeShowVat') %}#}
{#                            {% if context.salesChannel.taxCalculationType == 'horizontal' %}#}
{#                                {% sw_include '@Storefront/storefront/component/line-item/element/tax-price.html.twig' %}#}
{#                            {% endif %}#}
{#                        {% endif %}#}
                    </div>
                {% endblock %}
            {% else %}
                {% block component_line_item_type_product_col_unit_price %}
                    <div class="line-item-unit-price{% if lineItem.quantity > 1 %} is-shown{% endif %}">
{#                        {% sw_include '@Storefront/storefront/component/line-item/element/unit-price.html.twig' %}#}
                    </div>
                {% endblock %}
            {% endif %}

            {% block component_line_item_type_generic_col_total_price %}
                <div class="line-item-total-price">
{#                    {% sw_include '@Storefront/storefront/component/line-item/element/total-price.html.twig' %}#}
                </div>
            {% endblock %}

        </div>
    </div>
{% endblock %}
