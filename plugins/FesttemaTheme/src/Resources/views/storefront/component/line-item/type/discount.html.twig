{% sw_extends '@Storefront/storefront/component/line-item/type/discount.html.twig' %}

{% block component_line_item_type_discount %}
    {% if isDiscount is same as (true) and lineItem.payload.discountDisplay is same as ("positionBased") and isAcrisDiscountPositionBased is not defined %}
        {# hide general discount #}
    {% else %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{% block component_line_item_type_discount_col_total_price %}
    {% if isAcrisDiscountPositionBased is same as (true) and priceDiscount %}<div class="line-item-total-price"><div class="line-item-total-price-value">- {{ priceDiscount|currency }}</div></div>{% else %}{{ parent() }}{% endif %}
{% endblock %}