{% sw_extends '@Storefront/storefront/component/address/address-editor-modal-create-address.html.twig' %}

{% block component_address_address_editor_modal_create_address_form_fields_include %}
    {% sw_include '@Storefront/storefront/component/address/address-personal.html.twig' with {
        'data': postedData ?? address,
        'prefix': 'address',
        'idPrefix': typePrefix,
        'scope': 'parent',
        'parentSelector': 'form'
    } %}

    {% sw_include '@Storefront/storefront/component/address/address-form.html.twig' with {
        'data': postedData ?? address,
        'prefix': 'address',
        'idPrefix': typePrefix,
        'scope': 'parent',
        'parentSelector': 'form',
        'showFormCompany': true,
        'showNoShippingPostfix': true,
        'showVatIdField': changeBilling and context.customer and context.customer.guest ? true : false,
        'showPhoneInModal': true
    } %}
{% endblock %}