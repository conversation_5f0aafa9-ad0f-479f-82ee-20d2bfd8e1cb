
{% sw_extends '@Storefront/storefront/component/address/address-form.html.twig' %}

{% block component_address_form_country %}
    <div class="form-group col-md-6">
        {% set initialCountryId = null %}

        {% if data.get('countryId') %}
            {% set initialCountryId = data.get('countryId') %}
        {% elseif page.countries|length >= 1 %}
            {% set initialCountryId = (page.countries|first).id %}
        {% endif %}

        {% if formViolations.getViolations("/countryId") is not empty %}
            {% set violationPath = "/countryId" %}
        {% elseif formViolations.getViolations("/#{prefix}/countryId") is not empty %}
            {% set violationPath = "/#{prefix}/countryId" %}
        {% endif %}

        {% block component_address_form_country_label %}
            {{ parent() }}
        {% endblock %}

        {% block component_address_form_country_select %}
            {{ parent() }}
        {% endblock %}
    </div>

    <div class="form-group col-md-6  d-none">
        {% if formViolations.getViolations("/countryStateId") is not empty %}
            {% set violationPath = "/countryStateId" %}
        {% elseif formViolations.getViolations("/#{prefix}/countryStateId") is not empty %}
            {% set violationPath = "/#{prefix}/countryStateId" %}
        {% endif %}

        {% block component_address_form_country_state_label %}
            {{ parent() }}
        {% endblock %}

        {% block component_address_form_country_state_select %}
            {{ parent() }}
        {% endblock %}

        {% block component_address_form_country_error %}
            {{ parent() }}
        {% endblock %}
    </div>
{% endblock %}

{% block component_address_form_phone_number %}
    {% if prefix == 'shippingAddress' or showPhoneInModal %}
        {% if config('core.loginRegistration.showPhoneNumberField') %}
            <div class="form-group col-md-6">
                {% if formViolations.getViolations("/phoneNumber") is not empty %}
                    {% set violationPath = "/phoneNumber" %}
                {% elseif formViolations.getViolations("/#{prefix}/phoneNumber") is not empty %}
                    {% set violationPath = "/#{prefix}/phoneNumber" %}
                {% endif %}

                {% block component_address_form_phone_number_label %}
                    <label class="form-label"
                            for="{{ idPrefix ~ prefix }}AddressPhoneNumber">
                        {{ "address.phoneNumberLabel"|trans|sw_sanitize }}{{ config('core.loginRegistration.phoneNumberFieldRequired') ? "general.required"|trans|sw_sanitize }}
                    </label>
                {% endblock %}

                {% block component_address_form_phone_number_input %}
                    <input type="text"
                            class="form-control"
                            id="{{ idPrefix ~ prefix }}AddressPhoneNumber"
                            placeholder="{{ "address.phoneNumberPlaceholder"|trans|striptags }}"
                            name="{{ prefix }}[phoneNumber]"
                            value="{{ data.get('phoneNumber') }}"
                            {% if config('BsValidation.config.isMaxLengthPhone') %}
                                data-numeric-input
                                minlength="{{ config('BsValidation.config.minLengthPhone') }}"
                                maxlength="{{ config('BsValidation.config.maxLengthPhone') }}"
                                data-max-length="{{ config('BsValidation.config.maxLengthPhone') }}"
                                data-min-length="{{ config('BsValidation.config.minLengthPhone') }}"
                                data-max-length-message="{{ "BsValidation.maxLengthPhone"|trans({
                                                            '%maxLength%': config('BsValidation.config.maxLengthPhone')
                                                            })|sw_sanitize }}"
                                data-min-length-message="{{ "BsValidation.minLengthPhone"|trans({
                                                            '%minLength%': config('BsValidation.config.minLengthPhone')
                                                            })|sw_sanitize }}"
                            {% endif %}
                            {{ config('core.loginRegistration.phoneNumberFieldRequired') ? 'required="true"' }}>
                {% endblock %}

                {% block component_address_form_phone_error %}
                    {% if violationPath %}
                        {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                    {% endif %}
                {% endblock %}
            </div>
        {% endif %}
    {% endif %}
{% endblock %}