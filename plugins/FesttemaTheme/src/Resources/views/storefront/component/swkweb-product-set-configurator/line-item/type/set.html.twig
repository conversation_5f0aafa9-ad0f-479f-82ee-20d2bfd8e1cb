{% sw_extends '@Storefront/storefront/component/line-item/type/generic.html.twig' %}

{% set setLineItem = lineItem %}
{% set setMainProductLineItem = setLineItem.children.filterType('product').first() ?? setLineItem.children.filterByType('product').first() %}

{% set isShowMainProductAsMainItem = setLineItem.payload['swkweb-product-set-show-main-product-as-main-item'] %}
{% set lineItemLink = seoUrl('frontend.detail.page', {'productId': setMainProductLineItem.referencedId}) ~ '#' ~ lineItem.payload['swkweb-product-set-url-configuration'] %}

{% block component_line_item_type_generic_image_inner %}
    {% sw_include '@Storefront/storefront/component/line-item/element/image.html.twig' with {
        lineItem: setMainProductLineItem,
        lineItemLink: lineItemLink,
    } only %}
{% endblock %}

{% block component_line_item_type_generic_details %}
    {% with {lineItem: setMainProductLineItem} %}
        {{ parent() }}
    {% endwith %}
{% endblock %}

{% block component_line_item_type_generic_label %}
    {% if isShowMainProductAsMainItem %}
        {% set label = setMainProductLineItem.label %}
    {% endif %}

    {% sw_include '@Storefront/storefront/component/line-item/element/label.html.twig' with {
        lineItem: {
            label: label,
        },
        lineItemLink: lineItemLink,
    } only %}

    {% if setMainProductLineItem %}
        {% with {lineItem: setMainProductLineItem, parentQuantity: setLineItem.quantity} %}
            {{ block('swkweb_product_set_cart_item_details_quantity_price', '@SwkwebProductSet/storefront/component/swkweb-product-set-configurator/line-item/element/set-item.html.twig') }}
        {% endwith %}
    {% endif %}
{% endblock %}

{% block component_line_item_type_generic_delivery_date %}
    {% with {lineItem: setLineItem} %}
        {{ parent() }}
    {% endwith %}
{% endblock %}

{% block component_line_item_type_generic_children %}
    {% block swkweb_product_set_cart_item_set_items %}
        {% sw_include '@SwkwebProductSet/storefront/component/swkweb-product-set-configurator/line-item/element/set-items.html.twig' with {
            lineItem: setLineItem,
            parentQuantity: setLineItem.quantity,
        } only %}
    {% endblock %}
{% endblock %}
