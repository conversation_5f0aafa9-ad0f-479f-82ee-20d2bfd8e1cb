{% sw_extends '@SwkwebProductSet/storefront/component/swkweb-product-set-configurator/product-set.html.twig' %}

{% block swkweb_product_set_slots_loop %}
    <div id="swkwebProductSetSlider" class="carousel slide">
        {% block swkweb_product_set_slider_indicators %}
            <ol class="carousel-indicators">
                {% set slotCount = 0 %}
                {% for productSetSlotAssignment in productSet.slotAssignments|filter(v => v.slot.calculatedSlotTemplate != 'invisible') %}
                    {% set slotCount = slotCount + 1 %}
                    {% set productSetSlot = productSetSlotAssignment.slot %}
                    {% set slotOptional = productSetSlot.config.minimumSelectedOptions <= 0 %}
                    {% set slotTitle = slotCount ~ '. ' ~ productSetSlot.name %}

                    {% block swkweb_product_set_slider_indicator %}
                        <li
                          data-bs-toggle="tooltip"
                          title=""
                          data-html="true"
                          data-bs-target="#swkwebProductSetSlider"
                          data-bs-slide-to="{{ slotCount - 1 }}"
                          data-swkweb-set-slot-id="{{ productSetSlotAssignment.id }}"
                          class="{% if slotCount == 1 %}active{% endif %}{% if slotOptional %} is-optional is-valid{% endif %}"
                        >
                            {% block swkweb_product_set_slider_indicator_inner %}{% endblock %}
                        </li>
                    {% endblock %}
                {% endfor %}
            </ol>
        {% endblock %}

        {% block swkweb_product_set_slider_controls %}
            <div class="carousel-navigation" data-swkweb-product-set-sticky="true">
                {% block swkweb_product_set_slider_control_prev %}
                    <button class="carousel-control-prev" type="button" data-bs-target="#swkwebProductSetSlider" data-bs-slide="prev">
                        {% block swkweb_product_set_slider_control_prev_icon %}
                            {% sw_icon 'arrow-head-left' %}
                        {% endblock %}
                    </button>
                {% endblock %}

                {% block swkweb_product_set_slider_control_next %}
                    <button class="carousel-control-next" type="button" data-bs-target="#swkwebProductSetSlider" data-bs-slide="next">
                        {% block swkweb_product_set_slider_control_next_icon %}
                            {% sw_icon 'arrow-head-right' %}
                        {% endblock %}
                    </button>
                {% endblock %}
            </div>
        {% endblock %}

        <div class="carousel-inner" data-swkweb-product-set-sticky-element="true" data-swkweb-product-set-sticky-element-options="{{ {targetSelector: '.swkweb-product-set-slot-header'}|json_encode }}">
            {{ parent() }}
        </div>
    </div>
{% endblock %}

{% block swkweb_product_set_slots_include %}
    <div class="carousel-item{% if slotCount == 1 %} active{% endif %}">
        {{ parent() }}
    </div>
{% endblock %}
