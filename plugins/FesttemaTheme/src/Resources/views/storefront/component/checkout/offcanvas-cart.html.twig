{% sw_extends '@Storefront/storefront/component/checkout/offcanvas-cart.html.twig' %}

{% block component_offcanvas_cart_actions_promotion_input_group %}
    <div class="input-group d-none">
        {% block component_offcanvas_cart_actions_promotion_label %}
           {{ parent() }}
        {% endblock %}

        {% block component_offcanvas_cart_actions_promotion_input %}
            {{ parent() }}
        {% endblock %}

        {% block component_offcanvas_cart_actions_promotion_submit %}
            {{ parent() }}
        {% endblock %}
    </div>
{% endblock %}

{% block component_offcanvas_cart_hidden_line_items_information %}
    {{ parent() }}
    {% block cart_offcanvas_product_recomendations %}
    {% endblock %}

    {% if theme_config('sw-topbar-emarket') %}
        <div class="text-center e-market-logo-offcanvas">
            <a href="{{ "festtemaTheme.header.eMarketLink"|trans|sw_sanitize }}" class="" target="_new"><img src="{{ asset('bundles/festtematheme/images/e-maerket-logo.png', 'asset') }}" class="img-fluid"></a>
        </div>
    {% endif %}
{% endblock %}

{% block component_offcanvas_cart_actions_cart %}

{% endblock %}
