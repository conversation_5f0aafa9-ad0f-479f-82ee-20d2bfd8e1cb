{% sw_extends '@Storefront/storefront/component/checkout/offcanvas-cart-summary.html.twig' %}


{% block component_offcanvas_summary_total_value %}
    <dd class="col-5 summary-value summary-total">
        <strong>{{ page.cart.price.positionPrice|currency }}</strong>
    </dd>
{% endblock %}

{% block component_offcanvas_summary_content_info %}
    <div class="row offcanvas-shipping-info">
        <span class="col-7 shipping-label shipping-cost">
                <strong>{{ "checkout.summaryShipping"|trans|sw_sanitize }}</strong>
                    {% if loop.first %}
                        <small {% if page.shippingMethods|length %}class="js-toggle-shipping-selection d-none"{% endif %}>
                        ({{ activeShipping.shippingMethod.translated.name }})
                    </small>
                {% endif %}
        </span>

        <span class="col-5 pb-2 shipping-value shipping-cost">
            <strong>{{ activeShipping.shippingCosts.totalPrice < 0 ? '-' : '+' }} {{ activeShipping.shippingCosts.totalPrice|abs|currency }}</strong>
        </span>
    </div>
{% endblock %}

