{% sw_extends '@Storefront/storefront/component/shipping/shipping-method.html.twig' %}

{% block component_shipping_method_description %}
    <div class="shipping-method-description ft-pakkeshop">
        <strong>{{ shipping.translated.name }}</strong>
        {# Add price / price range information #}
        {% if shipping.extension('calculatedPrice') is not null %}
            <span class="shipping-method-price col-auto">
                    {% set price = shipping.extension('calculatedPrice').totalPrice %}
                    {% if price <= 0 %}
                        <span>({{ 'labels.free'|trans }})</span>
                    {% else %}
                        <span>({{ price|currency }})</span>
                    {% endif %}
                {% elseif shipping.extension('shippingPriceRange') is not null %}
                    {% set minPrice = shipping.extension('shippingPriceRange').min %}
                    {% set maxPrice = shipping.extension('shippingPriceRange').max %}
                    {% if minPrice == maxPrice %}
                        {% if minPrice <= 0 %}
                            <span>({{ 'labels.free'|trans }})</span>
                        {% else %}
                            <span>({{ minPrice|currency }})</span>
                        {% endif %}
                    {% else %}
                        <span>({{ minPrice|currency }} - {{ maxPrice|currency }})</span>
                    {% endif %}
            </span>
        {% endif %}
        {% if shipping.translated.description %}
            {% set shippingDescription = shipping.translated.description|raw %}

            {% if not shipping.id is same as(context.shippingMethod.id) %}
                {% set shippingDescription = (shippingDescription|length > 75 ? shippingDescription[:75] ~ ' ...' : shippingDescription) %}
            {% endif %}

            <p title="{{ shipping.translated.description|raw }}">{{ shippingDescription }}</p>
        {% endif %}
        {# Add parcelshop information #}
        {% if shipping.id is same as(context.shippingMethod.id) %}
            {% if not shippingMethodInvalid and context.shippingMethod.extension('shippingMethodType') is not null %}
                {% set selectedParcelShop = page.extensions.shipping_method_type_configuration.selectedParcelShop %}
                {# <strong>{% sw_include '@WexoShipping/storefront/component/parcelShopTranslation.html.twig' with {
                        snippet: 'shippingMethod.selectedParcelShop-'~shipping.extension('shippingMethodType').typeKey,
                        defaultSnippet: 'shippingMethod.parcelShop'
                    } %}</strong> #}
                <div class="{% if selectedParcelShop %}ft-select-packageshop{% endif %}">
                    <img src="{{ asset('bundles/festtematheme/images/map-marker-package.png', 'asset') }}" class="ft-packageshop-icon" alt="Choose pakkeshop">
                    <span>{{ selectedParcelShop.name }}</span>
                    <span>{{ selectedParcelShop.street }}</span>
                    <span>{{ selectedParcelShop.zipCode }} {{ selectedParcelShop.city }}</span>
                </div>
            {% endif %}
        {% endif %}
        {# Add deadline information #}
        {% if feature('FEATURE_WEXOSHIPPING_1') %}
            {% if shipping.extension('formattedDeadline') is not null %}
                {% set formattedDeadline = shipping.extension('formattedDeadline').formattedDeadline %}
                <strong class="shipping-method-deadline">
                    {{ formattedDeadline }}
                </strong>
            {% elseif shipping.extension('shippingMethodType').typeKey == 'unified' %}
                {% for pageShippingMethod in page.shippingMethods %}
                    {% if pageShippingMethod.extension('shippingMethodType') is not null and pageShippingMethod.extension('formattedDeadline') is not null %}
                        {% set formattedDeadline = pageShippingMethod.extension('formattedDeadline').formattedDeadline %}
                        <div class="shipping-method-deadline-unified">
                            <strong class="shipping-method-deadline">
                                {{ pageShippingMethod.name }}: {{ formattedDeadline }}
                            </strong>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endif %}
    </div>
{% endblock %}
