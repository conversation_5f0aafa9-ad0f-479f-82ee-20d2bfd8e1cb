
{% sw_extends '@Storefront/storefront/section/cms-section-block-container.html.twig' %}
{% set rowClass = '' %}
{% if block.type != 'product-listing' %}
    {% set rowClass = ' row ' %}
{% endif %}
{% set isNotCategory = true %}
{% block section_content_block_row %}
    <div class="cms-block-container-row {{ rowClass }} cms-row {{ sidebarClasses }}">
        
        {% if (block.type == 'image-text-cover') %}
            {% set slotElements = block.slots.elements %}
            {% set firstEl = slotElements|first %}
            {% if firstEl.config.media.value == 'category.media' %}
                {% set isNotCategory = false %}
            {% endif %}
        {% endif %}
        {% if isNotCategory %}
            {% sw_include "@Storefront/storefront/block/cms-block-" ~ block.type ~ ".html.twig" ignore missing %}
        {% else %}
            {% sw_include "@FesttemaTheme/storefront/block/cms-block-festtema-image-text-cover.html.twig"  ignore missing %}
        {% endif %}
    </div>
{% endblock %}
