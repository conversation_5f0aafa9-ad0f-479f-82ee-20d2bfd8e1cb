{% sw_extends '@Storefront/storefront/layout/footer/footer.html.twig' %}

{% block layout_footer_inner_container %}
    <div class="footer">
        <div class="container main-width">

            {% block layout_footer_navigation %}
                <div  
                    id="footerColumns"
                    class="row footer-columns"
                    {% if feature('v6.6.0.0') %}
                        data-collapse-footer-columns="true"
                    {% else %}
                        data-collapse-footer="true"
                    {% endif %}
                    role="list"
                >
                    {% block layout_footer_navigation_hotline %}
                        <div class="col-md-6 col-lg-3 footer-column">
                            {% block layout_footer_logo_image %}
                                <picture class="footer-logo-picture">
                                    {# {% if theme_config('sw-logo-mobile')  %}
                                        <img src="{{ theme_config('sw-logo-mobile') |sw_encode_url }}"
                                     alt="{{ "header.logoLink"|trans|striptags }}"
                                     class="img-fluid header-logo-main-img"/>
                                    {% endif %} #}
                                    {% if (app.request.schemeAndHttpHost in ['https://www.billigekostumer.dk', 'https://www.billigekostumer.dk/']) %}
                                        <img src="https://static.festtema.dk/media/84/93/71/1679057316/billigekostumer-logo-desktop-ny_resized.webp" alt="Gå til hjemmesiden" class="img-fluid header-logo-main-img" height="72" width="260">
                                    {% else %}
                                        <img src="https://static.festtema.dk/media/ef/53/a7/1686566798/festtema-mobile-logo-75.webp" alt="Gå til hjemmesiden" class="img-fluid header-logo-main-img" height="40" width="120">
                                    {% endif %}
                                </picture>
                            {% endblock %}

                            
                            {% block sw_festtema_layout_footer_newsletter %}  
                                {% if theme_config('sw-footer-show-newsletter') %}                                  
                                    <div class="cms-block cms-block-form">
                                        <div class="cms-element-form">
                                            <form action="{{ path('frontend.form.newsletter.register.handle') }}" method="post" data-form-csrf-handler="true" data-form-validation="true" data-toggle="modal">
                                                <div class="input-group mt-4 mb-4">
                                                    <input name="email" type="email" id="form-email" value="" placeholder="{{ 'newsletter.placeholderMail'|trans|sw_sanitize }}" required="required" class="form-control">
                                                    <button type="submit" aria-label="Submit" class="btn btn-outline-secondary">Sign Up
                                                    </button>
                                                
                                                </div>

                                                <div class="captcha-container">
                                                    {% block cms_form_newsletter_captcha %}
                                                        {% sw_include '@Storefront/storefront/component/captcha/base.html.twig' %}
                                                    {% endblock %}
                                                </div>
                                                {% block cms_form_newsletter_hidden_fields %}
                                                    <div class="form-hidden-fields">
                                                        {% block cms_form_newsletter_csrf %}
                                                            {# {{ sw_csrf('frontend.form.newsletter.register.handle') }} #}
                                                        {% endblock %}

                                                        <input type="hidden" name="option" value="{{ constant('Shopware\\Storefront\\Controller\\FormController::SUBSCRIBE') }}">

                                                        <input type="submit" class="submit--hidden d-none" />
                                                    </div>
                                                {% endblock %}
                                            </form>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endblock %}

                            {# {% block layout_footer_navigation_hotline_headline %}
                                <div class="footer-column-headline footer-headline">
                                    {{ 'footer.serviceHotlineHeadline'|trans|sw_sanitize }}
                                </div>
                            {% endblock %} #}


                            {# {% block layout_footer_navigation_hotline_content %}
                                <div class="footer-column-content footer-contact">
                                    <div class="footer-column-content-inner">
                                        <p class="footer-contact-hotline">
                                            {{ 'footer.serviceHotline'|trans|sw_sanitize }}
                                        </p>
                                    </div>
                                </div>
                            {% endblock %} #}
                        </div>
                    {% endblock %}

                    {% block layout_footer_navigation_columns %}
                        {% for root in page.footer.navigation.tree %}
                            {% block layout_footer_navigation_column %}
                                <div class="col-md-6 col-lg-3 footer-column">
                                    {% block layout_footer_navigation_information_headline %}
                                        <div class="footer-column-headline footer-headline">

                                            {% if root.category.type == 'folder' %}
                                                {{ root.category.translated.name }}
                                            {% else %}
                                                {# <a href="{{ category_url(root.category) }}"
                                                {% if category_linknewtab(root.category) %}target="_blank"{% endif %}
                                                title="{{ root.category.translated.name }}"> #}
                                                    {{ root.category.translated.name }}
                                                {# </a> #}
                                            {% endif %}
                                            
                                        </div>
                                    {% endblock %}

                                    {% block layout_footer_navigation_information_content %}
                                        <div class="footer-column-content">
                                            <div class="footer-column-content-inner">
                                                {% block layout_footer_navigation_information_links %}
                                                    <ul class="list-unstyled">
                                                        {% for treeItem in root.children %}
                                                            {% set category = treeItem.category %}
                                                            {% set name = category.translated.name %}

                                                            {# @deprecated tag:v6.5.0 - Use "category.translated.externalLink" directly or category_url function instead. #}
                                                            {% set externalLink = category.translated.externalLink %}

                                                            {% block layout_footer_navigation_information_link_item %}
                                                                <li class="footer-link-item">
                                                                    {% block layout_footer_navigation_information_link %}
                                                                        {% if category.type == 'folder' %}
                                                                            <div>{{ name }}</div>
                                                                        {% else %}
                                                                            <a class="footer-link"
                                                                            href="{{ category_url(category) }}"
                                                                            {% if category_linknewtab(category) %}target="_blank"{% endif %}
                                                                            title="{{ name }}">
                                                                                {{ name }}
                                                                            </a>
                                                                        {% endif %}
                                                                    {% endblock %}
                                                                </li>
                                                            {% endblock %}
                                                        {% endfor %}
                                                    </ul>
                                                {% endblock %}
                                            </div>
                                        </div>
                                    {% endblock %}
                                </div>
                            {% endblock %}
                        {% endfor %}
                    {% endblock %}
                    {% block layout_footer_contact_info %}
                        {% if theme_config('sw-footer-show-contact-info') %}
                            <div class="col-md-6 col-lg-3 footer-column">
                                <div class="footer-column-headline footer-headline">
                                    {{ "footer.contactInfo"|trans|sw_sanitize }}
                                </div>
                                <div class="footer-column-content">
                                    <ul class="site-address">
                                        {% if theme_config('sw-footer-contact-address') is not empty %}
                                            <li class="bi-geo-alt">{{ theme_config('sw-footer-contact-address') |sw_sanitize }}</li>
                                        {% endif %}
                                        {% if theme_config('sw-topbar-email') is not empty %}
                                            <li class="bi-envelope">
                                                <a href={{ theme_config('sw-topbar-email-url') }}>{{ theme_config('sw-topbar-email') |sw_sanitize }}</li></a>
                                        {% endif %}
                                        {% if theme_config('sw-topbar-contact') is not empty %}
                                            <li class="bi-telephone-plus"><a href="callto:{{ theme_config('sw-topbar-contact') |sw_sanitize  }}">{{ theme_config('sw-topbar-contact') |sw_sanitize  }}</a></li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        {% endif %}
                    {% endblock %}
                </div>
            {% endblock %}

            {# to-do: disable static block end enable this dynamic block #}
            {# {% block layout_footer_payment_shipping_logos %}
                <div class="footer-logos">
                    {% block layout_footer_payment_logos %}
                        {% for paymentMethod in page.salesChannelPaymentMethods %}
                            {% block layout_footer_payment_logo %}
                                {% if paymentMethod.media %}
                                    <div class="footer-logo is-payment">
                                        {% sw_thumbnails 'footer-payment-image-thumbnails' with {
                                            media: paymentMethod.media,
                                            sizes: {
                                                'default': '100px'
                                            },
                                            attributes: {
                                                'class': 'img-fluid footer-logo-image',
                                                'alt': (paymentMethod.media.translated.alt ?: paymentMethod.translated.name),
                                                'title': (paymentMethod.media.translated.title ?: paymentMethod.translated.name)
                                            }
                                        } %}
                                    </div>
                                {% endif %}
                            {% endblock %}
                        {% endfor %}
                    {% endblock %}

                    {% block layout_footer_shipping_logos %}
                        {% set shippingArray = [] %}
                        {% for shippingMethod in page.salesChannelShippingMethods %}
                            {% if (shippingMethod.mediaId not in shippingArray) %}
                                {% if shippingMethod.mediaId %}
                                    {% set shippingArray = shippingArray|merge([shippingMethod.mediaId]) %}
                                {% endif %}
                                {% block layout_footer_shipping_logo %}
                                    
                                    {% if shippingMethod.media %}
                                        <div class="footer-logo is-shipping">
                                            {% sw_thumbnails 'footer-shipping-image-thumbnails' with {
                                                media: shippingMethod.media,
                                                sizes: {
                                                    'default': '100px'
                                                },
                                                attributes: {
                                                    'class': 'img-fluid footer-logo-image',
                                                    'alt': (shippingMethod.media.translated.alt ?: shippingMethod.translated.name),
                                                    'title': (shippingMethod.media.translated.title ?: shippingMethod.translated.name)
                                                }
                                            } %}
                                        </div>
                                    {% endif %}
                                {% endblock %}
                            {% endif %}
                        {% endfor %}
                    {% endblock %}
                </div>
            {% endblock %} #}

            {% block layout_footer_payment_shipping_logos_static %}
                <div class="footer-logos">
                    {% if app.request.locale == 'da-DK' || app.request.locale == 'ba-DK' %}
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/visakort.webp" alt="Kreditkort" title="Kreditkort" height="24" width="85">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/mobilepay.webp" alt="MobilePay" title="MobilePay" height="25" width="76">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/googlepay.webp" alt="GooglePay" title="GooglePay" height="30" width="50">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/applepay.webp" alt="ApplePay" title="ApplePay" height="24" width="57">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/gls-logo.webp" alt="Levering til GLS pakkeshop" title="Levering til GLS pakkeshop" height="25" width="55">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/postnord-logo.webp" alt="Postnord - Fly Grønland" title="Postnord - Fly Grønland" height="35" width="35">
                        </div>
                    {% endif %}
                    {% if app.request.locale == 'nb-NO' %}
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/visakort.webp" alt="Kreditkort" title="Kreditkort" height="24" width="85">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/googlepay.webp" alt="GooglePay" title="GooglePay" height="30" width="50">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/applepay.webp" alt="ApplePay" title="ApplePay" height="24" width="57">
                        </div>
                    {% endif %}
                    {% if app.request.locale == 'sv-SE' %}
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/visakort.webp" alt="Kreditkort" title="Kreditkort" height="24" width="85">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/googlepay.webp" alt="GooglePay" title="GooglePay" height="30" width="50">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/applepay.webp" alt="ApplePay" title="ApplePay" height="24" width="57">
                        </div>
                        <div class="footer-logo is-payment">
                            <img src="https://static.festtema.dk/media/weedesign_pagespeed/2000/schenker-logo.webp" alt="Leverans til paketbutik" title="Leverans til paketbutik" height="45" width="45">
                        </div>
                    {% endif %}
                </div>
            {% endblock %}

        </div>
    </div>

    {% block layout_footer_bottom %}
        <div class="copyright">{{ "footer.copyrightInfo"|trans|sw_sanitize }}</div>
    {% endblock %}
{% endblock %}

