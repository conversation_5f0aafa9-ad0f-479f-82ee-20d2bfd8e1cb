{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}


{% block layout_head_meta_tags_schema_webpage %}
    {{ parent()}}
    {% if (app.request.schemeAndHttpHost in ['https://www.festtema.dk/', 'https://www.festtema.dk'] and controllerAction == 'home') %}
        <script type="application/ld+json">{
            "@context": "https://schema.org",
            "@graph": [
                {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "id": "site-navigation",
                "name": "Forside",
                "url": "https://www.festtema.dk/"
                },
                {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "id": "site-navigation",
                "name": " Nyheder inden for Fest og Udklædning",
                "url": "https://www.festtema.dk/Nyheder/"
                },
                {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "id": "site-navigation",
                "name": "Tema & Begivenheder",
                "url": "https://www.festtema.dk/tema/"
                },
                {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "id": "site-navigation",
                "name": "Kostumer & Udklædning",
                "url": "https://www.festtema.dk/Kostumer/"
                },
                {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "id": "site-navigation",
                "name": "Kundeservice",
                "url": "https://www.festtema.dk/Online-Shopping/Kundeservice/"
                }
            ]}
        </script>
    {% endif %}

    {% if (app.request.requestUri == '/navigation/8bca252dbb794963871be1d2a75cd885') %}
        <script type="application/ld+json">
            {
            "@context": "https://schema.org",
            "@type": "LocalBusiness",
            "name": "Festtema",
            "image": "https://static.festtema.dk/media/b8/5b/f8/1688476508/Festtema-din-Festbutik-i-Skovlunde%20%281%29%20%281%29.webp",
            "@id": "https://www.festtema.dk/Om-Festtema/Butikken-i-Skovlunde/#localbusiness",
            "url": "https://www.festtema.dk/Om-Festtema/Butikken-i-Skovlunde/",
            "telephone": "+4570203740",
            "priceRange": "$",
            "address": {
                "@type": "PostalAddress",
                "streetAddress": "Meterbuen 15",
                "addressLocality": "Skovlunde",
                "postalCode": "2740",
                "addressCountry": "DK"
            },
            "geo": {
                "@type": "GeoCoordinates",
                "latitude": 55.71669619999999,
                "longitude": 12.4164595
            },
            "openingHoursSpecification": [{
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday"
                ],
                "opens": "11:00",
                "closes": "18:00"
            },{
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": "Saturday",
                "opens": "10:00",
                "closes": "15:00"
            }],
            "sameAs": [
                "https://www.instagram.com/festtemadk",
                "https://www.facebook.com/Festtema",
                "https://www.linkedin.com/company/festtema/",
                "https://www.youtube.com/@festtemadk1872"
            ] 
            }
        </script>

    {% endif %}
{% endblock %}

{% block layout_head_stylesheet %}
    {{ parent()}}

    <script type="text/javascript" src="https://unpkg.com/default-passive-events"></script>
    {# {% if context.salesChannel.customFields.custom_product_hero_image and controllerAction == 'home' %}
        <link rel="preload" fetchpriority="high" as="image" href="{{ context.salesChannel.customFields.custom_product_hero_image }}" type="image/webp">
    {% endif %} #}
    {% if (page.cmsPage.type == 'product_list') %}
        {% set section = page.cmsPage.sections.elements | first %}
        {% set cmsBlock = section.blocks.elements | first %}
        {% set cmsSlots = cmsBlock.slots.elements | first %}
        {% if (cmsSlots.data.media.url) %}
            <link rel="preload" fetchpriority="high" as="image" href="{{ cmsSlots.data.media.url }}" type="image/webp">
        {% endif %}
    {% endif %}
    {% if page.product %}
        {% set productImg = page.product.media.first %}
        {% if 'weedesign_pagespeed' in productImg.media.url %}
            <link rel="preload" fetchpriority="high" as="image" href="{{ productImg.media.url|replace({'/2000/': '/400/'}) }}" type="image/webp">
        {% else %}
            <link rel="preload" fetchpriority="high" as="image" href="{{ productImg.media.url }}" type="image/webp">
        {% endif %}
    {% endif %}

    {% if app.request.locale == 'nb-NO' %}
        <script type="text/javascript">
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "o87bhusn37");
        </script>
    {% endif %}

    {% if (app.request.schemeAndHttpHost in ['https://www.festtema.dk/', 'https://www.festtema.dk']) %}
	<!-- Start e-maerket widget --><script type="text/javascript" src="https://widget.emaerket.dk/js/50f829999eed685550a2b6a8a524224b" async></script><!-- // end e-maerket widget -->
    {% endif %}

{% endblock %}
