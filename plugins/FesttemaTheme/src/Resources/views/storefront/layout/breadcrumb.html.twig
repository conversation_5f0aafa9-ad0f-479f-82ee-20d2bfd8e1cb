{% sw_extends '@Storefront/storefront/layout/breadcrumb.html.twig' %}
{% block layout_breadcrumb_list_item %}
    {% if loop.index == 1 %}
        <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
            <a href="{{ path('frontend.home.page') }}" class="breadcrumb-link" title="{{ "general.homeLink"|trans|striptags }}" itemprop="item">
                <link itemprop="url"
                      href="{{ app.request.getSchemeAndHttpHost() }}"/>
                <span class="breadcrumb-title" itemprop="name">{{ "general.homeLink"|trans|striptags }}</span>
            </a>
            <meta itemprop="position" content="1"/>
        </li>
        <div class="breadcrumb-placeholder">
            {% sw_icon 'arrow-medium-right' style { 'size': 'fluid', 'pack': 'solid'} %}
        </div>
    {% endif %}

    <li class="breadcrumb-item"
        {% if key is same as(categoryId) %}aria-current="page"{% endif %}
        itemprop="itemListElement"
        itemscope
        itemtype="https://schema.org/ListItem">
        {% if breadcrumbCategory.type == 'folder' %}
            <div itemprop="item">
                <div itemprop="name">{{ name |title }}</div>
            </div>
        {% else %}
            <a href="{{ category_url(breadcrumbCategory) }}"
                class="breadcrumb-link {% if key is same as(categoryId) %} is-active{% endif %}"
                title="{{ name }}"
                {% if category_linknewtab(breadcrumbCategory) %}target="_blank"{% endif %}
                itemprop="item">
                <link itemprop="url"
                        href="{{ category_url(breadcrumbCategory) }}"/>
                <span class="breadcrumb-title" itemprop="name">{{ name |title }}</span>
            </a>
        {% endif %}
        <meta itemprop="position" content="{{ (loop.index + 1) }}"/>
    </li>
{% endblock %}

