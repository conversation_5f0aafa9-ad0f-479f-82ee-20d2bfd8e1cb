{% set topSliderOptions = {
    slider: {
        nav: false,
        controls: false,
        gutter: 15,
        items: 1,
        slideBy: "page",
        mouseDrag: true,
        arrowKeys: false,
        swipeAngle: true,
        autoplay: true,
        autoplayButtonOutput: false,
        speed: 600,
    }
} %}
{% block layout_header_topbar_desktop %}
    {% set offer1 = "offers.offer1"|trans|sw_sanitize %}
    {% set offer2 = "offers.offer2"|trans|sw_sanitize %}
    {% set offer3 = "offers.offer3"|trans|sw_sanitize %}
    {% set displayOffers = (app.request.schemeAndHttpHost in ['https://www.festtema.dk/', 'https://www.festtema.dk', 'https://www.festtema.no/', 'https://www.festtema.no', 'https://festtema.no', 'https://festtema.no/'] and app.request.locale in ['da-DK','nb-NO'])? true: false %}
    <div class="topbar {% if not displayOffers %} desktop-view{% endif %}">
        <div class="main-width">
            <div class="row">
                <div class="col {% if displayOffers %} col-md-auto{% endif %} p-0 desktop-view">
                    {% if theme_config('sw-topbar-email') is not empty %}
                        <span class="mail">
                            <a href={{ theme_config('sw-topbar-email-url') }} aria-label="email">
                                <i class="bi bi-envelope"></i>{{ theme_config('sw-topbar-email') |sw_sanitize }}
                            </a>
                        </span>
                    {% endif %}
                    {% if theme_config('sw-topbar-contact') is not empty %}
                        <span class="phone">
                            <i class="bi bi-telephone-plus"></i><a href="callto:{{ theme_config('sw-topbar-contact') |sw_sanitize  }}" class="festtema-call-to">{{ theme_config('sw-topbar-contact') |sw_sanitize  }}</a>
                        </span>
                    {% endif %}
                </div>
                {% if displayOffers %}
                    <div class="col text-center">
                        <div class="topbar-discount-text" data-base-slider="true"
                        data-base-slider-options='{% block layout_top_slider_config %}{{ topSliderOptions|json_encode }}{% endblock %}'>
                            <div data-base-slider-container="true">
                                {% if offer1 != '' %}
                                    <div>{{ offer1 }}</div>
                                {% endif %}
                                {% if offer2 != '' %}
                                    <div>{{ offer2 }}</div>
                                {% endif %}
                                {% if offer3 != '' %}
                                    <div>{{ offer3 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}
                <div class="col col-md-auto p-0 topbar-social-icons">
                    <ul class="list-inline">
                        {% if theme_config('sw-social-insta-icon') %}
                            <li class="list-inline-item">
                                <a href={{ theme_config('sw-social-insta-url') }} target="_blank" aria-label="instagram">
                                    <i class="bi bi-instagram"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% if theme_config('sw-social-tiktok-icon') %}
                            <li class="list-inline-item">
                                <a href={{ theme_config('sw-social-tiktok-url') }} target="_blank" aria-label="tiktok">
                                    <i class="bi bi-tiktok"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% if theme_config('sw-social-youtube-icon') %}
                            <li class="list-inline-item">
                                <a href={{ theme_config('sw-social-youtube-url') }} target="_blank" aria-label="youtube">
                                    <i class="bi bi-youtube"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% if theme_config('sw-social-fb-icon') %}
                            <li class="list-inline-item">
                                <a href={{ theme_config('sw-social-fb-url') }} target="_blank" aria-label="facebook">
                                    <i class="bi bi-facebook"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% if theme_config('sw-social-linkedin-icon') %}
                            <li class="list-inline-item">
                                <a href={{ theme_config('sw-social-linkedin-url') }} target="_blank" aria-label="linkedin">
                                    <i class="bi bi-linkedin"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
