{% sw_extends '@Storefront/storefront/layout/header/actions/wishlist-widget.html.twig' %}

{% block layout_header_actions_wishlist_widget %}
    {% set showCounter = showCounter ?? true %}

    {% if showCounter %}
        <span class="header-wishlist-icon">
            <i class="bi bi-heart"></i>
        </span>
    {% endif %}

    {% set wishlistStorageOptions = {
        listPath: path('frontend.wishlist.product.list'),
        mergePath: path('frontend.wishlist.product.merge'),
        pageletPath: path('frontend.wishlist.product.merge.pagelet'),
    } %}

    {% set wishlistWidgetOptions = { showCounter: showCounter } %}

    <span class="badge {{ bgClass }}-primary header-wishlist-badge"
          id="wishlist-basket"
          data-wishlist-storage="true"
          data-wishlist-storage-options="{{ wishlistStorageOptions|json_encode }}"
          data-wishlist-widget="true"
          data-wishlist-widget-options="{{ wishlistWidgetOptions|json_encode }}"
    ></span>
{% endblock %}
