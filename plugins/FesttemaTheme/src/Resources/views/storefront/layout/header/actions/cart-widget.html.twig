{% sw_extends '@Storefront/storefront/layout/header/actions/cart-widget.html.twig' %}

{% block layout_header_actions_cart_widget %}
    {% if page.hasExtension(constant('Ng<PERSON>\\AchieveFreeShipping\\Struct\\PluginConfig::PLUGIN_CONFIG_EXTENSION_NAME')) %}
        {% set NgsAchieveFreeShippingPluginConfig = page.getExtension(constant('Ngs\\AchieveFreeShipping\\Struct\\PluginConfig::PLUGIN_CONFIG_EXTENSION_NAME')) %}
        {% if not NgsAchieveFreeShippingPluginConfig.isCheckoutPage %}
            {% set freeDeliveryPrices = NgsAchieveFreeShippingPluginConfig.freeDeliveryPrices %}
            {% set shippingMethod = page.cart.deliveries.elements[0].shippingMethod %}

            <div class="navigation--entry entry--cart is--free-delivery-remaining">
                <div class="ngs--sw-cart-button-container">
                    <i class="bi bi-bag"></i>
                    {% if page.cart.lineItems|length > 0 %}
                        <span class="badge bg-primary header-cart-badge">{{ page.cart.lineItems|length }}</span>
                    {% endif %}
                    <span class="header-cart-total">
                        {{ page.cart.price.positionPrice|currency }}
                    </span>
                </div>

                <div class="ngs--free-delivery-remaining-amount-container-widget ngs--free-delivery-remaining-amount-container ngs--free-delivery-remaining-amount-container-desktop is--minimized">
                    <div class="ngs--free-delivery-remaining-amount-free-box">
                        <span class="ngs--free-delivery-remaining-amount-truck">
                            <svg xmlns="http://www.w3.org/2000/svg" width="366.935" height="234.311" viewBox="0 0 366.935 234.311" fill="#ffbd5d" class="ngs--free-delivery-remaining-amount-truck-svg">
                              <g id="free-delivery" transform="translate(0 -86.946)">
                                <path id="Path_13455" data-name="Path 13455" d="M472.184,214.761c-6.475-20.373-14.967-41.564-35.975-50.92a85.648,85.648,0,0,0-18.466-5.809v56.729Z" transform="translate(-118.36 -20.141)"/>
                                <path id="Path_13456" data-name="Path 13456" d="M43.336,382.718a28.389,28.389,0,1,0,55.928-6.889H44.185a28.36,28.36,0,0,0-.849,6.889Z" transform="translate(-12.278 -89.849)"/>
                                <path id="Path_13457" data-name="Path 13457" d="M202.427,382.718a28.389,28.389,0,1,0,55.927-6.889H203.276a28.4,28.4,0,0,0-.849,6.889Z" transform="translate(-57.354 -89.849)"/>
                                <path id="Path_13458" data-name="Path 13458" d="M395.4,382.718a28.389,28.389,0,1,0,55.927-6.889H396.246A28.394,28.394,0,0,0,395.4,382.718Z" transform="translate(-112.028 -89.849)"/>
                                <path id="Subtraction_6" data-name="Subtraction 6" d="M-2435.952-3285.438h-224.331a10.762,10.762,0,0,1-10.75-10.75v-19.5h25.824a10.656,10.656,0,0,0,7.779-3.33,10.67,10.67,0,0,0,2.959-7.94,10.891,10.891,0,0,0-11.041-10.23h-25.521v-123.037a10.68,10.68,0,0,1,3.149-7.6,10.68,10.68,0,0,1,7.6-3.149H-2446.7a10.763,10.763,0,0,1,10.75,10.75v174.783Zm-136.175-89.619h4.052l5.958,12.085h9.814v-.391l-7.224-14.179a15.591,15.591,0,0,0,2.5-1.58,8.9,8.9,0,0,0,2.613-3.37,12.731,12.731,0,0,0,.939-5.236,10.207,10.207,0,0,0-1.659-5.933,10.267,10.267,0,0,0-4.712-3.626,19.34,19.34,0,0,0-7.226-1.232h-14.209v35.546h9.155v-12.084Zm51.392-23.461v35.546h24.707v-7.373h-15.527v-7.3h13.11v-7.1h-13.11v-6.372h15.6v-7.4h-24.781Zm-27.93,0v35.546h24.707v-7.373h-15.527v-7.3h13.11v-7.1h-13.11v-6.372h15.6v-7.4h-24.781Zm-59.888,0v35.546h9.18v-13.7h13.452v-7.373h-13.452v-7.08h14.771v-7.4h-23.951Zm41.382,16.064h-4.956v-8.667h5.054a4.465,4.465,0,0,1,3.32,1.147,4.4,4.4,0,0,1,1.123,3.2,5.154,5.154,0,0,1-.5,2.38,3.248,3.248,0,0,1-1.514,1.452A5.831,5.831,0,0,1-2567.17-3382.455Z" transform="translate(2671.033 3557.918)"/>
                                <path id="Path_13460" data-name="Path 13460" d="M457.622,291.949a10.75,10.75,0,0,0,10.75-10.75V245.43a32.773,32.773,0,0,0-1.44-9.839H390.071a10.75,10.75,0,0,1-10.75-10.75V155.664h-21.3V291.949Z" transform="translate(-101.438 -19.47)"/>
                              </g>
                            </svg>
                        </span>
                    </div>

                    <div class="ngs--free-delivery-remaining-amount-info-box popup">
                        <div class="ngs--free-delivery-remaining-amount-remaining-text">
                            {{ "NgsAchieveFreeShipping.NgsCartInfoFreeShippingRemainingText"|trans({'%freeDeliveryPrice%': freeDeliveryPrices[shippingMethod.id].value|currency})|sw_sanitize }}
                        </div>
                        <div class="ngs--free-delivery-remaining-amount-fill-container">
                            <span class="ngs--free-delivery-remaining-amount-fill" data-ngs-achieve-free-shipping-fill="true" data-fill="{{ freeDeliveryPrices[shippingMethod.id].percent }}%"></span>
                        </div>
                    </div>
                </div>
            </div>

            {% if NgsAchieveFreeShippingPluginConfig.showInHeaderMobile %}
                <div id="js--ngs-navigation-free-shipping" class="js--ngs-navigation-free-shipping navigation--entry ngs-entry--free-shipping">

                    <div class="btn is--icon-left">

                        <div class="ngs--free-delivery-remaining-amount-container-widget ngs--free-delivery-remaining-amount-container"
                             id="js--ngs-free-delivery-remaining-amount-box-mobile">

                            <div class="ngs--free-delivery-remaining-amount-free-box">
                            <span class="ngs--free-delivery-remaining-amount-truck">
                                <svg xmlns="http://www.w3.org/2000/svg" width="366.935" height="234.311" viewBox="0 0 366.935 234.311" fill="#ffbd5d" class="ngs--free-delivery-remaining-amount-truck-svg">
                                  <g id="free-delivery" transform="translate(0 -86.946)">
                                    <path id="Path_13455" data-name="Path 13455" d="M472.184,214.761c-6.475-20.373-14.967-41.564-35.975-50.92a85.648,85.648,0,0,0-18.466-5.809v56.729Z" transform="translate(-118.36 -20.141)"/>
                                    <path id="Path_13456" data-name="Path 13456" d="M43.336,382.718a28.389,28.389,0,1,0,55.928-6.889H44.185a28.36,28.36,0,0,0-.849,6.889Z" transform="translate(-12.278 -89.849)"/>
                                    <path id="Path_13457" data-name="Path 13457" d="M202.427,382.718a28.389,28.389,0,1,0,55.927-6.889H203.276a28.4,28.4,0,0,0-.849,6.889Z" transform="translate(-57.354 -89.849)"/>
                                    <path id="Path_13458" data-name="Path 13458" d="M395.4,382.718a28.389,28.389,0,1,0,55.927-6.889H396.246A28.394,28.394,0,0,0,395.4,382.718Z" transform="translate(-112.028 -89.849)"/>
                                    <path id="Subtraction_6" data-name="Subtraction 6" d="M-2435.952-3285.438h-224.331a10.762,10.762,0,0,1-10.75-10.75v-19.5h25.824a10.656,10.656,0,0,0,7.779-3.33,10.67,10.67,0,0,0,2.959-7.94,10.891,10.891,0,0,0-11.041-10.23h-25.521v-123.037a10.68,10.68,0,0,1,3.149-7.6,10.68,10.68,0,0,1,7.6-3.149H-2446.7a10.763,10.763,0,0,1,10.75,10.75v174.783Zm-136.175-89.619h4.052l5.958,12.085h9.814v-.391l-7.224-14.179a15.591,15.591,0,0,0,2.5-1.58,8.9,8.9,0,0,0,2.613-3.37,12.731,12.731,0,0,0,.939-5.236,10.207,10.207,0,0,0-1.659-5.933,10.267,10.267,0,0,0-4.712-3.626,19.34,19.34,0,0,0-7.226-1.232h-14.209v35.546h9.155v-12.084Zm51.392-23.461v35.546h24.707v-7.373h-15.527v-7.3h13.11v-7.1h-13.11v-6.372h15.6v-7.4h-24.781Zm-27.93,0v35.546h24.707v-7.373h-15.527v-7.3h13.11v-7.1h-13.11v-6.372h15.6v-7.4h-24.781Zm-59.888,0v35.546h9.18v-13.7h13.452v-7.373h-13.452v-7.08h14.771v-7.4h-23.951Zm41.382,16.064h-4.956v-8.667h5.054a4.465,4.465,0,0,1,3.32,1.147,4.4,4.4,0,0,1,1.123,3.2,5.154,5.154,0,0,1-.5,2.38,3.248,3.248,0,0,1-1.514,1.452A5.831,5.831,0,0,1-2567.17-3382.455Z" transform="translate(2671.033 3557.918)"/>
                                    <path id="Path_13460" data-name="Path 13460" d="M457.622,291.949a10.75,10.75,0,0,0,10.75-10.75V245.43a32.773,32.773,0,0,0-1.44-9.839H390.071a10.75,10.75,0,0,1-10.75-10.75V155.664h-21.3V291.949Z" transform="translate(-101.438 -19.47)"/>
                                  </g>
                                </svg>
                            </span>
                            </div>

                            <div class="ngs--free-delivery-remaining-amount-info-box">
                                <div class="ngs--free-delivery-remaining-amount-mobile-head">{{ "NgsAchieveFreeShipping.NgsCartInfoFreeShipping"|trans|sw_sanitize }}</div>
                                <div class="ngs--free-delivery-remaining-amount-fill-container">
                                    <span class="ngs--free-delivery-remaining-amount-fill" data-ngs-achieve-free-shipping-fill-mobile="true" data-fill="{{ freeDeliveryPrices[shippingMethod.id].percent }}%"></span>
                                </div>
                                <div class="f_freeDeliveryRemainingText ngs--free-delivery-remaining-amount-remaining-text">
                                    {{ "NgsAchieveFreeShipping.NgsCartInfoFreeShippingRemainingText"|trans({'%freeDeliveryPrice%': freeDeliveryPrices[shippingMethod.id].value|currency})|sw_sanitize }}
                                </div>

                                <p class="ngs--free-delivery-remaining-amount-info-text f_freeDeliveryInfoText">
                                    {{ "NgsAchieveFreeShipping.NgsCartInfoFreeShippingDifference"|trans({'%freeDeliveryPrice%': freeDeliveryPrices[shippingMethod.id].value|currency, '%shippingMethodName%': shippingMethod.translated.name})|sw_sanitize }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <i class="bi bi-bag"></i>
            {% if page.cart.lineItems|length > 0 %}
                <span class="badge bg-primary header-cart-badge">{{ page.cart.lineItems|length }}</span>
            {% endif %}
            <span class="header-cart-total">
                {{ page.cart.price.positionPrice|currency }}
            </span>
        {% endif %}
    {% else %}
        <i class="bi bi-bag"></i>
        {% if page.cart.lineItems|length > 0 %}
            <span class="badge bg-primary header-cart-badge">{{ page.cart.lineItems|length }}</span>
        {% endif %}
        <span class="header-cart-total">
            {{ page.cart.price.positionPrice|currency }}
        </span>
    {% endif %}
{% endblock %}