{% sw_extends '@Storefront/storefront/layout/header/header.html.twig' %}

{% block layout_header %}
    {% block block_festtema_topbar %}
        {% sw_include '@Storefront/storefront/layout/header/header-topbar.html.twig' %}
    {% endblock %}
    
    <div class="main-width col-sm-auto d-sm-block">
        <nav class="navbar navbar-light custom-menu-bar">
            <div class="nav-main-toggle">
                {% block layout_header_navigation_toggle_tablet_button %}
                    <button class="btn nav-main-toggle-btn header-actions-btn navbar-toggler" 
                            type="button"
                            data-offcanvas-menu="true"
                            aria-label="{{ "general.menuLink"|trans|striptags }}">
                        {% block layout_header_navigation_toggle_tablet_button_icon %}
                            <span class="navbar-toggler-icon"></span>
                        {% endblock %}
                    </button>
                {% endblock %}
            </div>
        </nav>
    </div>

    <div class="main-width">
        <div class="row brand-logo-part align-items-center header-row">
            {% block layout_header_logo %}
                <div class="col-7 col-sm-3 col-lg-auto header-logo-col festtema-custom-logo">
                    {% sw_include '@Storefront/storefront/layout/header/logo.html.twig' %}
                </div>
            {% endblock %}

            {% block layout_header_search %}
                <div class="col-12 order-2 col-sm order-sm-1 header-search-col">
                    <div class="row">
                        <div class="col">
                            {% sw_include '@Storefront/storefront/layout/header/search.html.twig' %}
                        </div>
                    </div>
                </div>
            {% endblock %}

            {% block layout_header_actions %}
                <div class="col-5 ps-0 order-1 col-sm-auto order-sm-2 header-actions-col">
                    <div class="row g-0 justify-content-end">
                        {% if theme_config('sw-topbar-emarket') %}
                            {% block layout_emarket_icon %}
                                <div class="col-auto e-market-header">
                                    <a href="{{ "festtemaTheme.header.eMarketLink"|trans|sw_sanitize }}" class="btn header-actions-btn" target="_new">
                                        {# <img src="{{ asset('bundles/festtematheme/images/e-maerket-icon.png', 'asset') }}" alt="e-market" class="img-fluid"> #}
                                        <img src="https://static.festtema.dk/media/6b/a9/a0/1687772922/e-market-1.webp" alt="e-market" class="img-fluid" height="24" width="24">
                                    </a>
                                </div>
                            {% endblock %}
                        {% endif %}

                        {% block layout_header_search_toggle %}
                            <div class="col-auto d-sm-none d-none">
                                <div class="search-toggle">
                                    <button class="btn header-actions-btn search-toggle-btn js-search-toggle-btn collapsed bi bi-search"
                                            type="button"
                                            {{ dataBsToggleAttr }}="collapse"
                                            {{ dataBsTargetAttr }}="#searchCollapse"
                                            aria-expanded="false"
                                            aria-controls="searchCollapse"
                                            aria-label="{{ "header.searchButton"|trans|striptags }}">
                                        {# {% sw_icon 'search' %} #}
                                    </button>
                                </div>
                            </div>
                        {% endblock %}

                        {% if config('core.cart.wishlistEnabled') %}
                            {% block layout_header_actions_wishlist %}
                                <div class="col-auto">
                                    <div class="header-wishlist">
                                        <a class="btn header-wishlist-btn header-actions-btn"
                                        href="{{ path('frontend.wishlist.page') }}"
                                        title="{{ 'header.wishlist'|trans|striptags }}"
                                        aria-label="{{ 'header.wishlist'|trans|striptags }}">
                                            {% sw_include '@Storefront/storefront/layout/header/actions/wishlist-widget.html.twig' %}
                                        </a>
                                    </div>
                                </div>
                            {% endblock %}
                        {% endif %}

                        {% block layout_header_actions_account %}
                            <div class="col-auto">
                                <div class="account-menu">
                                    {% sw_include '@Storefront/storefront/layout/header/actions/account-widget.html.twig' %}
                                </div>
                            </div>
                        {% endblock %}

                        {% block layout_header_actions_cart %}
                            <div class="col-auto">
                                <div class="header-cart"
                                     data-offcanvas-cart="true">
                                    <a class="btn header-cart-btn header-actions-btn"
                                        href="{{ path('frontend.checkout.cart.page') }}"
                                        data-cart-widget="true"
                                        title="{{ 'checkout.cartTitle'|trans|striptags }}"
                                        aria-label="{{ 'checkout.cartTitle'|trans|striptags }}">
                                            {% sw_include '@Storefront/storefront/layout/header/actions/cart-widget.html.twig' %}
                                        </a>
                                </div>
                            </div>
                        {% endblock %}
                    </div>
                </div>
            {% endblock %}
        </div>
    </div>
{% endblock %}
