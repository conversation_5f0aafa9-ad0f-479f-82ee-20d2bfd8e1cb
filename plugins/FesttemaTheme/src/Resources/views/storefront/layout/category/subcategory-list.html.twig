{% block festtema_layout_subcategory_list %} 
    <section class="team">
        <div class="grid">
            {% set flag = 0 %}
            {% for category in subCat %}
                <figure class="effect-steve {% if (flag < 3 ) %} mr-0 mr-sm-0 mr-md-2 mr-lg-4 {% endif %} mb-4">
                    <a href="{% if category.category.externalLink %}{{ category.category.externalLink }}{% else %}{{ seoUrl('frontend.navigation.page', { navigationId: category.category.id }) }}{% endif %}"
                                                   title="{{ category.category.translated.name }}">
                        {% if category.category.media.url %}
                            <img class="img-fluid" src="{{ category.category.media.url }}" alt="">
                        {% endif %}
                        <figcaption>
                            <h2>{{ category.category.name }}</h2>
                        </figcaption>
                    </a>
                </figure>
                {% set flag = flag + 1 %}
                {% if (flag == 4) %} {% set flag = 0 %} {% endif %}
            {% endfor %}
        </div> 
    </section>
{% endblock %}