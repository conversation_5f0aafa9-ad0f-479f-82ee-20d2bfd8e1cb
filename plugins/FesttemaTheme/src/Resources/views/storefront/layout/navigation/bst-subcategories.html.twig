{% set navigationMaxDepth = 4 %}
{% if not level %}
    {% set level = 0 %}
{% endif %}

{% set catSliderOptions = {
    slider: {
        nav: false,
        controls: true,
        gutter: 15,
        items: 1,
        slideBy: "page",
        mouseDrag: true,
        swipeAngle: false,
        autoWidth: autoWidth,
        speed: 400,
        responsive: {
            xs: {
                items: 2
            },
            sm: {
                items: 3
            },
            md: {
                items: 4
            },
            lg: {
                items: 5
            },
            xl: {
                items: 6
            },
            xxl: {
                items: 6
            }
        }
    }
} %}

{% block layout_subnavigation_categories %}
    {% block layout_subnavigation_categories_list %}
        {% if (activeResult.childCount > 0 or useNeighbours ) %}
            {% for item in navigationTree %}
                {% if (item.category.id in activeResult.id) or (item.category.id in activeResult.path) %}
                    {% if (item.category.id is same as(activeResult.id) and activeResult.childCount > 0) or (activeResult.childCount <= 0 and item.category.id == activeResult.parentId) %}
                        <div class="bst-subcategories child-{{ item.children|length }}{% if displayImages %} with-images{% endif %}"
                             data-base-slider="true"
                             data-base-slider-options='{% block layout_subnavigation_slider_config %}{{ catSliderOptions|json_encode }}{% endblock %}'>
                            <div class="bst-subcategory-list" data-base-slider-container="true">
                                {% for child in item.children %}
                                    {% if child.category.id != activeResult.id %}
                                        {% block layout_subnavigation_categories_list_entry %}
                                            {% if child.category.customFields.festtema_is_separator is not true %}
                                                <div class="subcategory-navigation-entry">
                                                    <figure class=" effect-steve">
                                                        {% block layout_subnavigation_categories_link %}
                                                            <a class="subcategory-navigation-link{% if child.category.id is same as(activeResult.id) %} is-active{% endif %}"
                                                            href="{% if child.category.externalLink %}{{ child.category.externalLink }}{% elseif child.category.internalLink %}{{ seoUrl('frontend.navigation.page', { navigationId: child.category.internalLink }) }}{% else %}{{ seoUrl('frontend.navigation.page', { navigationId: child.category.id }) }}{% endif %}"
                                                            title="{{ child.category.translated.name }}">
                                                            
                                                                {% if displayImages and child.category.media.url %}
                                                                    <div class="category-image-wrapper">
                                                                        {% block layout_subnavigation_categories_image %}
                                                                            {% sw_thumbnails 'cms-image-thumbnails' with {
                                                                                media: child.category.media,
                                                                                attributes: {
                                                                                    'alt': (child.category.media.alt ?: child.category.translated.name),
                                                                                    'title': (child.category.media.title ?: child.category.translated.name),
                                                                                    'height': '130',
                                                                                    'width': '180'
                                                                                }
                                                                            } %}
                                                                            {# <img class="img-fluid" src="{{ child.category.media.url }}" alt="{{ child.category.translated.name }}" /> #}
                                                                        {% endblock %}
                                                                    </div>
                                                                {% endif %}
                                                                {% block layout_subnavigation_categories_link_name %}
                                                                    <figcaption>
                                                                        <h2>
                                                                            {{ child.category.translated.name }}
                                                                        </h2>
                                                                    </figcaption>
                                                                {% endblock %}
                                                            </a>
                                                        {% endblock %}
                                                    </figure>
                                                </div>
                                            {% endif %}
                                        {% endblock %}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                {% endif %}
                {% block layout_navigation_categories_recoursion %}
                    {% if level < navigationMaxDepth %}
                        {% sw_include '@Storefront/storefront/layout/navigation/bst-subcategories.html.twig' with {
                            navigationTree: item.children,
                            activeResult: activeResult,
                            displayImages: displayImages,
                            useNeighbours: useNeighbours,
                            level: level + 1
                        } only %}
                    {% endif %}
                {% endblock %}
            {% endfor %}
        {% endif %}
    {% endblock %}
{% endblock %}
