{% sw_extends '@Storefront/storefront/element/cms-element-image-gallery.html.twig' %}

{% if element.fieldConfig is defined and element.data is defined %}
    {% set disableZoom = (sliderConfig.festtemaslider == true) ? true : false %}
{% endif %}

{% block element_image_gallery_inner_zoom_modal_action_buttons %}    
    {% if disableZoom %}
        <div class="zoom-modal-actions btn-group"
                role="group"
                aria-label="zoom actions">

            {% block element_image_gallery_inner_zoom_modal_action_zoom_out %}
                <button class="btn btn-light image-zoom-btn js-image-zoom-out">
                    {% block element_image_gallery_inner_zoom_modal_action_zoom_out_icon %}
                        {% sw_icon 'minus-circle' %}
                    {% endblock %}
                </button>
            {% endblock %}

            {% block element_image_gallery_inner_zoom_modal_action_zoom_reset %}
                <button class="btn btn-light image-zoom-btn js-image-zoom-reset">
                    {% block element_image_gallery_inner_zoom_modal_action_zoom_reset_icon %}
                        {% sw_icon 'screen-minimize' %}
                    {% endblock %}
                </button>
            {% endblock %}

            {% block element_image_gallery_inner_zoom_modal_action_zoom_in %}
                <button class="btn btn-light image-zoom-btn js-image-zoom-in">
                    {% block element_image_gallery_inner_zoom_modal_action_zoom_in_icon %}
                        {% sw_icon 'plus-circle' %}
                    {% endblock %}
                </button>
            {% endblock %}
        </div>
    {% endif %}
{% endblock %}
