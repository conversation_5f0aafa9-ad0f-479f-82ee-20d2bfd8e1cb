
{% block element_text %}
    {% set config = element.fieldConfig.elements %}

    <div class="cms-element-{{ element.type }}{% if config.verticalAlign.value %} has-vertical-alignment{% endif %}">
        {% block element_text_alignment %}
            {% if config.verticalAlign.value %}
                <div class="cms-element-alignment{% if config.verticalAlign.value == "center" %} align-self-center{% elseif config.verticalAlign.value == "flex-end" %} align-self-end{% else %} align-self-start{% endif %}">
            {% endif %}
                {% block element_text_inner %}
                    <div class="collapsible-content">
                        {{ element.data.content|raw }}
                    </div>
                {% endblock %}
            {% if config.verticalAlign.value %}
                </div>
            {% endif %}
        {% endblock %}
        <button class="read-less" onclick="(function(){ let collapsibleContent = document.querySelector('.collapsible-content');
        collapsibleContent.classList.toggle('open'); })()">{{ "festtemaTheme.category.readMore"|trans|sw_sanitize }}/{{ "festtemaTheme.category.readLess"|trans|sw_sanitize }}</button>
    </div>
{% endblock %}
