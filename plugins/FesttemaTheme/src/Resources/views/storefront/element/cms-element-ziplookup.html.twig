
{% block element_ziplookup %}
    <div class="cms-element-{{ element.type }} col-12">
        <div id="ziplookup" data-ziplookup>
            <h3>{{ "ziplookup.topText"|trans|sw_sanitize }}</h3>
            <div class="inner">
                <input type="number" class="zipcode" placeholder="{{ "ziplookup.placeholder"|trans|sw_sanitize }}">
                <button class="button search-zipcode js-search-zipcode">
                {{ "ziplookup.btnText"|trans|sw_sanitize }}</button>
                <div class="deliverytime">
                    <h4>
                    {{ "ziplookup.deliveryText"|trans|sw_sanitize }}
                    <span class="deliveryzip" data-no-information="{{ "ziplookup.noInfoText"|trans|sw_sanitize }}"></span> {{ "ziplookup.erText"|trans|sw_sanitize }}
                    </h4>
                    <span class="time" data-days="{{ "ziplookup.daysText"|trans|sw_sanitize }}"></span>
                    <input type="hidden" value="{{ app.request.locale }}" class="app-locale" />
                </div>
                <div class="ziplookup-bottom">
                    <p>
                        <em>{{ "ziplookup.bottomText"|trans|sw_sanitize }}</em>
                    </p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
