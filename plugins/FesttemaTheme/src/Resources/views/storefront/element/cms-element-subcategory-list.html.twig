
{% block element_subcategory_list %}
    <div class="cms-element-{{ element.type }}">
        {% set categories = page.header.navigation.tree %}
        {% set current = page.header.navigation.active %}
        
        {% if (current.childCount > 0) %}
            {% macro subCategories(categories, current) %}
                {% for category in categories %}
                    {% if (category.category.id in current.id) or (category.category.id in current.path) %}
                        {% if category.category.id is same as(current.id) %}
                            <div class="category-image-gallery">
                                {% sw_include '@Storefront/storefront/layout/category/subcategory-list.html.twig' with {
                                    subCat: category.children
                                } only %}
                            </div>
                        {% endif %}
                        {{ _self.subCategories(category.children, current) }}
                    {% endif %}
                {% endfor %}
            {% endmacro %}
            {{ _self.subCategories(categories, current) }}
        {% endif %}        
    </div>
{% endblock %}
