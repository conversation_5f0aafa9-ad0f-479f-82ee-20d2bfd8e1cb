{% block cms_form_contact %}
    <form action="{{ path(action) }}"
          method="post"
          data-form-validation="true">

        <div class="form-content">
        <div class="row g-2">
            {% block cms_form_contact_select_salutation %}
                {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-select-salutation.html.twig'
                    with {
                    additionalClass: 'col-md-12 d-none',
                    required: false
                }
                %}
            {% endblock %}

            {% block cms_form_contact_input_first_name %}
                {% set firstNameLabel = (isB2bcontactform)? 'bs-common-changes.company-name': 'account.personalFirstNameLabel' %}
                {% set firstNamePlaceholder = (isB2bcontactform)? 'bs-common-changes.company-name-placeholder': 'account.personalFirstNamePlaceholder' %}
                {% set firstNameFieldRequired = config('core.basicInformation.firstNameFieldRequired') == true %}
                {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-input.html.twig'
                    with {
                    fieldName: 'firstName',
                    required: firstNameFieldRequired,
                    additionalClass: 'col-md-3',
                    label: firstNameLabel,
                    placeholder: firstNamePlaceholder
                }
                %}
            {% endblock %}

            {% block cms_form_contact_input_last_name %}
                {% set lastNameLabel = (isB2bcontactform)? 'bs-common-changes.contact-person': 'account.personalLastNameLabel' %}
                {% set lastNamePlaceholder = (isB2bcontactform)? 'bs-common-changes.contact-person-placeholder': 'account.personalLastNamePlaceholder' %}
                {% set lastNameFieldRequired = config('core.basicInformation.lastNameFieldRequired') == true %}
                {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-input.html.twig'
                    with {
                    fieldName: 'lastName',
                    required: lastNameFieldRequired,
                    additionalClass: 'col-md-3',
                    label: lastNameLabel,
                    placeholder: lastNamePlaceholder
                }
                %}
            {% endblock %}

            {% block cms_form_contact_input_email %}
                {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-input.html.twig'
                    with {
                    fieldName: 'email',
                    type: 'email',
                    required: true,
                    additionalClass: 'col-md-3',
                    label: 'account.loginMailLabel',
                    placeholder: 'account.loginMailPlaceholder'
                }
                %}
            {% endblock %}
        </div>

        <div class="row g-2 d-none">
            {% block cms_form_contact_input_phome %}
                {% set phoneNumberFieldRequired = config('core.basicInformation.phoneNumberFieldRequired') == true %}
                {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-input.html.twig'
                    with {
                    fieldName: 'phone',
                    required: phoneNumberFieldRequired,
                    additionalClass: 'col-md-6',
                    label: 'account.personalPhoneLabel',
                    placeholder: 'account.personalPhonePlaceholder'
                }
                %}
            {% endblock %}
        </div>

        <div class="row g-2 d-none">
            {% block cms_form_contact_input_subject %}
                {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-subject-input.html.twig'
                    with {
                    required: true,
                    fieldName: 'subject',
                    additionalClass: 'col-12',
                    label: 'contact.subjectLabel',
                    placeholder: 'contact.subjectPlaceholder'
                }
                %}
            {% endblock %}
        </div>

        <div class="row g-2">
            {% block cms_form_contact_comment_textarea %}
                {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-textarea.html.twig'
                    with {
                    rows: 5,
                    required: true,
                    fieldName: 'comment',
                    additionalClass: 'col-12',
                    label: 'contact.commentLabel',
                    placeholder: 'contact.commentPlaceholder'
                }
                %}
            {% endblock %}
        </div>

        {% block cms_form_contact_required_fields_info %}
            {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-info-required.html.twig' %}
        {% endblock %}

        {% block cms_form_contact_captcha %}
            {% sw_include '@Storefront/storefront/component/captcha/base.html.twig' with { additionalClass : 'col-md-12' } %}
        {% endblock %}

        {% block cms_form_contact_privacy %}
            {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-privacy.html.twig'
                with { 'requiresTermsOfService': false }
            %}
        {% endblock %}

        {% block cms_form_contact_submit %}
            {% sw_include '@Storefront/storefront/element/cms-element-form/form-components/cms-element-form-submit.html.twig' %}
        {% endblock %}
        </div>

        {% block cms_form_contact_hidden_fields %}
            <div class="form-hidden-fields">
                {% if page.navigationId and page.entityName %}
                    <input type="hidden" name="navigationId" value="{{ page.navigationId }}">
                    <input type="hidden" name="entityName" value="{{ page.entityName }}">
                {% else %}
                    <input type="hidden" name="navigationId" value="{{ page.header.navigation.active.id }}">
                {% endif %}

                <input type="hidden" name="slotId" value="{{ element.id }}">

                <input type="submit" class="submit--hidden d-none">
            </div>
        {% endblock %}
    </form>
{% endblock %}
