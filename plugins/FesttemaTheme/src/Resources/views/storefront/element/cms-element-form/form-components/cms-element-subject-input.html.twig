{% block cms_element_form_input %}
    <div class="form-group {{ additionalClass }}">
        {% block cms_element_form_input_label %}
            <label class="form-label" for="form-{{ fieldName }}">
                {{- label|trans -}}{% if required %} {{- "general.required"|trans -}}{% endif %}
            </label>
        {% endblock %}

        {% block cms_element_form_input_input %}
            <input name="{{ fieldName }}"
                   type="{{ type ?? 'text'}}"
                   id="form-{{ fieldName }}"
                   value="{{ data.get( fieldName ) ?? 'Contact Form' }}"
                   placeholder="{{ placeholder|trans }}"
                   {% if required %}required="required"{% endif %}
                   class="form-control{% if formViolations.getViolations( '/' ~ fieldName ) %} is-invalid{% endif %}">fefwefwef

            {% if formViolations.getViolations( '/' ~ fieldName ) is not empty %}
                {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' with {
                    violationPath: ('/' ~ fieldName)
                } %}
            {% endif %}
        {% endblock %}
    </div>
{% endblock %}
