
{% if (page.header.navigation.active.childCount > 0) %}
    <div class="cms-element-{{ element.type }} subcategory-slider-list">
        {% set navigationResult1 = page.header.navigation.tree %}
        {% set activeResult1 = page.header.navigation.active %}
        {% set config = element.fieldConfig.elements %}
        {% set displayImages = config.showImages.value %}
        {% set useNeighbours = config.useNeighbours.value %}
        {% set autoWidth = config.autoWidth.value %}
        <div><h2 class="fs-sub-category-title">{{ "festtemaTheme.subCategorySlider.title"|trans|sw_sanitize }}</h2></div>
        <div class="subcategory-navigation-box category-image-gallery">
            <section class="team">
                <div class="grid">
                    {% sw_include '@Storefront/storefront/layout/navigation/bst-subcategories.html.twig' with {
                        navigationTree: page.header.navigation.tree,
                        activeResult: page.header.navigation.active,
                        displayImages: displayImages,
                        useNeighbours: useNeighbours,
                        autoWidth: autoWidth
                    } only %}
                </div>
            </section>
        </div>
    </div>
{% endif %}
