<?php declare(strict_types=1);

namespace FesttemaTheme\Core\Content\Cms\DataResolver\Element;

use DOMDocument;
use Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotEntity;
use Shopware\Core\Content\Cms\DataResolver\CriteriaCollection;
use Shopware\Core\Content\Cms\DataResolver\Element\AbstractCmsElementResolver;
use Shopware\Core\Content\Cms\DataResolver\Element\ElementDataCollection;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\EntityResolverContext;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\ResolverContext;
use Shopware\Core\Content\Cms\SalesChannel\Struct\TextStruct;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Util\HtmlSanitizer;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Package('buyers-experience')]
class TextCmsElementResolver extends AbstractCmsElementResolver
{
    private HtmlSanitizer $sanitizer;

    private TranslatorInterface $translator;

    public function __construct(HtmlSanitizer $sanitizer, TranslatorInterface $translator)
    {
        $this->sanitizer = $sanitizer;
        $this->translator = $translator;
    }

    public function getType(): string
    {
        return 'text';
    }

    public function collect(CmsSlotEntity $slot, ResolverContext $resolverContext): ?CriteriaCollection
    {
        return null;
    }

    public function enrich(CmsSlotEntity $slot, ResolverContext $resolverContext, ElementDataCollection $result): void
    {
        $text = new TextStruct();
        $slot->setData($text);
        $config = $slot->getFieldConfig()->get('content');
        if ($config === null) {
            return;
        }
        

        $content = null;

        if ($config->isMapped() && $resolverContext instanceof EntityResolverContext) {
            $content = $this->resolveEntityValueToString($resolverContext->getEntity(), $config->getStringValue(), $resolverContext);
        }

        if ($config->isStatic()) {
            if ($resolverContext instanceof EntityResolverContext) {
                $content = (string) $this->resolveEntityValues($resolverContext, $config->getStringValue());
            } else {
                $content = $config->getStringValue();
            }
        }

        if($config->getValue() == 'category.description' && !empty($content)){
            $string = strip_tags($content);
            $finalText = $content;
            $h1 = '';
            if (strlen($string) > 800) {
                preg_match('~<h1>([^{]*)</h1>~i', $content, $match);
                if(isset($match[1])){
                    $h1 = $match[1];
                    $content = str_replace('<h1>'.$h1.'</h1>', '', $content);
                }
                $stringCut = substr($content, 0, 800);
                $h1 = ($h1!='')? $h1: ''; 
                $doc = new DOMDocument();
                $doc->recover = true;
                $doc->strictErrorChecking = false;
                @$doc->loadHTML('<?xml encoding="utf-8"?>' .$stringCut);
                $finalText = $doc->saveHTML();
                $finalText = '<h1>'.$h1.'</h1><div class="js-less-content">'.$finalText.'<button class="js-read-more-link read-more">'.$this->translator->trans('festtemaTheme.category.readMore').'</button></div><div class="js-more-content" style="display:none">'.$content.'<button class="js-read-less-link read-less">'.$this->translator->trans('festtemaTheme.category.readLess').'</button></div>';
                $content = $finalText;
            }
        }
        
        if ($content !== null) {
            $text->setContent($this->sanitizer->sanitize($content));
        }
    }
}
