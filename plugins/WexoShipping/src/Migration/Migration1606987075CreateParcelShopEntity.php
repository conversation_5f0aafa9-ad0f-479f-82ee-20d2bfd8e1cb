<?php declare(strict_types=1);

namespace Wexo\Shipping\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * Class Migration1606987075CreateParcelShopEntity
 * @package Wexo\Shipping\Migration
 */
class Migration1606987075CreateParcelShopEntity extends MigrationStep
{
    /**
     * @return int
     */
    public function getCreationTimestamp(): int
    {
        return 1_606_987_075;
    }

    /**
     * @param Connection $connection
     * @throws \Doctrine\DBAL\DBALException
     */
    public function update(Connection $connection): void
    {
        $connection->executeUpdate('
            CREATE TABLE IF NOT EXISTS `wexo_parcel_shop` (
                `id` BINARY(16) NOT NULL,
                `order_id` BINARY(16) NOT NULL,
                `provider_id` VARCHAR(255) NOT NULL,
                `type_key` VARCHAR(255) NOT NULL,
                `latitude` FLOAT NOT NULL,
                `longitude` FLOAT NOT NULL,
                `name` VARCHA<PERSON>(255) NOT NULL,
                `street` VARCHAR(255) NOT NULL,
                `zip_code` VARCHAR(255) NOT NULL,
                `country_code` VARCHAR(255) NOT NULL,
                `city` VARCHAR(255) NOT NULL,
                `note` LONGTEXT NULL,
                `telephone` VARCHAR(255) NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3) NULL,
                PRIMARY KEY (`id`),
                CONSTRAINT `fk.wexo_shipping.order_id` FOREIGN KEY (`order_id`)
                    REFERENCES `order` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    /**
     * @param Connection $connection
     */
    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}
