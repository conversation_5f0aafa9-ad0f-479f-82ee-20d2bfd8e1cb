<?php declare(strict_types=1);

namespace Wexo\Shipping\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * Class Migration1611309499ChangeDeadlineFormatToPerWeekday
 * @package Wexo\Shipping\Migration
 */
class Migration1611309499ChangeDeadlineFormatToPerWeekday extends MigrationStep
{
    /**
     * @return int
     */
    public function getCreationTimestamp(): int
    {
        return 1_611_309_499;
    }

    /**
     * @param Connection $connection
     * @throws \Doctrine\DBAL\DBALException
     */
    public function update(Connection $connection): void
    {
        $connection->executeUpdate("
            ALTER TABLE `wexo_shipping_method_deadline` DROP COLUMN `deadline`;
	        ALTER TABLE `wexo_shipping_method_deadline` CHANGE `excluded_weekdays` `deadlines` JSON NOT NULL;
        ");
    }

    /**
     * @param Connection $connection
     */
    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}
