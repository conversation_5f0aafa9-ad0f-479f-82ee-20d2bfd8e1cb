<?php declare(strict_types=1);

namespace Wexo\Shipping\Service;

use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\CartBehavior;
use Shopware\Core\Checkout\Cart\Delivery\DeliveryCalculator;
use Shopware\Core\Checkout\Cart\Delivery\DeliveryProcessor;
use Shopware\Core\Checkout\Cart\Delivery\Struct\DeliveryCollection;
use Shopware\Core\Checkout\Cart\LineItem\CartDataCollection;
use Shopware\Core\Checkout\Cart\Price\Struct\CalculatedPrice;
use Shopware\Core\Checkout\Cart\Price\Struct\PriceCollection as CartPriceCollection;
use Shopware\Core\Checkout\Promotion\Cart\PromotionDeliveryProcessor;
use Shopware\Core\Checkout\Shipping\ShippingMethodEntity;
use Shopware\Core\System\SalesChannel\SalesChannelContext;

/**
 * Class ShippingPriceCalculator
 * @package Wexo\Shipping\Service
 * @see DeliveryCalculator
 */
class ShippingPriceCalculator
{
    public function __construct(
        private readonly DeliveryProcessor $deliveryProcessor,
        private readonly PromotionDeliveryProcessor $promotionDeliveryProcessor,
    ) {
    }

    public function getShippingCostsForCart(
        Cart $cart,
        ShippingMethodEntity $shippingMethod,
        SalesChannelContext $context
    ): CalculatedPrice {
        /* Set correct shipping method */
        $this->updateCartDataShippingMethod($cart->getData(), $shippingMethod);
        $this->buildCartDeliveries($cart, $context);

        return $cart->getDeliveries()->getShippingCosts()->sum();
    }

    private function buildCartDeliveries(Cart $cart, SalesChannelContext $channelContext): void
    {
        /* Remove selected user deliveries, so we can calculate the input shipping method. */
        $cart->setDeliveries(new DeliveryCollection());

        /* build deliveries */
        $this->deliveryProcessor->process(
            $cart->getData(),
            $cart,
            $cart,
            $channelContext,
            new CartBehavior()
        );

        /* add any deliveries promotions */
        $this->promotionDeliveryProcessor->process(
            $cart->getData(),
            $cart,
            $cart,
            $channelContext,
            new CartBehavior()
        );
    }

    public function createSalesChannelContextWithShippingMethod(
        SalesChannelContext $salesChannelContext,
        ShippingMethodEntity $shippingMethodEntity
    ): SalesChannelContext {
        return new SalesChannelContext(
            $salesChannelContext->getContext(),
            $salesChannelContext->getToken(),
            $salesChannelContext->getDomainId(),
            $salesChannelContext->getSalesChannel(),
            $salesChannelContext->getCurrency(),
            $salesChannelContext->getCurrentCustomerGroup(),
            $salesChannelContext->getTaxRules(),
            $salesChannelContext->getPaymentMethod(),
            $shippingMethodEntity,
            $salesChannelContext->getShippingLocation(),
            $salesChannelContext->getCustomer(),
            $salesChannelContext->getItemRounding(),
            $salesChannelContext->getTotalRounding(),
            $salesChannelContext->getRuleIds()
        );
    }

    public function updateCartDataShippingMethod(
        CartDataCollection $dataCollection,
        ShippingMethodEntity $shippingMethod
    ): CartDataCollection {
        $shippingMethodKey = DeliveryProcessor::buildKey($shippingMethod->getId());

        if (! isset($dataCollection->getElements()[$shippingMethodKey])) {
            foreach ($dataCollection->getElements() as $dataKey => $dataValue) {
                if (str_contains((string) $dataKey, "shipping-method")) {
                    /* remove last calculated shipping from cloned cart */
                    $dataCollection->remove($dataKey);
                }
            }
            $dataCollection->set($shippingMethodKey, $shippingMethod);
        }

        return $dataCollection;
    }
}
