<?php declare(strict_types=1);

namespace Wexo\Shipping\Struct;

use Shopware\Core\Framework\Struct\Struct;
use Wexo\Shipping\Core\Content\ParcelShop\ParcelShopEntity;

class ParcelShopConfiguration extends Struct
{
    public array $shippingMethods;
    public ?ParcelShopEntity $selectedParcelShop;

    public function __construct(array $shippingMethods, ?ParcelShopEntity $selectedParcelShop = null)
    {
        $this->shippingMethods = $shippingMethods;
        $this->selectedParcelShop = $selectedParcelShop;
    }
}
