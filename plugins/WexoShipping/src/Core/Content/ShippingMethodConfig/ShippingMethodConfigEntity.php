<?php declare(strict_types=1);

namespace Wexo\Shipping\Core\Content\ShippingMethodConfig;

use Shopware\Core\Checkout\Shipping\ShippingMethodEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;

class ShippingMethodConfigEntity extends Entity
{
    use EntityIdTrait;

    protected string $shippingMethodId;
    protected ?ShippingMethodEntity $shippingMethod = null;
    protected bool $shippingCommentRequired;

    public function getShippingMethodId(): string
    {
        return $this->shippingMethodId;
    }

    public function setShippingMethodId(string $shippingMethodId): void
    {
        $this->shippingMethodId = $shippingMethodId;
    }

    public function getShippingMethod(): ?ShippingMethodEntity
    {
        return $this->shippingMethod;
    }

    public function setShippingMethod(?ShippingMethodEntity $shippingMethod): void
    {
        $this->shippingMethod = $shippingMethod;
    }

    public function getShippingCommentRequired(): bool
    {
        return $this->shippingCommentRequired;
    }

    public function setShippingCommentRequired(bool $shippingCommentRequired): void
    {
        $this->shippingCommentRequired = $shippingCommentRequired;
    }
}
