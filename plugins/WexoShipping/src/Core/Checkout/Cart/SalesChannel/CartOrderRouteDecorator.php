<?php declare(strict_types=1);

namespace Wexo\Shipping\Core\Checkout\Cart\SalesChannel;

use Shopware\Core\Checkout\Cart\AbstractCartPersister;
use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\SalesChannel\AbstractCartOrderRoute;
use Shopware\Core\Checkout\Cart\SalesChannel\CartOrderRouteResponse;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Wexo\Shipping\Core\Content\Cart\CartExtension;

class CartOrderRouteDecorator extends AbstractCartOrderRoute
{
    public function __construct(
        private readonly AbstractCartOrderRoute $decorated,
        private readonly AbstractCartPersister $cartPersister
    ) {
    }

    public function getDecorated(): AbstractCartOrderRoute
    {
        return $this->decorated;
    }

    public function order(Cart $cart, SalesChannelContext $context, RequestDataBag $data): CartOrderRouteResponse
    {
        $shippingComment = $data->get('shippingComment') ?: null;
        if ($shippingComment) {
            if (!$cart->hasExtension(CartExtension::KEY)) {
                $cart->addExtension(CartExtension::KEY, new CartExtension());
            }
            $cart->getExtension(CartExtension::KEY)->shippingComment = $shippingComment;
            $this->cartPersister->save($cart, $context);
        }
        return $this->decorated->order($cart, $context, $data);
    }
}
