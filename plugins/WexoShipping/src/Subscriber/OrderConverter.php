<?php declare(strict_types=1);

namespace Wexo\Shipping\Subscriber;

use Shopware\Core\Checkout\Cart\Order\CartConvertedEvent;
use Shopware\Core\Checkout\Cart\Order\Transformer\AddressTransformer;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wexo\Shipping\Core\Checkout\Order\Exception\MissingShippingCommentException;
use Wexo\Shipping\Core\Content\Cart\CartExtension;
use Wexo\Shipping\Core\Content\Order\OrderExtension;

class OrderConverter implements EventSubscriberInterface
{
    public function __construct(private readonly SystemConfigService $systemConfigService)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CartConvertedEvent::class => 'addExtensionDataToOrder'
        ];
    }

    public function addExtensionDataToOrder(CartConvertedEvent $event): void
    {
        if (!$event->getCart()->hasExtension(CartExtension::KEY)) {
            return;
        }
        $convertedCart = $event->getConvertedCart();
        /** @var CartExtension $cartExtension */
        $cartExtension = $event->getCart()->getExtension(CartExtension::KEY);
        if ($cartExtension->parcelShop) {
            if ($this->systemConfigService->getBool('WexoShipping.config.setParcelShopAsShippingAddress')) {
                foreach ($convertedCart['deliveries'] as $index => $delivery) {
                    $delivery['shippingOrderAddress']['company'] = $cartExtension->parcelShop->getName();
                    $delivery['shippingOrderAddress']['street'] = $cartExtension->parcelShop->getStreet();
                    $delivery['shippingOrderAddress']['city'] = $cartExtension->parcelShop->getCity();
                    $delivery['shippingOrderAddress']['zipcode'] = $cartExtension->parcelShop->getZipCode();
                    $convertedCart['deliveries'][$index] = $delivery;
                }
            }
            if (!isset($convertedCart['addresses'])) {
                $activeBillingAddress = $event->getSalesChannelContext()->getCustomer()->getActiveBillingAddress();
                $billingAddress = AddressTransformer::transform($activeBillingAddress);
                $convertedCart['addresses'] = [$billingAddress];
                $convertedCart['billingAddressId'] = $billingAddress['id'];
            }
            $convertedCart[OrderExtension::PARCEL_SHOP_PROPERTY_NAME] = [
                'providerId' => $cartExtension->parcelShop->getProviderId(),
                'typeKey' => $cartExtension->parcelShop->getTypeKey(),
                'latitude' => $cartExtension->parcelShop->getLatitude(),
                'longitude' => $cartExtension->parcelShop->getLongitude(),
                'name' => $cartExtension->parcelShop->getName(),
                'street' => $cartExtension->parcelShop->getStreet(),
                'zipCode' => $cartExtension->parcelShop->getZipCode(),
                'countryCode' => $cartExtension->parcelShop->getCountryCode(),
                'city' => $cartExtension->parcelShop->getCity(),
                'note' => $cartExtension->parcelShop->getNote(),
                'telephone' => $cartExtension->parcelShop->getTelephone(),
            ];
        }
        $shippingConfig = $event->getSalesChannelContext()
            ->getShippingMethod()
            ->getExtension('shippingMethodConfig');
        if ($cartExtension->shippingComment) {
            $convertedCart[OrderExtension::SHIPPING_COMMENT_PROPERTY_NAME] = [
                'comment' => $cartExtension->shippingComment
            ];
        } elseif ($shippingConfig?->getShippingCommentRequired()) {
            throw new MissingShippingCommentException();
        }
        $event->setConvertedCart($convertedCart);
    }
}
