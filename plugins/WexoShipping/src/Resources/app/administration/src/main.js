import './page/sw-settings-shipping-detail/';
import './component/sw-order-detail-details/';
import ShippingMethodTypeService from './services/shippingMethodType.service';
import deDE from './module/wexo-shipping-type/snippet/de-DE.json';
import enGB from './module/wexo-shipping-type/snippet/en-GB.json';
import daDK from './module/wexo-shipping-type/snippet/da-DK.json';

Shopware.Locale.extend('de-DE', deDE);
Shopware.Locale.extend('en-GB', enGB);
Shopware.Locale.extend('da-DK', daDK);

const { Application } = Shopware;
Application.addServiceProvider('ShippingMethodTypeService', container => {
    const initContainer = Application.getContainer('init');
    return new ShippingMethodTypeService(initContainer.httpClient, container.loginService);
});