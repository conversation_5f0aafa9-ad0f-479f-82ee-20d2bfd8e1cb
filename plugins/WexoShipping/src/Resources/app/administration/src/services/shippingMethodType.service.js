const { ApiService } = Shopware.Classes;

export default class ShippingMethodTypeService extends ApiService {
    constructor(httpClient, loginService, apiEndpoint = 'wexo/shipping-types') {
        super(httpClient, loginService, apiEndpoint);
    }

    getTypes() {
        return this.httpClient.get(
            this.getApiBasePath(), {
                headers: this.getBasicHeaders()
            }
        ).then(response => {
            return ApiService.handleResponse(response);
        });
    }
}
