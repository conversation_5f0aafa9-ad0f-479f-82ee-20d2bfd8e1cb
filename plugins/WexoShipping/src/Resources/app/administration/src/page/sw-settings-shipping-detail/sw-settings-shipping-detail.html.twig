{% block sw_settings_shipping_detail_top_ruleshippingPriceStore %}
    {% parent() %}

    <sw-card v-if="shippingMethodConfig"
         class="sw-settings-shipping-detail__wexo_shipping_method_config"
         :title="$tc('wexoShipping.generalConfig')">
        <sw-checkbox-field :label="$tc('wexoShipping.shippingCommentRequired')"
                           :helpText="$tc('wexoShipping.shippingCommentRequiredHelp')"
                           v-model="shippingMethodConfig.shippingCommentRequired">
        </sw-checkbox-field>
    </sw-card>

    <sw-card v-if="shippingMethodDeadline && feature.isActive('FEATURE_WEXOSHIPPING_1')"
             class="sw-settings-shipping-detail__wexo_shipping_method_deadline"
             :title="$tc('wexoShipping.deadline.title')">
        {% block sw_settings_shipping_deadline %}
            <sw-container columns="1fr 1fr 1fr 1fr" gap="16px">
                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Monday"
                               :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                               :label="$tc('wexoShipping.deadline.days.monday')"
                               dateType="time">
                </sw-datepicker>
                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Tuesday"
                               :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                               :label="$tc('wexoShipping.deadline.days.tuesday')"
                               dateType="time">
                </sw-datepicker>
                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Wednesday"
                               :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                               :label="$tc('wexoShipping.deadline.days.wednesday')"
                               dateType="time">
                </sw-datepicker>
                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Thursday"
                               :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                               :label="$tc('wexoShipping.deadline.days.thursday')"
                               dateType="time">
                </sw-datepicker>
                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Friday"
                               :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                               :label="$tc('wexoShipping.deadline.days.friday')"
                               dateType="time">
                </sw-datepicker>
                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Saturday"
                               :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                               :label="$tc('wexoShipping.deadline.days.saturday')"
                               dateType="time">
                </sw-datepicker>
                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Sunday"
                               :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                               :label="$tc('wexoShipping.deadline.days.sunday')"
                               dateType="time">
                </sw-datepicker>
            </sw-container>
            <sw-container v-for="(excludedDate, index) in shippingMethodDeadline.excludedDates" columns="1fr 24px" gap="16px" :key="index">
                <sw-datepicker :config="{dateFormat: 'Y-m-d'}"
                               v-model="excludedDate.date"
                               :label="$tc('wexoShipping.deadline.exception')"
                               dateType="date">
                </sw-datepicker>
                <sw-icon style="margin-top: 36px; cursor: pointer;" name="default-basic-x-line" @click="() => deleteDeadlineException(index)"></sw-icon>
            </sw-container>
            <sw-button variant="primary"
                       :block="true"
                       @click="addDeadlineException">
                {{ $tc('wexoShipping.deadline.addDeadlineException') }}
            </sw-button>
        {% endblock %}
    </sw-card>

    <sw-card v-if="shippingMethodType"
             class="sw-settings-shipping-detail__wexo_shipping_method_type"
             :title="$tc('wexoShipping.parcelShop')">
        {% block sw_settings_shipping_parcel_shop_provider %}
            <sw-single-select v-model="shippingMethodType.typeKey"
                              :isLoading="loadingShippingTypes"
                              :disabled="loadingShippingTypes || !acl.can('shipping.editor')"
                              :options="shippingMethodTypes"
                              :label="$tc('wexoShipping.provider.choose')">
            </sw-single-select>
        {% endblock %}
        {% block sw_settings_shipping_ordering_penalties %}
            <template v-if="shippingMethodType.typeKey && feature.isActive('FEATURE_WEXOSHIPPING_1')">
                <sw-container v-for="(penalty, index) in shippingMethodType.penalties" columns="1fr 1fr 1fr 24px" gap="16px" :key="index">
                    <sw-datepicker :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                                   v-model="penalty.from"
                                   :label="$tc('wexoShipping.penalty.from')"
                                   :required="true"
                                   dateType="time">
                    </sw-datepicker>
                    <sw-datepicker :config="{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}"
                                   v-model="penalty.to"
                                   :label="$tc('wexoShipping.penalty.to')"
                                   :required="true"
                                   dateType="time">
                    </sw-datepicker>
                    <sw-number-field :label="$tc('wexoShipping.penalty.penaltyAmount')"
                                     v-model="penalty.penalty"
                                     :min="0"
                                     :allowEmpty="false"
                                     :required="true"
                                     placeholder="m">
                    </sw-number-field>
                    <sw-icon style="margin-top: 36px; cursor: pointer;" name="default-basic-x-line" @click="() => deletePenalty(index)"></sw-icon>
                </sw-container>
                <sw-button variant="primary"
                           :isLoading="loadingShippingTypes"
                           :block="true"
                           @click="addParcelShopPenalty">
                    {{ $tc('wexoShipping.penalty.addTimeSlot') }}
                </sw-button>
            </template>
        {% endblock %}
    </sw-card>
{% endblock %}

{% block sw_settings_shipping_detail_actions_save %}
    <sw-button-process
        class="sw-settings-shipping-method-detail__save-action"
        :isLoading="isProcessLoading"
        v-model="isSaveSuccessful"
        :disabled="isProcessLoading || !acl.can('shipping.editor')"
        variant="primary"
        v-tooltip.bottom="tooltipSave"
        @click.prevent="onSaveOverride"> <!-- Avoid overriding onSave directly to maximize compatibility -->
        {{ $tc('sw-settings-shipping.detail.buttonSave') }}
    </sw-button-process>
{% endblock %}
