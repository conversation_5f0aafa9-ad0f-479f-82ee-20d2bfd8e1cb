export default class ParcelShopMap {
    constructor(storeData, element) {
        this.storeData = storeData;
        this.el = element;

        this.map = L.map(this.el);
        this.markers = {};
        <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(this.map);

        if (this.storeData.parcelShops.length) {
            const parcelShop = this.storeData.parcelShops.find(
                parcelShop => parcelShop === this.storeData.selectedParcelShop
            ) || this.storeData.parcelShops[0];
            this.map.setView([parcelShop.latitude, parcelShop.longitude], 12)

            this.storeData.parcelShops.forEach(parcelShop => {
                this.createMarker(parcelShop);
            })
            this.map.invalidateSize();
        }
        const modal = this.el.closest(".modal");
        modal.addEventListener('shown.bs.modal', this.invalidateSize.bind(this));
        this.el.addEventListener('transitionend', this.invalidateSize.bind(this));
    }

    invalidateSize() {
        this.map && this.map.invalidateSize();
    }

    createMarker(parcelShop) {
        const icon = L.icon({
            iconUrl: this.storeData.options.images[parcelShop.typeKey],
            iconSize: [50, 30]
        });

        const marker = L.marker([parcelShop.latitude, parcelShop.longitude], {icon: icon})

        const popup = document.createElement('div');
        popup.classList.add('parcel-shop-marker');
        popup.appendChild(this.createName(parcelShop));
        popup.appendChild(this.createAddress(parcelShop));
        popup.appendChild(this.createOpeningHours(parcelShop));
        popup.appendChild(this.createButton(parcelShop));

        marker.bindPopup(popup);
        marker.addTo(this.map);
        const selected = this.storeData.selectedParcelShop === parcelShop;
        selected && marker._icon.classList.add('selected');
        this.markers[parcelShop.providerId] = marker;
    }

    createName(parcelShop) {
        const name =  document.createElement('div');
        name.classList.add('name')
        name.classList.add('parcel-shop-name');
        name.innerText = parcelShop.name;
        return name;
    }

    createAddress(parcelShop) {
        const address = document.createElement('div');
        address.classList.add('parcel-shop-address');
        address.innerText = parcelShop.street;
        return address;
    }

    createOpeningHours(parcelShop) {
        const openingHours = document.createElement('table');
        openingHours.classList.add('opening-hours')

        const openingHoursData = parcelShop.openingHours;

        openingHoursData.forEach(day => {
            const dayElementContainer = document.createElement('tr'),
                dayElement = document.createElement('td'),
                timeElement = document.createElement('td');

            dayElement.innerText = day['day'];
            timeElement.innerText = day['hours'];

            dayElementContainer.appendChild(dayElement);
            dayElementContainer.appendChild(timeElement);
            openingHours.appendChild(dayElementContainer);
        });
        return openingHours;
    }

    createButton(parcelShop) {
        const selected = this.storeData.selectedParcelShop === parcelShop;
        const wrapper = document.createElement('div');
        wrapper.classList.add('select-parcel-shop');
        const button = document.createElement('button')
        button.classList.add('btn');
        button.innerText = selected ?
            this.storeData.options.translations.selected :
            this.storeData.options.translations.select;
        button.disabled = selected;
        button.type = 'button';
        button.addEventListener('click', () => {
            this.storeData.setParcelShop(parcelShop);
        });
        wrapper.appendChild(button);
        return wrapper;
    }

    setParcelShop(parcelShop) {
        if (this.storeData.selectedParcelShop) {
            const marker = this.markers[this.storeData.selectedParcelShop.providerId];
            marker._icon.classList.remove('selected');
            const button = marker.getPopup()._content.querySelector('button');
            button.innerText = this.storeData.options.translations.select;
            button.disabled = false;
        }
        const marker = this.markers[parcelShop.providerId];
        this.map.setView([parcelShop.latitude, parcelShop.longitude], 14);
        marker._icon.classList.add('selected');
        marker.closePopup();
        const button = marker.getPopup()._content.querySelector('button');
        button.innerText = this.storeData.options.translations.selected;
        button.disabled = true;
    }

    scrollToParcelShop(parcelShop) {
        this.map.setView([parcelShop.latitude, parcelShop.longitude]);
    }

    show() {
        this.el.classList.add('show');
        this.invalidateSize();
    }

    hide() {
        this.el.classList.remove('show');
    }

    destroy() {
        this.map.remove();
    }
};
