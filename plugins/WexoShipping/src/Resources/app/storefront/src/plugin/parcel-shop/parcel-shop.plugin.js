import Plugin from 'src/plugin-system/plugin.class';
import DomAccess from 'src/helper/dom-access.helper';
import ParcelShopMap from "./parcel-shop-map.class";
import ParcelShopList from "./parcel-shop-list.class";
import ElementLoadingIndicatorUtil from 'src/utility/loading-indicator/element-loading-indicator.util';
import HttpClient from 'src/service/http-client.service';

import ParcelShopValidator from './validators/parcel-shop.validator';

const additionalShopAmount = 20;

export default class ParcelShopPlugin extends Plugin {
    /**
     * @type {{
     *     methodContainerSelector: string,
     *     searchButtonSelector: string,
     *     loadMoreButtonSelector: string,
     *     parcelShopIdInputSelector: string,
     *     currentParcelShop: Object,
     *     shippingCommentRequired: boolean,
     *     shippingCommentSelector: string,
     *     zipcode: string,
     *     typeKey: string,
     *     countryCode: string,
     *     street: string,
     *     amount: int,
     *     translations: Map<string, string>
     *     ?shippingMethodId: string
     *     ?shippingMethodIds: Map<string, string>
     * }}
     */
    static options = {
        apiUrl: '/wexo/parcel-shops',
        methodForm: '#changeShippingForm',
        methodContainerSelector: '.shipping-method',
        searchButtonSelector: '.search-parcel-shop',
        loadMoreButtonSelector: '.load-more-shops',
        parcelShopIdInputSelector: 'input[name=parcelShopId]',
        parcelShopStreetInputSelector: 'input[name=parcelShopStreet]',
        parcelShopZipCodeInputSelector: 'input[name=parcelShopZipCode]',
        currentParcelShop: null,
        shippingCommentRequired: false,
        shippingCommentSelector: '[name="shippingComment"]'
    };

    init() {
        // This is to keep track of the selected parcel shop when the page loads
        this.currentParcelShop = this.options.currentParcelShop;

        //Init HTML elements
        this._initElements();

        //Init parcel shop data
        this._initData();

        //Init events
        this._registerListeners();
    };

    _initElements() {
        this.shippingMethodContainer = this.el.closest(this.options.methodContainerSelector);

        this.searchButton = DomAccess.querySelector(this.el, this.options.searchButtonSelector);

        this.shippingMethodForm = DomAccess.querySelector(document, this.options.methodForm);
        this.shippingMethodInputElement = DomAccess.querySelector(this.shippingMethodContainer, '.shipping-method-input');

        this.parcelShopIdElement = DomAccess.querySelector(this.el, this.options.parcelShopIdInputSelector);
        this.parcelShopStreetElement = DomAccess.querySelector(this.el, this.options.parcelShopStreetInputSelector);
        this.parcelShopZipCodeElement = DomAccess.querySelector(this.el, this.options.parcelShopZipCodeInputSelector);

        this.selectionElements = DomAccess.querySelectorAll(this.shippingMethodContainer, '.parcel-shop-selection');

        this.mapElement = DomAccess.querySelector(this.el, `[data-provider-map=${this.options.typeKey}]`);
        this.listElement = DomAccess.querySelector(this.el, `[data-provider-list=${this.options.typeKey}]`);

        this.loadMoreButton = DomAccess.querySelector(this.el, this.options.loadMoreButtonSelector);
        this._httpClient = new HttpClient();
    }

    _initData() {
        this.parcelShops = [];
        this.search();
    }

    _registerListeners() {
        this.shippingMethodForm.addEventListener('submit', this.validate.bind(this));
        this.searchButton.addEventListener('click', this.search.bind(this));
        this.loadMoreButton.addEventListener('click', this.loadMore.bind(this));

        // This is to prevent the shipping method being selected before a parcel shop has been chosen
        this.shippingMethodInputElement.addEventListener('click', e => {
            e.preventDefault();
        });

        this.el.querySelectorAll('input').forEach(element => {
            element.addEventListener('keypress', e => {
                // Check if enter is pressed
                if (e.which === 10 || e.which === 13) {
                    e.preventDefault();
                    this.searchButton.click();
                }
            })
        })

        DomAccess.querySelector(this.shippingMethodContainer, '.parcel-shop-save').addEventListener('click', this.onSave.bind(this));
    }

    search() {
        const zipcodeElement = this.el.querySelector('input[name=parcel-shop-zipcode]');
        const streetElement = this.el.querySelector('input[name=parcel-shop-street]');
        if (!streetElement.value && !zipcodeElement.value) {
            streetElement.value = this.options.street;
        }

        if (!zipcodeElement.value) {
            if (!this.options.zipcode) {
                return Promise.resolve();
            }
            zipcodeElement.value = this.options.zipcode;
            streetElement.value = this.options.street;
        }

        const selectedParcelShopId = this.parcelShopIdElement.value || null;

        this.startLoading();
        return new Promise((resolve, reject) => {
            this._httpClient.get(`${this.options.apiUrl}?typeKey=${this.options.typeKey}&zipCode=${zipcodeElement.value}&countryCode=${this.options.countryCode}&street=${streetElement.value}&amount=${this.options.amount}`, (response) => {
                try {
                    response = JSON.parse(response);
                } catch (e) {
                    response = {
                        error: 'Unkown error'
                    }
                }

                if (response.error) {
                    this.showMap(false);
                    this.listElement.innerHTML = '<p class="parcel-shop-error">' + response.error + '</p>';
                    return reject();
                }

                this.parcelShops = response;

                this.selectedParcelShop = this.parcelShops.find(parcelShop => parcelShop.providerId === selectedParcelShopId);

                this.initViews();
                resolve();
            })
        }).then(() => {
            this.stopLoading();
        }).catch(() => {
            this.stopLoading();
            this.loadMoreButton.disabled = true;
        });
    }

    async loadMore() {
        if (!this.parcelShops.length) {
            return;
        }

        this.options.amount += additionalShopAmount;
        await this.search();

        // If our collection of parcelShops length is not equal to the amount we want, we have all the parcelShops available.
        if (this.parcelShops.length < this.options.amount) {
            this.loadMoreButton.disabled = true;
        }
    }

    stopLoading() {
        ElementLoadingIndicatorUtil.remove(this.el);
        DomAccess.querySelectorAll(this.el, '.load-button').forEach(el => el.disabled = false);
    }

    startLoading() {
        ElementLoadingIndicatorUtil.create(this.el);
        DomAccess.querySelectorAll(this.el, '.load-button').forEach(el => el.disabled = true);
    }

    showMap(show = true) {
        if (!this.mapPlugin) {
            return;
        }

        if (show) {
            this.mapPlugin.show();
        } else {
            this.mapPlugin.hide();
        }
    }

    initViews() {
        if (this.mapPlugin) {
            this.mapPlugin.destroy();
        }

        this.mapPlugin = new ParcelShopMap(this, this.mapElement);
        this.listPlugin = new ParcelShopList(this, this.listElement);
    }

    setParcelShop(parcelShop) {
        this.mapPlugin.setParcelShop(parcelShop);
        this.listPlugin.setParcelShop(parcelShop);

        // Update selected
        this.selectedParcelShop = parcelShop;

        this.parcelShopIdElement.value = parcelShop.providerId;
        this.parcelShopStreetElement.value = parcelShop.street;
        this.parcelShopZipCodeElement.value = parcelShop.zipCode;

        this._handleUpdate(parcelShop);
    }

    _handleUpdate(parcelShop) {
        // Disable all parcelShop inputs to make sure only one value is submitted
        DomAccess.querySelectorAll(document, this.options.parcelShopIdInputSelector).forEach((ele) => {
            ele.disabled = !ele.isSameNode(this.parcelShopIdElement);
        });
        DomAccess.querySelectorAll(document, this.options.parcelShopStreetInputSelector).forEach((ele) => {
            ele.disabled = !ele.isSameNode(this.parcelShopStreetElement);
        });
        DomAccess.querySelectorAll(document, this.options.parcelShopZipCodeInputSelector).forEach((ele) => {
            ele.disabled = !ele.isSameNode(this.parcelShopZipCodeElement);
        });

        // If the options contain several shipping method ids,
        // we're in a unified view and need to set the correct shipping method value.
        if (this.options.shippingMethodIds) {
            this.shippingMethodInputElement.value = this.options.shippingMethodIds[parcelShop.typeKey];
        }

        // Update all selection elements
        this.selectionElements.forEach((ele) => {
            ele.innerHTML = `${parcelShop.name} - ${parcelShop.street}, ${parcelShop.zipCode} ${parcelShop.city}`;
        });
    }

    showParcelShopOnMap(parcelShop) {
        this.mapPlugin.scrollToParcelShop(parcelShop);
        this.showMap();
    }

    onSave() {
        // When we save update current
        this.currentParcelShop = this.selectedParcelShop;

        if (this.currentParcelShop) {
            this.shippingMethodInputElement.checked = true;
            const event = new CustomEvent('change', {bubbles: true});
            this.shippingMethodInputElement.dispatchEvent(event);
        }
    }

    validate(e) {
        if (this.shippingMethodInputElement.checked) {
            let valid = true;

            const validator = new ParcelShopValidator();
            if (!validator.validate(this)) {
                validator.onInvalid(this);
                e.preventDefault();

                valid = false;
            } else {
                validator.onValid(this);
            }

            return valid;
        }

        return true;
    }
}
