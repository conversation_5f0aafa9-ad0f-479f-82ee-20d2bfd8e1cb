{% block wexo_shipping_component_shipping_parcel_shop_modal_base %}
    {% set typeKey = shipping.extension('shippingMethodType').typeKey %}
    {% set configuration = page.extensions.shipping_method_type_configuration %}
    {% set pluginOptions = configuration.shippingMethods[typeKey]|default([]) %}
    {% set pluginOptions = pluginOptions|merge({apiUrl: path('frontend.wexo.parcel-shops')}) %}
    {% set selectedParcelShop = configuration.selectedParcelShop %}

    {% if selectedParcelShop is not null and
        (selectedParcelShop.typeKey == typeKey or typeKey == 'unified') %}
        {% set parcelShopId = selectedParcelShop.providerId %}
        {% set parcelShopStreet = selectedParcelShop.street %}
        {% set parcelShopZipCode = selectedParcelShop.zipCode %}
        {% set parcelShopSelection = selectedParcelShop.name ~ " - " ~ selectedParcelShop.street ~ ", " ~ selectedParcelShop.zipCode ~ " " ~ selectedParcelShop.city %}
        {% set pluginOptions = pluginOptions|merge({currentParcelShop: selectedParcelShop}) %}
    {% endif %}

    {% block wexo_shipping_component_shipping_parcel_shop_modal %}
        <div class="modal fade parcel-shop-modal"
             id="parcelShopModal-{{ typeKey }}"
             tabindex="-1"
             role="dialog"
             data-bs-backdrop="static">
            {% block wexo_shipping_component_shipping_parcel_shop_modal_dialog %}
                <div class="modal-dialog modal-dialog-centered"
                     role="document">
                    {% block wexo_shipping_component_shipping_parcel_shop_modal_dialog_content %}
                        <div class="modal-content">

                            {% block wexo_shipping_component_shipping_parcel_shop_modal_dialog_content_header %}
                                <div class="modal-header">
                                    <button type="button"
                                            class="btn-close"
                                            data-bs-dismiss="modal"
                                            aria-label="Close">
                                        <span aria-hidden="true" class="close-text">
                                            {{ 'shippingMethod.modal.close'|trans|sw_sanitize }}
                                        </span>
                                    </button>
                                </div>
                            {% endblock %}

                            {% block wexo_shipping_component_shipping_parcel_shop_modal_dialog_content_body %}
                                <div class="modal-body">

                                    {% block wexo_shipping_component_shipping_parcel_shop_modal_body_plugin %}
                                        <div class="parcel-shop-plugin"
                                             data-parcel-shop-plugin
                                             data-parcel-shop-plugin-options="{{ pluginOptions|json_encode }}">
                                            {% block wexo_shipping_component_shipping_parcel_shop_modal_parcel_shop_id %}
                                                <input type="hidden" name="parcelShopId"
                                                       {% if not parcelShopId %}disabled{% endif %}
                                                       value="{{ parcelShopId|default("") }}"/>
                                                <input type="hidden" name="parcelShopStreet"
                                                       {% if not parcelShopStreet %}disabled{% endif %}
                                                       value="{{ parcelShopStreet|default("") }}"/>
                                                <input type="hidden" name="parcelShopZipCode"
                                                       {% if not parcelShopZipCode %}disabled{% endif %}
                                                       value="{{ parcelShopZipCode|default("") }}"/>
                                            {% endblock %}

                                            {% block wexo_shipping_component_shipping_parcel_shop_modal_body_parcel_selected %}
                                                <h5>
                                                    {% sw_include '@WexoShipping/storefront/component/parcelShopTranslation.html.twig' with {
                                                        snippet: 'shippingMethod.selectParcelShop-'~typeKey,
                                                        defaultSnippet: 'shippingMethod.selectParcelShop'
                                                    } %}
                                                    <div class="parcel-shop-selection">
                                                        {% if parcelShopSelection is defined %}
                                                            {{ parcelShopSelection }}
                                                        {% endif %}
                                                    </div>
                                                </h5>
                                            {% endblock %}

                                            {% block wexo_shipping_component_shipping_parcel_shop_modal_body_parcel_content %}
                                                <div class="parcel-shop-content">
                                                    {% block wexo_shipping_component_shipping_parcel_shop_modal_body_parcel_content_left %}
                                                        <div class="parcel-shop-left">
                                                            <div class="parcel-shop-headline d-flex w-100">
                                                                <div class="form-group parcel-shop-input">
                                                                    <label class="form-label"
                                                                           for="parcel-shop-zipcode">{{ 'shippingMethod.zipcode'|trans|sw_sanitize }}</label>
                                                                    <input name="parcel-shop-zipcode"
                                                                           type="text"
                                                                           class="form-control"
                                                                           placeholder="{{ 'shippingMethod.chooseZipcode'|trans|sw_sanitize }}"
                                                                           value="{{ context.customer.activeShippingAddress.zipcode }}"/>
                                                                </div>
                                                                <div class="form-group parcel-shop-input w-100">
                                                                    <label class="form-label"
                                                                           for="parcel-shop-street">{{ 'shippingMethod.street'|trans|sw_sanitize }}</label>
                                                                    <input name="parcel-shop-street"
                                                                           class="form-control"
                                                                           type="text"
                                                                           placeholder="{{ 'shippingMethod.chooseStreet'|trans|sw_sanitize }}"
                                                                           value="{{ context.customer.activeShippingAddress.street }}"/>
                                                                </div>
                                                                <div class="form-group">
                                                                    <button
                                                                        class="btn btn-secondary form-control load-button search-parcel-shop"
                                                                        type="button">
                                                                        {{ 'shippingMethod.search'|trans|sw_sanitize }}
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div class="parcel-shop-list-container">
                                                                <ul class="parcel-shop-list"
                                                                    data-provider-list="{{ typeKey }}">
                                                                </ul>
                                                                <div class="load-more-shops-button-container">
                                                                    <button
                                                                        class="btn parcel-shop-button load-button load-more-shops"
                                                                        type="button">
                                                                        {{ 'shippingMethod.loadMore'|trans|sw_sanitize }}
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            {% block wexo_shipping_component_shipping_parcel_save_button %}
                                                                <div class="parcel-shop-bottom">
                                                                    <button data-bs-dismiss="modal" class="parcel-shop-save w-100 btn btn-primary">
                                                                        {{ 'shippingMethod.modal.save'|trans|sw_sanitize }}
                                                                    </button>
                                                                </div>
                                                            {% endblock %}
                                                        </div>
                                                    {% endblock %}
                                                    {% block wexo_shipping_component_shipping_parcel_shop_modal_body_parcel_content_right %}
                                                        <div class="parcel-shop-right">
                                                            <div class="parcel-shop-map"
                                                                 data-provider-map="{{ typeKey }}">
                                                            </div>
                                                        </div>
                                                    {% endblock %}
                                                </div>
                                            {% endblock %}
                                        </div>
                                    {% endblock %}
                                </div>
                            {% endblock %}
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}
        </div>
    {% endblock %}
{% endblock %}
