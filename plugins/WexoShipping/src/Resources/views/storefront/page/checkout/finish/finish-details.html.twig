{% sw_extends '@Storefront/storefront/page/checkout/finish/finish-details.html.twig' %}

{% block page_checkout_finish_order_dispatch_method %}
    {{ parent() }}

    {% block shipping_parcel_shop_data %}
        {% if page.order.extension('parcelShop') is not null %}
            {% set parcelShop = page.order.extension('parcelShop') %}
            {% set typeKey = parcelShop.typeKey %}
            <p>
                <strong>{% sw_include '@WexoShipping/storefront/component/parcelShopTranslation.html.twig' with {
                        snippet: 'shippingMethod.parcelShop-'~typeKey,
                        defaultSnippet: 'shippingMethod.parcelShop'
                    } %}</strong>
                <br>
                <span>{{ parcelShop.name }}</span>
                <br>
                <span>{{ parcelShop.street }}</span>
                <br>
                <span>{{ parcelShop.zipCode }} {{ parcelShop.city }}</span>
            </p>
        {% endif %}
    {% endblock %}

    {% block shipping_shipping_comment_data %}
        {% if page.order.extension('shippingComment') is not null %}
            {% set shippingComment = page.order.extension('shippingComment') %}
            {% block page_checkout_finish_shipping_comment_header %}
                <strong>{{ "checkout.shippingCommentTitle"|trans|sw_sanitize }}</strong>
            {% endblock %}
            <br>
            {% block page_checkout_finish_shippping_comment_content %}
                <span>{{ shippingComment.comment }}</span>
            {% endblock %}
            <br>
        {% endif %}
    {% endblock %}
{% endblock %}
