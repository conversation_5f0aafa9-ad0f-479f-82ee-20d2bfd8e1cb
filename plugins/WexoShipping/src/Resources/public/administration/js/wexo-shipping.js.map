{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/WexoShipping/src/Resources/app/administration/src/page/sw-settings-shipping-detail/sw-settings-shipping-detail.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/WexoShipping/src/Resources/app/administration/src/page/sw-settings-shipping-detail/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/WexoShipping/src/Resources/app/administration/src/component/sw-order-detail-details/index.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/WexoShipping/src/Resources/app/administration/src/component/sw-order-detail-details/sw-order-detail-details.html.twig", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/WexoShipping/src/Resources/app/administration/src/services/shippingMethodType.service.js", "webpack:////Volumes/Macintosh/Workspace/shopware2/custom/plugins/WexoShipping/src/Resources/app/administration/src/main.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "_Shopware", "Shopware", "Component", "Context", "Criteria", "Data", "override", "template", "inject", "data", "shippingMethodConfigRepository", "shippingMethodConfig", "shippingMethodDeadlineRepository", "shippingMethodDeadline", "shippingMethodTypeRepository", "shippingMethodType", "shippingMethodTypes", "loadingShippingTypes", "methods", "createdComponent", "_this", "this", "$super", "ShippingMethodTypeService", "getTypes", "then", "types", "catch", "error", "createNotificationError", "title", "message", "repositoryFactory", "criteria", "addFilter", "equals", "shippingMethodId", "search", "api", "response", "total", "first", "shippingMethod", "id", "shippingCommentRequired", "typeKey", "deadlines", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday", "excludedDates", "onSaveOverride", "_this2", "promises", "_isNew", "push", "deleteParcelShop", "deadlinesSet", "values", "filter", "deadline", "length", "deleteDeadline", "Promise", "all", "onSave", "saveParcelShop", "saveDeadline", "saveAdditionalConfig", "_this3", "save", "_this4", "delete", "penalties", "_this5", "penalty", "from", "to", "_this6", "_this7", "addDeadlineException", "date", "deleteDeadlineException", "index", "splice", "addParcelShopPenalty", "Array", "isArray", "deletePenalty", "ApiService", "Classes", "_ApiService", "_inherits", "_super", "_createSuper", "httpClient", "loginService", "apiEndpoint", "arguments", "undefined", "_classCallCheck", "getApi<PERSON>ase<PERSON><PERSON>", "headers", "getBasicHeaders", "handleResponse", "Locale", "extend", "deDE", "enGB", "daDK", "Application", "addServiceProvider", "container", "initContainer", "getContainer"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,yBAIjBlC,EAAoBA,EAAoBmC,EAAI,Q,qtFClFtC,ICEfC,EAAqCC,SAA7BC,EAASF,EAATE,UAAWC,EAAOH,EAAPG,QACXC,EADwBJ,EAAJK,KACpBD,SAERF,EAAUI,SAAS,8BAA+B,CAC9CC,SDNW,mgPCQXC,OAAQ,CACJ,4BACA,WAGJC,KAAI,WACA,MAAO,CACHC,+BAAgC,KAChCC,qBAAsB,KACtBC,iCAAkC,KAClCC,uBAAwB,KACxBC,6BAA8B,KAC9BC,mBAAoB,KACpBC,oBAAqB,GACrBC,sBAAsB,IAI9BC,QAAS,CACLC,iBAAgB,WAAI,IAADC,EAAA,KACfC,KAAKC,OAAO,oBAEZD,KAAKE,0BAA0BC,WAC1BC,MAAK,SAAAC,GACFN,EAAKJ,oBAAsBU,EAC3BN,EAAKH,sBAAuB,KAE/BU,OAAM,SAAAC,GACHR,EAAKS,wBAAwB,CACzBC,MAAO,gDACPC,QAASH,OAIrBP,KAAKX,+BAAiCW,KAAKW,kBAAkB1C,OAAO,+BACpE+B,KAAKP,6BAA+BO,KAAKW,kBAAkB1C,OAAO,6BAClE+B,KAAKT,iCAAmCS,KAAKW,kBAAkB1C,OAAO,iCAEtE,IAAM2C,EAAW,IAAI7B,EACrB6B,EAASC,UAAU9B,EAAS+B,OAAO,mBAAoBd,KAAKe,mBAE5Df,KAAKX,+BAA+B2B,OAAOJ,EAAU9B,EAAQmC,KACxDb,MAAK,SAAAc,GACEA,EAASC,MACTpB,EAAKT,qBAAuB4B,EAASE,SAErCrB,EAAKT,qBAAuBS,EAAKV,+BAA+BpB,OAAOa,EAAQmC,KAC/ElB,EAAKT,qBAAqByB,iBAAmBhB,EAAKsB,eAAeC,IAAMvB,EAAKgB,iBAC5EhB,EAAKT,qBAAqBiC,yBAA0B,MAGhEvB,KAAKP,6BAA6BuB,OAAOJ,EAAU9B,EAAQmC,KACtDb,MAAK,SAAAc,GACEA,EAASC,MACTpB,EAAKL,mBAAqBwB,EAASE,SAEnCrB,EAAKL,mBAAqBK,EAAKN,6BAA6BxB,OAAOa,EAAQmC,KAC3ElB,EAAKL,mBAAmBqB,iBAAmBhB,EAAKsB,eAAeC,IAAMvB,EAAKgB,iBAC1EhB,EAAKL,mBAAmB8B,QAAU,SAGzClB,OAAM,SAAAC,GACHR,EAAKS,wBAAwB,CACzBC,MAAO,qCACPC,QAASH,OAGrBP,KAAKT,iCAAiCyB,OAAOJ,EAAU9B,EAAQmC,KAC1Db,MAAK,SAAAc,GACEA,EAASC,MACTpB,EAAKP,uBAAyB0B,EAASE,SAEvCrB,EAAKP,uBAAyBO,EAAKR,iCAAiCtB,OAAOa,EAAQmC,KACnFlB,EAAKP,uBAAuBuB,iBAAmBhB,EAAKsB,eAAeC,IAAMvB,EAAKgB,iBAC9EhB,EAAKP,uBAAuBiC,UAAY,CACpCC,OAAQ,KACRC,QAAS,KACTC,UAAW,KACXC,SAAU,KACVC,OAAQ,KACRC,SAAU,KACVC,OAAQ,MAEZjC,EAAKP,uBAAuByC,cAAgB,OAGnD3B,OAAM,SAAAC,GACHR,EAAKS,wBAAwB,CACzBC,MAAO,yCACPC,QAASH,QAIzB2B,eAAc,WAAI,IAADC,EAAA,KACPC,EAAW,GACZpC,KAAKN,mBAAmB8B,SAAYxB,KAAKN,mBAAmB2C,QAC7DD,EAASE,KAAKtC,KAAKuC,oBAEvB,IAAMC,EAAenF,OAAOoF,OAAOzC,KAAKR,uBAAuBiC,WAC1DiB,QAAO,SAAAC,GAAQ,OAAiB,OAAbA,KACnBC,OAIL,OAHKJ,GAAiBxC,KAAKR,uBAAuB6C,QAC9CD,EAASE,KAAKtC,KAAK6C,kBAEhBC,QAAQC,IAAIX,GAAUhC,MAAK,WAC9B,OAAO+B,EAAKa,SAAS5C,MAAK,WAClB+B,EAAKzC,mBAAmB8B,SACxBW,EAAKc,iBAELT,GACAL,EAAKe,eAETf,EAAKgB,8BAIjBA,qBAAoB,WAAI,IAADC,EAAA,KACnBpD,KAAKX,+BACAgE,KAAKrD,KAAKV,qBAAsBR,EAAQmC,KACxCb,MAAK,WACFgD,EAAK9D,qBAAqB+C,QAAS,KAEtC/B,OAAM,SAAAC,GACH6C,EAAK5C,wBAAwB,CACzBC,MAAO,6DACPC,QAASH,QAIzBgC,iBAAgB,WAAI,IAADe,EAAA,KACf,OAAOtD,KAAKP,6BAA6B8D,OAAOvD,KAAKN,mBAAmB4B,GAAIxC,EAAQmC,KAC/Eb,MAAK,WACFkD,EAAK5D,mBAAqB4D,EAAK7D,6BAA6BxB,OAAOa,EAAQmC,KAC3EqC,EAAK5D,mBAAmBqB,iBAAmBuC,EAAKvC,iBAChDuC,EAAK5D,mBAAmB8B,QAAU,KAClC8B,EAAK5D,mBAAmB8D,UAAY,MAEvClD,OAAM,SAAAC,GACH+C,EAAK9C,wBAAwB,CACzBC,MAAO,sCACPC,QAASH,QAIzB0C,eAAc,WAAI,IAADQ,EAAA,KACTzD,KAAKN,mBAAmB8D,YACxBxD,KAAKN,mBAAmB8D,UAAYxD,KAAKN,mBAAmB8D,UAAUd,QAAO,SAAAgB,GACzE,OAAOA,EAAQC,MAAQD,EAAQE,IAAMF,EAAQA,YAGrD1D,KAAKP,6BACA4D,KAAKrD,KAAKN,mBAAoBZ,EAAQmC,KACtCb,MAAK,WACFqD,EAAK/D,mBAAmB2C,QAAS,KAEpC/B,OAAM,SAAAC,GACHkD,EAAKjD,wBAAwB,CACzBC,MAAO,oCACPC,QAASH,QAIzBsC,eAAc,WAAI,IAADgB,EAAA,KACb,OAAO7D,KAAKT,iCAAiCgE,OAAOvD,KAAKR,uBAAuB8B,GAAIxC,EAAQmC,KACvFb,MAAK,WACFyD,EAAKrE,uBAAyBqE,EAAKtE,iCAAiCtB,OAAOa,EAAQmC,KACnF4C,EAAKrE,uBAAuBuB,iBAAmB8C,EAAK9C,iBACpD8C,EAAKrE,uBAAuBiC,UAAY,CACpCC,OAAQ,KACRC,QAAS,KACTC,UAAW,KACXC,SAAU,KACVC,OAAQ,KACRC,SAAU,KACVC,OAAQ,MAEZ6B,EAAKrE,uBAAuByC,cAAgB,MAE/C3B,OAAM,SAAAC,GACHsD,EAAKrD,wBAAwB,CACzBC,MAAO,0CACPC,QAASH,QAIzB2C,aAAY,WAAI,IAADY,EAAA,KACX9D,KAAKT,iCACA8D,KAAKrD,KAAKR,uBAAwBV,EAAQmC,KAC1Cb,MAAK,WACF0D,EAAKtE,uBAAuB6C,QAAS,KAExC/B,OAAM,SAAAC,GACHuD,EAAKtD,wBAAwB,CACzBC,MAAO,wCACPC,QAASH,QAIzBwD,qBAAoB,WAChB/D,KAAKR,uBAAuByC,cAAcK,KAAK,CAC3C0B,KAAM,QAGdC,wBAAuB,SAACC,GACpBlE,KAAKR,uBAAuByC,cAAckC,OAAOD,EAAO,IAE5DE,qBAAoB,WACXC,MAAMC,QAAQtE,KAAKN,mBAAmB8D,aACvCxD,KAAKN,mBAAmB8D,UAAY,IAExCxD,KAAKN,mBAAmB8D,UAAUlB,KAAK,CAACsB,GAAI,KAAMD,KAAM,KAAMD,QAAS,QAE3Ea,cAAa,SAACL,GACVlE,KAAKN,mBAAmB8D,UAAUW,OAAOD,EAAO,O,0vDC3NtCtF,SAAdC,UAEEI,SAAS,0BAA2B,CAC1CC,SCLW,6mDCAf,IAAQsF,EAAe5F,SAAS6F,QAAxBD,WAEatE,EAAyB,SAAAwE,I,qRAAAC,CAAAzE,EAAAwE,GAAA,I,MAAAE,EAAAC,EAAA3E,GAC1C,SAAAA,EAAY4E,EAAYC,GAAoD,IAAtCC,EAAWC,UAAArC,OAAA,QAAAsC,IAAAD,UAAA,GAAAA,UAAA,GAAG,sBAAqB,OAAAE,EAAA,KAAAjF,GAAA0E,EAAA9H,KAAA,KAC/DgI,EAAYC,EAAcC,GAWnC,O,EAVA9E,G,EAAA,EAAAhC,IAAA,WAAAN,MAED,WACI,OAAOoC,KAAK8E,WAAWtH,IACnBwC,KAAKoF,iBAAkB,CACnBC,QAASrF,KAAKsF,oBAEpBlF,MAAK,SAAAc,GACH,OAAOsD,EAAWe,eAAerE,W,8EAExChB,EAbyC,CAASsE,G,oCCKvD5F,SAAS4G,OAAOC,OAAO,QAASC,GAChC9G,SAAS4G,OAAOC,OAAO,QAASE,GAChC/G,SAAS4G,OAAOC,OAAO,QAASG,GAEhC,IAAQC,EAAgBjH,SAAhBiH,YACRA,EAAYC,mBAAmB,6BAA6B,SAAAC,GACxD,IAAMC,EAAgBH,EAAYI,aAAa,QAC/C,OAAO,IAAI/F,EAA0B8F,EAAclB,WAAYiB,EAAUhB", "file": "static/js/wexo-shipping.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/bundles/wexoshipping/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"I4Q4\");\n", "export default \"{% block sw_settings_shipping_detail_top_ruleshippingPriceStore %}\\n    {% parent() %}\\n\\n    <sw-card v-if=\\\"shippingMethodConfig\\\"\\n         class=\\\"sw-settings-shipping-detail__wexo_shipping_method_config\\\"\\n         :title=\\\"$tc('wexoShipping.generalConfig')\\\">\\n        <sw-checkbox-field :label=\\\"$tc('wexoShipping.shippingCommentRequired')\\\"\\n                           :helpText=\\\"$tc('wexoShipping.shippingCommentRequiredHelp')\\\"\\n                           v-model=\\\"shippingMethodConfig.shippingCommentRequired\\\">\\n        </sw-checkbox-field>\\n    </sw-card>\\n\\n    <sw-card v-if=\\\"shippingMethodDeadline && feature.isActive('FEATURE_WEXOSHIPPING_1')\\\"\\n             class=\\\"sw-settings-shipping-detail__wexo_shipping_method_deadline\\\"\\n             :title=\\\"$tc('wexoShipping.deadline.title')\\\">\\n        {% block sw_settings_shipping_deadline %}\\n            <sw-container columns=\\\"1fr 1fr 1fr 1fr\\\" gap=\\\"16px\\\">\\n                <sw-datepicker v-model=\\\"shippingMethodDeadline.deadlines.Monday\\\"\\n                               :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.days.monday')\\\"\\n                               dateType=\\\"time\\\">\\n                </sw-datepicker>\\n                <sw-datepicker v-model=\\\"shippingMethodDeadline.deadlines.Tuesday\\\"\\n                               :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.days.tuesday')\\\"\\n                               dateType=\\\"time\\\">\\n                </sw-datepicker>\\n                <sw-datepicker v-model=\\\"shippingMethodDeadline.deadlines.Wednesday\\\"\\n                               :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.days.wednesday')\\\"\\n                               dateType=\\\"time\\\">\\n                </sw-datepicker>\\n                <sw-datepicker v-model=\\\"shippingMethodDeadline.deadlines.Thursday\\\"\\n                               :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.days.thursday')\\\"\\n                               dateType=\\\"time\\\">\\n                </sw-datepicker>\\n                <sw-datepicker v-model=\\\"shippingMethodDeadline.deadlines.Friday\\\"\\n                               :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.days.friday')\\\"\\n                               dateType=\\\"time\\\">\\n                </sw-datepicker>\\n                <sw-datepicker v-model=\\\"shippingMethodDeadline.deadlines.Saturday\\\"\\n                               :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.days.saturday')\\\"\\n                               dateType=\\\"time\\\">\\n                </sw-datepicker>\\n                <sw-datepicker v-model=\\\"shippingMethodDeadline.deadlines.Sunday\\\"\\n                               :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.days.sunday')\\\"\\n                               dateType=\\\"time\\\">\\n                </sw-datepicker>\\n            </sw-container>\\n            <sw-container v-for=\\\"(excludedDate, index) in shippingMethodDeadline.excludedDates\\\" columns=\\\"1fr 24px\\\" gap=\\\"16px\\\" :key=\\\"index\\\">\\n                <sw-datepicker :config=\\\"{dateFormat: 'Y-m-d'}\\\"\\n                               v-model=\\\"excludedDate.date\\\"\\n                               :label=\\\"$tc('wexoShipping.deadline.exception')\\\"\\n                               dateType=\\\"date\\\">\\n                </sw-datepicker>\\n                <sw-icon style=\\\"margin-top: 36px; cursor: pointer;\\\" name=\\\"default-basic-x-line\\\" @click=\\\"() => deleteDeadlineException(index)\\\"></sw-icon>\\n            </sw-container>\\n            <sw-button variant=\\\"primary\\\"\\n                       :block=\\\"true\\\"\\n                       @click=\\\"addDeadlineException\\\">\\n                {{ $tc('wexoShipping.deadline.addDeadlineException') }}\\n            </sw-button>\\n        {% endblock %}\\n    </sw-card>\\n\\n    <sw-card v-if=\\\"shippingMethodType\\\"\\n             class=\\\"sw-settings-shipping-detail__wexo_shipping_method_type\\\"\\n             :title=\\\"$tc('wexoShipping.parcelShop')\\\">\\n        {% block sw_settings_shipping_parcel_shop_provider %}\\n            <sw-single-select v-model=\\\"shippingMethodType.typeKey\\\"\\n                              :isLoading=\\\"loadingShippingTypes\\\"\\n                              :disabled=\\\"loadingShippingTypes || !acl.can('shipping.editor')\\\"\\n                              :options=\\\"shippingMethodTypes\\\"\\n                              :label=\\\"$tc('wexoShipping.provider.choose')\\\">\\n            </sw-single-select>\\n        {% endblock %}\\n        {% block sw_settings_shipping_ordering_penalties %}\\n            <template v-if=\\\"shippingMethodType.typeKey && feature.isActive('FEATURE_WEXOSHIPPING_1')\\\">\\n                <sw-container v-for=\\\"(penalty, index) in shippingMethodType.penalties\\\" columns=\\\"1fr 1fr 1fr 24px\\\" gap=\\\"16px\\\" :key=\\\"index\\\">\\n                    <sw-datepicker :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                                   v-model=\\\"penalty.from\\\"\\n                                   :label=\\\"$tc('wexoShipping.penalty.from')\\\"\\n                                   :required=\\\"true\\\"\\n                                   dateType=\\\"time\\\">\\n                    </sw-datepicker>\\n                    <sw-datepicker :config=\\\"{enableTime: true, time_24hr: true, dateFormat: 'H:i', noCalendar: true}\\\"\\n                                   v-model=\\\"penalty.to\\\"\\n                                   :label=\\\"$tc('wexoShipping.penalty.to')\\\"\\n                                   :required=\\\"true\\\"\\n                                   dateType=\\\"time\\\">\\n                    </sw-datepicker>\\n                    <sw-number-field :label=\\\"$tc('wexoShipping.penalty.penaltyAmount')\\\"\\n                                     v-model=\\\"penalty.penalty\\\"\\n                                     :min=\\\"0\\\"\\n                                     :allowEmpty=\\\"false\\\"\\n                                     :required=\\\"true\\\"\\n                                     placeholder=\\\"m\\\">\\n                    </sw-number-field>\\n                    <sw-icon style=\\\"margin-top: 36px; cursor: pointer;\\\" name=\\\"default-basic-x-line\\\" @click=\\\"() => deletePenalty(index)\\\"></sw-icon>\\n                </sw-container>\\n                <sw-button variant=\\\"primary\\\"\\n                           :isLoading=\\\"loadingShippingTypes\\\"\\n                           :block=\\\"true\\\"\\n                           @click=\\\"addParcelShopPenalty\\\">\\n                    {{ $tc('wexoShipping.penalty.addTimeSlot') }}\\n                </sw-button>\\n            </template>\\n        {% endblock %}\\n    </sw-card>\\n{% endblock %}\\n\\n{% block sw_settings_shipping_detail_actions_save %}\\n    <sw-button-process\\n        class=\\\"sw-settings-shipping-method-detail__save-action\\\"\\n        :isLoading=\\\"isProcessLoading\\\"\\n        v-model=\\\"isSaveSuccessful\\\"\\n        :disabled=\\\"isProcessLoading || !acl.can('shipping.editor')\\\"\\n        variant=\\\"primary\\\"\\n        v-tooltip.bottom=\\\"tooltipSave\\\"\\n        @click.prevent=\\\"onSaveOverride\\\"> \\n        {{ $tc('sw-settings-shipping.detail.buttonSave') }}\\n    </sw-button-process>\\n{% endblock %}\\n\";", "import template from './sw-settings-shipping-detail.html.twig';\n\nconst { Component, Context, Data } = Shopware;\nconst { Criteria } = Data;\n\nComponent.override('sw-settings-shipping-detail', {\n    template,\n\n    inject: [\n        'ShippingMethodTypeService',\n        'feature'\n    ],\n\n    data() {\n        return {\n            shippingMethodConfigRepository: null,\n            shippingMethodConfig: null,\n            shippingMethodDeadlineRepository: null,\n            shippingMethodDeadline: null,\n            shippingMethodTypeRepository: null,\n            shippingMethodType: null,\n            shippingMethodTypes: [],\n            loadingShippingTypes: true\n        };\n    },\n    \n    methods: {\n        createdComponent() {\n            this.$super('createdComponent');\n\n            this.ShippingMethodTypeService.getTypes()\n                .then(types => {\n                    this.shippingMethodTypes = types;\n                    this.loadingShippingTypes = false;\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error loading available shipping method types',\n                        message: error\n                    })\n                });\n\n            this.shippingMethodConfigRepository = this.repositoryFactory.create('wexo_shipping_method_config')\n            this.shippingMethodTypeRepository = this.repositoryFactory.create('wexo_shipping_method_type')\n            this.shippingMethodDeadlineRepository = this.repositoryFactory.create('wexo_shipping_method_deadline')\n\n            const criteria = new Criteria();\n            criteria.addFilter(Criteria.equals('shippingMethodId', this.shippingMethodId));\n\n            this.shippingMethodConfigRepository.search(criteria, Context.api)\n                .then(response => {\n                    if (response.total) {\n                        this.shippingMethodConfig = response.first();\n                    } else {\n                        this.shippingMethodConfig = this.shippingMethodConfigRepository.create(Context.api);\n                        this.shippingMethodConfig.shippingMethodId = this.shippingMethod.id || this.shippingMethodId;\n                        this.shippingMethodConfig.shippingCommentRequired = false;\n                    }\n                })\n            this.shippingMethodTypeRepository.search(criteria, Context.api)\n                .then(response => {\n                    if (response.total) {\n                        this.shippingMethodType = response.first();\n                    } else {\n                        this.shippingMethodType = this.shippingMethodTypeRepository.create(Context.api);\n                        this.shippingMethodType.shippingMethodId = this.shippingMethod.id || this.shippingMethodId;\n                        this.shippingMethodType.typeKey = null;\n                    }\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error loading shipping method type',\n                        message: error\n                    })\n                })\n            this.shippingMethodDeadlineRepository.search(criteria, Context.api)\n                .then(response => {\n                    if (response.total) {\n                        this.shippingMethodDeadline = response.first();\n                    } else {\n                        this.shippingMethodDeadline = this.shippingMethodDeadlineRepository.create(Context.api);\n                        this.shippingMethodDeadline.shippingMethodId = this.shippingMethod.id || this.shippingMethodId;\n                        this.shippingMethodDeadline.deadlines = {\n                            Monday: null,\n                            Tuesday: null,\n                            Wednesday: null,\n                            Thursday: null,\n                            Friday: null,\n                            Saturday: null,\n                            Sunday: null\n                        };\n                        this.shippingMethodDeadline.excludedDates = [];\n                    }\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error loading shipping method deadline',\n                        message: error\n                    })\n                })\n        },\n        onSaveOverride() {\n            const promises = [];\n            if (!this.shippingMethodType.typeKey && !this.shippingMethodType._isNew) {\n                promises.push(this.deleteParcelShop());\n            }\n            const deadlinesSet = Object.values(this.shippingMethodDeadline.deadlines)\n                .filter(deadline => deadline !== null)\n                .length\n            if (!deadlinesSet && !this.shippingMethodDeadline._isNew)  {\n                promises.push(this.deleteDeadline());\n            }\n            return Promise.all(promises).then(() => {\n                return this.onSave().then(() => {\n                    if (this.shippingMethodType.typeKey) {\n                        this.saveParcelShop();\n                    }\n                    if (deadlinesSet) {\n                        this.saveDeadline();\n                    }\n                    this.saveAdditionalConfig();\n                });\n            });\n        },\n        saveAdditionalConfig() {\n            this.shippingMethodConfigRepository\n                .save(this.shippingMethodConfig, Context.api)\n                .then(() => {\n                    this.shippingMethodConfig._isNew = false;\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error saving additional WEXO shipping method configuration',\n                        message: error\n                    })\n                })\n        },\n        deleteParcelShop() {\n            return this.shippingMethodTypeRepository.delete(this.shippingMethodType.id, Context.api)\n                .then(() => {\n                    this.shippingMethodType = this.shippingMethodTypeRepository.create(Context.api);\n                    this.shippingMethodType.shippingMethodId = this.shippingMethodId;\n                    this.shippingMethodType.typeKey = null;\n                    this.shippingMethodType.penalties = [];\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error deleting shipping method type',\n                        message: error\n                    })\n                });\n        },\n        saveParcelShop() {\n            if (this.shippingMethodType.penalties) {\n                this.shippingMethodType.penalties = this.shippingMethodType.penalties.filter(penalty => {\n                    return penalty.from && penalty.to && penalty.penalty;\n                })\n            }\n            this.shippingMethodTypeRepository\n                .save(this.shippingMethodType, Context.api)\n                .then(() => {\n                    this.shippingMethodType._isNew = false;\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error saving shipping method type',\n                        message: error\n                    })\n                })\n        },\n        deleteDeadline() {\n            return this.shippingMethodDeadlineRepository.delete(this.shippingMethodDeadline.id, Context.api)\n                .then(() => {\n                    this.shippingMethodDeadline = this.shippingMethodDeadlineRepository.create(Context.api);\n                    this.shippingMethodDeadline.shippingMethodId = this.shippingMethodId;\n                    this.shippingMethodDeadline.deadlines = {\n                        Monday: null,\n                        Tuesday: null,\n                        Wednesday: null,\n                        Thursday: null,\n                        Friday: null,\n                        Saturday: null,\n                        Sunday: null\n                    };\n                    this.shippingMethodDeadline.excludedDates = [];\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error deleting shipping method deadline',\n                        message: error\n                    })\n                });\n        },\n        saveDeadline() {\n            this.shippingMethodDeadlineRepository\n                .save(this.shippingMethodDeadline, Context.api)\n                .then(() => {\n                    this.shippingMethodDeadline._isNew = false;\n                })\n                .catch(error => {\n                    this.createNotificationError({\n                        title: 'Error saving shipping method deadline',\n                        message: error\n                    })\n                })\n        },\n        addDeadlineException() {\n            this.shippingMethodDeadline.excludedDates.push({\n                date: null\n            });\n        },\n        deleteDeadlineException(index) {\n            this.shippingMethodDeadline.excludedDates.splice(index, 1);\n        },\n        addParcelShopPenalty() {\n            if (!Array.isArray(this.shippingMethodType.penalties)) {\n                this.shippingMethodType.penalties = [];\n            }\n            this.shippingMethodType.penalties.push({to: null, from: null, penalty: null});\n        },\n        deletePenalty(index) {\n            this.shippingMethodType.penalties.splice(index, 1);\n        }\n    }\n})\n", "import template from './sw-order-detail-details.html.twig';\n\nconst { Component } = Shopware;\n\nComponent.override('sw-order-detail-details', {\n    template\n});\n", "export default \"{% block sw_order_detail_details_shipping %}\\n    {% parent() %}\\n    <sw-card v-if=\\\"order.extensions.shippingComment\\\"\\n             :title=\\\"$tc('wexoShipping.shippingComment')\\\"\\n             position-identifier=\\\"sw-order-detail-details-shipping-comment\\\">\\n            <sw-textarea-field\\n                    v-model=\\\"order.extensions.shippingComment.comment\\\"\\n                    disabled\\n            />\\n    </sw-card>\\n    <sw-card v-if=\\\"order.extensions.parcelShop\\\"\\n            :title=\\\"$tc('wexoShipping.parcelShop')\\\"\\n            position-identifier=\\\"sw-order-detail-details-parcel-shop\\\"\\n    >\\n        <sw-container\\n                gap=\\\"0px 30px\\\"\\n                columns=\\\"1fr 1fr\\\"\\n        >\\n            <sw-text-field\\n                    v-model=\\\"order.extensions.parcelShop.name\\\"\\n                    :label=\\\"$tc('sw-customer.detailAddresses.columnCompany')\\\"\\n                    disabled\\n            />\\n            <sw-text-field\\n                    v-model=\\\"order.extensions.parcelShop.street\\\"\\n                    :label=\\\"$tc('sw-customer.detailAddresses.columnStreet')\\\"\\n                    disabled\\n            />\\n            <sw-text-field\\n                    v-model=\\\"order.extensions.parcelShop.zipCode\\\"\\n                    :label=\\\"$tc('sw-customer.detailAddresses.columnZipCode')\\\"\\n                    disabled\\n            />\\n            <sw-text-field\\n                    v-model=\\\"order.extensions.parcelShop.city\\\"\\n                    :label=\\\"$tc('sw-customer.detailAddresses.columnCity')\\\"\\n                    disabled\\n            />\\n        </sw-container>\\n    </sw-card>\\n{% endblock %}\";", "const { ApiService } = Shopware.Classes;\n\nexport default class ShippingMethodTypeService extends ApiService {\n    constructor(httpClient, loginService, apiEndpoint = 'wexo/shipping-types') {\n        super(httpClient, loginService, apiEndpoint);\n    }\n\n    getTypes() {\n        return this.httpClient.get(\n            this.getApiBasePath(), {\n                headers: this.getBasicHeaders()\n            }\n        ).then(response => {\n            return ApiService.handleResponse(response);\n        });\n    }\n}\n", "import './page/sw-settings-shipping-detail/';\nimport './component/sw-order-detail-details/';\nimport ShippingMethodTypeService from './services/shippingMethodType.service';\nimport deDE from './module/wexo-shipping-type/snippet/de-DE.json';\nimport enGB from './module/wexo-shipping-type/snippet/en-GB.json';\nimport daDK from './module/wexo-shipping-type/snippet/da-DK.json';\n\nShopware.Locale.extend('de-DE', deDE);\nShopware.Locale.extend('en-GB', enGB);\nShopware.Locale.extend('da-DK', daDK);\n\nconst { Application } = Shopware;\nApplication.addServiceProvider('ShippingMethodTypeService', container => {\n    const initContainer = Application.getContainer('init');\n    return new ShippingMethodTypeService(initContainer.httpClient, container.loginService);\n});"], "sourceRoot": ""}