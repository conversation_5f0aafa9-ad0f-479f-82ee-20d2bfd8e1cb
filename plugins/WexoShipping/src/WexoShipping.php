<?php declare(strict_types=1);

namespace Wexo\Shipping;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Feature;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;

class WexoShipping extends Plugin
{
    final public const WEXO_SHIPPING_FEATURE_FLAG = 'FEATURE_WEXOSHIPPING_1';

    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);
    }

    public function uninstall(UninstallContext $uninstallContext): void
    {
        parent::uninstall($uninstallContext);

        if ($uninstallContext->keepUserData()) {
            return;
        }

        $connection = $this->container->get(Connection::class);

        $connection->executeUpdate('DROP TABLE IF EXISTS `wexo_parcel_shop`');
        $connection->executeUpdate('DROP TABLE IF EXISTS `wexo_shipping_comment`');
        $connection->executeUpdate('DROP TABLE IF EXISTS `wexo_shipping_method_config`');
        $connection->executeUpdate('DROP TABLE IF EXISTS `wexo_shipping_method_deadline`');
        $connection->executeUpdate('DROP TABLE IF EXISTS `wexo_shipping_method_type`');
    }

    public function boot(): void
    {
        Feature::registerFeature(
            self::WEXO_SHIPPING_FEATURE_FLAG,
            ['major' => false, 'default' => false, 'description' => 'Controlling Wexo shipping module functionality']
        );
    }
}
