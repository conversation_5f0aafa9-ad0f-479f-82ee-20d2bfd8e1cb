<?php declare(strict_types=1);

namespace Wexo\Shipping\Controller;

use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wexo\Shipping\Controller\Api\ParcelShopController;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ParcelShopStorefrontController
{
    public function __construct(protected ParcelShopController $parcelShopController)
    {
    }

    #[Route(
        path: '/wexo/parcel-shops',
        name: 'frontend.wexo.parcel-shops',
        defaults: ['XmlHttpRequest' => true],
        methods: ['GET']
    )]
    public function getParcelShops(Request $request, SalesChannelContext $context): Response
    {
        return $this->parcelShopController->getParcelShops($request, $context);
    }
}
