<?php declare(strict_types=1);

namespace Wexo\Shipping\Controller\Api;

use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Wexo\Shipping\WexoShipping;
use GuzzleHttp\Exception\ClientException;
use Psr\Log\LoggerInterface;
use Shopware\Core\Checkout\Shipping\ShippingMethodEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Feature;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wexo\Shipping\Core\Content\ParcelShop\ParcelShopEntity;
use Wexo\Shipping\Service\TypeOptionService;
use Symfony\Component\Routing\Annotation\Route;
use Wexo\Shipping\Subscriber\Checkout;
use SoapFault;

class ParcelShopController
{
    public function __construct(
        private readonly TypeOptionService $typeOptionService,
        private readonly EntityRepository $shippingMethodRepository,
        private readonly LoggerInterface $logger
    ) {
    }

    #[Route(
        path: '/store-api/wexo/parcel-shops',
        name: 'store-api.action.wexo.parcel-shops',
        defaults: ['_routeScope' => ['store-api']],
        methods: ['GET']
    )]
    public function getParcelShops(Request $request, SalesChannelContext $context): JsonResponse
    {
        //Perform validation
        $typeKey = $request->query->get('typeKey');
        if (!$typeKey) {
            return $this->handleErrorResponse('Missing typeKey param');
        }
        $countryCode = $request->query->get('countryCode');
        $zipCode = $request->query->get('zipCode');
        if (!$countryCode || !$zipCode) {
            return $this->handleErrorResponse('Missing countryCode/zipCode param');
        }

        $parcelShops = [];
        try {
            if ($typeKey === Checkout::UNIFIED_VIEW_TYPE_KEY) {
                $parcelShops = $this->getUnifiedParcelShops($request, $context);
            } else {
                $optionType = $this->typeOptionService->getType($typeKey);
                if (!$optionType) {
                    return $this->handleErrorResponse('Shipping provider is not installed / active');
                }
                //Retrieve data from the provider.
                $parcelShops = $optionType->getParcelShops($request->query, $context);
            }
        } catch (ClientException | SoapFault $e) {
            // The request failed
            return $this->shippingProviderException("{$e->getMessage()} (zipCode: $zipCode)", $typeKey);
        } catch (\Exception $e) {
            $this->logger->error("[WexoShipping] Exception: {$e->getMessage()} (TYPEKEY: $typeKey)");
            return $this->handleErrorResponse('Unknown error. Please try again');
        }

        if (count($parcelShops) <= 0) {
            return $this->shippingProviderException('Empty list', $typeKey);
        }

        return new JsonResponse($parcelShops);
    }

    protected function getUnifiedParcelShops(Request $request, SalesChannelContext $context): array
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('active', true));
        $shippingMethods = $this->shippingMethodRepository->search(
            $criteria,
            $context->getContext()
        )->getEntities();
        $shippingMethods = $shippingMethods->filterByActiveRules($context);
        $shippingMethods = $shippingMethods->filter(
            fn(ShippingMethodEntity $shippingMethod) => $shippingMethod->hasExtension('shippingMethodType')
        );
        $parcelShops = [];
        $activePenalties = [];
        foreach ($shippingMethods as $shippingMethod) {
            $typeKey = $shippingMethod->getExtension('shippingMethodType')->getTypeKey();
            $typeOptionService = $this->typeOptionService->getType($typeKey);
            if ($typeOptionService) {
                $penalties = $shippingMethod->getExtension('shippingMethodType')->getPenalties();
                if ($penalties && Feature::isActive(WexoShipping::WEXO_SHIPPING_FEATURE_FLAG)) {
                    foreach ($penalties as $penalty) {
                        $from = \DateTime::createFromFormat('H:i', $penalty['from']);
                        $to = \DateTime::createFromFormat('H:i', $penalty['to']);
                        $penaltyAmount = $penalty['penalty'];
                        $now = time();
                        if ($from->getTimestamp() <= $now && $to->getTimestamp() >= $now) {
                            $activePenalties[$typeKey] = $penaltyAmount;
                            break;
                        }
                    }
                }

                $parcelShops = array_merge($parcelShops, $typeOptionService->getParcelShops($request->query, $context));
            }
        }
        //Sort the shops by distance + their penalties.
        usort($parcelShops, function (ParcelShopEntity $a, ParcelShopEntity $b) use ($activePenalties) {
            $penaltyA = $activePenalties[$a->getTypeKey()] ?? 0;
            $penaltyB = $activePenalties[$b->getTypeKey()] ?? 0;
            return ($a->getDistance() + $penaltyA) > ($b->getDistance() + $penaltyB);
        });

        return $parcelShops;
    }

    private function shippingProviderException(string $message, string $typeKey): JsonResponse
    {
        $this->logger->error("[WexoShipping] Shipping Provider Exception: $message, (TYPEKEY: $typeKey)");
        return $this->handleErrorResponse('We could not find any parcel shops with the provided information');
    }

    /**
     * We always return 200 OK instead of 400 Bad Request or another form or error code.
     * This is by design since Shopware does not allow any errors in the network tab,
     * since it will show in the webserver logs.
     *
     * This is not best practice, however do not change it, please.
     */
    private function handleErrorResponse($msg): JsonResponse
    {
        return new JsonResponse(
            [
                'error' => $msg
            ],
            200
        );
    }
}
