import deepmerge from 'deepmerge';

const BaseSliderPlugin = window.PluginManager.getPlugin('BaseSlider').get('class')

export default class BlogSliderPlugin extends BaseSliderPlugin {

    /**
     * default slider options
     *
     * @type {*}
     */
    static options = deepmerge(BaseSliderPlugin.options, {
        containerSelector: '[data-blog-slider-container=true]',
        controlsSelector: '[data-blog-slider-controls=true]',
        blogboxMinWidth: '300px',
    });

    /**
     * returns the slider settings for the current viewport
     *
     * @param viewport
     * @private
     */
    _getSettings(viewport) {
        super._getSettings(viewport);

        this._addItemLimit();
    }

    /**
     * extends the slider settings with the slider item limit depending on the blog-box and the container width
     *
     * @private
     */
    _addItemLimit() {
        const containerWidth = this._getInnerWidth();
        const gutter = this._sliderSettings.gutter;
        const itemWidth = parseInt(this.options.blogboxMinWidth.replace('px', ''), 0);

        const itemLimit = Math.floor(containerWidth / (itemWidth + gutter));

        this._sliderSettings.items = Math.max(1, itemLimit);
    }

    /**
     * returns the inner width of the container without padding
     *
     * @returns {number}
     * @private
     */
    _getInnerWidth() {
        const computedStyle = getComputedStyle(this.el);

        if (!computedStyle) return;

        // width with padding
        let width = this.el.clientWidth;

        width -= parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight);

        return width;
    }
}
