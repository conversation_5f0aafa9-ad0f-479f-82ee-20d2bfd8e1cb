$box-standard-height-img: 200px;
$box-image-height-img: 332px;

.blog-slider-box {
    height: 100%;
}

.blog-slider-variant-characteristics {
    min-height: 3em;

    .blog-slider-variant-characteristics-text {
        @include truncate-multiline(1.5em, 2, $sw-background-color);

        font-size: $font-size-sm;
    }

    .blog-slider-variant-characteristics-option {
        font-weight: $font-weight-bold;
    }
}

.blog-slider-image-wrapper {
    height: $box-standard-height-img;
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: 180px;
    margin-bottom: 15px;
    position: relative;
}

.blog-slider-image-link {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.is-cover {
        align-items: flex-start;
    }
}

.blog-slider-image {
    display: block;
    max-width: 100%;
    // 'max-height: 100%' not working in firefox
    max-height: $box-standard-height-img;

    &.is-standard,
    &.is-cover,
    &.is-contain {
        width: 100%;
        height: 100%;
    }

    &.is-cover {
        object-fit: cover;
    }

    &.is-contain {
        object-fit: contain;
    }

    &.is-standard {
        object-fit: scale-down;
    }
}

.blog-slider-image-placeholder {
    max-width: 180px;
    height: 100%;
}

.blog-slider-rating {
    margin-bottom: 10px;
    height: 25px;
}

/* stylelint-disable value-no-vendor-prefix, property-no-vendor-prefix */
// See for further information: https://github.com/postcss/autoprefixer/issues/1141#issuecomment-430938342
.blog-slider-name {
    color: $headings-color;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    height: 44px;
    margin-bottom: 10px;
    font-size: 18px;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;

    &:hover {
        color: var(--text-color-brand-primary);
        text-decoration: none;
    }
}

.blog-slider-description {
    height: 54px;
    margin-top: 10px;
    font-size: 14px;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
/* stylelint-enable value-no-vendor-prefix, property-no-vendor-prefix */

.blog-slider-price-info {
    margin-top: 10px;

    .blog-slider-advanced-list-price-wrapper {
        font-weight: $font-weight-normal;
    }
}

.blog-slider-price-unit {
    height: 36px;
    font-size: 14px;
    line-height: 18px;
    overflow: hidden;
}

.blog-slider-unit-label {
    font-weight: $font-weight-bold;
}

.blog-slider-cheapest-price {
    min-height: 20px;
    font-size: $font-size-sm;
    line-height: 20px;
    margin-bottom: 32px;

    .blog-slider-cheapest-price-price {
        font-weight: $font-weight-bold;
    }

    &.with-regulation-price,
    &.with-list-price {
        margin-bottom: 6px;
    }

    &.with-from-price {
        margin-bottom: 5px;
    }
}

.blog-slider-price {
    color: $headings-color;
    min-height: 20px;
    margin-top: 10px;
    margin-bottom: 0;
    font-size: 18px;
    font-weight: $font-weight-bold;
    line-height: 20px;
    overflow: hidden;

    &.with-list-price {
        color: #e52427;
    }

    .list-price {
        color: $headings-color;
        font-weight: $font-weight-normal;
        padding-left: 5px;
        font-size: 12px;

        .list-price-price {
            text-decoration: line-through;
        }
    }

    .regulation-price {
        color: $headings-color;
        font-weight: $font-weight-normal;
        padding-left: 5px;
        font-size: 12px;
        padding-left: 0;
    }

    .list-price-no-line-through .list-price-price {
        text-decoration: none;
    }

    .strikeprice-text {
        color: $headings-color;
        font-weight: $font-weight-normal;
        padding-left: 5px;
        font-size: 12px;
    }
}

.blog-slider-action {
    margin-top: 20px;
}

.blog-slider-badges {
    position: absolute;
    top: 30px;
    left: -1px;
    z-index: 10;

    .badge {
        padding: 0 8px;
        height: 32px;
        margin: 0 0 8px;
        line-height: 32px;
        font-weight: $font-weight-bold;
        color: $white;
        border-radius: 0 3px 3px 0;
        border-color: transparent;
        font-size: 18px;
        letter-spacing: 0;
    }
}

.blog-slider-box {
    &.box-image {
        .blog-slider-image-wrapper {
            height: $box-image-height-img;
        }

        .blog-slider-image {
            max-height: $box-image-height-img;
        }

        // box-image uses cover mode for standard display mode
        .blog-slider-image.is-standard {
            height: 100%;
            object-fit: cover;
        }
    }
}

.blog-slider-price-wrapper {
    font-size: 18px;
}

.blog-slider-box {
    border-color: $border-color;
    background-color: $white;

    .card-body {
        --#{$prefix}card-spacer-y: #{$spacer};
        --#{$prefix}card-spacer-x: #{$spacer};
    }
}

.blog-slider-name {
    font-weight: $font-weight-semibold;
}

.btn-read {
    @extend .btn-lg;
    @include button-variant($buy-btn-bg, $buy-btn-bg, $buy-btn-color);

    &.disabled,
    &:disabled {
        opacity: 1;
        background: $disabled-btn-bg;
        border-color: $disabled-btn-border-color;
        color: $gray-300;
    }
}
