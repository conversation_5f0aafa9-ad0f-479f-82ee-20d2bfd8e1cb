@import 'layout/header/search-suggest';
@import 'layout/slider/blog-slider';
@import 'layout/slider/blog-slider-box';

.blog-image,
.blog-image-placeholder {
    object-fit: cover;
    width: 100%;
    height: 250px;
}

.blog-card {

    .card-text {
        height: 85px;
        overflow: hidden;
    }

    .card-footer {
        display: flex;
        align-items: center;
        background: none;
        padding: 0;
        margin-top: 25px;

        .blog-author-avatar,
        .blog-image-placeholder {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 20px;
        }

        .blog-author-display-name {
            font-weight: $font-weight-bold;
            margin-bottom: 0;
        }
    }
}

.has-element-loader .cms-element-product-listing {
    .blog-image-link span.icon{
        opacity: 0;
    }

    .blog-date,
    .blog-title a,
    .blog-image-link {
        border-radius: $border-radius;
        color: transparent;
        animation: skeletonShimmer 1.5s linear 0s infinite normal forwards running;
        background: linear-gradient(to right, $gray-300 8%, $gray-100 18%, $gray-300 28%);
        background-size: 800px 100px;
    }
}
