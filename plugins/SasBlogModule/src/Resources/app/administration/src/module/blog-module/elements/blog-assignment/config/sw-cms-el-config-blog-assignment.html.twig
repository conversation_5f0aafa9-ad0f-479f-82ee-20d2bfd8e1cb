<!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
{% block sw_cms_element_assignment_config %}
<div class="sw-cms-el-config-blog-assignment">
    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_cms_element_assignment_config_tabs %}
    <sw-tabs
        position-identifier="sw-cms-element-assignment"
        class="sw-cms-el-config-blog-assignment__tabs"
        default-item="content"
    >
        <template #default="{ active }">
            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_assignment_config_tab_content %}
            <sw-tabs-item
                name="content"
                :title="$tc('sw-cms.elements.general.config.tab.content')"
                :active-tab="active"
            >
                {{ $tc('sw-cms.elements.general.config.tab.content') }}
            </sw-tabs-item>
            {% endblock %}

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_assignment_config_tab_options %}
            <sw-tabs-item
                name="options"
                :title="$tc('sw-cms.elements.general.config.tab.options')"
                :active-tab="active"
            >
                {{ $tc('sw-cms.elements.general.config.tab.options') }}
            </sw-tabs-item>
            {% endblock %}
        </template>

        <template #content="{ active }">
            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_assignment_config_content %}
            <sw-container
                v-if="active === 'content'"
                class="sw-cms-el-config-blog-assignment__tab-content"
            >
                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block sw_cms_element_assignment_config_content_warning_text %}
                <sw-alert
                    v-if="isProductPageType"
                    class="sw-cms-el-config-blog-assignment__warning-text"
                    variant="info"
                >
                    {{ $tc('sas-blog.elements.assignment.config.infoText.productDetailElement') }}
                </sw-alert>
                {% endblock %}

                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block sw_cms_element_assignment_config_content_products %}
                <sw-entity-single-select
                    v-if="!isProductPageType"
                    ref="cmsProductSelection"
                    v-model="element.config.product.value"
                    entity="product"
                    class="sw-cms-el-config-blog-assignment__products"
                    :label="$tc('sas-blog.elements.assignment.config.label.selection')"
                    :placeholder="$tc('sas-blog.elements.assignment.config.placeholder.selection')"
                    :criteria="productCriteria"
                    :context="productSelectContext"
                    show-clearable-button
                    {% if VUE3 %}
                    @update:value="onProductChange"
                    {% else %}
                    @change="onProductChange"
                    {% endif %}
                >
                    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                    {% block sw_entity_single_select_variant_selected_item %}
                    <template #selection-label-property="{ item }">
                        <sw-product-variant-info
                            :variations="item.variation"
                        >
                            {{ item.translated.name || item.name }}
                        </sw-product-variant-info>
                    </template>
                    {% endblock %}

                    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                    {% block sw_entity_single_select_variant_result_item %}
                    <template #result-item="{ item, index }">
                        <sw-select-result
                            v-bind="{ item, index }"
                        >
                            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                            {% block sw_entity_single_select_base_results_list_result_label %}
                            <span class="sw-select-result__result-item-text">
                                <sw-product-variant-info :variations="item.variation">
                                    {{ item.translated.name || item.name }}
                                </sw-product-variant-info>
                            </span>
                            {% endblock %}
                        </sw-select-result>
                    </template>
                    {% endblock %}
                </sw-entity-single-select>
                {% endblock %}
            </sw-container>
            {% endblock %}

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_assignment_config_options %}
            <sw-container
                v-else-if="active === 'options'"
                class="sw-cms-el-config-blog-assignment__tab-options"
            >
                <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                {% block sw_cms_element_assignment_config_options_min_width %}
                <sw-text-field
                    {% if VUE3 %}
                    v-model:value="element.config.elMinWidth.value"
                    {% else %}
                    v-model="element.config.elMinWidth.value"
                    {% endif %}
                    :label="$tc('sas-blog.elements.assignment.config.label.minWidth')"
                    :placeholder="$tc('sas-blog.elements.assignment.config.placeholder.minWidth')"
                />
                {% endblock %}
            </sw-container>
            {% endblock %}
        </template>
    </sw-tabs>
    {% endblock %}
</div>
{% endblock %}
