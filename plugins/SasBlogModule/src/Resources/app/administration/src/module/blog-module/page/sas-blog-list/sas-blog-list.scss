@import "~scss/variables";

$sw-property-list-color-error: $color-crimson-500;
$sw-property-list-color-success: $color-emerald-500;

.sas-blog-list {

    .is--inactive {
        color: $sw-property-list-color-error;
    }

    .is--active {
        color: $sw-property-list-color-success;
    }

    .sw-data-grid__cell--title {
        width: 82%;
    }

    .sw-page__content {
        .sw-page__side-content {
            grid-area: a;

            .sw-tree .sw-tree-actions__headline {
                font-size: 14px;
                font-weight: bold;
                height: 64px;
                color: $color-darkgray-200;
            }

            .sw-tree__content, .sw-tree .sw-tree-actions__headline {
                border-left: 1px solid $color-gray-300;
            }
        }

        .sw-page__main-content {
            grid-area: b;
        }

        .sw-page__sidebar {
            grid-area: c;
            padding: 40px 15px;
        }

        &.has--smart-bar.has--side-content {
            grid-template-columns: 120px 440px auto;
            grid-template-areas: "c a b";
        }

        .sw-data-grid__cell--active {
            .sw-data-grid__cell-content {
                justify-content: center;
            }
        }
    }
}
