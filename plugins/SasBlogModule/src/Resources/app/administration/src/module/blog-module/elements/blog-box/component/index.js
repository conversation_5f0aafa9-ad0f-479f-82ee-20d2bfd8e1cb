import template from './sw-cms-el-blog-box.html.twig';
import './sw-cms-el-blog-box.scss';

const { Mixin, Filter } = Shopware;

/**
 * @private
 * @package buyers-experience
 */
export default {
    template,

    mixins: [
        Mixin.getByName('cms-element'),
        Mixin.getByName('placeholder'),
    ],

    computed: {
        blog() {
            if (!this.element?.data?.blog) {
                return {
                    name: 'Lorem ipsum dolor',
                    description: `Lorem ipsum dolor sit amet, consetetur sadipscing elitr,
                    sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat,
                    sed diam voluptua.`,
                    price: [
                        { gross: 19.90 },
                    ],
                    cover: {
                        media: {
                            url: '/administration/static/img/cms/preview_glasses_large.jpg',
                            alt: 'Lorem Ipsum dolor',
                        },
                    },
                };
            }

            return this.element.data.blog;
        },

        displaySkeleton() {
            return !this.element?.data?.blog;
        },

        mediaUrl() {
            if (this.blog.media) {
                if (this.blog.media.id) {
                    return this.blog.media.url;
                }

                return this.assetFilter(this.blog.media.url);
            }

            return this.assetFilter('administration/static/img/cms/preview_glasses_large.jpg');
        },

        altTag() {
            if (!this.blog?.media?.alt) {
                return null;
            }

            return this.blog.media.alt;
        },

        displayModeClass() {
            if (this.element.config.displayMode.value === 'standard') {
                return null;
            }

            return `is--${this.element.config.displayMode.value}`;
        },

        verticalAlignStyle() {
            if (!this.element.config?.verticalAlign?.value) {
                return null;
            }

            return `align-content: ${this.element.config.verticalAlign.value};`;
        },

        assetFilter() {
            return Filter.getByName('asset');
        },

        truncateFilter() {
            return Filter.getByName('truncate');
        },

        currencyFilter() {
            return Filter.getByName('currency');
        },
    },

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            this.initElementConfig('blog-box');
            this.initElementData('blog-box');
        },
    },
};
