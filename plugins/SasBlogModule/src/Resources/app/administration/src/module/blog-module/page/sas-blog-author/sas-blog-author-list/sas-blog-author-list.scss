@import "~scss/variables";

.sas-blog-author-list {

    .sw-data-grid__cell--title {
        width: 82%;
    }

    .sw-page__content {
        .sw-page__main-content {
            grid-area: b;
            border-left: 1px solid $color-gray-300;
        }

        .sw-page__sidebar {
            grid-area: a;
            padding: 40px 15px;
        }

        &.has--smart-bar.has--side-bar {
            grid-template-columns: 120px auto;
            grid-template-areas: "a b";
        }
    }
}
