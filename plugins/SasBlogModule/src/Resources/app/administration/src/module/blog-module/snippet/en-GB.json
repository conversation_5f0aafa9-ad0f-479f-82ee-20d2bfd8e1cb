{"sas-blog": {"general": {"mainMenuItemGeneral": "Blog", "mainMenuItemList": "Blog", "mainMenuItemAdd": "New blog entry", "descriptionTextModule": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Blog Einträge.", "placeholderSearchBar": "Search all blog entries", "tooltipLanguageSwitch": "Create an entry first in the system language"}, "list": {"textBlogOverview": "Blog entries", "buttonAddBlogEntry": "Create blog entry", "messageEmpty": "No blog entries ...", "table": {"title": "Title", "active": "Active", "author": "Author"}}, "detail": {"productAssignment": {"assignEmptyStateDescription": "You can add products to this article. Within the product detail page, we'll show the related articles.", "label": "Product Assignment", "btnView": "View Products Assignment", "preview": {"label": {"assignmentTitle": "Blog assignment title"}}, "config": {"label": {"selection": "Product", "minWidth": "Minimal width"}, "placeholder": {"selection": "Select product...", "minWidth": "Enter minimal width..."}, "infoText": {"productDetailElement": "This element's product data is automatically loaded by the product associated with this layout. The content just shows a sample preview."}}}, "serp": {"title": "SERP preview", "metaDescription": "Meta Description", "metaTitle": "Meta Title", "metaKeywords": "Meta Keywords"}, "sidebar": {"heading": "Blog detail", "basicInformation": "Basic information", "settings": "Settings"}, "saveBlogEntry": "Save entry", "buttonCancel": "Cancel", "activeLabel": "Entry active", "contentLabel": "Markdown content", "slugLabel": "Slug", "slugPlaceholder": "Undefined", "titleLabel": "Title", "mediaLabel": "Teaser Image", "titlePlaceholder": "Enter your blog title", "teaserLabel": "Teaser", "teaserImageLabel": "Teaser Image", "teaserPlaceholder": "The teaser text which will be shown as an introduction within the blog listing ...", "editorCardTitle": "Your article", "editorLabel": "Blog Content", "editorPlaceholder": "Start your blog post ...", "publishedAtLabel": "Published at", "visibility": {"label": "Article active"}, "detailTeaser": {"label": "Show teaser image on detail page"}, "editor": {"headerPlaceholder": "Type in your heading text", "paragraphPlaceholder": "Start typing ...", "warningTitle": "Warning title", "warningMessage": "Write your warning message ...", "quotePlaceholder": "Tell me about your quote ...", "quoteCaption": "... the caption"}, "notification": {"save-success": {"title": "Success!", "text": "Your blog entry has been saved."}, "error": {"missingCategory": "Please choose at least one blog category!", "pageInvalid": "Errors occured. Please check the error list in the editor."}}, "author": {"label": "Author", "placeholder": "Select a blog's author"}, "category": {"label": "Categories", "placeholder": "Select blog's categories"}, "salesChannels": {"labelSalesChannels": "Sales Channel", "placeholderSalesChannels": "Add Sales Channels..."}, "warning": {"maxTextCountReached": "There're {char<PERSON>eft} character(s) left"}}, "elements": {"assignment": {"label": "Blog Assignment", "preview": {"label": {"assignmentTitle": "Blog Assignment"}}, "config": {"label": {"selection": "Product", "minWidth": "Minimal width"}, "placeholder": {"selection": "Select product...", "minWidth": "Enter minimal width..."}, "infoText": {"productDetailElement": "This element's blog data is automatically loaded by the product associated with this layout. The content just shows a sample preview."}}}, "blogBox": {"label": "Blog box", "component": {"label": {"actionRead": "Read Article", "badgeNew": "New", "priceUnitContent": "Content:"}}, "config": {"label": {"selection": "Blog", "layoutType": "Layout type", "layoutTypeStandard": "Standard", "layoutTypeImage": "Big image", "layoutTypeMinimal": "Minimal content"}, "placeholder": {"selection": "Select a blog..."}}}, "listing": {"config": {"paginationCount": "Pagination count", "showFilter": {"label": "Listing filter", "options": {"all": "All", "select": "Select filter", "filter": {"category": "Show Category filter", "author": "Show Author filter"}}}, "showType": {"label": "Show type", "options": {"all": "All", "select": "Select categories"}}, "blogCategories": {"label": "Categories", "placeholder": "Select categories"}}, "component": {"elementHeadline": "Blog listing element", "infoText": "The blog listing content will be automatically fetched by the element."}}, "detail": {"config": {"showMeta": {"label": "Detail view meta information", "showAuthor": "Show author", "showCategory": "Show category"}}, "component": {"elementHeadline": "Blog detail element", "infoText": "The blog detail content will be automatically fetched by the element. There's currently no configuration available."}}, "single-select": {"label": "Blog Single Select", "selectFieldLabel": "Select a blog entry", "previewText": "<PERSON>rem I<PERSON>um Dolor"}, "newestListing": {"config": {"itemCount": "Item count", "showType": {"label": "Show type", "options": {"all": "All", "select": "Select categories"}}, "blogCategories": {"label": "Categories", "placeholder": "Select categories"}}, "component": {"elementHeadline": "Blog newest listing element", "infoText": "The blog newest listing content will be automatically fetched by the element."}, "preview": {"label": "Blog Newest Listing"}}}, "blocks": {"blog": {"listing": {"previewTitle": "Blog Listing", "label": "Blog Listing"}, "detail": {"previewTitle": "Blog Detail View", "label": "Blog Detail"}, "singleEntry": {"previewTitle": "Single Blog Entry", "label": "Single Blog Entry"}, "newestListing": {"previewTitle": "Blog Newest Listing", "label": "Blog Newest Listing"}, "blog-assignment": {"previewTitle": "Blog Assignment", "label": "Blog Assignment"}}}}, "sw-seo-url-template-card": {"routeNames": {"sas-frontend-blog-detail": "Blog Detail Page"}}, "sas-blog-category": {"general": {"treeHeadline": "Blog categories", "mainMenuItemIndex": "Categories", "mainMenuItemList": "Overview", "descriptionTextModule": "Manage categories here.", "headlineCategories": "Categories", "placeholderSearchBar": "Search categories...", "buttonCreate": "Create category", "buttonSafeCategory": "Save", "descriptionLabel": "Description", "messageSaveSuccess": "Category \"{name}\" has been saved.", "useAsLogo": "Use as display image", "treeHeadSelected": "{count} selected", "emptyStateHeadline": "No category selected", "actions": {"actionsDisabledInLanguage": "Categories can only be created in the default language.", "createSub": "New subcategory", "createBefore": "New category before", "createAfter": "New category after"}}, "modal": {"textDeleteMultipleConfirm": "Are you sure you want to delete the {count} selected blogs?"}}, "sas-blog-author": {"general": {"mainMenuItemList": "Author"}, "list": {"textBlogOverview": "Blog authors", "buttonAdd": "Add new blog author", "messageEmpty": "No blog authors ...", "table": {"fullName": "Full name", "displayName": "Display name", "email": "Email", "salutation": "Salutation"}}, "detail": {"title": "Blog author", "messageSaveSuccess": "Author \"{name}\" has been saved.", "buttonSave": "Save", "buttonCancel": "Cancel", "buttonEdit": "Edit", "firstName": {"label": "First Name", "placeholder": "Enter author's first name"}, "lastName": {"label": "Last Name", "placeholder": "Enter author's last name"}, "displayName": {"label": "Display Name", "placeholder": "Enter author's display name"}, "email": {"label": "Email", "placeholder": "Enter author's email"}, "description": {"label": "Description", "placeholder": "Enter author's short description"}, "salutation": {"label": "Salutation", "placeholder": "Salutation"}, "media": {"label": "Author's avatar"}}}, "sas-blog-tree-item": {"actions": {"edit": "Edit"}}, "sw-cms": {"detail": {"notification": {"messageMissingFields": "Please fill in all required fields.", "messageSlugDuplicated": "This slug is already in use. Please choose another one."}}}, "sw-privileges": {"permissions": {"sas-blog": {"label": "Blog"}, "sas-blog-author": {"label": "Author Blog"}, "sas-blog-category": {"label": "Category Blog"}}}}