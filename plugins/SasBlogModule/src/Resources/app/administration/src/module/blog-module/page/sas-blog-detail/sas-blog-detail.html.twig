{% block sw_cms_detail_toolbar_page_name %}
    <h2 class="sw-cms-detail__page-name">
        {{ placeholder(blog, 'title') }}
    </h2>
{% endblock %}

{% block sw_cms_detail_toolbar_language_switch %}
    <sw-language-switch
        slot="language-switch"
        :disabled="isLoading"
        :save-changes-function="saveOnLanguageChange"
        @on-change="onChangeLanguage"
    />
{% endblock %}

{% block sw_cms_detail_toolbar_back_button %}
    <router-link
        :to="backPath"
        class="sw-cms-detail__back-btn"
    >
        <sw-icon name="regular-times" />
    </router-link>
{% endblock %}

{% block sw_cms_detail_toolbar_page_type %}
{% endblock %}

{% block sw_cms_detail_toolbar_actions_save %}
    <sw-button-process
        v-tooltip.bottom="tooltipSave"
        class="sw-cms-detail__save-action"
        :is-loading="isLoading"
        :process-success="isSaveSuccessful"
        :disabled="isLoading || page.locked"
        variant="primary"
        @process-finish="saveFinish"
        @click="onSaveBlog"
    >
        {{ $tc('sw-cms.detail.label.buttonSave') }}
    </sw-button-process>
{% endblock %}


{% block sw_cms_detail_stage_empty_stade_content %}
    <div class="sw-cms-detail__empty-stage-content">
        <h2 class="sw-cms-detail__empty-stage-headline">
            {{ $tc('sw-cms.detail.label.headlineEmptyState') }}
        </h2>
        <p class="sw-cms-detail__empty-stage-claim">
            {{ $tc('sw-cms.detail.label.claimEmptyState') }}
        </p>
        <sw-cms-stage-add-section
            ref="cmsStageAddSection"
            :key="0"
            :force-choose="true"
            @stage-section-add="addAdditionalSection($event, 0)"
        />
    </div>
{% endblock %}
{% block sw_cms_detail_stage_add_first_section %}
    <sw-cms-stage-add-section
        :key="0"
        @stage-section-add="addAdditionalSection($event, 0)"
    />
{% endblock %}
{% block sw_cms_detail_stage_add_last_section %}
    <sw-cms-stage-add-section
        :key="page.sections.length + 1"
        @stage-section-add="addAdditionalSection($event, page.sections.length)"
    />
{% endblock %}


{% block sw_cms_detail_stage_content_section %}
    <sas-cms-section
        class="sw-cms-stage-section sas-blog-cms-stage-section"
        :key="section.id"
        :page="page"
        :section="section"
        :active="selectedSection !== null && selectedSection.id === section.id"
        @page-config-open="pageConfigOpen"
        @block-duplicate="onBlockDuplicate"
    />
{% endblock %}

{% block sw_cms_detail_sidebar %}
    <sas-cms-sidebar
        v-if="blog"
        ref="cmsSidebar"
        :page="page"
        :blog="blog"
        :demo-entity="currentMappingEntity"
        :demo-entity-id-prop="demoEntityId"
        @demo-entity-change="onDemoEntityChange"
        @block-duplicate="onBlockDuplicate"
        @section-duplicate="onSectionDuplicate"
        @block-stage-drop="onPageUpdate"
        @block-navigator-sort="onBlockNavigatorSort"
        @page-type-change="onPageTypeChange"
        @page-update="onPageUpdate"
        @page-save="onPageSave"
        @open-layout-assignment="onOpenLayoutAssignment"
    />
{% endblock %}

