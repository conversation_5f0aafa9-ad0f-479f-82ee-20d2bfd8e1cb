<!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
{% block sw_cms_element_blog_box %}
<div
    v-if="blog"
    class="sw-cms-el-blog-box"
    :class="`box-${element.config.boxLayout.value}`"
>

    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_cms_element_blog_box_content %}
    <div
        class="sw-cms-el-blog-box__content"
        :style="verticalAlignStyle"
    >
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_element_blog_box_media %}
        <div class="sw-cms-el-blog-box__media">
            <a
                href="#"
                :title="placeholder(blog, 'title')"
                class="sw-cms-el-blog-box__image-link"
            >
                <img
                    class="sw-cms-el-blog-box__image"
                    :class="displayModeClass"
                    :src="mediaUrl"
                    :alt="altTag"
                >
            </a>
        </div>
        {% endblock %}

        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_element_blog_box_info_skeleton %}
        <div
            v-if="displaySkeleton"
            class="sw-cms-el-blog-box__info"
        >

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_blog_box_name_skeleton %}
            <div class="sw-cms-el-blog-box__skeleton-name"></div>
            {% endblock %}

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_blog_box_description_skeleton %}
            <div class="sw-cms-el-blog-box__skeleton-description"></div>
            <div class="sw-cms-el-blog-box__skeleton-description"></div>
            {% endblock %}

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_blog_box_actions_skeleton %}
            <div class="sw-cms-el-blog-box__actions">
                <a
                    href="#"
                    class="sw-cms-el-blog-box__buy-action"
                >
                    {{ $tc('sas-blog.elements.blogBox.component.label.actionRead') }}
                </a>
            </div>
            {% endblock %}

        </div>
        {% endblock %}

        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_element_blog_box_info %}
        <div
            v-else
            class="sw-cms-el-blog-box__info"
        >

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_blog_box_name %}
            <a
                href="#"
                class="sw-cms-el-blog-box__name"
                :title="placeholder(blog, 'name')"
            >
                {{ placeholder(blog, 'title', blog.translated.title) }}
            </a>
            {% endblock %}

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_blog_box_description %}
            <div
                v-if="element.config.boxLayout.value === 'standard'"
                class="sw-cms-el-blog-box__description"
            >
                {{ truncateFilter(placeholder(blog, 'teaser', blog.translated.teaser), 200, true, '...') }}
            </div>
            {% endblock %}

            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_element_blog_box_actions %}
            <div class="sw-cms-el-blog-box__actions">
                <a
                    href="#"
                    class="sw-cms-el-blog-box__buy-action"
                >
                    {{ $tc('sas-blog.elements.blogBox.component.label.actionRead') }}
                </a>
            </div>
            {% endblock %}

        </div>
        {% endblock %}

    </div>
    {% endblock %}

</div>
{% endblock %}
