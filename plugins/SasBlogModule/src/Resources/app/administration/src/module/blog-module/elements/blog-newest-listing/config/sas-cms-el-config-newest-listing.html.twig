{% block sas_cms_element_newest_listing_config %}
<div class="sas-cms-el-config-newest-listing">

    {% block sas_cms_element_newest_listing_config_itemCount_field %}
        <sw-number-field
            numberType="int"
            :step="1"
            :min="1"
            :max="null"
            v-model="element.config.itemCount.value"
            :digits="2"
            :allowEmpty="false"
            :label="$tc('sas-blog.elements.newestListing.config.itemCount')">
        </sw-number-field>
    {% endblock %}

    {% block sas_cms_element_blog_listing_config_show_type %}
        <sw-select-field :label="$tc('sas-blog.elements.newestListing.config.showType.label')"
                         v-model="element.config.showType.value">
            {% block sas_cms_element_download_card_config_layout_select_options %}
                <option value="all">{{ $tc('sas-blog.elements.newestListing.config.showType.options.all') }}</option>
                <option value="select">{{ $tc('sas-blog.elements.newestListing.config.showType.options.select') }}</option>
            {% endblock %}
        </sw-select-field>
    {% endblock %}

    {% block sas_cms_element_blog_listing_config_select_categories %}
        <sas-blog-category-tree-field
            v-if="element.config.showType.value !== 'all' && selectedCategories"
            v-model="selectedCategories"
            :label="$tc('sas-blog.elements.newestListing.config.blogCategories.label')"
            :categoriesCollection="selectedCategories"
            class="sw-product-detail__select-category"
            :placeholder="$tc('sas-blog.elements.newestListing.config.blogCategories.placeholder')">
        </sas-blog-category-tree-field>
    {% endblock %}

</div>
{% endblock %}
