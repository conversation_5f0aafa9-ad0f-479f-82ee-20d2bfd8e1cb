{"sas-blog": {"general": {"mainMenuItemGeneral": "Blog", "mainMenuItemList": "Blog", "mainMenuItemAdd": "Blog Eintrag anlegen", "descriptionTextModule": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Blog Einträge.", "placeholderSearchBar": "Durchsuche alle Einträge", "tooltipLanguageSwitch": "Du musst zu<PERSON>t einen Eintrag in der Systemsprache anlegen"}, "list": {"textBlogOverview": "Blog Einträge", "buttonAddBlogEntry": "Blog Eintrag erstellen", "messageEmpty": "<PERSON><PERSON> Ein<PERSON>ä<PERSON> vorhanden", "table": {"title": "Titel", "active": "Aktiv", "author": "Autor"}}, "detail": {"productAssignment": {"assignEmptyStateDescription": "Sie können Produkte zu diesem Artikel hinzufügen. Auf der Produkt-Detailseite zeigen wir die zugehörigen Artikel an.", "label": "Produktzuweisung", "btnView": "Produktzuweisung anzeigen", "preview": {"label": {"assignmentTitle": "Titel der Blog-Zuweisung"}}, "config": {"label": {"selection": "Produkt", "minWidth": "Minimale Breite"}, "placeholder": {"selection": "Produkt auswählen...", "minWidth": "Minimale Breite eingeben..."}, "infoText": {"productDetailElement": "Die Produktdaten dieses Elements werden automatisch vom mit diesem Layout verknüpften Produkt geladen. Der Inhalt zeigt nur eine Beispielvorschau."}}}, "serp": {"title": "SERP Vorschau", "metaDescription": "Meta-Beschreibung", "metaTitle": "Meta-Titel", "metaKeywords": "Meta-Keywords"}, "sidebar": {"heading": "Blog detail", "basicInformation": "Grundlegende Information", "settings": "Einstellungen"}, "saveBlogEntry": "Eintrag speichern", "buttonCancel": "Abbrechen", "activeLabel": "Artikel aktiv", "contentLabel": "Markdown Inhalt", "slugLabel": "Slug", "slugPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleLabel": "Titel", "mediaLabel": "Teaser Image", "titlePlaceholder": "Geben Sie den Titel Ihres Blogs ein", "teaserLabel": "Teaser", "teaserImageLabel": "Teaser Bild", "teaserPlaceholder": "Der Teaser Text, welcher im Blog Listing angezeigt wird ...", "editorCardTitle": "<PERSON><PERSON>", "editorLabel": "Blog Inhalt", "editorPlaceholder": "Starte deinen Blog Beitrag ...", "publishedAtLabel": "Erstellungsdatum", "visibility": {"label": "Artikel aktiv"}, "detailTeaser": {"label": "Zeige Teaser Image auf Detailseite"}, "editor": {"headerPlaceholder": "Überschrift", "paragraphPlaceholder": "<PERSON>e an zu schreiben ...", "warningTitle": "Titel der Warnung", "warningMessage": "<PERSON><PERSON> ...", "quotePlaceholder": "Verrate mir dein Zitat ...", "quoteCaption": "... und den Autor"}, "notification": {"save-success": {"title": "Erfolreich!", "text": "<PERSON>in Blog Artikel wurde gespeichert."}, "error": {"missingCategory": "Bitte wähle mind. eine Kategorie!", "pageInvalid": "Einige Fehler sind aufgetreten. Bitte überprüfe die Liste im Editor."}}, "author": {"label": "Autor", "placeholder": "<PERSON><PERSON><PERSON><PERSON> einen Autor"}, "category": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>e eine Kategorie"}, "salesChannels": {"labelSalesChannels": "Verkaufskanal", "placeholderSalesChannels": "Wähle Verkaufskanäle aus ..."}, "warning": {"maxTextCountReached": "Es sind noch {char<PERSON>eft} Zeichen übrig"}}, "elements": {"assignment": {"label": "Blog Zuweisung", "preview": {"label": {"assignmentTitle": "Blog Zuweisung"}}, "config": {"label": {"selection": "Produkt", "minWidth": "Minimale Breite"}, "placeholder": {"selection": "Produkt auswählen...", "minWidth": "Minimale Breite eingeben..."}, "infoText": {"productDetailElement": "Die Blogdaten dieses Elements werden automatisch vom mit diesem Layout verknüpften Produkt geladen. Der Inhalt zeigt nur eine Beispielvorschau."}}}, "blogBox": {"label": "Blog-Box", "component": {"label": {"actionRead": "<PERSON><PERSON><PERSON> lesen", "badgeNew": "<PERSON>eu", "priceUnitContent": "Inhalt:"}}, "config": {"label": {"selection": "Blog", "layoutType": "Layout-Typ", "layoutTypeStandard": "Standard", "layoutTypeImage": "Großes Bild", "layoutTypeMinimal": "Minimaler Inhalt"}, "placeholder": {"selection": "Wählen Sie einen Blog aus..."}}}, "listing": {"config": {"paginationCount": "Pagination count", "showFilter": {"label": "<PERSON><PERSON><PERSON>", "options": {"all": "Alle", "select": "<PERSON><PERSON> au<PERSON>", "filter": {"category": "<PERSON><PERSON><PERSON>", "author": "Zeige Autor Filter"}}}, "showType": {"label": "Anzeigetyp", "options": {"all": "Alle", "select": "Kate<PERSON><PERSON> auswählen"}}, "blogCategories": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Kate<PERSON><PERSON> auswählen"}}, "component": {"elementHeadline": "Blog Listen Element", "infoText": "Der Blog Listen Inhalt wird automatisch in der Storefront ausgespielt."}}, "detail": {"config": {"showMeta": {"label": "Detail Ansicht Meta Informationen", "showAuthor": "Zeige Autor", "showCategory": "<PERSON><PERSON><PERSON>"}}, "component": {"elementHeadline": "Blog Detail Element", "infoText": "Der Blog Inhalt wird automatisch in der Storefront ausgespielt. Derzeit gibt es keine Element Konfiguration."}}, "single-select": {"label": "Blog Single Select", "selectFieldLabel": "Wähle einen Blog Eintrag", "previewText": "<PERSON>rem I<PERSON>um Dolor"}, "newestListing": {"config": {"itemCount": "<PERSON><PERSON><PERSON> der Artikel", "showType": {"label": "<PERSON><PERSON>igen", "options": {"all": "Alle", "select": "Kate<PERSON><PERSON> auswählen"}}, "blogCategories": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Kate<PERSON><PERSON> auswählen"}}, "component": {"elementHeadline": "Blog neuestes Listenelement", "infoText": "Der Inhalt des Blogs mit den neuesten Einträgen wird automatisch von diesem Element abgerufen."}, "preview": {"label": "Blog Neueste Auflistung"}}}, "blocks": {"blog": {"listing": {"previewTitle": "Blog Liste", "label": "Blog Listen Ansicht"}, "detail": {"previewTitle": "Blog Detail", "label": "Blog Detail"}, "singleEntry": {"previewTitle": "Einzelner Blogeintrag", "label": "Einzelner Blogeintrag"}, "newestListing": {"previewTitle": "Blog Neueste Auflistung", "label": "Blog Neueste Auflistung"}, "blog-assignment": {"previewTitle": "Blog-Zuweisung", "label": "Blog-Zuweisung"}}}}, "sw-seo-url-template-card": {"routeNames": {"sas-frontend-blog-detail": "Blog Detailseite"}}, "sas-blog-category": {"general": {"treeHeadline": "Blog Kategorien", "mainMenuItemIndex": "<PERSON><PERSON><PERSON>", "mainMenuItemList": "Overview", "descriptionTextModule": "<PERSON>er<PERSON><PERSON> hier deine Kategorien.", "headlineCategories": "<PERSON><PERSON><PERSON>", "placeholderSearchBar": "<PERSON>e in Kategorien...", "buttonCreate": "<PERSON><PERSON><PERSON>", "buttonSafeCategory": "Speichern", "descriptionLabel": "Beschreibung", "messageSaveSuccess": "<PERSON><PERSON><PERSON> \"{name}\" wurde g<PERSON><PERSON><PERSON><PERSON>.", "useAsLogo": "Use as display image", "treeHeadSelected": "{count} ausgewählt", "emptyStateHeadline": "<PERSON><PERSON> Kategorie ausgewählt", "actions": {"actionsDisabledInLanguage": "Kategorien können nur in der Default Sprache erstellt werden.", "createSub": "Neue Unterkategorie", "createBefore": "Neue Kategorie davor", "createAfter": "Neue Kategorie <PERSON>"}}, "modal": {"textDeleteMultipleConfirm": "<PERSON><PERSON>chtest Du diese {count} blogs Kategorien wirklich löschen?"}}, "sas-blog-author": {"general": {"mainMenuItemList": "Autor"}, "list": {"textBlogOverview": "Autoren", "buttonAdd": "<PERSON><PERSON>", "messageEmpty": "<PERSON><PERSON> ...", "table": {"fullName": "Voller Name", "displayName": "Anzeigename", "email": "Email", "salutation": "<PERSON><PERSON><PERSON>"}}, "detail": {"title": "Autor", "messageSaveSuccess": "Autor \"{name}\" wurde g<PERSON><PERSON><PERSON>.", "buttonSave": "Speichern", "buttonCancel": "Abbrechen", "buttonEdit": "Editieren", "firstName": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Gebe den Vornamen des Autors ein"}, "lastName": {"label": "Nachname", "placeholder": "<PERSON><PERSON><PERSON> den Nachnamen des Autors ein"}, "displayName": {"label": "Anzeigename", "placeholder": "Gebe den Anzeigenamen des Autors ein"}, "email": {"label": "Email", "placeholder": "<PERSON><PERSON>e die Email Adresse des Autors ein"}, "description": {"label": "Beschreibung", "placeholder": "Gebe eine kurze Beschreibung des Autors ein"}, "salutation": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>"}, "media": {"label": "Avatar des Autors"}}}, "sas-blog-tree-item": {"actions": {"edit": "Editieren"}}, "sw-cms": {"detail": {"notification": {"messageMissingFields": "Bitte fülle alle Pflichtfelder aus.", "messageSlugDuplicated": "<PERSON>ser Slug wird bereits verwendet. Bitte wählen Si<PERSON> einen anderen."}}}, "sw-privileges": {"permissions": {"sas-blog": {"label": "Blog"}, "sas-blog-author": {"label": "Blog Autor"}, "sas-blog-category": {"label": "Blog Kategorien"}}}}