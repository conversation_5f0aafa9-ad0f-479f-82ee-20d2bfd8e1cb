<!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
{% block sw_cms_element_blog_box_config %}
<div class="sw-cms-el-config-blog-box">
    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_cms_element_blog_box_config_blog_select %}
    <sw-entity-single-select
        ref="cmsBlogSelection"
        {% if VUE3 %}
        v-model:value="element.config.blog.value"
        {% else %}
        v-model="element.config.blog.value"
        {% endif %}
        :label="$tc('sas-blog.elements.blogBox.config.label.selection')"
        :placeholder="$tc('sas-blog.elements.blogBox.config.placeholder.selection')"
        entity="blog"
        :criteria="blogCriteria"
        :context="blogSelectContext"
        show-clearable-button
        {% if VUE3 %}
        @update:value="onBlogChange"
        {% else %}
        @change="onBlogChange"
        {% endif %}
    >
        <template #result-item="{ item, index }">
            <slot
                name="result-item"
                v-bind="{ item, index }"
            >
                <sw-select-result
                    v-bind="{ item, index }"
                >
                    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
                    {% block sw_entity_single_select_base_results_list_result_label %}
                    <span class="sw-select-result__result-item-text">
                       {{ item.translated.title || item.title }}
                    </span>
                    {% endblock %}
                </sw-select-result>
            </slot>
        </template>
    </sw-entity-single-select>
    {% endblock %}

    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_cms_element_blog_box_config_layout_select %}
    <sw-select-field
        {% if VUE3 %}
        v-model:value="element.config.boxLayout.value"
        {% else %}
        v-model="element.config.boxLayout.value"
        {% endif %}
        :label="$tc('sas-blog.elements.blogBox.config.label.layoutType')"
    >
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_element_blog_box_config_layout_select_options %}
        <option value="standard">
            {{ $tc('sas-blog.elements.blogBox.config.label.layoutTypeStandard') }}
        </option>
        <option value="image">
            {{ $tc('sas-blog.elements.blogBox.config.label.layoutTypeImage') }}
        </option>
        <option value="minimal">
            {{ $tc('sas-blog.elements.blogBox.config.label.layoutTypeMinimal') }}
        </option>
        {% endblock %}
    </sw-select-field>
    {% endblock %}

    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_cms_element_blog_box_config_displaymode_select %}
    <sw-select-field
        {% if VUE3 %}
        v-model:value="element.config.displayMode.value"
        {% else %}
        v-model="element.config.displayMode.value"
        {% endif %}
        :label="$tc('sw-cms.elements.general.config.label.displayMode')"
    >
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_element_blog_box_config_displaymode_select_options %}
        <option value="standard">
            {{ $tc('sw-cms.elements.general.config.label.displayModeStandard') }}
        </option>
        <option value="cover">
            {{ $tc('sw-cms.elements.general.config.label.displayModeCover') }}
        </option>
        <option value="contain">
            {{ $tc('sw-cms.elements.general.config.label.displayModeContain') }}
        </option>
        {% endblock %}
    </sw-select-field>
    {% endblock %}

    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_cms_element_blog_box_config_settings_vertical_align %}
    <sw-select-field
        {% if VUE3 %}
        v-model:value="element.config.verticalAlign.value"
        {% else %}
        v-model="element.config.verticalAlign.value"
        {% endif %}
        :label="$tc('sw-cms.elements.general.config.label.verticalAlign')"
        :placeholder="$tc('sw-cms.elements.general.config.label.verticalAlign')"
    >
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_element_blog_box_config_settings_vertical_align_options %}
        <option value="flex-start">
            {{ $tc('sw-cms.elements.general.config.label.verticalAlignTop') }}
        </option>
        <option value="center">
            {{ $tc('sw-cms.elements.general.config.label.verticalAlignCenter') }}
        </option>
        <option value="flex-end">
            {{ $tc('sw-cms.elements.general.config.label.verticalAlignBottom') }}
        </option>
        {% endblock %}
    </sw-select-field>
    {% endblock %}
</div>
{% endblock %}
