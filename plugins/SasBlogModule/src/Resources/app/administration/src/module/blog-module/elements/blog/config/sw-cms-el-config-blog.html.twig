{% block sas_cms_element_blog_listing_config %}
<div class="sw-cms-el-config-blog">

    {% block sas_cms_element_blog_listing_config_paginationCount_field %}
        <sw-number-field
            numberType="int"
            :step="1"
            :min="1"
            :max="null"
            v-model="element.config.paginationCount.value"
            :digits="2"
            :allowEmpty="false"
            :label="$tc('sas-blog.elements.listing.config.paginationCount')">
        </sw-number-field>
    {% endblock %}

    {% block sas_cms_element_blog_listing_config_show_type %}
        <sw-select-field :label="$tc('sas-blog.elements.listing.config.showType.label')"
                         v-model="element.config.showType.value">
            {% block sas_cms_element_download_card_config_layout_select_options %}
                <option value="all">{{ $tc('sas-blog.elements.listing.config.showType.options.all') }}</option>
                <option value="select">{{ $tc('sas-blog.elements.listing.config.showType.options.select') }}</option>
            {% endblock %}
        </sw-select-field>
    {% endblock %}


    {% block sas_cms_element_blog_listing_config_select_categories %}
        <sas-blog-category-tree-field
            v-if="element.config.showType.value !== 'all' && selectedCategories"
            v-model="selectedCategories"
            :label="$tc('sas-blog.elements.listing.config.blogCategories.label')"
            :categoriesCollection="selectedCategories"
            class="sw-product-detail__select-category"
            :placeholder="$tc('sas-blog.elements.listing.config.blogCategories.placeholder')">
        </sas-blog-category-tree-field>
    {% endblock %}

    {% block sas_cms_element_blog_listing_config_show_filter_select %}
        <sw-card
            :title="$tc('sas-blog.elements.listing.config.showFilter.label')"
            :hero="false"
            :isLoading="false"
            :large="false"
            class="sw-blog-config-filter">

            <sw-checkbox-field
                :label="$tc('sas-blog.elements.listing.config.showFilter.options.filter.category')"
                v-model="element.config.showCategoryFilter.value">
            </sw-checkbox-field>

            <sw-checkbox-field
                :label="$tc('sas-blog.elements.listing.config.showFilter.options.filter.author')"
                v-model="element.config.showAuthorFilter.value">
            </sw-checkbox-field>
        </sw-card>
    {% endblock %}

</div>
{% endblock %}
