@import "~scss/variables";

.sw-cms-el-config-blog-box {
    .sw-select,
    .sw-single-select {
        margin-bottom: 22px;
    }

    .sw-select-result .sw-select-result__result-item-text {
        display: block;

        .sw-highlight-text {
            display: inline;
        }

        .sw-cms-el-config-blog-box__select-blog-number {
            display: inline;
            color: $color-gray-400;
            margin-left: 10px;
        }
    }

    .sw-entity-single-select__selection {
        white-space: nowrap;
        display: flex;
        align-items: center;
    }

    .sw-entity-single-select__selection-text {
        vertical-align: middle;
        padding-right: 16px;
        width: 100%;
    }
}
