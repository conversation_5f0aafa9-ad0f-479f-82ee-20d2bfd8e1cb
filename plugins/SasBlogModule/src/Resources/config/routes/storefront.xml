<routes xmlns="http://symfony.com/schema/routing"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://symfony.com/schema/routing
        https://symfony.com/schema/routing/routing-1.0.xsd">

    <route id="frontend.landing.page"
           path="/landingPage/{landingPageId}"
           methods="GET"
           controller="Shopware\Storefront\Controller\LandingPageController::index">
        <default key="_httpCache">true</default>
        <default key="_routeScope"><list><string>storefront</string></list></default>
        <default key="XmlHttpRequest">true</default>
        <default key="_controllerName">landingpage</default>
        <default key="_controllerAction">index</default>
        <option key="seo">true</option>
    </route>
</routes>
