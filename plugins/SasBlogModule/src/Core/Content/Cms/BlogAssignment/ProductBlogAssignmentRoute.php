<?php declare(strict_types=1);

namespace Sas\BlogModule\Core\Content\Cms\BlogAssignment;

use Sas\BlogModule\Content\Blog\BlogEntriesCollection;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Plugin\Exception\DecorationPatternException;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['store-api']])]
class ProductBlogAssignmentRoute extends AbstractProductBlogAssignmentRoute
{
    /**
     * @internal
     */
    public function __construct(
        private readonly EntityRepository $productRepository,
    ) {
    }

    public function getDecorated(): AbstractProductBlogAssignmentRoute
    {
        throw new DecorationPatternException(self::class);
    }

    #[Route(path: '/store-api/product/{productId}/blog-assignment', name: 'store-api.product.blog-assignment', defaults: ['_entity' => 'product'], methods: ['POST'])]
    public function load(string $productId, Request $request, SalesChannelContext $context, Criteria $criteria): ProductBlogAssignmentRouteResponse
    {
        $product = $this->loadProduct($productId, $context);
        if (!$product) {
            return new ProductBlogAssignmentRouteResponse(new BlogEntriesCollection());
        }

        $blogs = $product->getExtension('assignedBlogs');
        if (!$blogs instanceof BlogEntriesCollection) {
            return new ProductBlogAssignmentRouteResponse(new BlogEntriesCollection());
        }

        return new ProductBlogAssignmentRouteResponse($blogs);
    }

    private function loadProduct(string $productId, SalesChannelContext $context): ?ProductEntity
    {
        $criteria = new Criteria([$productId]);
        $criteria->addAssociation('assignedBlogs.media');

        $product = $this->productRepository->search($criteria, $context->getContext())->first();

        if (!$product instanceof ProductEntity) {
            return null;
        }

        return $product;
    }
}
