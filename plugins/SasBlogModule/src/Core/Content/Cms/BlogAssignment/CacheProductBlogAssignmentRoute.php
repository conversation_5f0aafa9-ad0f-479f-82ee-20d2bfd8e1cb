<?php declare(strict_types=1);

namespace Sas\BlogModule\Core\Content\Cms\BlogAssignment;

use Sas\BlogModule\Content\Blog\Events\BlogAssignmentRouteCacheKeyEvent;
use Sas\BlogModule\Content\Blog\Events\BlogAssignmentRouteCacheTagsEvent;
use Shopware\Core\Framework\Adapter\Cache\AbstractCacheTracer;
use Shopware\Core\Framework\Adapter\Cache\CacheValueCompressor;
use Shopware\Core\Framework\DataAbstractionLayer\Cache\EntityCacheKeyGenerator;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\RuleAreas;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Util\Json;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

#[Route(defaults: ['_routeScope' => ['store-api']])]
class CacheProductBlogAssignmentRoute extends AbstractProductBlogAssignmentRoute
{
    /**
     * @internal
     *
     * @param AbstractCacheTracer<ProductBlogAssignmentRouteResponse> $tracer
     */
    public function __construct(
        private readonly AbstractProductBlogAssignmentRoute $decorated,
        private readonly CacheInterface $cache,
        private readonly EntityCacheKeyGenerator $generator,
        private readonly AbstractCacheTracer $tracer,
        private readonly EventDispatcherInterface $dispatcher,
    ) {
    }

    public function getDecorated(): AbstractProductBlogAssignmentRoute
    {
        return $this->decorated;
    }

    public static function buildName(string $id): string
    {
        return 'blog-assignment-route-' . $id;
    }

    #[Route(path: '/store-api/product/{productId}/blog-assignment', name: 'store-api.product.blog-assignment', defaults: ['_entity' => 'product'], methods: ['POST'])]
    public function load(string $productId, Request $request, SalesChannelContext $context, Criteria $criteria): ProductBlogAssignmentRouteResponse
    {
        $key = $this->generateKey($productId, $request, $context, $criteria);

        if ($key === null) {
            return $this->getDecorated()->load($productId, $request, $context, $criteria);
        }

        $value = $this->cache->get($key, function (ItemInterface $item) use ($productId, $request, $context, $criteria) {
            $name = self::buildName($productId);

            $response = $this->tracer->trace($name, fn () => $this->getDecorated()->load($productId, $request, $context, $criteria));

            $item->tag($this->generateTags($productId, $request, $response, $context, $criteria));

            return CacheValueCompressor::compress($response);
        });

        return CacheValueCompressor::uncompress($value);
    }

    private function generateKey(string $productId, Request $request, SalesChannelContext $context, Criteria $criteria): ?string
    {
        $parts = [
            $this->generator->getCriteriaHash($criteria),
            $this->generator->getSalesChannelContextHash($context, [RuleAreas::PRODUCT_AREA]),
        ];

        $event = new BlogAssignmentRouteCacheKeyEvent($productId, $parts, $request, $context, $criteria);
        $this->dispatcher->dispatch($event);

        if (!$event->shouldCache()) {
            return null;
        }

        return self::buildName($productId) . '-' . md5(Json::encode($event->getParts()));
    }

    /**
     * @return array<string>
     */
    private function generateTags(string $productId, Request $request, ProductBlogAssignmentRouteResponse $response, SalesChannelContext $context, Criteria $criteria): array
    {
        $tags = array_merge(
            $this->tracer->get(self::buildName($productId)),
            [self::buildName($productId)]
        );

        $event = new BlogAssignmentRouteCacheTagsEvent($productId, $tags, $request, $response, $context, $criteria);
        $this->dispatcher->dispatch($event);

        return array_unique(array_filter($event->getTags()));
    }
}
