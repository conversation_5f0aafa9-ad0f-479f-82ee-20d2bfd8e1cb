<?php declare(strict_types=1);

namespace Bs\ProductUsp;

use Bs\ProductUsp\Service\CustomFieldService;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\ActivateContext;
use Shopware\Core\Framework\Plugin\Context\DeactivateContext;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;

class BsProductUsp extends Plugin
{
    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);

        $this->getCustomFieldsService()->addCustomFields($installContext->getContext());
    }

    public function uninstall(UninstallContext $uninstallContext): void
    {
        parent::uninstall($uninstallContext);

        $this->getCustomFieldsService()->removeCustomFields($uninstallContext->getContext());
    }

    public function activate(ActivateContext $activateContext): void
    {
        parent::activate($activateContext);

        $this->getCustomFieldsService()->activateCustomFields($activateContext->getContext());
    }

    public function deactivate(DeactivateContext $deactivateContext): void
    {
        parent::deactivate($deactivateContext);

        $this->getCustomFieldsService()->deactivateCustomFields($deactivateContext->getContext());
    }

    /**
     * @return CustomFieldService
     */
    private function getCustomFieldsService(): CustomFieldService
    {
        if ($this->container->has(CustomFieldService::class)) {
            return $this->container->get(CustomFieldService::class);
        }

        return new CustomFieldService(
            $this->container->get('custom_field_set.repository'),
            $this->container->get('custom_field_set_relation.repository')
        );
    }
}
