{% sw_extends '@Storefront/storefront/page/product-detail/headline.html.twig' %}

{% block product_indholder_block %}
    {{ parent() }}
    <div>
        <ul class="bs-product-usp">
            {% for i in 1..8  %}
                {% if attribute(page.product.customFields, 'product_usp_' ~ i) != '' and attribute(page.product.customFields, 'product_usp_' ~ i) != 'null' %}
                    <li>{% sw_icon 'checkmark' style {'pack': 'solid', 'size': 'xs'} %}<span class="bs-usp-text">{{ attribute(page.product.customFields, 'product_usp_' ~ i) }}</span></li>
                {% endif %} 
            {% endfor %}
        </ul>
    </div>
{% endblock %}