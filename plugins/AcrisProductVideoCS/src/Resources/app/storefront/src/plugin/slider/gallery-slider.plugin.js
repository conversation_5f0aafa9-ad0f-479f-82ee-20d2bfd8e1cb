import ViewportDetection from 'src/helper/viewport-detection.helper';
import GallerySliderParent from 'src/plugin/slider/gallery-slider.plugin';
import deepmerge from 'deepmerge';

export default class GallerySliderPlugin extends GallerySliderParent {
    static options = deepmerge(GallerySliderParent.options, {
        enteredFullscreen: false,
        iframeElement: null,
        gallerySliderItemContainerClass: '.gallery-slider-item-container',
        tnsSlideActiveClass: 'tns-slide-active',
        productVideoClass: '.acris-product-video-iframe',
        navigationThumbnailSliderEvent: 'acrisNavigateThumbnailSlider',
    });

    init() {
        super.init();
        document.addEventListener('fullscreenchange', this.onFullscreenChange.bind(this));
    }

    _navigateThumbnailSlider() {
        document.$emitter.publish(this.options.navigationThumbnailSliderEvent);
        super._navigateThumbnailSlider();
    }

    onFullscreenChange() {
        this.options.enteredFullscreen = !!document.fullscreenElement;
        if (this.options.enteredFullscreen !== true && this.options.iframeElement) {
            this.rebuild(ViewportDetection.getCurrentViewport());
        }
    }

    rebuild(viewport = ViewportDetection.getCurrentViewport()) {
        let videoActive = this._checkProductVideo();

        if (!videoActive || this.options.enteredFullscreen !== true) {
            this.options.iframeElement = null;
            super.rebuild(viewport);
        }
    }

    _checkProductVideo() {
        let videoActive = false;

        if (!this.options.iframeElement) {
            var childElements = this.el.querySelectorAll(this.options.gallerySliderItemContainerClass);

            if (childElements) {
                for (var key in childElements) {
                    if (childElements[key] && childElements[key].classList && childElements[key].classList.contains(this.options.tnsSlideActiveClass)) {
                        var searchedElement = childElements[key].querySelector(this.options.productVideoClass);

                        if (searchedElement) {
                            this.options.iframeElement = searchedElement;
                        }
                    }
                }
            }
        }

        if (this.options.iframeElement) {
            videoActive = true;
        }

        return videoActive;
    }
}
