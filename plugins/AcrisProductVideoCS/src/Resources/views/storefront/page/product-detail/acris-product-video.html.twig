{% block acris_product_detail_page_product_video_videos %}
    {% if page.product.extensions.acrisVideos|first %}
        {% set productVideo = page.product.extensions.acrisVideos %}
        {% if productVideo.type == 'youtube' %}
            {% set productVideoImg = 'https://img.youtube.com/vi/' ~''~ productVideo.translated.link|split('/')|last ~''~ '/2.jpg' %}
        {% else %}
            {% set productVideoImg = 'https://vumbnail.com/' ~''~ productVideo.translated.link|split('/')|last ~''~ '.jpg' %}
        {% endif %}
    {% endif %}

    {% block acris_product_detail_page_product_video_videos_inner %}
        {% if productVideo and config('AcrisProductVideoCS.config').displayVideosInDescription == true %}
            {% if productVideo|length == 1 and config('AcrisProductVideoCS.config').showFirstVideoAlsoInDescription == false %}

            {% else %}
                {% block acris_product_detail_page_product_video_videos_title %}
                    {% if cmsElement is defined and cmsElement == true %}
                        {% if config.showTitle.value %}
                            <div class="h3 product-detail-description-title">
                                {{ "acrisProductVideo.description.videoDescriptionLabel"|trans|sw_sanitize }}
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="h3 product-detail-description-title">
                            {{ "acrisProductVideo.description.videoDescriptionLabel"|trans|sw_sanitize }}
                        </div>
                    {% endif %}
                {% endblock %}
            {% endif %}
            {% block acris_product_detail_page_product_video_videos_container %}
                <div class="acris-product-video-description-container product-detail-description-text container">
                    {% block acris_product_detail_page_product_video_videos_row %}
                        <div class="acris-product-video-description-row row">
                            {% for item in productVideo %}
                                {% if item.metaData %}
                                    {% set metaUrlData = item.metaData %}
                                {% endif %}
                                {% set noCookie = item.translated.link|split('youtube-nocookie.com') %}
                                {% if loop.index == 1 %}
                                    {% if config('AcrisProductVideoCS.config').showFirstVideoAlsoInDescription == true %}
                                        {% if item.type == 'youtube' or item.type == 'vimeo' %}
                                            {% set loadVideoAfterClick = 'loadNow' %}
                                            {% if config('AcrisProductVideoCS.config').loadVideosAfterClick == true %}
                                                {% set loadVideoAfterClick = 'loadAfter' %}
                                                {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/iframe-youtube-load-after.html.twig' %}
                                            {% else %}
                                                {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/iframe-youtube.html.twig' %}
                                            {% endif %}
                                        {% elseif item.type == 'embed' %}
                                            {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/iframe-embed.html.twig' %}
                                        {% elseif item.type == 'media' %}
                                            {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/video-media.html.twig' %}
                                        {% endif %}
                                    {% endif %}
                                {% else %}
                                    {% if item.type == 'youtube' or item.type == 'vimeo' %}
                                        {% set loadVideoAfterClick = 'loadNow' %}
                                        {% if config('AcrisProductVideoCS.config').loadVideosAfterClick == true %}
                                            {% set loadVideoAfterClick = 'loadAfter' %}
                                            {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/iframe-youtube-load-after.html.twig' %}
                                        {% else %}
                                            {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/iframe-youtube.html.twig' %}
                                        {% endif %}
                                    {% elseif item.type == 'embed' %}
                                        {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/iframe-embed.html.twig' %}
                                    {% elseif item.type == 'media' %}
                                        {% sw_include '@AcrisProductVideoCS/storefront/component/custom/description/video-media.html.twig' %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}
        {% endif %}
    {% endblock %}
{% endblock %}
